<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>附件管理器 - GoldenLedger</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            padding: 20px 30px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .stats {
            padding: 20px 30px;
            background: #f1f5f9;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #4f46e5;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-weight: 500;
        }

        .content {
            padding: 30px;
        }

        .attachment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .attachment-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .attachment-card:hover {
            border-color: #4f46e5;
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.1);
        }

        .attachment-preview {
            height: 200px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .attachment-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }

        .file-icon {
            font-size: 4rem;
            color: #94a3b8;
        }

        .attachment-info {
            padding: 20px;
        }

        .attachment-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .attachment-meta {
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .journal-link {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .journal-link a {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
        }

        .journal-link a:hover {
            text-decoration: underline;
        }

        .attachment-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .loading {
            text-align: center;
            padding: 60px;
            color: #64748b;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #4f46e5;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px;
            color: #64748b;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .attachment-grid {
                grid-template-columns: 1fr;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📎 附件管理器</h1>
            <p>管理所有上传的附件文件，查看与记录的关联关系</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="refreshData()">
                🔄 刷新数据
            </button>
            <button class="btn btn-secondary" onclick="exportAttachments()">
                📦 导出附件
            </button>
            <button class="btn btn-secondary" onclick="showUploadModal()">
                📤 批量上传
            </button>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索附件名称、类型或关联记录..." onkeyup="filterAttachments()">
            </div>
        </div>

        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-number" id="totalAttachments">-</div>
                <div class="stat-label">总附件数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSize">-</div>
                <div class="stat-label">总大小</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="imageCount">-</div>
                <div class="stat-label">图片文件</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="documentCount">-</div>
                <div class="stat-label">文档文件</div>
            </div>
        </div>

        <div class="content">
            <div id="loadingContainer" class="loading">
                <div class="spinner"></div>
                <p>正在加载附件数据...</p>
            </div>

            <div id="attachmentContainer" class="attachment-grid" style="display: none;">
                <!-- 附件卡片将在这里动态生成 -->
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="empty-state-icon">📎</div>
                <h3>暂无附件</h3>
                <p>还没有上传任何附件文件</p>
            </div>
        </div>
    </div>

    <!-- 附件详情模态框 -->
    <div id="attachmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>附件详情</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalContent">
                <!-- 模态框内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="api_config.js"></script>
    <script>
        let allAttachments = [];
        let allJournalEntries = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });

        // 获取认证token
        function getAuthToken() {
            return localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        }

        // 检查认证状态
        function checkAuth() {
            const token = getAuthToken();
            if (!token) {
                alert('请先登录系统');
                window.location.href = 'index.html';
                return false;
            }
            return true;
        }

        // 加载所有数据
        async function loadData() {
            try {
                if (!checkAuth()) return;

                showLoading(true);

                const token = getAuthToken();
                const headers = {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                };

                // 并行加载附件和记录数据
                const [attachmentsResponse, entriesResponse] = await Promise.all([
                    fetch(`${API_CONFIG.baseURL}/api/attachments/all`, { headers }),
                    fetch(`${API_CONFIG.baseURL}/api/journal-entries/default`, { headers })
                ]);

                if (attachmentsResponse.ok) {
                    const attachmentsData = await attachmentsResponse.json();
                    allAttachments = attachmentsData.data || [];
                } else if (attachmentsResponse.status === 401) {
                    alert('登录已过期，请重新登录');
                    localStorage.removeItem('authToken');
                    sessionStorage.removeItem('authToken');
                    window.location.href = 'index.html';
                    return;
                } else {
                    allAttachments = [];
                }

                if (entriesResponse.ok) {
                    const entriesData = await entriesResponse.json();
                    allJournalEntries = entriesData.data || [];
                } else {
                    allJournalEntries = [];
                }

                updateStats();
                renderAttachments();
                showLoading(false);

            } catch (error) {
                console.error('加载数据失败:', error);
                showLoading(false);
                showEmptyState();
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loadingContainer').style.display = show ? 'block' : 'none';
            document.getElementById('attachmentContainer').style.display = show ? 'none' : 'block';
            document.getElementById('emptyState').style.display = 'none';
        }

        // 显示空状态
        function showEmptyState() {
            document.getElementById('loadingContainer').style.display = 'none';
            document.getElementById('attachmentContainer').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
        }

        // 更新统计信息
        function updateStats() {
            const totalAttachments = allAttachments.length;
            const totalSize = allAttachments.reduce((sum, att) => sum + (att.file_size || 0), 0);
            const imageCount = allAttachments.filter(att => att.file_type && att.file_type.startsWith('image/')).length;
            const documentCount = allAttachments.filter(att => att.file_type && (att.file_type.includes('pdf') || att.file_type.includes('document'))).length;

            document.getElementById('totalAttachments').textContent = totalAttachments;
            document.getElementById('totalSize').textContent = formatFileSize(totalSize);
            document.getElementById('imageCount').textContent = imageCount;
            document.getElementById('documentCount').textContent = documentCount;
        }

        // 渲染附件列表
        function renderAttachments(attachments = allAttachments) {
            const container = document.getElementById('attachmentContainer');
            
            if (attachments.length === 0) {
                showEmptyState();
                return;
            }

            container.innerHTML = attachments.map(attachment => {
                const journalEntry = allJournalEntries.find(entry => entry.id === attachment.journal_entry_id);
                const isImage = attachment.file_type && attachment.file_type.startsWith('image/');
                const downloadUrl = `https://goldenledger-files.souyousann.workers.dev/${attachment.file_path}`;

                return `
                    <div class="attachment-card">
                        <div class="attachment-preview">
                            ${isImage ? 
                                `<img src="${downloadUrl}" alt="${attachment.file_name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                 <div class="file-icon" style="display: none;">🖼️</div>` :
                                `<div class="file-icon">${getFileIcon(attachment.file_type)}</div>`
                            }
                        </div>
                        <div class="attachment-info">
                            <div class="attachment-title">${attachment.file_name}</div>
                            <div class="attachment-meta">
                                ${formatFileSize(attachment.file_size)} • ${formatDate(attachment.upload_date)}
                            </div>
                            ${journalEntry ? `
                                <div class="journal-link">
                                    <a href="javascript:void(0)" onclick="showJournalEntry('${journalEntry.id}')">
                                        📝 ${journalEntry.description || '未命名记录'}
                                    </a>
                                </div>
                            ` : '<div class="journal-link">⚠️ 关联记录不存在</div>'}
                            <div class="attachment-actions">
                                <button class="btn btn-primary btn-small" onclick="downloadAttachment('${attachment.id}', '${attachment.file_name}')">
                                    📥 下载
                                </button>
                                <button class="btn btn-secondary btn-small" onclick="showAttachmentDetails('${attachment.id}')">
                                    👁️ 详情
                                </button>
                                <button class="btn btn-danger btn-small" onclick="deleteAttachment('${attachment.id}', '${attachment.file_name}')">
                                    🗑️ 删除
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.style.display = 'grid';
            document.getElementById('emptyState').style.display = 'none';
        }

        // 获取文件图标
        function getFileIcon(fileType) {
            if (!fileType) return '📄';
            if (fileType.startsWith('image/')) return '🖼️';
            if (fileType.includes('pdf')) return '📕';
            if (fileType.includes('document') || fileType.includes('word')) return '📝';
            if (fileType.includes('spreadsheet') || fileType.includes('excel')) return '📊';
            if (fileType.includes('presentation') || fileType.includes('powerpoint')) return '📊';
            return '📄';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
        }

        // 过滤附件
        function filterAttachments() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const filteredAttachments = allAttachments.filter(attachment => {
                const journalEntry = allJournalEntries.find(entry => entry.id === attachment.journal_entry_id);
                return attachment.file_name.toLowerCase().includes(searchTerm) ||
                       (attachment.file_type && attachment.file_type.toLowerCase().includes(searchTerm)) ||
                       (journalEntry && journalEntry.description && journalEntry.description.toLowerCase().includes(searchTerm));
            });
            renderAttachments(filteredAttachments);
        }

        // 刷新数据
        function refreshData() {
            loadData();
        }

        // 下载附件
        function downloadAttachment(attachmentId, filename) {
            const attachment = allAttachments.find(att => att.id === attachmentId);
            if (attachment) {
                const downloadUrl = `https://goldenledger-files.souyousann.workers.dev/${attachment.file_path}`;
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            }
        }

        // 显示附件详情
        function showAttachmentDetails(attachmentId) {
            const attachment = allAttachments.find(att => att.id === attachmentId);
            const journalEntry = allJournalEntries.find(entry => entry.id === attachment.journal_entry_id);
            
            if (attachment) {
                const isImage = attachment.file_type && attachment.file_type.startsWith('image/');
                const downloadUrl = `https://goldenledger-files.souyousann.workers.dev/${attachment.file_path}`;
                
                document.getElementById('modalContent').innerHTML = `
                    <div style="margin-bottom: 20px;">
                        ${isImage ? `<img src="${downloadUrl}" style="max-width: 100%; border-radius: 8px;">` : 
                                   `<div style="text-align: center; padding: 40px; background: #f8fafc; border-radius: 8px;">
                                        <div style="font-size: 4rem; margin-bottom: 10px;">${getFileIcon(attachment.file_type)}</div>
                                        <p>${attachment.file_name}</p>
                                    </div>`}
                    </div>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr><td style="padding: 8px; font-weight: bold;">文件名:</td><td style="padding: 8px;">${attachment.file_name}</td></tr>
                        <tr><td style="padding: 8px; font-weight: bold;">文件大小:</td><td style="padding: 8px;">${formatFileSize(attachment.file_size)}</td></tr>
                        <tr><td style="padding: 8px; font-weight: bold;">文件类型:</td><td style="padding: 8px;">${attachment.file_type}</td></tr>
                        <tr><td style="padding: 8px; font-weight: bold;">上传时间:</td><td style="padding: 8px;">${formatDate(attachment.upload_date)}</td></tr>
                        <tr><td style="padding: 8px; font-weight: bold;">存储路径:</td><td style="padding: 8px; word-break: break-all;">${attachment.file_path}</td></tr>
                        <tr><td style="padding: 8px; font-weight: bold;">关联记录:</td><td style="padding: 8px;">${journalEntry ? journalEntry.description : '记录不存在'}</td></tr>
                    </table>
                `;
                document.getElementById('attachmentModal').style.display = 'block';
            }
        }

        // 删除附件
        async function deleteAttachment(attachmentId, filename) {
            if (!confirm(`确定要删除附件 "${filename}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const token = getAuthToken();
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const response = await fetch(`${API_CONFIG.baseURL}/api/attachments/${attachmentId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ filename: filename })
                });

                if (response.ok) {
                    alert('附件删除成功');
                    loadData(); // 重新加载数据
                } else if (response.status === 401) {
                    alert('登录已过期，请重新登录');
                    window.location.href = 'index.html';
                } else {
                    const error = await response.json();
                    alert('删除失败: ' + (error.error || '未知错误'));
                }
            } catch (error) {
                console.error('删除附件失败:', error);
                alert('删除失败，请重试');
            }
        }

        // 显示记录详情
        function showJournalEntry(entryId) {
            // 这里可以跳转到记录详情页面或显示记录详情
            window.open(`index.html#entry-${entryId}`, '_blank');
        }

        // 导出附件
        function exportAttachments() {
            alert('附件导出功能开发中...');
        }

        // 显示上传模态框
        function showUploadModal() {
            alert('批量上传功能开发中...');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('attachmentModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('attachmentModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>

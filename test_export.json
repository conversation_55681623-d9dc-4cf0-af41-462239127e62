[{"id": "J20250712005440", "company_id": "default", "entry_date": "2025-07-12", "entry_time": "00:54:38", "entry_datetime": "2025-07-12T00:54:38", "description": "コンビニで事務用品を購入", "debit_account": "事務用品費", "credit_account": "現金", "amount": 1200.02, "reference_number": "REF20250712005440", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 15:54:58", "updated_at": "2025-07-11 15:55:30", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250712000724", "company_id": "default", "entry_date": "2025-07-12", "entry_time": "00:07:23", "entry_datetime": "2025-07-12T00:07:23", "description": "コンビニで事務用品を購入", "debit_account": "事務用品費", "credit_account": "現金または預金", "amount": 1200, "reference_number": "REF20250712000724", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 15:07:27", "updated_at": "2025-07-11 15:07:27", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711211125", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "23:51:14", "entry_datetime": "2025-07-11T23:51:14", "description": "测试编辑功能 - 手机端修复", "debit_account": "事務用品費", "credit_account": "現金", "amount": 1233, "reference_number": "TEST123", "ai_generated": 0, "ai_confidence": 0, "attachment_path": "attachments/J20250711211125_915f2123.jpg", "created_at": "2025-07-11 12:11:30", "updated_at": "2025-07-11 14:54:29", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711204552", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "20:45:50", "entry_datetime": "2025-07-11T20:45:50", "description": "島対面一般鮮魚で魚3匹を購入", "debit_account": "食品支出", "credit_account": "现金/银行存款", "amount": 600, "reference_number": "REF20250711204552", "ai_generated": 0, "ai_confidence": 0, "attachment_path": "attachments/J20250711204552_52973e43.JPG", "created_at": "2025-07-11 11:45:55", "updated_at": "2025-07-11 11:45:55", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711165206", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "16:52:01", "entry_datetime": "2025-07-11T16:52:01", "description": "島対面一般鮮魚で魚を購入", "debit_account": "食品支出", "credit_account": "现金/银行存款", "amount": 600, "reference_number": "REF20250711165206", "ai_generated": 0, "ai_confidence": 0, "attachment_path": "attachments/J20250711165206_c01ac89b.JPG", "created_at": "2025-07-11 07:52:09", "updated_at": "2025-07-11 10:12:32", "debit_tax_rate": 8, "credit_tax_rate": 0}, {"id": "REV20250711003", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "16:45:00", "entry_datetime": "2025-07-11T16:45:00", "description": "銀行預金利息収入", "debit_account": "普通預金", "credit_account": "受取利息", "amount": 150, "reference_number": "INT-2025-001", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 07:19:08", "updated_at": "2025-07-11 11:45:16", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711155839", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "15:58:29", "entry_datetime": "2025-07-11T15:58:29", "description": "島対面一般鮮魚で一般鮮魚を購入", "debit_account": "食品支出", "credit_account": "现金/银行存款", "amount": 600, "reference_number": "REF20250711155839", "ai_generated": 0, "ai_confidence": 0, "attachment_path": "attachments/J20250711155839_3cdd1dd6.JPG", "created_at": "2025-07-11 06:58:47", "updated_at": "2025-07-11 06:58:47", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "EXP20250711003", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "15:20:00", "entry_datetime": "2025-07-11T16:19:08.075731", "description": "従業員交通費", "debit_account": "交通費", "credit_account": "現金", "amount": 3200, "reference_number": "TRANS-2025-07", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 07:19:08", "updated_at": "2025-07-11 07:19:08", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "REV20250711002", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "14:30:00", "entry_datetime": "2025-07-11T16:19:08.044951", "description": "XYZ商事からのサービス収入", "debit_account": "現金", "credit_account": "売上高", "amount": 25000, "reference_number": "SRV-2025-002", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 07:19:08", "updated_at": "2025-07-11 07:19:08", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711141938", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "14:19:34", "entry_datetime": "2025-07-11T14:19:34", "description": "島対面一般鮮魚で魚(6匹)を600円で購入", "debit_account": "食品支出", "credit_account": "现金/银行存款", "amount": 600, "reference_number": "REF20250711141938", "ai_generated": 0, "ai_confidence": 0, "attachment_path": "attachments/J20250711141938_b217ae5b.jpg", "created_at": "2025-07-11 05:19:44", "updated_at": "2025-07-11 06:57:46", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711141322", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "14:13:20", "entry_datetime": "2025-07-11T14:13:20", "description": "セブンイレブンで事務用品を購入", "debit_account": "事務用品費", "credit_account": "現金または預金", "amount": 1200, "reference_number": "REF20250711141322", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 05:13:22", "updated_at": "2025-07-11 06:53:55", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711140747", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "14:07:46", "entry_datetime": "2025-07-11T14:07:46", "description": "セブンイレブンで事務用品を購入", "debit_account": "事務用品費", "credit_account": "現金または預金（具体的な口座は不明）", "amount": 1200, "reference_number": "REF20250711140747", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 05:07:47", "updated_at": "2025-07-11 06:53:58", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "EXP20250711002", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "11:15:00", "entry_datetime": "2025-07-11T16:19:08.068412", "description": "電気代支払い", "debit_account": "水道光熱費", "credit_account": "普通預金", "amount": 8500, "reference_number": "ELEC-2025-07", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 07:19:08", "updated_at": "2025-07-11 07:19:08", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "REV20250711001", "company_id": "default", "entry_date": "2025-07-11", "entry_time": "09:00:00", "entry_datetime": "2025-07-11T16:19:08.035932", "description": "ABC株式会社からの売上", "debit_account": "普通預金", "credit_account": "売上高", "amount": 50000, "reference_number": "INV-2025-001", "ai_generated": 0, "ai_confidence": 0, "attachment_path": null, "created_at": "2025-07-11 07:19:08", "updated_at": "2025-07-11 07:19:08", "debit_tax_rate": 0, "credit_tax_rate": 0}, {"id": "J20250711222843", "company_id": "default", "entry_date": "2025-07-08", "entry_time": "22:28:42", "entry_datetime": "2025-07-08T22:28:42", "description": "ロピア 松戸店で食品、飲料などを9261円で購入", "debit_account": "食品及饮料", "credit_account": "现金或银行账户", "amount": 9261, "reference_number": "REF20250711222843", "ai_generated": 0, "ai_confidence": 0, "attachment_path": "attachments/J20250711222843_d98245d9.jpg", "created_at": "2025-07-11 13:28:49", "updated_at": "2025-07-11 13:28:49", "debit_tax_rate": 0, "credit_tax_rate": 0}]
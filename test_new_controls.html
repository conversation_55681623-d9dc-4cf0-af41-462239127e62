<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新按钮设计测试 - GoldenLedger</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .demo-section h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✨ ";
            margin-right: 10px;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #fecfef;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .note {
            background: rgba(255, 154, 158, 0.2);
            border-left: 4px solid #ff9a9e;
            padding: 15px;
            border-radius: 0 10px 10px 0;
            margin-top: 20px;
        }
        
        .note strong {
            color: #fecfef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 新按钮设计测试</h1>
            <p>重新设计的独立按钮控制系统</p>
        </div>
        
        <div class="demo-section">
            <h2>🎵 音乐控制系统</h2>
            <ul class="feature-list">
                <li>独立的圆形音乐按钮，无白色背景</li>
                <li>渐变色设计，播放时有动画效果</li>
                <li>点击按钮显示音量控制面板</li>
                <li>面板有指向按钮的箭头</li>
                <li>点击外部区域自动隐藏面板</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>👁️ 背景控制系统</h2>
            <ul class="feature-list">
                <li>独立的眼睛按钮，位于音乐按钮下方</li>
                <li>粉色渐变设计，与音乐按钮区分</li>
                <li>点击显示背景设置面板</li>
                <li>面板显示在按钮旁边，不是屏幕中央</li>
                <li>包含背景图片和动画效果控制</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🧪 测试说明</h3>
            <ol>
                <li><strong>查看右上角</strong>：应该看到两个独立的圆形按钮</li>
                <li><strong>点击音乐按钮 🎵</strong>：开始播放音乐并显示音量控制</li>
                <li><strong>点击眼睛按钮 👁️</strong>：显示背景设置面板</li>
                <li><strong>点击面板外部</strong>：面板自动隐藏</li>
                <li><strong>移动端测试</strong>：按F12切换到移动端视图测试</li>
            </ol>
        </div>
        
        <div class="note">
            <strong>注意：</strong>这是新设计的测试版本。所有控制面板初始状态都是完全隐藏的，只显示独立的按钮。点击按钮后才会显示相应的控制面板。
        </div>
    </div>

    <!-- 加载新的控制系统 -->
    <script src="music_control_new.js"></script>
    <script src="background_control_new.js"></script>
</body>
</html>

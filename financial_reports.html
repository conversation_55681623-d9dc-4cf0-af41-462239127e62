<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 高级财务报表系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .report-card { transition: all 0.3s ease; }
        .report-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .print-hidden { display: none; }
        @media print {
            .no-print { display: none !important; }
            .print-hidden { display: block !important; }
            body { background: white !important; }
        }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6 no-print">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">📊 高级财务报表系统</h1>
                    <p class="text-lg opacity-90">专业财务分析和报表生成</p>
                </div>
                <div class="flex items-center space-x-3">
                    <select id="period-selector" class="bg-white/20 text-white border border-white/30 rounded-lg px-3 py-2 text-sm">
                        <option value="current-month">今月</option>
                        <option value="last-month">先月</option>
                        <option value="current-quarter">今四半期</option>
                        <option value="current-year">今年度</option>
                        <option value="custom">カスタム期間</option>
                    </select>
                    <button id="export-pdf-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        📄 PDF出力
                    </button>
                    <button id="print-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        🖨️ 印刷
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Report Type Selection -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 no-print">
            <div class="report-card bg-white rounded-xl p-6 shadow-sm border cursor-pointer" data-report="balance-sheet">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">⚖️</span>
                    </div>
                    <h3 class="font-semibold mb-2">貸借対照表</h3>
                    <p class="text-gray-600 text-sm">Balance Sheet</p>
                </div>
            </div>

            <div class="report-card bg-white rounded-xl p-6 shadow-sm border cursor-pointer" data-report="income-statement">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">📈</span>
                    </div>
                    <h3 class="font-semibold mb-2">損益計算書</h3>
                    <p class="text-gray-600 text-sm">Income Statement</p>
                </div>
            </div>

            <div class="report-card bg-white rounded-xl p-6 shadow-sm border cursor-pointer" data-report="cash-flow">
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">💰</span>
                    </div>
                    <h3 class="font-semibold mb-2">キャッシュフロー</h3>
                    <p class="text-gray-600 text-sm">Cash Flow Statement</p>
                </div>
            </div>

            <div class="report-card bg-white rounded-xl p-6 shadow-sm border cursor-pointer" data-report="trial-balance">
                <div class="text-center">
                    <div class="w-16 h-16 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🧮</span>
                    </div>
                    <h3 class="font-semibold mb-2">試算表</h3>
                    <p class="text-gray-600 text-sm">Trial Balance</p>
                </div>
            </div>
        </section>

        <!-- Report Content -->
        <section id="report-content" class="space-y-8">
            <!-- Balance Sheet -->
            <div id="balance-sheet-report" class="report-section hidden">
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold">貸借対照表</h2>
                        <p class="text-gray-600">Balance Sheet</p>
                        <p id="balance-sheet-period" class="text-sm text-gray-500 mt-2">2024年1月31日現在</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Assets -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4 text-blue-600">資産の部 (Assets)</h3>
                            <div class="space-y-3">
                                <div class="border-b pb-2">
                                    <h4 class="font-medium text-gray-800">流動資産</h4>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>現金</span>
                                    <span class="font-mono">¥500,000</span>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>普通預金</span>
                                    <span class="font-mono">¥2,300,000</span>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>売掛金</span>
                                    <span class="font-mono">¥800,000</span>
                                </div>
                                <div class="flex justify-between pl-4 border-t pt-2 font-semibold">
                                    <span>流動資産合計</span>
                                    <span class="font-mono">¥3,600,000</span>
                                </div>

                                <div class="border-b pb-2 mt-4">
                                    <h4 class="font-medium text-gray-800">固定資産</h4>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>建物</span>
                                    <span class="font-mono">¥5,000,000</span>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>機械装置</span>
                                    <span class="font-mono">¥1,200,000</span>
                                </div>
                                <div class="flex justify-between pl-4 border-t pt-2 font-semibold">
                                    <span>固定資産合計</span>
                                    <span class="font-mono">¥6,200,000</span>
                                </div>

                                <div class="flex justify-between font-bold text-lg border-t-2 pt-3 text-blue-600">
                                    <span>資産合計</span>
                                    <span class="font-mono">¥9,800,000</span>
                                </div>
                            </div>
                        </div>

                        <!-- Liabilities & Equity -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4 text-red-600">負債・純資産の部</h3>
                            <div class="space-y-3">
                                <div class="border-b pb-2">
                                    <h4 class="font-medium text-gray-800">流動負債</h4>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>買掛金</span>
                                    <span class="font-mono">¥400,000</span>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>未払金</span>
                                    <span class="font-mono">¥200,000</span>
                                </div>
                                <div class="flex justify-between pl-4 border-t pt-2 font-semibold">
                                    <span>流動負債合計</span>
                                    <span class="font-mono">¥600,000</span>
                                </div>

                                <div class="border-b pb-2 mt-4">
                                    <h4 class="font-medium text-gray-800">固定負債</h4>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>長期借入金</span>
                                    <span class="font-mono">¥2,000,000</span>
                                </div>
                                <div class="flex justify-between pl-4 border-t pt-2 font-semibold">
                                    <span>固定負債合計</span>
                                    <span class="font-mono">¥2,000,000</span>
                                </div>

                                <div class="flex justify-between font-semibold border-t pt-2">
                                    <span>負債合計</span>
                                    <span class="font-mono">¥2,600,000</span>
                                </div>

                                <div class="border-b pb-2 mt-4">
                                    <h4 class="font-medium text-gray-800">純資産</h4>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>資本金</span>
                                    <span class="font-mono">¥5,000,000</span>
                                </div>
                                <div class="flex justify-between pl-4">
                                    <span>利益剰余金</span>
                                    <span class="font-mono">¥2,200,000</span>
                                </div>
                                <div class="flex justify-between pl-4 border-t pt-2 font-semibold">
                                    <span>純資産合計</span>
                                    <span class="font-mono">¥7,200,000</span>
                                </div>

                                <div class="flex justify-between font-bold text-lg border-t-2 pt-3 text-red-600">
                                    <span>負債・純資産合計</span>
                                    <span class="font-mono">¥9,800,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Income Statement -->
            <div id="income-statement-report" class="report-section hidden">
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold">損益計算書</h2>
                        <p class="text-gray-600">Income Statement</p>
                        <p id="income-statement-period" class="text-sm text-gray-500 mt-2">2024年1月1日〜2024年1月31日</p>
                    </div>

                    <div class="max-w-2xl mx-auto space-y-4">
                        <div class="flex justify-between items-center py-2">
                            <span class="font-medium">売上高</span>
                            <span class="font-mono text-lg">¥3,500,000</span>
                        </div>

                        <div class="border-t pt-4">
                            <h4 class="font-medium text-gray-800 mb-3">売上原価</h4>
                            <div class="pl-4 space-y-2">
                                <div class="flex justify-between">
                                    <span>商品仕入高</span>
                                    <span class="font-mono">¥1,200,000</span>
                                </div>
                                <div class="flex justify-between font-semibold border-t pt-2">
                                    <span>売上原価</span>
                                    <span class="font-mono">¥1,200,000</span>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center py-2 font-semibold text-lg border-t">
                            <span>売上総利益</span>
                            <span class="font-mono text-green-600">¥2,300,000</span>
                        </div>

                        <div class="border-t pt-4">
                            <h4 class="font-medium text-gray-800 mb-3">販売費及び一般管理費</h4>
                            <div class="pl-4 space-y-2">
                                <div class="flex justify-between">
                                    <span>給料手当</span>
                                    <span class="font-mono">¥800,000</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>地代家賃</span>
                                    <span class="font-mono">¥150,000</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>水道光熱費</span>
                                    <span class="font-mono">¥50,000</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>消耗品費</span>
                                    <span class="font-mono">¥30,000</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>その他</span>
                                    <span class="font-mono">¥70,000</span>
                                </div>
                                <div class="flex justify-between font-semibold border-t pt-2">
                                    <span>販管費合計</span>
                                    <span class="font-mono">¥1,100,000</span>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center py-2 font-semibold text-lg border-t">
                            <span>営業利益</span>
                            <span class="font-mono text-blue-600">¥1,200,000</span>
                        </div>

                        <div class="border-t pt-4">
                            <h4 class="font-medium text-gray-800 mb-3">営業外損益</h4>
                            <div class="pl-4 space-y-2">
                                <div class="flex justify-between">
                                    <span>受取利息</span>
                                    <span class="font-mono text-green-600">¥5,000</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>支払利息</span>
                                    <span class="font-mono text-red-600">(¥15,000)</span>
                                </div>
                                <div class="flex justify-between font-semibold border-t pt-2">
                                    <span>営業外損益</span>
                                    <span class="font-mono text-red-600">(¥10,000)</span>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center py-3 font-bold text-xl border-t-2 text-green-600">
                            <span>当期純利益</span>
                            <span class="font-mono">¥1,190,000</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div id="charts-section" class="grid grid-cols-1 lg:grid-cols-2 gap-8 no-print">
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <h3 class="text-lg font-semibold mb-4">📊 収益構成</h3>
                    <canvas id="revenue-composition-chart" width="400" height="300"></canvas>
                </div>

                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <h3 class="text-lg font-semibold mb-4">📈 月次推移</h3>
                    <canvas id="monthly-trend-chart" width="400" height="300"></canvas>
                </div>
            </div>
        </section>

        <!-- Analysis Section -->
        <section class="bg-white rounded-xl p-6 shadow-sm border no-print">
            <h3 class="text-lg font-semibold mb-4">🔍 AI財務分析</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">💪 強み</h4>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li>• 売上総利益率が65.7%と高水準</li>
                        <li>• 流動比率が6.0倍で安全性良好</li>
                        <li>• 営業利益率が34.3%と優秀</li>
                    </ul>
                </div>

                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2">⚠️ 注意点</h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• 固定費の比率が高い</li>
                        <li>• 売掛金の回転率要確認</li>
                        <li>• 設備投資の計画性</li>
                    </ul>
                </div>

                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">📈 改善提案</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• 売上拡大による固定費効率化</li>
                        <li>• 売掛金管理の強化</li>
                        <li>• キャッシュフロー改善</li>
                    </ul>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 全局变量
        let revenueCompositionChart, monthlyTrendChart;

        // 初始化图表
        function initCharts() {
            // 收益构成图
            const revenueCtx = document.getElementById('revenue-composition-chart').getContext('2d');
            revenueCompositionChart = new Chart(revenueCtx, {
                type: 'doughnut',
                data: {
                    labels: ['売上高', '営業外収益', 'その他'],
                    datasets: [{
                        data: [3500000, 5000, 50000],
                        backgroundColor: [
                            '#10b981',
                            '#3b82f6',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 月次推移图
            const monthlyCtx = document.getElementById('monthly-trend-chart').getContext('2d');
            monthlyTrendChart = new Chart(monthlyCtx, {
                type: 'line',
                data: {
                    labels: ['10月', '11月', '12月', '1月'],
                    datasets: [{
                        label: '売上高',
                        data: [3200000, 3300000, 3400000, 3500000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }, {
                        label: '営業利益',
                        data: [1000000, 1100000, 1150000, 1200000],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + (value / 1000000).toFixed(1) + 'M';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 显示报表
        function showReport(reportType) {
            // 隐藏所有报表
            document.querySelectorAll('.report-section').forEach(section => {
                section.classList.add('hidden');
            });

            // 显示选中的报表
            const reportSection = document.getElementById(reportType + '-report');
            if (reportSection) {
                reportSection.classList.remove('hidden');
            }

            // 更新报表卡片样式
            document.querySelectorAll('.report-card').forEach(card => {
                card.classList.remove('ring-2', 'ring-blue-500');
            });
            document.querySelector(`[data-report="${reportType}"]`).classList.add('ring-2', 'ring-blue-500');
        }

        // 导出PDF
        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // 添加标题
            doc.setFontSize(20);
            doc.text('財務報告書', 20, 30);

            // 添加日期
            doc.setFontSize(12);
            doc.text(`作成日: ${new Date().toLocaleDateString('ja-JP')}`, 20, 45);

            // 添加内容（简化版）
            doc.setFontSize(14);
            doc.text('貸借対照表', 20, 65);
            
            doc.setFontSize(10);
            doc.text('資産合計: ¥9,800,000', 20, 80);
            doc.text('負債合計: ¥2,600,000', 20, 90);
            doc.text('純資産合計: ¥7,200,000', 20, 100);

            doc.text('損益計算書', 20, 120);
            doc.text('売上高: ¥3,500,000', 20, 135);
            doc.text('営業利益: ¥1,200,000', 20, 145);
            doc.text('当期純利益: ¥1,190,000', 20, 155);

            // 保存PDF
            doc.save('financial-report.pdf');
        }

        // 打印
        function printReport() {
            window.print();
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 默认显示贷借对照表
            showReport('balance-sheet');

            // 报表选择
            document.querySelectorAll('.report-card').forEach(card => {
                card.addEventListener('click', function() {
                    const reportType = this.getAttribute('data-report');
                    showReport(reportType);
                });
            });

            // 导出PDF按钮
            document.getElementById('export-pdf-btn').addEventListener('click', exportToPDF);

            // 打印按钮
            document.getElementById('print-btn').addEventListener('click', printReport);

            // 期间选择器
            document.getElementById('period-selector').addEventListener('change', function() {
                const period = this.value;
                // 这里可以根据选择的期间更新报表数据
                console.log('Period changed to:', period);
            });
        });
    </script>
</body>
</html>

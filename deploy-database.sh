#!/bin/bash

# GoldenLedger D1数据库部署脚本
# 用于创建和初始化使用量跟踪数据库

echo "🚀 开始部署GoldenLedger D1数据库..."

# 数据库配置
DATABASE_NAME="goldenledger-db"
DATABASE_ID="bcff395d-3e68-44c7-b355-13f3014fa240"

echo "📊 数据库信息:"
echo "  名称: $DATABASE_NAME"
echo "  ID: $DATABASE_ID"

# 执行数据库架构
echo "🔧 执行数据库架构..."
wrangler d1 execute $DATABASE_NAME --file=database/schema.sql

if [ $? -eq 0 ]; then
    echo "✅ 数据库架构创建成功！"
else
    echo "❌ 数据库架构创建失败！"
    exit 1
fi

# 验证表创建
echo "🔍 验证表创建..."
wrangler d1 execute $DATABASE_NAME --command="SELECT name FROM sqlite_master WHERE type='table';"

echo "🎉 数据库部署完成！"
echo ""
echo "📋 创建的表："
echo "  - usage_tracking: 用户使用量跟踪"
echo "  - chat_sessions: 聊天会话记录"
echo "  - chat_messages: 聊天消息记录"
echo "  - user_plans: 用户计划配置"
echo "  - system_config: 系统配置"
echo ""
echo "🔗 数据库管理链接："
echo "  https://dash.cloudflare.com/bc6dbb1a5bb06691cdd6a8df6aa768f8/workers/d1/databases/$DATABASE_ID/metrics"
echo ""
echo "🌸 现在可以开始使用さくらちゃん的专业客服功能了！"

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_injector.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-blue-600">📸 OCR发票识别测试</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">上传发票/收据图片</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">选择文件:</label>
                    <input type="file" id="fileInput" accept="image/*,.pdf" class="w-full p-2 border rounded">
                </div>
                <button onclick="testOCR()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    测试OCR识别
                </button>
            </div>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">测试结果</h2>
            <div id="result" class="p-4 bg-gray-50 rounded min-h-32"></div>
        </div>
        
        <!-- 操作日志 -->
        <div class="bg-black text-green-400 p-6 rounded-lg shadow mt-6">
            <h2 class="text-xl font-semibold mb-4 text-white">📋 操作日志</h2>
            <div id="debug-log" class="font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto"></div>
            <button onclick="clearLog()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                清空日志
            </button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const colors = {
                info: 'text-green-400',
                error: 'text-red-400',
                warning: 'text-yellow-400',
                success: 'text-blue-400'
            };
            logDiv.innerHTML += `<span class="${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        async function testOCR() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<p class="text-red-600">请先选择一个文件</p>';
                log('测试失败: 未选择文件', 'error');
                return;
            }
            
            const file = fileInput.files[0];
            log(`开始测试OCR，文件: ${file.name}, 大小: ${(file.size / 1024).toFixed(2)}KB`, 'info');
            
            resultDiv.innerHTML = '<p class="text-blue-600">🔄 正在处理OCR识别...</p>';
            
            try {
                // 创建FormData
                const formData = new FormData();
                formData.append('invoice', file);
                formData.append('company_id', 'default');
                
                const startTime = Date.now();
                
                // 发送OCR请求
                const response = await fetch(`${API_BASE_URL}/ai-bookkeeping/invoice-ocr`, {
                    method: 'POST',
                    body: formData
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                log(`OCR请求完成，耗时: ${responseTime}ms，状态: ${response.status}`, response.status === 200 ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="text-green-600">
                            <h3 class="font-semibold text-lg mb-3">🎉 OCR识别成功!</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <p><strong>商户名称:</strong> ${result.vendor || 'N/A'}</p>
                                    <p><strong>日期:</strong> ${result.date || 'N/A'}</p>
                                    <p><strong>金额:</strong> ¥${result.amount || 'N/A'}</p>
                                    <p><strong>税额:</strong> ¥${result.tax || 'N/A'}</p>
                                    <p><strong>描述:</strong> ${result.description || 'N/A'}</p>
                                    <p><strong>置信度:</strong> ${(result.confidence * 100).toFixed(1)}%</p>
                                </div>
                                <div>
                                    <p><strong>文件信息:</strong></p>
                                    <p class="text-sm">类型: ${result.content_type}</p>
                                    <p class="text-sm">文件名: ${result.original_filename}</p>
                                    <p class="text-sm">响应时间: ${responseTime}ms</p>
                                </div>
                            </div>
                            ${result.items && result.items.length > 0 ? `
                                <div class="mt-4">
                                    <p><strong>商品明细:</strong></p>
                                    <div class="bg-gray-100 p-3 rounded mt-2">
                                        ${result.items.map(item => `
                                            <div class="text-sm">
                                                ${item.name} - 数量: ${item.quantity} - 单价: ¥${item.unit_price} - 小计: ¥${item.total}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <details class="mt-4">
                            <summary class="cursor-pointer text-sm text-gray-600">查看完整响应</summary>
                            <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto max-h-64">${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    `;
                    log('OCR识别成功!', 'success');
                } else {
                    resultDiv.innerHTML = `
                        <div class="text-red-600">
                            <h3 class="font-semibold text-lg mb-2">❌ OCR识别失败</h3>
                            <p><strong>错误信息:</strong> ${result.error}</p>
                            <p class="text-sm mt-2">响应时间: ${responseTime}ms</p>
                        </div>
                        <details class="mt-4">
                            <summary class="cursor-pointer text-sm text-gray-600">查看完整响应</summary>
                            <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto max-h-64">${JSON.stringify(result, null, 2)}</pre>
                        </details>
                    `;
                    log(`OCR识别失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`OCR测试错误: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="text-red-600">
                        <h3 class="font-semibold text-lg mb-2">❌ 请求失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <p class="text-sm mt-2">请检查网络连接和服务器状态</p>
                    </div>
                `;
            }
        }
        
        // 页面加载时的初始化
        window.addEventListener('load', function() {
            log('OCR测试工具已加载', 'info');
            log('API端点: ' + API_BASE_URL + '/ai-bookkeeping/invoice-ocr', 'info');
        });
        
        // 文件选择时显示文件信息
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                log(`文件已选择: ${file.name}, 类型: ${file.type}, 大小: ${(file.size / 1024).toFixed(2)}KB`, 'info');
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floating Button Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            color: white;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
        }

        .countdown {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .status {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info { background: rgba(0, 191, 255, 0.2); }
        .log-success { background: rgba(0, 255, 0, 0.2); }
        .log-error { background: rgba(255, 68, 68, 0.2); }

        .test-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 8px;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255, 105, 180, 0.3);
        }

        .floating-btn-demo {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            border-radius: 50%;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 24px rgba(255, 105, 180, 0.3);
            transition: all 0.3s ease;
            z-index: 9999;
            animation: float 3s ease-in-out infinite;
        }

        .floating-btn-demo:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px rgba(255, 105, 180, 0.4);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            width: 0%;
            transition: width 0.1s ease;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌸 浮动按钮显示测试</h1>
        <p>测试浮动按钮在2秒后自动显示的功能</p>

        <div class="countdown" id="countdown">2</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>

        <div class="status" id="status">
            <div class="log-entry log-info">[开始] 页面加载完成，开始倒计时...</div>
        </div>

        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="resetTest()">🔄 重新测试</button>
            <button class="test-btn" onclick="showButtonNow()">👁️ 立即显示按钮</button>
            <button class="test-btn" onclick="hideButton()">🙈 隐藏按钮</button>
            <button class="test-btn" onclick="openMainPage()">🏠 打开主页</button>
        </div>

        <div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px; margin-top: 20px;">
            <h3>测试说明</h3>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li>页面加载后开始2秒倒计时</li>
                <li>倒计时结束后右下角应该出现樱花浮动按钮</li>
                <li>按钮有浮动动画和悬停效果</li>
                <li>点击按钮会有缩放反馈</li>
                <li>可以使用控制按钮测试各种状态</li>
            </ul>
        </div>
    </div>

    <!-- 测试用浮动按钮 -->
    <div id="floating-btn-demo" class="floating-btn-demo" onclick="onFloatingButtonClick()">
        <svg width="30" height="30" viewBox="0 0 100 100" fill="white">
            <g transform="translate(50,50)">
                <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(0)" opacity="0.9"/>
                <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(0)" opacity="0.7"/>
                <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(72)" opacity="0.9"/>
                <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(72)" opacity="0.7"/>
                <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(144)" opacity="0.9"/>
                <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(144)" opacity="0.7"/>
                <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(216)" opacity="0.9"/>
                <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(216)" opacity="0.7"/>
                <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(288)" opacity="0.9"/>
                <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(288)" opacity="0.7"/>
                <circle cx="0" cy="0" r="4" fill="#FFD700" opacity="0.8"/>
                <circle cx="0" cy="0" r="2" fill="#FFA500" opacity="0.9"/>
            </g>
        </svg>
    </div>

    <script>
        let countdownTimer;
        let progressTimer;
        let startTime;

        function log(message, type = 'info') {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            status.appendChild(logEntry);
            status.scrollTop = status.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateCountdown(seconds) {
            const countdownEl = document.getElementById('countdown');
            const progressEl = document.getElementById('progress');
            
            countdownEl.textContent = seconds;
            const progress = ((2 - seconds) / 2) * 100;
            progressEl.style.width = progress + '%';
            
            if (seconds > 0) {
                log(`倒计时: ${seconds}秒`, 'info');
            }
        }

        function showFloatingButton() {
            const btn = document.getElementById('floating-btn-demo');
            btn.style.display = 'flex';
            btn.style.transform = 'scale(0)';
            btn.style.transition = 'transform 0.3s ease-out';
            
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 50);
            
            log('🌸 浮动按钮已显示！', 'success');
        }

        function startCountdown() {
            let seconds = 2;
            startTime = Date.now();
            
            updateCountdown(seconds);
            
            countdownTimer = setInterval(() => {
                seconds--;
                updateCountdown(seconds);
                
                if (seconds <= 0) {
                    clearInterval(countdownTimer);
                    clearInterval(progressTimer);
                    document.getElementById('countdown').textContent = '✅';
                    document.getElementById('progress').style.width = '100%';
                    log('⏰ 倒计时结束，显示浮动按钮', 'success');
                    showFloatingButton();
                }
            }, 1000);
            
            // 更平滑的进度条更新
            progressTimer = setInterval(() => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min((elapsed / 2000) * 100, 100);
                document.getElementById('progress').style.width = progress + '%';
            }, 50);
        }

        function resetTest() {
            clearInterval(countdownTimer);
            clearInterval(progressTimer);
            
            const btn = document.getElementById('floating-btn-demo');
            btn.style.display = 'none';
            
            document.getElementById('status').innerHTML = '<div class="log-entry log-info">[重置] 测试重新开始...</div>';
            
            setTimeout(() => {
                log('🔄 开始新的测试周期', 'info');
                startCountdown();
            }, 500);
        }

        function showButtonNow() {
            clearInterval(countdownTimer);
            clearInterval(progressTimer);
            document.getElementById('countdown').textContent = '👁️';
            document.getElementById('progress').style.width = '100%';
            log('👁️ 手动显示浮动按钮', 'info');
            showFloatingButton();
        }

        function hideButton() {
            const btn = document.getElementById('floating-btn-demo');
            btn.style.display = 'none';
            log('🙈 浮动按钮已隐藏', 'info');
        }

        function onFloatingButtonClick() {
            const btn = document.getElementById('floating-btn-demo');
            btn.style.transform = 'scale(0.9)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 150);
            
            log('🌸 浮动按钮被点击！', 'success');
            alert('🌸 樱花浮动按钮点击成功！\n\n在主页面中，点击此按钮会打开chatbox。');
        }

        function openMainPage() {
            log('🏠 打开主页面...', 'info');
            window.open('http://localhost:8080', '_blank');
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 浮动按钮测试页面加载完成', 'success');
            log('⏱️ 开始2秒倒计时测试...', 'info');
            startCountdown();
        });
    </script>
</body>
</html>

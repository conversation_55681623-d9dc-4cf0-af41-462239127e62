/**
 * 订阅状态管理系统
 * 处理用户订阅状态检查、权限控制、使用量限制等
 */

class SubscriptionManager {
    constructor() {
        this.apiBaseUrl = window.GoldenLedgerAPI?.baseUrl || 'https://goldenledger-api.souyousann.workers.dev';
        this.currentUser = null;
        this.currentSubscription = null;
        this.usageStats = null;
        
        // 初始化
        this.init();
    }

    /**
     * 初始化订阅管理器
     */
    async init() {
        try {
            await this.loadCurrentUser();
            await this.loadSubscriptionStatus();
            await this.loadUsageStats();
            
            console.log('✅ Subscription Manager initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Subscription Manager:', error);
        }
    }

    /**
     * 加载当前用户信息
     */
    async loadCurrentUser() {
        const token = localStorage.getItem('goldenledger_session_token');
        if (!token) {
            throw new Error('No authentication token found');
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/auth/verify`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (result.success) {
                this.currentUser = result.user;
            } else {
                throw new Error('Invalid authentication token');
            }
        } catch (error) {
            console.error('Failed to load current user:', error);
            throw error;
        }
    }

    /**
     * 加载订阅状态
     */
    async loadSubscriptionStatus() {
        if (!this.currentUser) {
            throw new Error('User not loaded');
        }

        try {
            const token = localStorage.getItem('goldenledger_session_token');
            const response = await fetch(`${this.apiBaseUrl}/api/subscription/status`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (result.success) {
                this.currentSubscription = result.subscription;
            }
        } catch (error) {
            console.error('Failed to load subscription status:', error);
            // 不抛出错误，允许免费用户继续使用
        }
    }

    /**
     * 加载使用量统计
     */
    async loadUsageStats() {
        if (!this.currentUser) return;

        try {
            const token = localStorage.getItem('goldenledger_session_token');
            const response = await fetch(`${this.apiBaseUrl}/api/usage/stats`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (result.success) {
                this.usageStats = result.stats;
            }
        } catch (error) {
            console.error('Failed to load usage stats:', error);
        }
    }

    /**
     * 获取当前套餐信息
     */
    getCurrentPlan() {
        if (!this.currentSubscription) {
            return window.PayPalConfig?.plans?.free || {
                id: 'free',
                name: '無料プラン',
                limits: {
                    ai_requests_monthly: 10,
                    data_retention_days: 30,
                    export_enabled: false,
                    advanced_ai: false,
                    multi_company: false
                }
            };
        }

        const planId = this.currentSubscription.plan_id;
        return window.PayPalConfig?.plans?.[planId] || null;
    }

    /**
     * 检查功能权限
     */
    hasFeatureAccess(feature) {
        const plan = this.getCurrentPlan();
        if (!plan || !plan.limits) return false;

        switch (feature) {
            case 'ai_bookkeeping':
                return this.checkAIRequestsLimit();
            case 'export':
                return plan.limits.export_enabled === true;
            case 'advanced_ai':
                return plan.limits.advanced_ai === true;
            case 'multi_company':
                return plan.limits.multi_company === true;
            case 'api_access':
                return plan.limits.api_access === true;
            case 'multi_user':
                return plan.limits.multi_user === true;
            default:
                return false;
        }
    }

    /**
     * 检查AI请求限制
     */
    checkAIRequestsLimit() {
        const plan = this.getCurrentPlan();
        if (!plan || !plan.limits) return false;

        // 无限制套餐
        if (plan.limits.ai_requests_monthly === null) {
            return true;
        }

        // 检查当月使用量
        const monthlyLimit = plan.limits.ai_requests_monthly;
        const currentUsage = this.getCurrentMonthUsage('ai_bookkeeping');
        
        return currentUsage < monthlyLimit;
    }

    /**
     * 获取当月使用量
     */
    getCurrentMonthUsage(featureType) {
        if (!this.usageStats || !this.usageStats[featureType]) {
            return 0;
        }

        const now = new Date();
        const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
        
        return this.usageStats[featureType][currentMonth] || 0;
    }

    /**
     * 记录功能使用
     */
    async recordUsage(featureType) {
        if (!this.currentUser) return;

        try {
            const token = localStorage.getItem('goldenledger_session_token');
            const response = await fetch(`${this.apiBaseUrl}/api/usage/record`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    feature_type: featureType,
                    usage_count: 1
                })
            });

            const result = await response.json();
            if (result.success) {
                // 更新本地使用量统计
                await this.loadUsageStats();
            }
        } catch (error) {
            console.error('Failed to record usage:', error);
        }
    }

    /**
     * 获取使用量统计信息
     */
    getUsageInfo(featureType) {
        const plan = this.getCurrentPlan();
        if (!plan || !plan.limits) {
            return {
                current: 0,
                limit: 0,
                unlimited: false,
                percentage: 0
            };
        }

        const limit = plan.limits[`${featureType}_monthly`];
        const current = this.getCurrentMonthUsage(featureType);
        
        if (limit === null) {
            return {
                current,
                limit: null,
                unlimited: true,
                percentage: 0
            };
        }

        return {
            current,
            limit,
            unlimited: false,
            percentage: limit > 0 ? (current / limit) * 100 : 0
        };
    }

    /**
     * 显示升级提示
     */
    showUpgradePrompt(feature, reason = '') {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="cyber-glass rounded-3xl p-8 max-w-md w-full mx-4">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-crown text-2xl text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-yellow-400 mb-4">プランをアップグレード</h3>
                    <p class="text-cyan-200 mb-6">
                        ${reason || 'この機能を使用するには、有料プランへのアップグレードが必要です。'}
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button onclick="this.closest('.fixed').remove()" 
                                class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors">
                            キャンセル
                        </button>
                        <a href="payment.html" 
                           class="bg-gradient-to-r from-yellow-500 to-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-yellow-600 hover:to-orange-700 transition-all duration-300 text-center">
                            プランを見る
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 3秒后自动关闭（可选）
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 10000);
    }

    /**
     * 显示使用量警告
     */
    showUsageWarning(featureType) {
        const usageInfo = this.getUsageInfo(featureType);
        
        if (usageInfo.unlimited) return;
        
        const percentage = usageInfo.percentage;
        let message = '';
        let color = 'yellow';
        
        if (percentage >= 100) {
            message = `${featureType}の月間使用量上限に達しました。`;
            color = 'red';
        } else if (percentage >= 80) {
            message = `${featureType}の月間使用量が${Math.round(percentage)}%に達しています。`;
            color = 'orange';
        } else if (percentage >= 60) {
            message = `${featureType}の月間使用量が${Math.round(percentage)}%に達しています。`;
            color = 'yellow';
        } else {
            return; // 警告不需要显示
        }
        
        // 显示警告通知
        this.showNotification(message, color);
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
        
        const colors = {
            info: 'bg-blue-600',
            success: 'bg-green-600',
            warning: 'bg-yellow-600',
            error: 'bg-red-600',
            yellow: 'bg-yellow-600',
            orange: 'bg-orange-600',
            red: 'bg-red-600'
        };
        
        notification.className += ` ${colors[type] || colors.info} text-white`;
        notification.innerHTML = `
            <div class="flex items-center">
                <div class="flex-1">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <button onclick="this.parentNode.parentNode.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 动画显示
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // 5秒后自动消失
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    /**
     * 刷新订阅状态
     */
    async refresh() {
        await this.loadSubscriptionStatus();
        await this.loadUsageStats();
    }
}

// 全局实例
window.SubscriptionManager = new SubscriptionManager();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionManager;
}

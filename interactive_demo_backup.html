<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智会云 GoTax｜下一代 AI 记账平台 - 交互式演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script>
        // 智能控制台日志管理
        if (typeof window !== 'undefined') {
            const originalWarn = console.warn;
            const originalLog = console.log;

            // 抑制特定的警告和日志
            console.warn = function(...args) {
                const message = args[0] || '';
                // 忽略的警告类型
                const ignoredWarnings = [
                    'cdn.tailwindcss.com should not be used in production',
                    '前端页面检查失败',
                    'HEAD request failed'
                ];

                if (ignoredWarnings.some(warning => message.includes && message.includes(warning))) {
                    return; // 忽略这些警告
                }
                originalWarn.apply(console, args);
            };

            // 过滤fetch成功的日志
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                return originalFetch.apply(this, args).then(response => {
                    // 不在控制台显示成功的fetch请求
                    return response;
                }).catch(error => {
                    // 只在真正的错误时显示
                    if (!error.message.includes('HEAD') && !error.message.includes('AbortError')) {
                        console.warn('网络请求失败:', error.message);
                    }
                    throw error;
                });
            };
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .chat-bubble { max-width: 300px; padding: 12px 16px; border-radius: 18px; margin: 8px 0; }
        .chat-user { background: #00d4aa; color: white; margin-left: auto; }
        .chat-ai { background: #f3f4f6; color: #374151; }
        .journal-preview { background: linear-gradient(135deg, #f0fdf9 0%, #eff6ff 100%); border: 1px solid #00d4aa; }
        .loading { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl font-bold mb-4">🚀 智会云 GoTax｜下一代 AI 记账平台</h1>
            <p class="text-xl opacity-90">交互式演示 - 真实的AI记账体验</p>
            <div class="mt-4 flex justify-center space-x-4 text-sm">
                <span id="backend-status" class="bg-white/20 px-3 py-1 rounded-full">🔄 检查后端状态...</span>
                <span id="frontend-status" class="bg-white/20 px-3 py-1 rounded-full">🔄 检查前端状态...</span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Quick Links -->
        <section class="mb-8">
            <div class="grid md:grid-cols-3 gap-6">
                <a href="/master_dashboard.html" target="_blank" class="bg-white rounded-xl p-6 shadow-sm border hover:shadow-lg transition-shadow">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🎨</span>
                        </div>
                        <h3 class="font-semibold mb-2">主控制台界面</h3>
                        <p class="text-gray-600 text-sm">访问主控制台和所有功能</p>
                    </div>
                </a>
                
                <a href="/docs" target="_blank" class="bg-white rounded-xl p-6 shadow-sm border hover:shadow-lg transition-shadow">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">📖</span>
                        </div>
                        <h3 class="font-semibold mb-2">API文档</h3>
                        <p class="text-gray-600 text-sm">查看后端API接口</p>
                    </div>
                </a>
                
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">💬</span>
                        </div>
                        <h3 class="font-semibold mb-2">AI记账演示</h3>
                        <p class="text-gray-600 text-sm">下方交互式体验</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive AI Chat -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-8">💬 真实AI记账体验</h2>
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Chat Header -->
                <div class="gradient-bg text-white p-4">
                    <h3 class="font-semibold">AI会計アシスタント</h3>
                    <p class="text-sm opacity-90">连接到真实后端API</p>
                </div>
                
                <!-- Chat Messages -->
                <div id="chat-messages" class="p-4 space-y-4 h-96 overflow-y-auto">
                    <div class="chat-bubble chat-ai">
                        こんにちは！AI会計アシスタントです。取引内容を教えてください。
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="border-t p-4">
                    <div class="flex space-x-2">
                        <input
                            id="chat-input"
                            type="text"
                            placeholder="例: 今日コンビニで事務用品を1200円で購入"
                            class="flex-1 border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <!-- 文件上传按钮 -->
                        <label for="invoice-upload" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 cursor-pointer flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <span>発票</span>
                        </label>
                        <input
                            id="invoice-upload"
                            type="file"
                            accept="image/*,.pdf"
                            class="hidden"
                        >
                        <button
                            id="send-btn"
                            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
                        >
                            送信
                        </button>
                    </div>
                    <div class="mt-2 flex flex-wrap gap-2">
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="今日コンビニで事務用品を1200円で購入">
                            事務用品購入
                        </button>
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="ABC会社から売上50万円を銀行振込で受取">
                            売上受取
                        </button>
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="今月の家賃15万円を支払い">
                            家賃支払い
                        </button>
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="電気代8500円を口座振替で支払い">
                            電気代支払い
                        </button>
                    </div>

                    <!-- 对话历史记录面板 -->
                    <div class="mt-6">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold">💬 对话历史 (<span id="history-count">0</span>/30)</h3>
                            <div class="space-x-2">
                                <button id="toggle-history" class="text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-full transition-colors">
                                    显示历史
                                </button>
                                <button id="clear-history" class="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-full transition-colors">
                                    清空历史
                                </button>
                            </div>
                        </div>

                        <div id="chat-history-panel" class="hidden bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                            <div id="history-list" class="space-y-3">
                                <!-- 历史记录将在这里显示 -->
                            </div>
                            <div id="no-history" class="text-center text-gray-500 py-8 hidden">
                                <div class="text-4xl mb-2">📝</div>
                                <p>还没有对话历史</p>
                                <p class="text-sm">开始与AI对话，历史记录会自动保存</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- System Status -->
        <section class="text-center">
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-4">🚀 システム状態</h2>
                <div id="system-stats" class="grid md:grid-cols-4 gap-6">
                    <div>
                        <div class="text-3xl font-bold text-blue-500">--</div>
                        <div class="text-gray-600">API応答時間</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-green-500">--</div>
                        <div class="text-gray-600">処理成功数</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-purple-500">--</div>
                        <div class="text-gray-600">生成仕訳数</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-orange-500">--</div>
                        <div class="text-gray-600">平均信頼度</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 全局变量
        let processedCount = 0;
        let successCount = 0;
        let totalConfidence = 0;
        let responseTimes = [];

        // AI对话历史记录管理
        const CHAT_HISTORY_KEY = 'goldenledger_ai_chat_history';
        const MAX_CHAT_HISTORY = 30; // 最大保存30条对话

        // 对话历史记录结构
        class ChatHistory {
            constructor() {
                this.conversations = this.loadFromStorage();
            }

            // 从本地存储加载历史记录
            loadFromStorage() {
                try {
                    const stored = localStorage.getItem(CHAT_HISTORY_KEY);
                    if (stored) {
                        const parsed = JSON.parse(stored);
                        return Array.isArray(parsed) ? parsed : [];
                    }
                } catch (error) {
                    console.warn('加载对话历史失败:', error);
                }
                return [];
            }

            // 保存到本地存储
            saveToStorage() {
                try {
                    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(this.conversations));
                } catch (error) {
                    console.warn('保存对话历史失败:', error);
                }
            }

            // 添加新对话
            addConversation(userMessage, aiResponse, timestamp = new Date()) {
                const conversation = {
                    id: Date.now() + Math.random(),
                    timestamp: timestamp.toISOString(),
                    userMessage: userMessage,
                    aiResponse: aiResponse,
                    displayTime: timestamp.toLocaleString('zh-CN')
                };

                this.conversations.unshift(conversation); // 添加到开头

                // 保持最大数量限制
                if (this.conversations.length > MAX_CHAT_HISTORY) {
                    this.conversations = this.conversations.slice(0, MAX_CHAT_HISTORY);
                }

                this.saveToStorage();
                return conversation;
            }

            // 获取所有对话
            getAllConversations() {
                return this.conversations;
            }

            // 清空历史记录
            clearHistory() {
                this.conversations = [];
                this.saveToStorage();
            }

            // 获取对话数量
            getCount() {
                return this.conversations.length;
            }
        }

        // 初始化对话历史管理器
        const chatHistory = new ChatHistory();

        // 确认并保存记录
        async function confirmAndSave(button) {
            try {
                // 获取仕訳数据
                const journalDataStr = button.getAttribute('data-journal').replace(/&apos;/g, "'");
                const journalData = JSON.parse(journalDataStr);

                // 显示确认状态
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="animate-spin">⏳</span> 保存中...';
                button.disabled = true;

                // 发送保存请求
                const response = await fetch('/journal-entries/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        company_id: 'default',
                        ...journalData,
                        confirmed: true
                    })
                });

                if (response.ok) {
                    const result = await response.json();

                    // 显示成功状态
                    button.innerHTML = '✅ 已保存';
                    button.className = 'bg-green-600 text-white px-4 py-2 rounded text-sm cursor-default';

                    // 显示成功消息
                    showNotification('仕訳记录已成功保存到数据库！', 'success');

                    // 禁用编辑按钮
                    const editButton = button.parentElement.querySelector('button[onclick*="editEntry"]');
                    if (editButton) {
                        editButton.disabled = true;
                        editButton.className = 'border border-gray-200 text-gray-400 px-4 py-2 rounded text-sm cursor-not-allowed';
                        editButton.innerHTML = '✏️ 已确认';
                    }

                } else {
                    throw new Error('保存失败');
                }

            } catch (error) {
                console.error('保存记录失败:', error);

                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;

                showNotification('保存失败，请重试', 'error');
            }
        }

        // 编辑记录
        function editEntry(button) {
            try {
                // 获取仕訳数据
                const journalDataStr = button.getAttribute('data-journal').replace(/&apos;/g, "'");
                const journalData = JSON.parse(journalDataStr);

                // 创建编辑模态框
                showEditModal(journalData, button);

            } catch (error) {
                console.error('编辑记录失败:', error);
                showNotification('无法编辑记录', 'error');
            }
        }

        // 显示编辑模态框
        function showEditModal(journalData, button) {
            // 创建模态框HTML
            const modalHTML = `
                <div id="edit-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">編集仕訳</h3>
                            <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <form id="edit-form" class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                                    <input type="date" id="edit-date" value="${journalData.entry_date}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">时间</label>
                                    <input type="time" id="edit-time" value="${journalData.entry_time || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                <input type="text" id="edit-description" value="${(journalData.description || '').replace(/"/g, '&quot;')}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">借方科目</label>
                                    <select id="edit-debit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                        <option value="">选择借方科目</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">贷方科目</label>
                                    <select id="edit-credit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                        <option value="">选择贷方科目</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">金额</label>
                                <input type="number" id="edit-amount" value="${journalData.amount}" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div class="flex space-x-3 pt-4">
                                <button type="button" onclick="saveEditedEntry('${button.getAttribute('data-journal')}')"
                                        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                    保存修改
                                </button>
                                <button type="button" onclick="closeEditModal()"
                                        class="flex-1 border border-gray-300 px-4 py-2 rounded hover:bg-gray-50">
                                    取消
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // 加载科目选项
            loadAccountOptionsForDemo(journalData);
        }

        // 加载科目选项（演示页面版本）
        async function loadAccountOptionsForDemo(journalData) {
            try {
                // 获取科目列表
                const response = await fetch('/journal-entries/default/accounts');
                if (!response.ok) {
                    throw new Error('获取科目列表失败');
                }

                const accounts = await response.json();

                // 获取下拉框元素
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (!debitSelect || !creditSelect) {
                    console.error('找不到科目选择框');
                    return;
                }

                // 清空现有选项
                debitSelect.innerHTML = '<option value="">选择借方科目</option>';
                creditSelect.innerHTML = '<option value="">选择贷方科目</option>';

                // 添加所有科目选项
                Object.keys(accounts).forEach(category => {
                    // 为每个类别创建选项组
                    const debitOptgroup = document.createElement('optgroup');
                    debitOptgroup.label = category;
                    const creditOptgroup = document.createElement('optgroup');
                    creditOptgroup.label = category;

                    accounts[category].forEach(account => {
                        // 借方科目选项
                        const debitOption = document.createElement('option');
                        debitOption.value = account;
                        debitOption.textContent = account;
                        if (journalData.debit_account === account) {
                            debitOption.selected = true;
                        }
                        debitOptgroup.appendChild(debitOption);

                        // 贷方科目选项
                        const creditOption = document.createElement('option');
                        creditOption.value = account;
                        creditOption.textContent = account;
                        if (journalData.credit_account === account) {
                            creditOption.selected = true;
                        }
                        creditOptgroup.appendChild(creditOption);
                    });

                    debitSelect.appendChild(debitOptgroup);
                    creditSelect.appendChild(creditOptgroup);
                });

            } catch (error) {
                console.error('加载科目选项失败:', error);
                // 如果加载失败，回退到文本输入
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (debitSelect) {
                    const debitValue = (journalData.debit_account || '').replace(/"/g, '&quot;');
                    debitSelect.outerHTML = `<input type="text" id="edit-debit" value="${debitValue}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">`;
                }
                if (creditSelect) {
                    const creditValue = (journalData.credit_account || '').replace(/"/g, '&quot;');
                    creditSelect.outerHTML = `<input type="text" id="edit-credit" value="${creditValue}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">`;
                }
            }
        }

        // 关闭编辑模态框
        function closeEditModal() {
            const modal = document.getElementById('edit-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 保存编辑后的记录
        async function saveEditedEntry(originalData) {
            try {
                // 获取编辑后的数据
                const entryDate = document.getElementById('edit-date').value;
                const entryTime = document.getElementById('edit-time').value;

                // 构建完整的datetime
                let entryDatetime = '';
                if (entryDate && entryTime) {
                    entryDatetime = `${entryDate}T${entryTime}`;
                } else if (entryDate) {
                    entryDatetime = `${entryDate}T${new Date().toTimeString().slice(0, 8)}`;
                }

                const editedData = {
                    id: JSON.parse(originalData).id,
                    entry_date: entryDate,
                    entry_time: entryTime,
                    entry_datetime: entryDatetime,
                    description: document.getElementById('edit-description').value,
                    debit_account: document.getElementById('edit-debit').value,
                    credit_account: document.getElementById('edit-credit').value,
                    amount: parseFloat(document.getElementById('edit-amount').value),
                    reference_number: JSON.parse(originalData).reference_number || ''
                };

                // 发送更新请求
                const response = await fetch('/journal-entries/update', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        company_id: 'default',
                        ...editedData
                    })
                });

                if (response.ok) {
                    // 关闭模态框
                    closeEditModal();

                    // 显示成功消息
                    showNotification('仕訳记录已成功更新！', 'success');

                    // 更新页面显示的数据
                    updateDisplayedEntry(editedData);

                } else {
                    throw new Error('更新失败');
                }

            } catch (error) {
                console.error('更新记录失败:', error);
                showNotification('更新失败，请重试', 'error');
            }
        }

        // 更新显示的记录数据
        function updateDisplayedEntry(editedData) {
            // 找到当前显示的仕訳卡片并更新内容
            const entryCards = document.querySelectorAll('.bg-blue-50');
            entryCards.forEach(card => {
                const buttons = card.querySelectorAll('button[data-journal]');
                buttons.forEach(button => {
                    const journalData = JSON.parse(button.getAttribute('data-journal'));
                    if (journalData.id === editedData.id) {
                        // 更新按钮的数据属性
                        const newJournalData = JSON.stringify(editedData);
                        button.setAttribute('data-journal', newJournalData);

                        // 更新显示的内容
                        const cardContent = card.querySelector('.space-y-2');
                        if (cardContent) {
                            cardContent.innerHTML = `
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-blue-600 font-medium">借方</span>
                                        <div class="text-blue-900 font-semibold">${editedData.debit_account}</div>
                                        <div class="text-blue-700">¥${editedData.amount.toLocaleString()}</div>
                                    </div>
                                    <div>
                                        <span class="text-green-600 font-medium">贷方</span>
                                        <div class="text-green-900 font-semibold">${editedData.credit_account}</div>
                                        <div class="text-green-700">¥${editedData.amount.toLocaleString()}</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-600">
                                    <div>摘要: ${editedData.description}</div>
                                    <div>日期: ${editedData.entry_date}${editedData.entry_time ? ' ' + editedData.entry_time : ''}</div>
                                </div>
                            `;
                        }
                    }
                });
            });
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500'
            };

            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 隐藏动画
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 检查服务状态
        async function checkServices() {
            // 检查后端
            try {
                const backendResponse = await fetch('/health', {
                    method: 'GET',
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(5000) // 5秒超时
                });
                if (backendResponse.ok) {
                    document.getElementById('backend-status').innerHTML = '✅ 后端服务正常';
                    document.getElementById('backend-status').className = 'bg-green-500/20 text-green-800 px-3 py-1 rounded-full';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                // 只在真正的错误时显示警告
                if (error.name !== 'TimeoutError') {
                    console.warn('后端服务检查失败:', error.message);
                }
                document.getElementById('backend-status').innerHTML = '❌ 后端服务离线';
                document.getElementById('backend-status').className = 'bg-red-500/20 text-red-800 px-3 py-1 rounded-full';
            }

            // 检查前端页面
            try {
                const frontendResponse = await fetch('/master_dashboard.html', {
                    method: 'GET',
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(3000) // 3秒超时
                });
                if (frontendResponse.ok) {
                    document.getElementById('frontend-status').innerHTML = '✅ 前端页面正常';
                    document.getElementById('frontend-status').className = 'bg-green-500/20 text-green-800 px-3 py-1 rounded-full';
                } else {
                    throw new Error('Frontend pages not responding');
                }
            } catch (error) {
                // 不在控制台显示错误，只更新状态
                document.getElementById('frontend-status').innerHTML = '⚠️ 前端页面检查中';
                document.getElementById('frontend-status').className = 'bg-yellow-500/20 text-yellow-800 px-3 py-1 rounded-full';
            }
        }

        // 处理发票上传
        async function handleInvoiceUpload(file) {
            const messagesContainer = document.getElementById('chat-messages');

            // 显示上传中的消息
            const uploadMessage = document.createElement('div');
            uploadMessage.className = 'flex justify-end mb-4';
            uploadMessage.innerHTML = `
                <div class="chat-bubble bg-blue-500 text-white">
                    📄 正在上传发票: ${file.name}
                    <div class="mt-2">
                        <div class="bg-white/20 rounded-full h-2">
                            <div class="bg-white h-2 rounded-full animate-pulse" style="width: 50%"></div>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(uploadMessage);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            try {
                // 创建FormData
                const formData = new FormData();
                formData.append('invoice', file);
                formData.append('company_id', 'default');

                // 发送到后端OCR处理
                const response = await fetch('/ai-bookkeeping/invoice-ocr', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                // 移除上传中的消息
                uploadMessage.remove();

                if (result.success) {
                    // 显示OCR识别结果
                    const ocrMessage = document.createElement('div');
                    ocrMessage.className = 'flex justify-start mb-4';
                    ocrMessage.innerHTML = `
                        <div class="chat-bubble bg-gray-100 text-gray-800">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="text-green-600">✅</span>
                                <span class="font-medium">发票识别成功</span>
                            </div>
                            <div class="text-sm space-y-1">
                                <div><strong>金额:</strong> ¥${result.amount || 'N/A'}</div>
                                <div><strong>日期:</strong> ${result.date || 'N/A'}</div>
                                <div><strong>商户:</strong> ${result.vendor || 'N/A'}</div>
                                <div><strong>描述:</strong> ${result.description || 'N/A'}</div>
                            </div>
                            <button onclick="confirmInvoiceEntry(this)"
                                    class="mt-3 bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                                    data-result='${JSON.stringify(result).replace(/'/g, "&apos;")}'>
                                确认记账
                            </button>
                        </div>
                    `;
                    messagesContainer.appendChild(ocrMessage);
                } else {
                    // 显示错误消息
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'flex justify-start mb-4';
                    errorMessage.innerHTML = `
                        <div class="chat-bubble bg-red-100 text-red-800">
                            <div class="flex items-center space-x-2">
                                <span class="text-red-600">❌</span>
                                <span>发票识别失败: ${result.error || '未知错误'}</span>
                            </div>
                        </div>
                    `;
                    messagesContainer.appendChild(errorMessage);
                }

            } catch (error) {
                // 移除上传中的消息
                uploadMessage.remove();

                // 显示网络错误
                const errorMessage = document.createElement('div');
                errorMessage.className = 'flex justify-start mb-4';
                errorMessage.innerHTML = `
                    <div class="chat-bubble bg-red-100 text-red-800">
                        <div class="flex items-center space-x-2">
                            <span class="text-red-600">❌</span>
                            <span>上传失败: ${error.message}</span>
                        </div>
                    </div>
                `;
                messagesContainer.appendChild(errorMessage);
            }

            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 确认发票记账
        async function confirmInvoiceEntry(buttonElement) {
            try {
                const resultJson = buttonElement.getAttribute('data-result').replace(/&apos;/g, "'");
                const result = JSON.parse(resultJson);

                // 禁用按钮
                buttonElement.disabled = true;
                buttonElement.textContent = '处理中...';

                // 构造自然语言描述
                const naturalText = `${result.date || '今日'}${result.vendor || ''}で${result.description || '商品'}を${result.amount || '0'}円で購入`;

                // 准备附件数据
                const attachmentData = {
                    file_content: result.file_content,
                    content_type: result.content_type,
                    original_filename: result.original_filename
                };

                // 调用现有的sendMessage函数，传递附件数据
                await sendMessage(naturalText, attachmentData);

                // 更新按钮状态
                buttonElement.textContent = '已记账';
                buttonElement.className = 'mt-3 bg-green-500 text-white px-3 py-1 rounded text-sm cursor-not-allowed';

            } catch (error) {
                console.error('确认记账失败:', error);
                buttonElement.disabled = false;
                buttonElement.textContent = '重试';
                buttonElement.className = 'mt-3 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600';
            }
        }

        // 发送消息到AI
        async function sendMessage(text, attachmentData = null) {
            const messagesContainer = document.getElementById('chat-messages');
            const sendBtn = document.getElementById('send-btn');

            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-bubble chat-user';
            userMessage.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">${text}</div>
                    <div class="text-xs text-gray-500 ml-2">${new Date().toLocaleTimeString('zh-CN')}</div>
                </div>
            `;
            messagesContainer.appendChild(userMessage);
            
            // 添加加载消息
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'chat-bubble chat-ai loading';
            loadingMessage.textContent = '処理中...';
            messagesContainer.appendChild(loadingMessage);
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // 禁用发送按钮
            sendBtn.disabled = true;
            
            try {
                const startTime = Date.now();
                const response = await fetch('/ai-bookkeeping/natural-language', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        company_id: 'default'
                    })
                });
                
                const responseTime = Date.now() - startTime;
                responseTimes.push(responseTime);
                
                const data = await response.json();
                
                // 移除加载消息
                messagesContainer.removeChild(loadingMessage);
                
                if (data.success) {
                    // 创建仕訳预览
                    const aiMessage = document.createElement('div');
                    aiMessage.className = 'chat-bubble chat-ai';
                    aiMessage.innerHTML = `
                        <div class="space-y-3">
                            <p>承知いたしました。以下の仕訳を生成しました：</p>
                            <div class="journal-preview rounded-lg p-4">
                                <h4 class="font-semibold mb-3">生成された仕訳</h4>
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label class="text-xs text-gray-500 uppercase">借方</label>
                                        <div class="bg-blue-50 border border-blue-200 rounded p-2">
                                            <div class="font-medium text-blue-900">${data.journal_entry.debit_account}</div>
                                            <div class="text-sm text-blue-700">¥${data.journal_entry.amount.toLocaleString()}</div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="text-xs text-gray-500 uppercase">貸方</label>
                                        <div class="bg-green-50 border border-green-200 rounded p-2">
                                            <div class="font-medium text-green-900">${data.journal_entry.credit_account}</div>
                                            <div class="text-sm text-green-700">¥${data.journal_entry.amount.toLocaleString()}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <strong>摘要:</strong> ${data.journal_entry.description}
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <strong>日期时间:</strong> ${data.journal_entry.entry_date}${data.journal_entry.entry_time ? ' ' + data.journal_entry.entry_time : ''}
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <strong>信頼度:</strong> ${(data.confidence * 100).toFixed(1)}%
                                </div>
                                ${data.warnings.length > 0 ? `
                                    <div class="bg-yellow-50 border border-yellow-200 rounded p-2 mb-3">
                                        <strong>注意:</strong> ${data.warnings.join(', ')}
                                    </div>
                                ` : ''}
                                <div class="flex flex-wrap gap-2">
                                    <button onclick="confirmAndSave(this)" class="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600 transition-colors" data-journal='${JSON.stringify({...data.journal_entry, attachment: attachmentData}).replace(/'/g, "&apos;")}'>
                                        ✓ 確認して記録
                                    </button>
                                    <button onclick="editEntry(this)" class="border border-gray-300 px-4 py-2 rounded text-sm hover:bg-gray-50 transition-colors" data-journal='${JSON.stringify({...data.journal_entry, attachment: attachmentData}).replace(/'/g, "&apos;")}'>
                                        ✏️ 編集
                                    </button>
                                    <a href="/journal_entries.html" class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 inline-block transition-colors">
                                        📊 查看所有记录
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.appendChild(aiMessage);

                    // 保存对话历史
                    const aiResponseText = `承知いたしました。以下の仕訳を生成しました：\n借方: ${data.journal_entry.debit_account} ¥${data.journal_entry.amount.toLocaleString()}\n貸方: ${data.journal_entry.credit_account} ¥${data.journal_entry.amount.toLocaleString()}\n摘要: ${data.journal_entry.description}`;
                    chatHistory.addConversation(text, aiResponseText);

                    // 更新统计
                    processedCount++;
                    successCount++;
                    totalConfidence += data.confidence;
                    updateStats();
                    
                } else {
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'chat-bubble chat-ai';
                    errorMessage.textContent = `申し訳ございません。エラーが発生しました: ${data.error}`;
                    messagesContainer.appendChild(errorMessage);
                    
                    processedCount++;
                    updateStats();
                }
                
            } catch (error) {
                // 移除加载消息
                if (messagesContainer.contains(loadingMessage)) {
                    messagesContainer.removeChild(loadingMessage);
                }

                console.warn('AI记账请求失败:', error.message);

                const errorMessage = document.createElement('div');
                errorMessage.className = 'chat-bubble chat-ai';
                errorMessage.innerHTML = `
                    <div class="space-y-3">
                        <p>💡 AI功能正在开发中，您可以尝试：</p>
                        <div class="bg-blue-50 border border-blue-200 rounded p-3">
                            <div class="space-y-2 text-sm">
                                <div>• <a href="/journal_entries.html" class="text-blue-600 hover:underline">查看现有记录</a></div>
                                <div>• <a href="/master_dashboard.html" class="text-blue-600 hover:underline">访问主控制台</a></div>
                                <div>• 手动添加测试数据</div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">技术信息: ${error.message}</div>
                    </div>
                `;
                messagesContainer.appendChild(errorMessage);

                // 保存错误对话历史
                const errorResponseText = "AI功能正在开发中，请尝试访问其他功能页面。";
                chatHistory.addConversation(text, errorResponseText);

                processedCount++;
                updateStats();
            }
            
            // 重新启用发送按钮
            sendBtn.disabled = false;
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 初始化系统统计
        async function initializeSystemStats() {
            try {
                // 获取系统统计数据
                const response = await fetch('/dashboard/summary/default');
                if (response.ok) {
                    const data = await response.json();

                    // 使用API数据初始化统计
                    if (data.ai_stats) {
                        const aiStats = data.ai_stats;
                        processedCount = aiStats.total_processed || 0;
                        successCount = Math.round(processedCount * (aiStats.success_rate || 0));
                        totalConfidence = successCount * (aiStats.avg_confidence || 0);

                        // 初始化响应时间数组
                        if (aiStats.avg_response_time) {
                            responseTimes = [aiStats.avg_response_time];
                        }
                    }
                }
            } catch (error) {
                console.log('无法获取初始统计数据，使用默认值');
            }

            // 更新显示
            updateStats();
        }

        // 更新统计信息
        function updateStats() {
            const avgResponseTime = responseTimes.length > 0 ?
                responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
            const avgConfidence = processedCount > 0 ? totalConfidence / successCount : 0;

            const statsContainer = document.getElementById('system-stats');
            statsContainer.innerHTML = `
                <div>
                    <div class="text-3xl font-bold text-blue-500">${avgResponseTime.toFixed(0)}ms</div>
                    <div class="text-gray-600">API応答時間</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-green-500">${successCount}</div>
                    <div class="text-gray-600">処理成功数</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-purple-500">${processedCount}</div>
                    <div class="text-gray-600">総処理数</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-orange-500">${(avgConfidence * 100).toFixed(1)}%</div>
                    <div class="text-gray-600">平均信頼度</div>
                </div>
            `;

            // 更新历史记录计数
            updateHistoryCount();
        }

        // 更新历史记录计数
        function updateHistoryCount() {
            const count = chatHistory.getCount();
            const historyCountElement = document.getElementById('history-count');
            if (historyCountElement) {
                historyCountElement.textContent = count;
            }
        }

        // 显示/隐藏历史记录面板
        function toggleHistoryPanel() {
            const panel = document.getElementById('chat-history-panel');
            const button = document.getElementById('toggle-history');

            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                button.textContent = '隐藏历史';
                loadHistoryList();
            } else {
                panel.classList.add('hidden');
                button.textContent = '显示历史';
            }
        }

        // 加载历史记录列表
        function loadHistoryList() {
            const historyList = document.getElementById('history-list');
            const noHistory = document.getElementById('no-history');
            const conversations = chatHistory.getAllConversations();

            if (conversations.length === 0) {
                historyList.innerHTML = '';
                noHistory.classList.remove('hidden');
                return;
            }

            noHistory.classList.add('hidden');

            historyList.innerHTML = conversations.map((conv, index) => `
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">#${conversations.length - index}</span>
                        <span class="text-xs text-gray-500">${conv.displayTime}</span>
                    </div>
                    <div class="space-y-2">
                        <div class="text-sm">
                            <span class="font-medium text-blue-600">用户:</span>
                            <span class="text-gray-800">${conv.userMessage}</span>
                        </div>
                        <div class="text-sm">
                            <span class="font-medium text-green-600">AI:</span>
                            <span class="text-gray-700">${conv.aiResponse.substring(0, 100)}${conv.aiResponse.length > 100 ? '...' : ''}</span>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <button onclick="replayConversation('${conv.userMessage.replace(/'/g, '\\\'')}')" class="text-xs text-blue-600 hover:text-blue-800">
                            重新发送
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chat-input');
            const sendBtn = document.getElementById('send-btn');
            
            // 发送按钮点击
            sendBtn.addEventListener('click', function() {
                const text = chatInput.value.trim();
                if (text) {
                    sendMessage(text);
                    chatInput.value = '';
                }
            });
            
            // 回车发送
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendBtn.click();
                }
            });
            
            // 快速输入按钮
            document.querySelectorAll('.quick-input').forEach(btn => {
                btn.addEventListener('click', function() {
                    const text = this.getAttribute('data-text');
                    chatInput.value = text;
                    sendBtn.click();
                });
            });

            // 文件上传处理
            const invoiceUpload = document.getElementById('invoice-upload');
            invoiceUpload.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleInvoiceUpload(file);
                }
            });
            
            // 历史记录按钮事件
            document.getElementById('toggle-history').addEventListener('click', toggleHistoryPanel);
            document.getElementById('clear-history').addEventListener('click', clearChatHistory);

            // 检查服务状态
            checkServices();

            // 初始化系统统计
            initializeSystemStats();

            // 加载对话历史
            loadChatHistoryOnPageLoad();

            // 定期检查服务状态
            setInterval(checkServices, 30000);
        });

        // 重新发送历史对话
        function replayConversation(message) {
            document.getElementById('chat-input').value = message;
            // 聚焦到输入框
            document.getElementById('chat-input').focus();
        }

        // 清空历史记录
        function clearChatHistory() {
            if (confirm('确定要清空所有对话历史吗？此操作不可撤销。')) {
                chatHistory.clearHistory();
                loadHistoryList();
                updateHistoryCount();

                // 显示清空成功提示
                const messagesContainer = document.getElementById('chat-messages');
                const notification = document.createElement('div');
                notification.className = 'text-center text-gray-500 text-sm py-2 bg-gray-100 rounded-lg';
                notification.textContent = '✅ 对话历史已清空';
                messagesContainer.appendChild(notification);

                setTimeout(() => {
                    if (messagesContainer.contains(notification)) {
                        messagesContainer.removeChild(notification);
                    }
                }, 3000);
            }
        }

        // 页面加载时恢复历史记录
        function loadChatHistoryOnPageLoad() {
            const conversations = chatHistory.getAllConversations();
            const messagesContainer = document.getElementById('chat-messages');

            if (conversations.length > 0) {
                // 显示历史记录恢复提示
                const notification = document.createElement('div');
                notification.className = 'text-center text-blue-600 text-sm py-3 bg-blue-50 rounded-lg mb-4';
                notification.innerHTML = `
                    <div class="flex items-center justify-center space-x-2">
                        <span>📚</span>
                        <span>已恢复 ${conversations.length} 条对话历史</span>
                        <button onclick="toggleHistoryPanel()" class="text-blue-800 underline ml-2">查看历史</button>
                    </div>
                `;
                messagesContainer.appendChild(notification);
            }

            updateHistoryCount();
        }
    </script>
</body>
</html>

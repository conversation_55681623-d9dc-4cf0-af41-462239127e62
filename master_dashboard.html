<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 企业级智能记账系统总览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>

    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>

    <!-- 用户管理系统 -->
    <script src="user_manager.js"></script>
    <script src="user_status_component.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .feature-card { transition: all 0.3s ease; }
        .feature-card:hover { transform: translateY(-4px); box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .status-indicator { animation: pulse 2s infinite; }
        .hero-animation { animation: float 6s ease-in-out infinite; }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 登录验证脚本 -->
    <script>
        // 检查用户登录状态
        function checkAuthStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');

            if (!token || !user) {
                // 未登录，重定向到登录页面
                alert('この機能を利用するにはログインが必要です。');
                window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
                return false;
            }

            return true;
        }

        // 页面加载时检查登录状态
        if (!checkAuthStatus()) {
            // 如果未登录，停止页面加载
            document.body.style.display = 'none';
        }
    </script>

    <!-- Navigation Bar -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-teal-500 rounded-xl flex items-center justify-center">
                        <span class="text-white text-xl font-bold">🚀</span>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-800">GoldenLedger</div>
                        <div class="text-xs text-gray-500">Smart AI-Powered Finance</div>
                    </div>
                </div>

                <div class="flex items-center space-x-4" data-auth-show style="display: none;">
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-800" data-user-name>ユーザー</div>
                        <div class="text-xs text-gray-500" data-user-company>会社名</div>
                    </div>
                    <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span class="text-gray-600 text-sm font-bold" data-user-initial>U</span>
                    </div>
                    <button
                        data-logout
                        class="text-gray-500 hover:text-red-600 transition-colors"
                        title="ログアウト"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="gradient-bg text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="hero-animation">
                <h1 class="text-5xl font-bold mb-6">🚀 GoldenLedger — Smart AI-Powered Finance System</h1>
                <p class="text-2xl opacity-90 mb-8">企业级AI全自动记账解决方案</p>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div class="flex items-center space-x-2">
                        <span class="status-indicator w-3 h-3 bg-green-400 rounded-full"></span>
                        <span>AI引擎运行中</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="status-indicator w-3 h-3 bg-blue-400 rounded-full"></span>
                        <span>8个模块已激活</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="status-indicator w-3 h-3 bg-purple-400 rounded-full"></span>
                        <span>多语言支持</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Quick Stats - 用户特定数据 -->
    <section class="container mx-auto px-4 -mt-8 mb-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl p-6 shadow-lg border">
                <div class="text-center">
                    <div id="total-entries" class="text-3xl font-bold text-blue-600">0</div>
                    <div class="text-sm text-gray-600">総仕訳数</div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-lg border">
                <div class="text-center">
                    <div id="monthly-entries" class="text-3xl font-bold text-green-600">0</div>
                    <div class="text-sm text-gray-600">今月の仕訳数</div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-lg border">
                <div class="text-center">
                    <div id="companies-count" class="text-3xl font-bold text-purple-600">0</div>
                    <div class="text-sm text-gray-600">登録会社数</div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-lg border">
                <div class="text-center">
                    <div class="text-3xl font-bold text-orange-600">
                        <span id="user-name-display">ユーザー</span>
                    </div>
                    <div class="text-sm text-gray-600">ログイン中</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Features Grid -->
    <main class="container mx-auto px-4 py-8">
        <h2 class="text-3xl font-bold text-center mb-12">🎯 完整功能模块</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Core AI Features -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">💬</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">AI智能记账</h3>
                    <p class="text-gray-600 mb-6">一句话自动生成仕訳分录，支持自然语言理解</p>
                    <div class="space-y-2">
                        <a href="interactive_demo.html" target="_blank" class="block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                            🎯 交互式演示
                        </a>
                        <a href="journal_entries.html" target="_blank" class="block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
                            📊 查看记录
                        </a>
                        <a href="demo.html" target="_blank" class="block bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors">
                            📖 基础演示
                        </a>
                    </div>
                </div>
            </div>

            <!-- Data Management -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">📊</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">数据分析中心</h3>
                    <p class="text-gray-600 mb-6">高级财务分析和实时数据监控</p>
                    <div class="space-y-2">
                        <a href="advanced_dashboard.html" target="_blank" class="block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
                            📈 高级仪表盘 (已优化)
                        </a>
                        <a href="financial_reports.html" target="_blank" class="block bg-emerald-500 text-white px-4 py-2 rounded hover:bg-emerald-600 transition-colors">
                            📋 财务报表
                        </a>
                    </div>
                </div>
            </div>

            <!-- Import & Export -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">📥</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">批量数据处理</h3>
                    <p class="text-gray-600 mb-6">CSV、Excel、自然语言批量导入</p>
                    <div class="space-y-2">
                        <a href="data_import_tool.html" target="_blank" class="block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors">
                            📤 导入工具
                        </a>
                        <a href="data_export.html" target="_blank" class="block bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 transition-colors">
                            📊 数据导出
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Management -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">🖥️</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">系统监控</h3>
                    <p class="text-gray-600 mb-6">实时系统状态和性能监控</p>
                    <div class="space-y-2">
                        <a href="system_monitor.html" target="_blank" class="block bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors">
                            🔍 监控中心
                        </a>
                        <a href="performance_monitoring.html" target="_blank" class="block bg-amber-500 text-white px-4 py-2 rounded hover:bg-amber-600 transition-colors">
                            ⚡ 性能监控
                        </a>
                        <a href="api_documentation.html" target="_blank" class="block bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors">
                            📚 API文档
                        </a>
                    </div>
                </div>
            </div>

            <!-- Multilingual Support -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-pink-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">🌍</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">多语言支持</h3>
                    <p class="text-gray-600 mb-6">中文、日语、英语、韩语智能记账</p>
                    <div class="space-y-2">
                        <a href="multilingual_interface.html" target="_blank" class="block bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600 transition-colors">
                            🗣️ 多语言界面
                        </a>
                        <a href="#" class="block bg-rose-500 text-white px-4 py-2 rounded hover:bg-rose-600 transition-colors">
                            🔄 实时翻译
                        </a>
                    </div>
                </div>
            </div>

            <!-- AI Audit -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">🔍</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">AI智能审计</h3>
                    <p class="text-gray-600 mb-6">自动风险检测和合规性审计</p>
                    <div class="space-y-2">
                        <a href="ai_audit_system.html" target="_blank" class="block bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors">
                            🕵️ 审计系统
                        </a>
                        <a href="#" class="block bg-crimson-500 text-white px-4 py-2 rounded hover:bg-crimson-600 transition-colors">
                            🛡️ 风险控制
                        </a>
                    </div>
                </div>
            </div>

            <!-- Real-time Communication -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-cyan-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">📡</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">实时通信</h3>
                    <p class="text-gray-600 mb-6">WebSocket实时监控和通知系统</p>
                    <div class="space-y-2">
                        <a href="realtime_monitoring.html" target="_blank" class="block bg-cyan-500 text-white px-4 py-2 rounded hover:bg-cyan-600 transition-colors">
                            📡 实时监控
                        </a>
                        <a href="http://localhost:8000/ws/stats" target="_blank" class="block bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600 transition-colors">
                            📊 连接统计
                        </a>
                    </div>
                </div>
            </div>

            <!-- Enterprise Deployment -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">🚀</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">企业级部署</h3>
                    <p class="text-gray-600 mb-6">Docker容器化和Kubernetes部署</p>
                    <div class="space-y-2">
                        <button onclick="showDeploymentInfo()" class="block w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors">
                            🐳 Docker部署
                        </button>
                        <button onclick="showKubernetesInfo()" class="block w-full bg-slate-500 text-white px-4 py-2 rounded hover:bg-slate-600 transition-colors">
                            ☸️ Kubernetes
                        </button>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-indigo-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">👥</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">用户管理</h3>
                    <p class="text-gray-600 mb-6">认证、权限、多因子认证管理</p>
                    <div class="space-y-2">
                        <a href="user_management.html" target="_blank" class="block bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 transition-colors">
                            🔐 用户认证
                        </a>
                        <a href="#" onclick="showPermissionInfo()" class="block bg-violet-500 text-white px-4 py-2 rounded hover:bg-violet-600 transition-colors">
                            🔑 权限管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- Backup Management -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-emerald-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">💾</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">数据备份</h3>
                    <p class="text-gray-600 mb-6">自动备份、增量备份、数据恢复</p>
                    <div class="space-y-2">
                        <a href="backup_management.html" target="_blank" class="block bg-emerald-500 text-white px-4 py-2 rounded hover:bg-emerald-600 transition-colors">
                            💾 备份管理
                        </a>
                        <a href="#" onclick="showBackupInfo()" class="block bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600 transition-colors">
                            🔄 恢复管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- Fixed Assets Management -->
            <div class="feature-card bg-white rounded-xl p-8 shadow-sm border">
                <div class="text-center">
                    <div class="w-20 h-20 bg-amber-100 rounded-xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-4xl">🏢</span>
                    </div>
                    <h3 class="text-xl font-bold mb-4">固定資産管理</h3>
                    <p class="text-gray-600 mb-6">固定資産台帳・償却計算・資産管理</p>
                    <div class="space-y-2">
                        <a href="fixed_assets.html" target="_blank" class="block bg-amber-500 text-white px-4 py-2 rounded hover:bg-amber-600 transition-colors">
                            🏢 固定資産の一覧
                        </a>
                        <a href="#" onclick="showAssetCalculator()" class="block bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-colors">
                            📊 償却計算ツール
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technology Stack -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-12">
            <h3 class="text-2xl font-bold text-center mb-8">🛠️ 技术架构</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center p-4">
                    <div class="text-4xl mb-3">⚛️</div>
                    <h4 class="font-semibold mb-2">前端技术</h4>
                    <p class="text-sm text-gray-600">React 18 + TypeScript + Tailwind CSS + Framer Motion</p>
                </div>
                <div class="text-center p-4">
                    <div class="text-4xl mb-3">🐍</div>
                    <h4 class="font-semibold mb-2">后端技术</h4>
                    <p class="text-sm text-gray-600">FastAPI + SQLite + Redis + Microsoft AutoGen</p>
                </div>
                <div class="text-center p-4">
                    <div class="text-4xl mb-3">🤖</div>
                    <h4 class="font-semibold mb-2">AI技术</h4>
                    <p class="text-sm text-gray-600">Google Gemini + OpenAI + 自然语言处理</p>
                </div>
                <div class="text-center p-4">
                    <div class="text-4xl mb-3">☁️</div>
                    <h4 class="font-semibold mb-2">部署方案</h4>
                    <p class="text-sm text-gray-600">Docker + 云原生 + 微服务架构</p>
                </div>
            </div>
        </section>

        <!-- 用户数据显示区域 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-8">📊 あなたのデータ</h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 最近的会计分录 -->
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <span class="text-2xl mr-2">📝</span>
                        最近の仕訳
                    </h3>
                    <div id="recent-entries-container" class="space-y-3">
                        <div class="text-center py-8 text-gray-500">
                            <div class="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                            <p>データを読み込み中...</p>
                        </div>
                    </div>
                </div>

                <!-- 公司列表 -->
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <span class="text-2xl mr-2">🏢</span>
                        登録会社
                    </h3>
                    <div id="companies-list-container" class="space-y-3">
                        <div class="text-center py-8 text-gray-500">
                            <div class="animate-spin w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                            <p>データを読み込み中...</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-8 text-white text-center">
            <h3 class="text-2xl font-bold mb-6">🚀 快速开始</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h4 class="font-semibold mb-3">1. 体验AI记账</h4>
                    <p class="text-sm opacity-90 mb-4">尝试自然语言输入，体验一句话生成仕訳</p>
                    <a href="interactive_demo.html" target="_blank" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded transition-colors">
                        开始体验
                    </a>
                </div>
                <div>
                    <h4 class="font-semibold mb-3">2. 查看数据分析</h4>
                    <p class="text-sm opacity-90 mb-4">探索高级财务分析和实时监控功能</p>
                    <a href="advanced_dashboard.html" target="_blank" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded transition-colors">
                        查看分析
                    </a>
                </div>
                <div>
                    <h4 class="font-semibold mb-3">3. 系统概览</h4>
                    <p class="text-sm opacity-90 mb-4">查看完整系统架构和功能矩阵</p>
                    <a href="system_overview.html" target="_blank" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded transition-colors">
                        系统概览
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12 mt-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h4 class="font-bold mb-4">🚀 goldenledger会計AI</h4>
                    <p class="text-gray-400 text-sm">企业级AI智能记账系统，让记账变得简单智能。</p>
                </div>
                <div>
                    <h4 class="font-bold mb-4">核心功能</h4>
                    <ul class="text-gray-400 text-sm space-y-2">
                        <li>• AI自然语言记账</li>
                        <li>• 多语言支持</li>
                        <li>• 智能审计系统</li>
                        <li>• 财务报表分析</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-4">技术特性</h4>
                    <ul class="text-gray-400 text-sm space-y-2">
                        <li>• 实时数据处理</li>
                        <li>• 批量导入导出</li>
                        <li>• 系统监控管理</li>
                        <li>• API接口开放</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-bold mb-4">快速链接</h4>
                    <ul class="text-gray-400 text-sm space-y-2">
                        <li><a href="http://localhost:8000/docs" target="_blank" class="hover:text-white">API文档</a></li>
                        <li><a href="http://localhost:3000" target="_blank" class="hover:text-white">React应用</a></li>
                        <li><a href="system_monitor.html" target="_blank" class="hover:text-white">系统监控</a></li>
                        <li><a href="interactive_demo.html" target="_blank" class="hover:text-white">在线演示</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm">
                <p>&copy; 2024 GoldenLedger — Smart AI-Powered Finance System. 基于Microsoft AutoGen和Google Gemini构建.</p>
            </div>
        </div>
    </footer>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面加载动画
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 实时更新统计数据
            setInterval(updateStats, 5000);
        });

        // 更新统计数据
        function updateStats() {
            // 模拟实时数据更新
            const stats = document.querySelectorAll('.text-3xl.font-bold');
            stats.forEach(stat => {
                const currentValue = parseInt(stat.textContent);
                if (!isNaN(currentValue)) {
                    // 随机小幅变化
                    const change = Math.floor(Math.random() * 3) - 1;
                    const newValue = Math.max(0, currentValue + change);
                    stat.textContent = newValue.toString();
                }
            });
        }

        // 添加点击统计
        document.querySelectorAll('a[href$=".html"]').forEach(link => {
            link.addEventListener('click', function() {
                console.log(`访问功能模块: ${this.href}`);
            });
        });

        // 显示部署信息
        function showDeploymentInfo() {
            const deploymentInfo = `
🐳 Docker部署指南

1. 构建镜像:
   docker build -t goldenledger:1.0.0 .

2. 启动服务:
   docker-compose up -d

3. 查看状态:
   docker-compose ps

4. 查看日志:
   docker-compose logs -f

5. 停止服务:
   docker-compose down

📁 配置文件:
- Dockerfile: 镜像构建配置
- docker-compose.yml: 服务编排配置
- requirements.txt: Python依赖
            `;

            alert(deploymentInfo);
        }

        function showKubernetesInfo() {
            const k8sInfo = `
☸️ Kubernetes部署指南

1. 构建并推送镜像:
   docker build -t goldenledger:1.0.0 .
   docker tag goldenledger:1.0.0 your-registry/goldenledger:1.0.0
   docker push your-registry/goldenledger:1.0.0

2. 部署应用:
   kubectl apply -f k8s-deployment.yaml

3. 查看状态:
   kubectl get pods -l app=goldenledger
   kubectl get services

4. 查看日志:
   kubectl logs -l app=goldenledger

5. 扩容:
   kubectl scale deployment goldenledger --replicas=5

🔧 使用部署脚本:
   ./deploy.sh --env production --type kubernetes
            `;

            alert(k8sInfo);
        }

        // 检查WebSocket连接状态
        function checkWebSocketStatus() {
            fetch('http://localhost:8000/ws/stats')
                .then(response => response.json())
                .then(data => {
                    const wsStatus = document.createElement('div');
                    wsStatus.className = 'fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg';
                    wsStatus.innerHTML = `
                        📡 WebSocket状态<br>
                        活跃连接: ${data.active_connections}<br>
                        总连接数: ${data.total_connections}<br>
                        消息发送: ${data.messages_sent}
                    `;
                    document.body.appendChild(wsStatus);

                    setTimeout(() => wsStatus.remove(), 5000);
                })
                .catch(error => {
                    console.log('WebSocket服务未启用');
                });
        }

        // 定期检查WebSocket状态
        setInterval(checkWebSocketStatus, 30000);

        // 显示权限管理信息
        function showPermissionInfo() {
            const permissionInfo = `
🔑 权限管理系统

📋 角色权限:
- 超级管理员: 所有权限
- 管理员: 系统配置、用户管理、财务管理
- 会计师: 财务数据创建、更新、AI使用
- 审计师: 财务数据查看、AI审计
- 查看者: 财务数据查看、报表查看
- 访客: 基础财务数据查看

🔐 安全特性:
- JWT令牌认证
- 多因子认证 (MFA)
- 密码强度验证
- 登录历史记录
- 会话管理

🛡️ 权限控制:
- 基于角色的访问控制 (RBAC)
- 细粒度权限管理
- 动态权限验证
- API接口保护
            `;

            alert(permissionInfo);
        }

        // 显示备份管理信息
        function showBackupInfo() {
            const backupInfo = `
💾 数据备份系统

📋 备份类型:
- 完整备份: 备份所有数据和配置
- 增量备份: 仅备份新增和修改的数据
- 差异备份: 备份自上次完整备份以来的变化

⚙️ 自动化特性:
- 定时自动备份 (可配置间隔)
- 智能备份策略
- 自动清理旧备份
- 备份完整性验证

🔄 恢复功能:
- 一键数据恢复
- 选择性文件恢复
- 恢复进度监控
- 恢复历史记录

💽 存储管理:
- 数据压缩
- 存储空间监控
- 云存储支持 (计划中)
- 备份加密 (计划中)
            `;

            alert(backupInfo);
        }

        // 显示固定资产计算器信息
        function showAssetCalculator() {
            const calculatorInfo = `
🏢 固定資産償却計算ツール

📊 計算機能:
- 定額法償却計算
- 定率法償却計算
- 生産高比例法計算
- 年数合計法計算

💡 主要機能:
- 取得価額・耐用年数入力
- 月割計算対応
- 残存価額設定
- 償却限度額チェック
- 年度別償却スケジュール表示

🔧 対応資産:
- 建物・建物附属設備
- 機械装置・車両運搬具
- 工具器具備品
- ソフトウェア・特許権

📋 出力機能:
- 償却計算書PDF出力
- Excel形式エクスポート
- 仕訳データ自動生成

※ 現在開発中の機能です
            `;

            alert(calculatorInfo);
        }

        // 更新用户界面函数
        function updateUserUI() {
            console.log('🔄 更新用户界面...');

            // 检查是否有支付成功信息
            const paymentSuccess = localStorage.getItem('paymentSuccess');
            if (paymentSuccess) {
                try {
                    const paymentData = JSON.parse(paymentSuccess);
                    console.log('💳 检测到支付成功信息:', paymentData);

                    // 显示支付成功的弹窗
                    setTimeout(() => {
                        showPaymentSuccessModal(paymentData);
                    }, 1000); // 延迟1秒显示，确保页面完全加载

                    // 清除支付成功信息，避免重复显示
                    localStorage.removeItem('paymentSuccess');
                } catch (error) {
                    console.error('解析支付成功信息失败:', error);
                    localStorage.removeItem('paymentSuccess');
                }
            }

            // 更新用户信息显示
            const user = window.authManager?.getCurrentUser();
            if (user) {
                console.log('👤 当前用户:', user);

                // 获取用户订阅状态
                fetchUserSubscriptionStatus(user.email);
            }
        }

        // 显示支付成功模态框
        function showPaymentSuccessModal(paymentData) {
            // 创建模态框HTML
            const modalHTML = `
                <div id="paymentSuccessModal" class="modal" style="display: block; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
                    <div class="modal-content" style="background-color: #fefefe; margin: 10% auto; padding: 30px; border-radius: 10px; width: 90%; max-width: 500px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                        <div style="color: #4CAF50; font-size: 48px; margin-bottom: 20px;">✅</div>
                        <h2 style="color: #333; margin-bottom: 20px;">支付成功！</h2>
                        <p style="color: #666; font-size: 16px; margin-bottom: 15px;">${paymentData.message}</p>
                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
                            <h4 style="color: #333; margin-bottom: 15px;">支付详情</h4>
                            <p><strong>套餐:</strong> ${paymentData.planId === 'basic' ? 'Basic Plan' : 'Pro Plan'}</p>
                            <p><strong>订单ID:</strong> ${paymentData.orderId}</p>
                            <p><strong>金额:</strong> ${paymentData.amount} ${paymentData.currency}</p>
                            <p><strong>时间:</strong> ${new Date(paymentData.timestamp).toLocaleString('ja-JP')}</p>
                        </div>
                        <button onclick="closePaymentSuccessModal()" style="background-color: #4CAF50; color: white; border: none; padding: 12px 30px; border-radius: 5px; cursor: pointer; font-size: 16px; margin-top: 20px;">
                            开始使用
                        </button>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        // 关闭支付成功模态框
        function closePaymentSuccessModal() {
            const modal = document.getElementById('paymentSuccessModal');
            if (modal) {
                modal.remove();
            }
        }

        // 检查用户认证状态
        function checkAuthStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');

            if (!token || !user) {
                showLoginPrompt();
                return false;
            }

            return true;
        }

        // 显示登录提示
        function showLoginPrompt() {
            const mainContent = document.querySelector('.main-content, main, .dashboard-content');
            if (mainContent) {
                mainContent.innerHTML = `
                    <div class="text-center py-12">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">需要登录</h3>
                        <p class="mt-1 text-sm text-gray-500">请先登录以查看您的仪表板</p>
                        <div class="mt-6">
                            <button onclick="goToLogin()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                </svg>
                                立即登录
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // 跳转到登录页面
        function goToLogin() {
            window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
        }

        // 获取用户仪表板数据
        async function fetchUserDashboardData() {
            try {
                if (!checkAuthStatus()) {
                    return;
                }

                const token = localStorage.getItem('goldenledger_session_token');
                console.log('🔍 获取用户仪表板数据...');

                const response = await fetch(window.GoldenLedgerAPI.url('/api/user/dashboard'), {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    console.log('📊 用户仪表板数据:', data.data);
                    updateDashboardDisplay(data.data);
                    return data.data;
                } else {
                    console.error('获取仪表板数据失败:', data.error);
                    if (data.code === 'AUTH_REQUIRED' || data.code === 'INVALID_SESSION') {
                        // 认证失败，清除本地存储并显示登录提示
                        localStorage.removeItem('goldenledger_session_token');
                        localStorage.removeItem('goldenledger_user');
                        showLoginPrompt();
                        return null;
                    }
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('获取用户仪表板数据失败:', error);
                throw error;
            }
        }

        // 获取用户订阅状态（保持向后兼容）
        async function fetchUserSubscriptionStatus(email) {
            try {
                console.log('🔍 获取用户订阅状态:', email);

                const response = await fetch(`${window.GoldenLedgerAPI.url('/api/user/subscription-status')}?email=${encodeURIComponent(email)}`);
                const data = await response.json();

                if (data.success && data.user) {
                    console.log('📊 用户订阅信息:', data.user);
                    updateSubscriptionDisplay(data.user);
                } else {
                    console.log('ℹ️ 用户暂无订阅信息');
                }
            } catch (error) {
                console.error('获取用户订阅状态失败:', error);
            }
        }

        // 更新订阅信息显示
        function updateSubscriptionDisplay(userData) {
            // 在页面上显示用户的订阅信息
            const subscriptionInfo = userData.subscription;

            if (subscriptionInfo && subscriptionInfo.status === 'active') {
                // 创建订阅状态显示元素
                const statusHTML = `
                    <div id="subscriptionStatus" style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px; border-radius: 8px; margin: 20px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h4 style="margin: 0 0 10px 0;">🎉 当前套餐: ${subscriptionInfo.plan_id === 'basic' ? 'Basic Plan' : 'Pro Plan'}</h4>
                        <p style="margin: 5px 0;">状态: ${subscriptionInfo.status === 'active' ? '✅ 激活中' : '❌ 未激活'}</p>
                        <p style="margin: 5px 0;">到期时间: ${new Date(subscriptionInfo.expires_at).toLocaleDateString('ja-JP')}</p>
                        <p style="margin: 5px 0; font-size: 14px; opacity: 0.9;">感谢您的支持！</p>
                    </div>
                `;

                // 查找合适的位置插入订阅状态
                const container = document.querySelector('.container') || document.body;
                const existingStatus = document.getElementById('subscriptionStatus');

                if (existingStatus) {
                    existingStatus.outerHTML = statusHTML;
                } else {
                    container.insertAdjacentHTML('afterbegin', statusHTML);
                }
            }
        }

        // 页面加载时等待认证管理器完成认证检查
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Dashboard loaded - waiting for auth manager');

            // 等待认证管理器和页面保护器完成初始化
            let retries = 0;
            const maxRetries = 50; // 5秒

            while ((!window.authManager || !window.pageInitializer) && retries < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }

            if (window.authManager && window.pageInitializer) {
                try {
                    // 等待页面保护器完成认证检查
                    await window.pageInitializer.initialize();

                    // 如果到达这里，说明认证成功，可以初始化页面
                    console.log('✅ 认证检查完成，初始化仪表板');

                    // 更新用户界面
                    updateUserUI();

                    // 更新用户头像初始字母
                    const user = window.authManager.getCurrentUser();
                    if (user) {
                        const initial = user.name ? user.name.charAt(0).toUpperCase() :
                                       user.email ? user.email.charAt(0).toUpperCase() : 'U';
                        const avatarElement = document.querySelector('[data-user-initial]');
                        if (avatarElement) {
                            avatarElement.textContent = initial;
                        }
                    }

                    // 检查认证状态并获取用户仪表板数据
                    if (checkAuthStatus()) {
                        try {
                            await fetchUserDashboardData();
                        } catch (error) {
                            console.error('获取仪表板数据失败:', error);
                            // 如果获取仪表板数据失败，尝试获取用户订阅状态作为备用
                            if (user && user.email) {
                                await fetchUserSubscriptionStatus(user.email);
                            }
                        }
                    } else {
                        // 认证失败，不继续执行后续逻辑
                        return;
                    }

                    // 手动初始化用户状态组件，使用真实的Google OAuth用户数据
                    if (window.UserStatusComponent && user) {
                        // 销毁可能存在的旧组件
                        if (window.userStatusComponent) {
                            window.userStatusComponent.destroy();
                        }

                        // 创建新的用户状态组件
                        window.userStatusComponent = new UserStatusComponent();
                        console.log('✅ 用户状态组件已手动初始化，使用Google OAuth数据');
                    }
                } catch (error) {
                    console.error('页面初始化失败:', error);
                    // 页面保护器会处理重定向
                }
            } else {
                console.error('认证管理器或页面保护器未加载');
                window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.pathname);
            }
        });
        // 更新仪表板显示
        function updateDashboardDisplay(dashboardData) {
            try {
                // 更新统计数据
                const totalEntriesElement = document.getElementById('total-entries');
                const monthlyEntriesElement = document.getElementById('monthly-entries');
                const companiesCountElement = document.getElementById('companies-count');

                if (totalEntriesElement) {
                    totalEntriesElement.textContent = dashboardData.total_entries || 0;
                }
                if (monthlyEntriesElement) {
                    monthlyEntriesElement.textContent = dashboardData.monthly_entries || 0;
                }
                if (companiesCountElement) {
                    companiesCountElement.textContent = dashboardData.companies?.length || 0;
                }

                // 更新最近的会计分录
                updateRecentEntries(dashboardData.recent_entries || []);

                // 更新公司列表
                updateCompaniesList(dashboardData.companies || []);

                // 更新用户信息显示
                const user = JSON.parse(localStorage.getItem('goldenledger_user') || '{}');
                updateUserInfoDisplay(user);

                console.log('✅ 仪表板显示已更新');
            } catch (error) {
                console.error('更新仪表板显示失败:', error);
            }
        }

        // 更新最近的会计分录显示
        function updateRecentEntries(entries) {
            const container = document.getElementById('recent-entries-container');
            if (!container) return;

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-4">まだ仕訳がありません</p>';
                return;
            }

            const entriesHtml = entries.map(entry => `
                <div class="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="font-medium text-gray-900">${entry.description || '記述なし'}</h4>
                        <span class="text-sm text-gray-500">${new Date(entry.created_at).toLocaleDateString('ja-JP')}</span>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">借方:</span>
                            <span class="font-medium">${entry.debit_account || 'N/A'}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">貸方:</span>
                            <span class="font-medium">${entry.credit_account || 'N/A'}</span>
                        </div>
                    </div>
                    <div class="mt-2 text-right">
                        <span class="text-lg font-semibold text-green-600">¥${(entry.amount || 0).toLocaleString()}</span>
                    </div>
                </div>
            `).join('');

            container.innerHTML = entriesHtml;
        }

        // 更新公司列表显示
        function updateCompaniesList(companies) {
            const container = document.getElementById('companies-list-container');
            if (!container) return;

            if (companies.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-4">会社が登録されていません</p>';
                return;
            }

            const companiesHtml = companies.map(company => `
                <div class="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                    <h4 class="font-medium text-gray-900 mb-2">${company.name || '会社名未設定'}</h4>
                    <p class="text-sm text-gray-600 mb-2">${company.description || '説明なし'}</p>
                    <div class="flex justify-between items-center text-xs text-gray-500">
                        <span>作成日: ${new Date(company.created_at).toLocaleDateString('ja-JP')}</span>
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">アクティブ</span>
                    </div>
                </div>
            `).join('');

            container.innerHTML = companiesHtml;
        }

        // 更新用户信息显示
        function updateUserInfoDisplay(user) {
            const userNameElement = document.getElementById('user-name-display');
            const userEmailElement = document.getElementById('user-email-display');
            const userAvatarElement = document.getElementById('user-avatar-display');

            if (userNameElement) {
                userNameElement.textContent = user.name || user.username || 'ユーザー';
            }
            if (userEmailElement) {
                userEmailElement.textContent = user.email || '';
            }
            if (userAvatarElement && user.avatar_url) {
                userAvatarElement.src = user.avatar_url;
            }
        }
    </script>
</body>
</html>

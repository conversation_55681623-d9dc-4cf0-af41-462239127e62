<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ログイン - GoldenLedger | Smart AI-Powered Finance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <!-- 背景和音乐控制已禁用以避免重定向问题 -->
    <!-- <script src="background_control_new.js"></script> -->
    <!-- <script src="music_control_new.js"></script> -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .login-card { 
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-field {
            transition: all 0.3s ease;
            border: 2px solid #e1e5e9;
        }
        .input-field:focus {
            border-color: #00d4aa;
            box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <!-- 背景和音乐控制已禁用以避免重定向问题 -->
    <!-- <div id="background-controls"></div> -->
    <!-- <div id="music-controls"></div> -->

    <div class="w-full max-w-md">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
                <span class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-purple-600">GL</span>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">GoldenLedger</h1>
            <p class="text-white/80">Smart AI-Powered Finance System</p>
        </div>

        <!-- 登录表单 -->
        <div class="login-card rounded-2xl shadow-2xl p-8">
            <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">ログイン</h2>
            
            <form id="loginForm" class="space-y-6">
                <!-- 邮箱输入 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        メールアドレス
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="<EMAIL>"
                        required
                    >
                </div>

                <!-- 密码输入 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        パスワード
                    </label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="••••••••"
                        required
                    >
                </div>

                <!-- 记住我 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" id="remember" class="rounded border-gray-300 text-green-500 focus:ring-green-500">
                        <span class="ml-2 text-sm text-gray-600">ログイン状態を保持</span>
                    </label>
                    <a href="#" class="text-sm text-green-600 hover:text-green-700">
                        パスワードを忘れた？
                    </a>
                </div>

                <!-- 登录按钮 -->
                <button 
                    type="submit" 
                    id="loginBtn"
                    class="btn-primary w-full py-3 px-4 rounded-lg text-white font-semibold focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                    ログイン
                </button>

                <!-- Demo登录按钮 -->
                <button 
                    type="button" 
                    id="demoLoginBtn"
                    class="w-full py-3 px-4 rounded-lg border-2 border-gray-300 text-gray-700 font-semibold hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                    🚀 デモアカウントでログイン
                </button>
            </form>

            <!-- 分割线 -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <p class="text-center text-sm text-gray-600">
                    アカウントをお持ちでない方は
                    <a href="register.html" class="text-green-600 hover:text-green-700 font-semibold">
                        新規登録
                    </a>
                </p>
            </div>

            <!-- 返回首页 -->
            <div class="mt-4 text-center">
                <a href="index.html" class="text-sm text-gray-500 hover:text-gray-700">
                    ← ホームに戻る
                </a>
            </div>
        </div>
    </div>

    <script>
        console.log('New login page loaded - no auth redirects');

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            const submitBtn = document.getElementById('loginBtn');
            
            // 显示加载状态
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '🔄 ログイン中...';
            submitBtn.disabled = true;
            
            // 模拟登录处理
            setTimeout(() => {
                // 创建用户数据
                const userData = {
                    email: email,
                    name: email.split('@')[0],
                    loginTime: new Date().toISOString(),
                    sessionId: 'session_' + Date.now(),
                    remember: remember
                };
                
                // 保存到localStorage
                localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
                
                // 显示成功消息
                alert('✅ ログインに成功しました！\nダッシュボードに移動します。');
                
                // 跳转到仪表板
                window.location.href = 'master_dashboard.html';
            }, 1500);
        });

        // Demo登录处理
        document.getElementById('demoLoginBtn').addEventListener('click', function() {
            const userData = {
                email: '<EMAIL>',
                name: 'Demo User',
                loginTime: new Date().toISOString(),
                sessionId: 'demo_' + Date.now(),
                remember: false
            };
            
            localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
            alert('🎉 デモアカウントでログインしました！');
            window.location.href = 'master_dashboard.html';
        });
    </script>
</body>
</html>

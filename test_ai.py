#!/usr/bin/env python3
"""
测试AI功能
"""
import asyncio
import sys
import os

# 添加backend目录到路径
sys.path.append('backend')

async def test_ai_agents():
    """测试AI智能体"""
    try:
        from ai_agents import get_coordinator
        
        print("🤖 测试AI智能体功能...")
        
        # 获取协调器
        coordinator = get_coordinator()
        print("✅ AI协调器初始化成功")
        
        # 测试自然语言处理
        test_text = "购买办公用品1000円"
        print(f"📝 测试输入: {test_text}")
        
        result = await coordinator.process_natural_language_request(
            text=test_text,
            company_id="test_company"
        )
        
        print("✅ AI处理完成")
        print(f"📊 结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_ai():
    """测试简单AI调用"""
    try:
        import google.generativeai as genai
        
        print("🔧 测试Google Gemini API...")
        
        # 配置API
        genai.configure(api_key='YOUR_GEMINI_API_KEY_HERE')
        
        # 创建模型
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # 测试简单调用
        response = model.generate_content("Hello, how are you?")
        print(f"✅ Gemini响应: {response.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🧪 开始AI功能测试")
    print("=" * 50)
    
    # 测试简单AI调用
    simple_success = await test_simple_ai()
    print()
    
    # 测试AI智能体
    if simple_success:
        agents_success = await test_ai_agents()
    else:
        print("⚠️ 跳过AI智能体测试（基础AI调用失败）")
        agents_success = False
    
    print()
    print("=" * 50)
    if simple_success and agents_success:
        print("🎉 所有AI测试通过！")
    elif simple_success:
        print("⚠️ 基础AI功能正常，智能体需要调试")
    else:
        print("❌ AI功能存在问题")

if __name__ == "__main__":
    asyncio.run(main())

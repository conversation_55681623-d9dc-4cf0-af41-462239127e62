<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Floating Button Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
        }

        /* 完全复制主页面的浮动按钮样式 */
        :root {
            --sakura-pink: #FF69B4;
            --sakura-light-pink: #FFB6C1;
            --sakura-shadow: rgba(255, 105, 180, 0.2);
        }

        .sakura-floating-btn {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 60px !important;
            height: 60px !important;
            background: linear-gradient(135deg, var(--sakura-pink), var(--sakura-light-pink)) !important;
            border-radius: 50% !important;
            cursor: pointer !important;
            align-items: center !important;
            justify-content: center !important;
            box-shadow: 0 8px 24px var(--sakura-shadow) !important;
            transition: all 0.3s ease !important;
            z-index: 99997 !important;
        }

        .sakura-floating-btn.sakura-btn-hidden {
            display: none !important;
        }

        .sakura-floating-btn.sakura-btn-visible {
            display: flex !important;
        }

        .sakura-floating-btn:hover {
            transform: scale(1.1) !important;
            box-shadow: 0 12px 32px var(--sakura-shadow) !important;
        }

        .sakura-avatar {
            width: 40px !important;
            height: 40px !important;
            background: white !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }

        .sakura-icon {
            width: 24px !important;
            height: 24px !important;
            color: var(--sakura-pink) !important;
        }

        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 99999;
            font-family: monospace;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            text-align: left;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .btn {
            background: #FF69B4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .btn:hover {
            background: #FF1493;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 最终浮动按钮测试</h1>
        <p>完全复制主页面的浮动按钮逻辑和样式</p>

        <div>
            <button class="btn" onclick="runMainPageLogic()">🚀 运行主页面逻辑</button>
            <button class="btn" onclick="manualShow()">👁️ 手动显示</button>
            <button class="btn" onclick="manualHide()">🙈 手动隐藏</button>
            <button class="btn" onclick="checkElements()">🔍 检查元素</button>
        </div>

        <div class="log" id="log">
            <div style="color: #00FF00;">[开始] 最终测试页面加载完成</div>
        </div>
    </div>

    <!-- 状态指示器 -->
    <div class="status">
        🌸 Status: <span id="status-text" style="color: #FFD700;">Loading...</span>
    </div>

    <!-- 浮动按钮 - 完全复制主页面的HTML结构 -->
    <div id="sakura-floating-btn" class="sakura-floating-btn sakura-btn-hidden" onclick="onFloatingBtnClick()">
        <div class="sakura-avatar">
            <svg class="sakura-icon" viewBox="0 0 100 100" fill="currentColor">
                <!-- 樱花花瓣 -->
                <g transform="translate(50,50)">
                    <!-- 花瓣1 -->
                    <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(0)" opacity="0.9"/>
                    <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(0)" opacity="0.7"/>
                    
                    <!-- 花瓣2 -->
                    <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(72)" opacity="0.9"/>
                    <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(72)" opacity="0.7"/>
                    
                    <!-- 花瓣3 -->
                    <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(144)" opacity="0.9"/>
                    <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(144)" opacity="0.7"/>
                    
                    <!-- 花瓣4 -->
                    <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(216)" opacity="0.9"/>
                    <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(216)" opacity="0.7"/>
                    
                    <!-- 花瓣5 -->
                    <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(288)" opacity="0.9"/>
                    <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(288)" opacity="0.7"/>
                    
                    <!-- 花心 -->
                    <circle cx="0" cy="0" r="4" fill="#FFD700" opacity="0.8"/>
                    <circle cx="0" cy="0" r="2" fill="#FFA500" opacity="0.9"/>
                </g>
            </svg>
        </div>
    </div>

    <script>
        // 完全复制主页面的JavaScript逻辑
        function log(message, color = '#00BFFF') {
            const logContainer = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = color;
            div.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(div);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateChatboxStatus(message, color = '#FFD700') {
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = message;
                statusText.style.color = color;
            }
            log(`Status: ${message}`, color);
        }

        function checkElements() {
            const floatingBtn = document.getElementById('sakura-floating-btn');
            const statusText = document.getElementById('status-text');
            
            log('🔍 元素检查:', '#00BFFF');
            log(`  - 浮动按钮: ${floatingBtn ? '✅ 存在' : '❌ 不存在'}`, floatingBtn ? '#00FF00' : '#FF4444');
            log(`  - 状态文本: ${statusText ? '✅ 存在' : '❌ 不存在'}`, statusText ? '#00FF00' : '#FF4444');
            
            if (floatingBtn) {
                const classes = Array.from(floatingBtn.classList);
                const computedStyle = window.getComputedStyle(floatingBtn);
                const display = computedStyle.display;
                
                log(`  - 类: ${classes.join(', ')}`, '#00BFFF');
                log(`  - display: ${display}`, '#00BFFF');
                log(`  - 可见: ${display === 'flex' ? '是' : '否'}`, display === 'flex' ? '#00FF00' : '#FF4444');
            }
        }

        function manualShow() {
            const floatingBtn = document.getElementById('sakura-floating-btn');
            if (floatingBtn) {
                floatingBtn.classList.remove('sakura-btn-hidden');
                floatingBtn.classList.add('sakura-btn-visible');
                log('👁️ 手动显示浮动按钮', '#00FF00');
                updateChatboxStatus('Button Visible', '#00FF00');
            }
        }

        function manualHide() {
            const floatingBtn = document.getElementById('sakura-floating-btn');
            if (floatingBtn) {
                floatingBtn.classList.remove('sakura-btn-visible');
                floatingBtn.classList.add('sakura-btn-hidden');
                log('🙈 手动隐藏浮动按钮', '#FFD700');
                updateChatboxStatus('Button Hidden', '#FFD700');
            }
        }

        function runMainPageLogic() {
            log('🚀 运行主页面逻辑...', '#FF69B4');
            updateChatboxStatus('Initializing...', '#FFD700');
            
            const floatingBtn = document.getElementById('sakura-floating-btn');
            
            if (floatingBtn) {
                // 确保浮动按钮初始隐藏
                floatingBtn.classList.remove('sakura-btn-visible');
                floatingBtn.classList.add('sakura-btn-hidden');
                log('✅ 浮动按钮初始隐藏', '#00FF00');
                updateChatboxStatus('Button Hidden', '#00BFFF');
                
                // 倒计时显示
                let countdown = 2;
                const countdownInterval = setInterval(() => {
                    updateChatboxStatus(`Button in ${countdown}s`, '#FFD700');
                    countdown--;
                    
                    if (countdown < 0) {
                        clearInterval(countdownInterval);
                    }
                }, 1000);
                
                // Show floating button after 2 seconds
                setTimeout(() => {
                    floatingBtn.classList.remove('sakura-btn-hidden');
                    floatingBtn.classList.add('sakura-btn-visible');
                    log('🌸 2秒后显示浮动按钮', '#FF69B4');
                    updateChatboxStatus('Button Visible! 🌸', '#FF69B4');
                    
                    // 添加一个小的入场动画
                    floatingBtn.style.transform = 'scale(0)';
                    floatingBtn.style.transition = 'transform 0.3s ease-out';
                    
                    setTimeout(() => {
                        floatingBtn.style.transform = 'scale(1)';
                        updateChatboxStatus('Ready to Chat! 🌸', '#FF69B4');
                        log('✨ 入场动画完成', '#FF69B4');
                    }, 50);
                }, 2000);
            } else {
                log('❌ 浮动按钮元素不存在', '#FF4444');
                updateChatboxStatus('Button Error', '#FF4444');
            }
        }

        function onFloatingBtnClick() {
            log('🌸 浮动按钮被点击！', '#FF69B4');
            updateChatboxStatus('Button Clicked! 🌸', '#FF69B4');
            
            const btn = document.getElementById('sakura-floating-btn');
            if (btn) {
                btn.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    btn.style.transform = 'scale(1)';
                }, 150);
            }
            
            alert('🌸 樱花浮动按钮点击成功！\n\n在主页面中，这会打开chatbox。');
        }

        // 页面加载完成后
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 最终测试页面加载完成', '#00FF00');
            updateChatboxStatus('Page Loaded', '#00FF00');
            
            setTimeout(() => {
                checkElements();
                log('💡 点击"运行主页面逻辑"开始测试', '#00BFFF');
            }, 500);
        });
    </script>
</body>
</html>

/**
 * GoldenLedger - 音乐功能自动注入器
 * 自动在所有页面中添加音乐控制功能
 */

(function() {
    'use strict';
    
    // 检查是否已经注入过音乐控制器
    if (window.musicInjectorLoaded) {
        console.log('🎵 音乐注入器已加载，跳过重复加载');
        return;
    }
    
    window.musicInjectorLoaded = true;
    console.log('🎵 开始注入音乐控制功能...');

    // 配置
    const CONFIG = {
        musicUrl: '/music/love.mp3',
        enableOnPages: [
            // 主要功能页面
            'index.html',
            'master_dashboard.html',
            'interactive_demo.html',
            'journal_entries.html',
            'financial_reports.html',
            'login_simple.html',
            'register.html',
            
            // 管理页面
            'user_management.html',
            'user_settings.html',
            'backup_management.html',
            'data_export.html',
            'data_import_tool.html',
            'fixed_assets.html',
            
            // 测试页面
            'test_multiuser.html',
            'test_gemini_api.html',
            'project_status_check.html',
            'cost_analysis_jpy.html',
            'system_monitor.html',
            
            // 支付页面
            'payment.html',
            'payment-success.html',
            'pricing.html',
            
            // 其他页面
            'api_documentation.html',
            'terms_of_service.html',
            'privacy_policy.html'
        ],
        excludePages: [
            // 排除的页面
            'auth/error.html',
            'payment-failed.html'
        ]
    };

    // 检查当前页面是否应该启用音乐功能
    function shouldEnableMusic() {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop() || 'index.html';
        
        // 检查排除列表
        if (CONFIG.excludePages.some(page => currentPath.includes(page))) {
            return false;
        }
        
        // 检查启用列表
        if (CONFIG.enableOnPages.includes(currentPage)) {
            return true;
        }
        
        // 如果是根路径，启用音乐
        if (currentPath === '/' || currentPath === '/index.html' || currentPage === '') {
            return true;
        }
        
        // 默认启用（可以根据需要修改）
        return true;
    }

    // 动态加载通用音乐控制器
    function loadMusicController() {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载过
            if (window.UniversalMusicController) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/universal_music_control.js';
            script.onload = () => {
                console.log('🎵 通用音乐控制器脚本加载成功');
                resolve();
            };
            script.onerror = () => {
                console.error('🎵 通用音乐控制器脚本加载失败');
                reject(new Error('Failed to load music controller'));
            };
            
            document.head.appendChild(script);
        });
    }

    // 创建简化版音乐控制器（备用方案）
    function createFallbackMusicController() {
        console.log('🎵 使用备用音乐控制器');
        
        const controllerHTML = `
            <div id="fallback-music-controller" class="fixed bottom-4 right-4 z-50">
                <button id="fallback-music-btn" 
                        class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 
                               text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 
                               flex items-center justify-center transform hover:scale-105"
                        title="播放/暂停音乐">
                    <span id="fallback-music-icon">🎵</span>
                </button>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', controllerHTML);

        // 简单的音乐控制逻辑
        const audio = new Audio(CONFIG.musicUrl);
        audio.loop = true;
        audio.volume = 0.3;
        
        let isPlaying = false;
        
        const btn = document.getElementById('fallback-music-btn');
        const icon = document.getElementById('fallback-music-icon');
        
        btn.addEventListener('click', async () => {
            try {
                if (isPlaying) {
                    audio.pause();
                    isPlaying = false;
                    icon.textContent = '🎵';
                    btn.classList.remove('animate-pulse');
                } else {
                    await audio.play();
                    isPlaying = true;
                    icon.textContent = '🎶';
                    btn.classList.add('animate-pulse');
                }
            } catch (error) {
                console.error('🎵 音乐播放失败:', error);
            }
        });

        // 添加简单样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
            .animate-pulse {
                animation: pulse 2s infinite;
            }
        `;
        document.head.appendChild(style);
    }

    // 检查音频文件是否存在
    function checkAudioFile() {
        return new Promise((resolve) => {
            const audio = new Audio();
            audio.addEventListener('canplay', () => resolve(true));
            audio.addEventListener('error', () => resolve(false));
            audio.src = CONFIG.musicUrl;
        });
    }

    // 主初始化函数
    async function initMusicInjector() {
        // 检查是否应该启用音乐
        if (!shouldEnableMusic()) {
            console.log('🎵 当前页面不启用音乐功能');
            return;
        }

        console.log('🎵 当前页面启用音乐功能');

        // 检查音频文件
        const audioExists = await checkAudioFile();
        if (!audioExists) {
            console.warn('🎵 音频文件不存在或无法加载:', CONFIG.musicUrl);
            return;
        }

        try {
            // 尝试加载完整的音乐控制器
            await loadMusicController();
            
            // 等待DOM完全加载
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    setTimeout(() => {
                        if (typeof initUniversalMusicController === 'function') {
                            initUniversalMusicController();
                        }
                    }, 500);
                });
            } else {
                setTimeout(() => {
                    if (typeof initUniversalMusicController === 'function') {
                        initUniversalMusicController();
                    }
                }, 500);
            }
            
        } catch (error) {
            console.warn('🎵 完整音乐控制器加载失败，使用备用方案:', error);
            
            // 使用备用方案
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createFallbackMusicController);
            } else {
                createFallbackMusicController();
            }
        }
    }

    // 页面特定的音乐设置
    function applyPageSpecificSettings() {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop() || 'index.html';
        
        // 根据页面类型调整音乐设置
        const pageSettings = {
            'index.html': { volume: 0.3, autoPlay: false },
            'master_dashboard.html': { volume: 0.2, autoPlay: false },
            'interactive_demo.html': { volume: 0.25, autoPlay: false },
            'login_simple.html': { volume: 0.2, autoPlay: false },
            'test_multiuser.html': { volume: 0.15, autoPlay: false },
            'payment.html': { volume: 0.1, autoPlay: false }
        };
        
        const settings = pageSettings[currentPage];
        if (settings && window.musicController) {
            setTimeout(() => {
                if (window.musicController.setVolume) {
                    window.musicController.setVolume(settings.volume);
                }
            }, 1000);
        }
    }

    // 添加页面切换监听（SPA支持）
    function setupSPASupport() {
        let lastUrl = location.href;
        
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                console.log('🎵 检测到页面变化，重新初始化音乐控制器');
                
                // 延迟重新初始化，确保新页面内容已加载
                setTimeout(() => {
                    initMusicInjector();
                    applyPageSpecificSettings();
                }, 1000);
            }
        }).observe(document, { subtree: true, childList: true });
    }

    // 添加全局快捷键
    function setupGlobalShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Alt + M 快速切换音乐
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                
                if (window.musicController && window.musicController.togglePlayPause) {
                    window.musicController.togglePlayPause();
                } else {
                    // 备用方案
                    const fallbackBtn = document.getElementById('fallback-music-btn');
                    if (fallbackBtn) {
                        fallbackBtn.click();
                    }
                }
            }
        });
    }

    // 添加音乐状态同步（多标签页支持）
    function setupTabSync() {
        // 监听其他标签页的音乐状态变化
        window.addEventListener('storage', (e) => {
            if (e.key === 'golden_ledger_music_settings') {
                console.log('🎵 检测到其他标签页音乐状态变化');
                
                if (window.musicController && window.musicController.loadUserSettings) {
                    window.musicController.loadUserSettings();
                }
            }
        });
    }

    // 性能监控
    function setupPerformanceMonitoring() {
        const startTime = performance.now();
        
        window.addEventListener('load', () => {
            const loadTime = performance.now() - startTime;
            console.log(`🎵 音乐功能初始化耗时: ${loadTime.toFixed(2)}ms`);
        });
    }

    // 错误处理
    function setupErrorHandling() {
        window.addEventListener('error', (e) => {
            if (e.filename && e.filename.includes('music')) {
                console.error('🎵 音乐功能发生错误:', e.error);
                
                // 尝试恢复
                setTimeout(() => {
                    if (!document.getElementById('music-controller') && !document.getElementById('fallback-music-controller')) {
                        console.log('🎵 尝试恢复音乐功能');
                        createFallbackMusicController();
                    }
                }, 2000);
            }
        });
    }

    // 启动初始化
    console.log('🎵 音乐注入器开始初始化...');
    
    // 设置各种功能
    setupGlobalShortcuts();
    setupTabSync();
    setupPerformanceMonitoring();
    setupErrorHandling();
    setupSPASupport();
    
    // 主初始化
    initMusicInjector().then(() => {
        console.log('🎵 音乐注入器初始化完成');
        
        // 应用页面特定设置
        setTimeout(applyPageSpecificSettings, 2000);
        
    }).catch((error) => {
        console.error('🎵 音乐注入器初始化失败:', error);
    });

    // 导出到全局作用域供调试使用
    window.musicInjector = {
        config: CONFIG,
        shouldEnableMusic,
        initMusicInjector,
        applyPageSpecificSettings
    };

})();

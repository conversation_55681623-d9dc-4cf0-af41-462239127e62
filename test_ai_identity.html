<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI身份识别测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_injector.js"></script>
    <style>
        .test-result {
            transition: all 0.3s ease;
        }
        .test-result.success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        .test-result.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        .test-result.info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- 页面标题 -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">
                    🤖 AI身份识别测试
                </h1>
                <p class="text-gray-600">
                    测试AI聊天机器人的身份识别和个性化回答功能
                </p>
            </div>

            <!-- 测试控制面板 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-2xl font-semibold mb-4">🧪 测试控制面板</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <button onclick="testJapaneseIdentity()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        🇯🇵 日语身份测试
                    </button>
                    <button onclick="testChineseIdentity()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                        🇨🇳 中文身份测试
                    </button>
                    <button onclick="testEnglishIdentity()" 
                            class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                        🇺🇸 英语身份测试
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="testPersonalityModule()" 
                            class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors">
                        🧠 个性化模块测试
                    </button>
                    <button onclick="clearResults()" 
                            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        🗑️ 清除结果
                    </button>
                </div>
            </div>

            <!-- 测试结果显示 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-semibold mb-4">📊 测试结果</h2>
                <div id="test-results" class="space-y-4">
                    <div class="text-gray-500 text-center py-8">
                        点击上方按钮开始测试...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="/env-config.js?v=3.6.9"></script>
    <script src="/chatbot/AIPersonality.js"></script>
    <script src="/chatbot/UsageTracker.js"></script>
    <script src="/chatbot/GeminiAPI.js"></script>

    <script>
        let testCount = 0;
        let geminiAPI = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            addResult('🚀 初始化AI身份识别测试系统...', 'info');
            
            try {
                // 等待模块加载
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 初始化API
                geminiAPI = new GeminiAPI();
                addResult('✅ GeminiAPI初始化成功', 'success');
                
                // 检查个性化模块
                if (window.AIPersonality) {
                    addResult('✅ AI个性化模块加载成功', 'success');
                } else {
                    addResult('⚠️ AI个性化模块未加载', 'error');
                }
                
            } catch (error) {
                addResult(`❌ 初始化失败: ${error.message}`, 'error');
            }
        });

        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type} p-4 rounded-lg`;
            
            const timestamp = new Date().toLocaleTimeString();
            div.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="font-medium">${message}</div>
                    </div>
                    <div class="text-sm opacity-75">${timestamp}</div>
                </div>
            `;
            
            if (results.firstChild && results.firstChild.textContent.includes('点击上方按钮')) {
                results.innerHTML = '';
            }
            
            results.appendChild(div);
            div.scrollIntoView({ behavior: 'smooth' });
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = `
                <div class="text-gray-500 text-center py-8">
                    点击上方按钮开始测试...
                </div>
            `;
            testCount = 0;
        }

        async function testJapaneseIdentity() {
            testCount++;
            addResult(`🧪 测试 #${testCount}: 日语身份识别`, 'info');
            
            const questions = [
                'あなたは誰ですか？',
                'どんなAIですか？',
                '自己紹介してください'
            ];
            
            for (const question of questions) {
                await testIdentityQuestion(question, 'ja');
            }
        }

        async function testChineseIdentity() {
            testCount++;
            addResult(`🧪 测试 #${testCount}: 中文身份识别`, 'info');
            
            const questions = [
                '你是谁？',
                '你是什么AI？',
                '介绍一下自己'
            ];
            
            for (const question of questions) {
                await testIdentityQuestion(question, 'zh');
            }
        }

        async function testEnglishIdentity() {
            testCount++;
            addResult(`🧪 测试 #${testCount}: 英语身份识别`, 'info');
            
            const questions = [
                'Who are you?',
                'What are you?',
                'Introduce yourself'
            ];
            
            for (const question of questions) {
                await testIdentityQuestion(question, 'en');
            }
        }

        async function testIdentityQuestion(question, expectedLang) {
            try {
                addResult(`❓ 问题: "${question}"`, 'info');
                
                if (!geminiAPI) {
                    addResult('❌ GeminiAPI未初始化', 'error');
                    return;
                }
                
                const response = await geminiAPI.sendMessage(question);
                
                // 检查回答是否包含公司信息
                const hasCompanyInfo = response.includes('GoldenOrangeTech') || 
                                     response.includes('GoldenLedger');
                
                if (hasCompanyInfo) {
                    addResult(`✅ 回答正确包含公司信息`, 'success');
                    addResult(`💬 AI回答: ${response.substring(0, 200)}...`, 'info');
                } else {
                    addResult(`❌ 回答未包含预期的公司信息`, 'error');
                    addResult(`💬 AI回答: ${response.substring(0, 200)}...`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ 测试失败: ${error.message}`, 'error');
            }
            
            // 添加延迟避免API限制
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        async function testPersonalityModule() {
            testCount++;
            addResult(`🧪 测试 #${testCount}: 个性化模块功能`, 'info');
            
            if (!window.AIPersonality) {
                addResult('❌ AI个性化模块未加载', 'error');
                return;
            }
            
            try {
                // 测试身份检测
                const testMessages = [
                    { msg: 'あなたは誰ですか？', expected: 'ja' },
                    { msg: '你是谁？', expected: 'zh' },
                    { msg: 'Who are you?', expected: 'en' },
                    { msg: '今天天气怎么样？', expected: null }
                ];
                
                for (const test of testMessages) {
                    const detected = window.AIPersonality.detectIdentityQuery(test.msg);
                    const result = detected === test.expected ? '✅' : '❌';
                    addResult(`${result} "${test.msg}" -> 检测语言: ${detected || 'null'}`, 
                             detected === test.expected ? 'success' : 'error');
                }
                
                // 测试身份回答生成
                const identityResponse = window.AIPersonality.generateIdentityResponse('ja');
                if (identityResponse.includes('GoldenOrangeTech')) {
                    addResult('✅ 身份回答生成正确', 'success');
                } else {
                    addResult('❌ 身份回答生成失败', 'error');
                }
                
            } catch (error) {
                addResult(`❌ 个性化模块测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

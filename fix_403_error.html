<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复403错误工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-red-600">🔧 修复403错误工具</h1>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h2 class="font-semibold text-yellow-800 mb-2">💡 问题诊断</h2>
            <p class="text-yellow-700">403错误通常是因为AI使用次数超过了免费用户限制。我们已经将限制从10次/月提升到1000次/月，但需要清理数据库中的旧使用记录。</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 步骤1: 获取Token -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">步骤1: 获取认证Token</h2>
                <div class="space-y-4">
                    <button onclick="getTokenFromStorage()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 w-full">
                        从本地存储获取Token
                    </button>
                    <div>
                        <label class="block text-sm font-medium mb-2">Session Token:</label>
                        <textarea id="sessionToken" class="w-full p-2 border rounded text-xs h-16" placeholder="Token将显示在这里"></textarea>
                    </div>
                    <div id="token-status" class="p-3 bg-gray-50 rounded"></div>
                </div>
            </div>
            
            <!-- 步骤2: 检查AI使用情况 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">步骤2: 检查AI使用情况</h2>
                <div class="space-y-4">
                    <button onclick="checkAIUsage()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        检查当前使用情况
                    </button>
                    <div id="usage-status" class="p-3 bg-gray-50 rounded min-h-20"></div>
                </div>
            </div>
            
            <!-- 步骤3: 重置AI使用记录 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4 text-red-600">步骤3: 重置AI使用记录</h2>
                <div class="space-y-4">
                    <div class="bg-red-50 border border-red-200 rounded p-3">
                        <p class="text-red-700 text-sm">⚠️ 这将清除当月的AI使用记录，重置使用计数为0</p>
                    </div>
                    <button onclick="resetAIUsage()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 w-full">
                        重置AI使用记录
                    </button>
                    <div id="reset-status" class="p-3 bg-gray-50 rounded min-h-20"></div>
                </div>
            </div>
            
            <!-- 步骤4: 测试AI处理 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4 text-green-600">步骤4: 测试AI处理</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">测试文本:</label>
                        <input type="text" id="testText" class="w-full p-2 border rounded" value="今天买了办公用品100元" placeholder="输入要处理的文本">
                    </div>
                    <button onclick="testAIProcessing()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        测试AI处理
                    </button>
                    <div id="test-status" class="p-3 bg-gray-50 rounded min-h-20"></div>
                </div>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="bg-black text-green-400 p-6 rounded-lg shadow mt-6">
            <h2 class="text-xl font-semibold mb-4 text-white">📋 操作日志</h2>
            <div id="debug-log" class="font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto"></div>
            <button onclick="clearLog()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                清空日志
            </button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const colors = {
                info: 'text-green-400',
                error: 'text-red-400',
                warning: 'text-yellow-400',
                success: 'text-blue-400'
            };
            logDiv.innerHTML += `<span class="${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // 步骤1: 获取Token
        function getTokenFromStorage() {
            log('正在从本地存储获取Token...', 'info');
            const token = localStorage.getItem('goldenledger_session_token');
            const tokenInput = document.getElementById('sessionToken');
            const statusDiv = document.getElementById('token-status');
            
            if (token) {
                tokenInput.value = token;
                statusDiv.innerHTML = `
                    <div class="text-green-600">
                        <p><strong>✅ Token找到</strong></p>
                        <p>长度: ${token.length} 字符</p>
                        <p>前缀: ${token.substring(0, 20)}...</p>
                    </div>
                `;
                log(`Token获取成功，长度: ${token.length}`, 'success');
            } else {
                statusDiv.innerHTML = '<p class="text-red-600"><strong>❌ 未找到Token</strong><br>请先登录生产环境: <a href="https://ledger.goldenorangetech.com/login_simple.html" target="_blank" class="underline">点击登录</a></p>';
                log('未找到Token，请先登录', 'error');
            }
        }
        
        // 步骤2: 检查AI使用情况
        async function checkAIUsage() {
            const token = document.getElementById('sessionToken').value;
            const statusDiv = document.getElementById('usage-status');
            
            if (!token) {
                statusDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                log('检查AI使用失败: 没有Token', 'error');
                return;
            }
            
            log('正在检查AI使用情况...', 'info');
            statusDiv.innerHTML = '<p>检查中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/reset-usage?action=stats`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                log(`AI使用情况检查响应: ${response.status}`, response.status === 200 ? 'success' : 'error');
                
                if (data.success) {
                    statusDiv.innerHTML = `
                        <div class="text-blue-600">
                            <p><strong>📊 AI使用统计</strong></p>
                            <p><strong>当月使用:</strong> ${data.data.monthly_usage} 次</p>
                            <p><strong>总使用:</strong> ${data.data.total_usage} 次</p>
                            <p><strong>当前月份:</strong> ${data.data.current_month}</p>
                            <p><strong>用户ID:</strong> ${data.data.user_id}</p>
                            ${data.data.monthly_usage >= 10 ? 
                                '<p class="text-red-600 mt-2">⚠️ 当月使用已超过原限制(10次)，需要重置</p>' : 
                                '<p class="text-green-600 mt-2">✅ 使用次数正常</p>'
                            }
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `<p class="text-red-600">❌ 检查失败: ${data.error}</p>`;
                }
            } catch (error) {
                log(`AI使用情况检查错误: ${error.message}`, 'error');
                statusDiv.innerHTML = `<p class="text-red-600">❌ 检查失败: ${error.message}</p>`;
            }
        }
        
        // 步骤3: 重置AI使用记录
        async function resetAIUsage() {
            const token = document.getElementById('sessionToken').value;
            const statusDiv = document.getElementById('reset-status');
            
            if (!token) {
                statusDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                return;
            }
            
            if (!confirm('确定要重置AI使用记录吗？这将清除当月的所有AI使用记录。')) {
                return;
            }
            
            log('开始重置AI使用记录...', 'warning');
            statusDiv.innerHTML = '<p>重置中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/reset-usage?action=reset`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                log(`重置AI使用记录响应: ${response.status}`, response.status === 200 ? 'success' : 'error');
                
                if (data.success) {
                    statusDiv.innerHTML = `
                        <div class="text-green-600">
                            <p><strong>✅ 重置成功!</strong></p>
                            <p><strong>删除记录数:</strong> ${data.deleted_records}</p>
                            <p><strong>重置月份:</strong> ${data.month}</p>
                            <p class="mt-2 text-sm">现在可以重新测试AI处理功能了</p>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `<p class="text-red-600">❌ 重置失败: ${data.error}</p>`;
                }
            } catch (error) {
                log(`重置AI使用记录错误: ${error.message}`, 'error');
                statusDiv.innerHTML = `<p class="text-red-600">❌ 重置失败: ${error.message}</p>`;
            }
        }
        
        // 步骤4: 测试AI处理
        async function testAIProcessing() {
            const token = document.getElementById('sessionToken').value;
            const text = document.getElementById('testText').value;
            const statusDiv = document.getElementById('test-status');
            
            if (!token) {
                statusDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                return;
            }
            
            if (!text) {
                statusDiv.innerHTML = '<p class="text-red-600">请输入测试文本</p>';
                return;
            }
            
            log(`开始测试AI处理，文本: "${text}"`, 'info');
            statusDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        text: text,
                        company_id: 'default'
                    })
                });
                const endTime = Date.now();
                
                const data = await response.json();
                log(`AI测试完成，耗时: ${endTime - startTime}ms，状态: ${response.status}`, response.status === 200 ? 'success' : 'error');
                
                if (response.status === 200 && data.success) {
                    statusDiv.innerHTML = `
                        <div class="text-green-600">
                            <p><strong>🎉 AI处理成功!</strong></p>
                            <p><strong>响应时间:</strong> ${endTime - startTime}ms</p>
                            <div class="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                                <p><strong>借方科目:</strong> ${data.data.debit_account}</p>
                                <p><strong>贷方科目:</strong> ${data.data.credit_account}</p>
                                <p><strong>金额:</strong> ${data.data.amount}</p>
                                <p><strong>描述:</strong> ${data.data.description}</p>
                            </div>
                            <p class="mt-2 text-sm text-green-700">✅ 403错误已修复！AI处理功能正常工作。</p>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="text-red-600">
                            <p><strong>❌ AI处理失败</strong></p>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>错误:</strong> ${data.error || '未知错误'}</p>
                            ${response.status === 403 ? 
                                '<p class="mt-2 text-sm">如果仍然是403错误，请先执行步骤3重置AI使用记录</p>' : ''
                            }
                        </div>
                    `;
                }
            } catch (error) {
                log(`AI测试错误: ${error.message}`, 'error');
                statusDiv.innerHTML = `<p class="text-red-600">❌ 测试失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动获取token
        window.addEventListener('load', function() {
            log('403错误修复工具已加载', 'info');
            getTokenFromStorage();
        });
    </script>
</body>
</html>

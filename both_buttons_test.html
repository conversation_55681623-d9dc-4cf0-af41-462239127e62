<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双按钮拖动测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        .test-panel {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100001;
            max-width: 350px;
            font-size: 12px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin-top: 120px;
        }
        
        .button-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-panel" id="testPanel">
        <h3>🎯 双按钮拖动测试</h3>
        <div id="statusList">
            <div class="status-item">
                <div class="status-dot status-warning"></div>
                <span>初始化中...</span>
            </div>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="resetAllPositions()" style="padding: 4px 8px; background: #333; border: none; color: white; border-radius: 4px; margin-right: 5px;">
                重置位置
            </button>
            <button onclick="showAllPositions()" style="padding: 4px 8px; background: #333; border: none; color: white; border-radius: 4px;">
                显示位置
            </button>
        </div>
    </div>

    <div class="test-container">
        <h1 class="text-3xl font-bold text-center mb-8">🎵👁️ 双按钮拖动测试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="button-info">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    🎵 音乐按钮
                </h2>
                <ul class="space-y-2 text-sm">
                    <li>✅ 任意方向拖动</li>
                    <li>✅ 点击播放/暂停</li>
                    <li>✅ 位置自动保存</li>
                    <li>✅ 边界限制</li>
                    <li>✅ 拖动状态反馈</li>
                </ul>
                <div id="musicStatus" class="mt-3 p-2 bg-gray-100 rounded text-xs">
                    状态: 检测中...
                </div>
            </div>
            
            <div class="button-info">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    👁️ 眼睛按钮
                </h2>
                <ul class="space-y-2 text-sm">
                    <li>✅ 任意方向拖动</li>
                    <li>✅ 点击背景控制</li>
                    <li>✅ 位置自动保存</li>
                    <li>✅ 边界限制</li>
                    <li>✅ 半透明显示</li>
                </ul>
                <div id="eyeStatus" class="mt-3 p-2 bg-gray-100 rounded text-xs">
                    状态: 检测中...
                </div>
            </div>
        </div>
        
        <div class="mt-8 p-6 bg-blue-50 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🧪 测试说明</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-semibold mb-2">桌面端测试:</h4>
                    <ul class="space-y-1">
                        <li>• 用鼠标拖动两个按钮</li>
                        <li>• 测试横向、纵向、对角线拖动</li>
                        <li>• 验证边界限制</li>
                        <li>• 测试点击功能</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-2">移动端测试:</h4>
                    <ul class="space-y-1">
                        <li>• 用手指触摸拖动</li>
                        <li>• 测试触摸响应</li>
                        <li>• 验证移动端适配</li>
                        <li>• 检查按钮大小</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                拖动测试完成后，刷新页面检查位置是否保存
            </p>
        </div>
    </div>

    <script>
        const statusList = document.getElementById('statusList');
        const musicStatus = document.getElementById('musicStatus');
        const eyeStatus = document.getElementById('eyeStatus');
        
        let testResults = [];
        
        function updateStatus(test, status, message = '') {
            const existing = testResults.find(t => t.test === test);
            if (existing) {
                existing.status = status;
                existing.message = message;
            } else {
                testResults.push({ test, status, message });
            }
            renderStatus();
        }
        
        function renderStatus() {
            const statusHtml = testResults.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'warning' ? 'status-warning' : 'status-error';
                return `
                    <div class="status-item">
                        <div class="status-dot ${statusClass}"></div>
                        <span>${result.test} ${result.message}</span>
                    </div>
                `;
            }).join('');
            statusList.innerHTML = statusHtml;
        }
        
        function resetAllPositions() {
            localStorage.removeItem('golden_ledger_music_settings');
            localStorage.removeItem('golden_ledger_background_settings');
            updateStatus('位置重置', 'success', '- 已清除所有保存位置');
            setTimeout(() => location.reload(), 1000);
        }
        
        function showAllPositions() {
            const musicSettings = JSON.parse(localStorage.getItem('golden_ledger_music_settings') || '{}');
            const bgSettings = JSON.parse(localStorage.getItem('golden_ledger_background_settings') || '{}');
            
            alert(`保存的位置:\n\n🎵 音乐按钮: ${JSON.stringify(musicSettings.position || '默认位置')}\n\n👁️ 眼睛按钮: ${JSON.stringify(bgSettings.position || '默认位置')}`);
        }
        
        function updateButtonStatus() {
            // 检查音乐按钮
            const musicController = document.getElementById('music-controller');
            const musicBtn = document.getElementById('music-toggle');
            
            if (musicController && musicBtn) {
                const rect = musicController.getBoundingClientRect();
                const isDraggable = musicBtn.style.cursor === 'grab';
                musicStatus.innerHTML = `
                    位置: (${Math.round(rect.left)}, ${Math.round(rect.top)})<br>
                    拖动: ${isDraggable ? '✅ 可拖动' : '❌ 不可拖动'}<br>
                    样式: ${musicController.style.left || '默认'}, ${musicController.style.top || '默认'}
                `;
                updateStatus('🎵 音乐按钮', isDraggable ? 'success' : 'error', isDraggable ? '- 可拖动' : '- 不可拖动');
            } else {
                musicStatus.innerHTML = '❌ 未找到音乐按钮';
                updateStatus('🎵 音乐按钮', 'error', '- 未找到');
            }
            
            // 检查眼睛按钮
            const bgController = document.querySelector('.background-control');
            const bgBtn = document.getElementById('background-toggle');
            
            if (bgController && bgBtn) {
                const rect = bgController.getBoundingClientRect();
                const isDraggable = bgBtn.style.cursor === 'grab';
                eyeStatus.innerHTML = `
                    位置: (${Math.round(rect.left)}, ${Math.round(rect.top)})<br>
                    拖动: ${isDraggable ? '✅ 可拖动' : '❌ 不可拖动'}<br>
                    样式: ${bgController.style.left || '默认'}, ${bgController.style.top || '默认'}
                `;
                updateStatus('👁️ 眼睛按钮', isDraggable ? 'success' : 'error', isDraggable ? '- 可拖动' : '- 不可拖动');
            } else {
                eyeStatus.innerHTML = '❌ 未找到眼睛按钮';
                updateStatus('👁️ 眼睛按钮', 'error', '- 未找到');
            }
        }
        
        // 监听页面加载
        window.addEventListener('load', function() {
            updateStatus('页面加载', 'success', '- 完成');
            
            // 延迟检查按钮状态
            setTimeout(() => {
                updateButtonStatus();
                
                // 定期更新状态
                setInterval(updateButtonStatus, 2000);
                
                // 监听拖动事件
                let musicDragDetected = false;
                let eyeDragDetected = false;
                
                // 监听控制台日志
                const originalLog = console.log;
                console.log = function(...args) {
                    originalLog.apply(console, args);
                    
                    const message = args.join(' ');
                    if (message.includes('🎵 开始拖动') && !musicDragDetected) {
                        musicDragDetected = true;
                        updateStatus('🎵 拖动测试', 'success', '- 拖动功能正常');
                    } else if (message.includes('👁️ 开始拖动') && !eyeDragDetected) {
                        eyeDragDetected = true;
                        updateStatus('👁️ 拖动测试', 'success', '- 拖动功能正常');
                    }
                };
                
            }, 1500);
        });
        
        console.log('🧪 双按钮拖动测试页面初始化完成');
    </script>
</body>
</html>

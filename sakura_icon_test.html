<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sakura Icon Test</title>
    <style>
        body {
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
        }

        .test-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .icon-showcase {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
        }

        .floating-btn-demo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 24px rgba(255, 105, 180, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            animation: float 3s ease-in-out infinite;
        }

        .floating-btn-demo:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px rgba(255, 105, 180, 0.4);
        }

        .floating-btn-demo::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            animation: glow 2s ease-in-out infinite alternate;
            z-index: -1;
        }

        .floating-btn-demo:hover::after {
            content: '🌸';
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20px;
            animation: pop 0.6s ease-out;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        @keyframes glow {
            0% { transform: scale(0.8); opacity: 0.3; }
            100% { transform: scale(1.2); opacity: 0.6; }
        }

        @keyframes pop {
            0% { opacity: 0; transform: translateX(-50%) translateY(10px) scale(0.5); }
            50% { opacity: 1; transform: translateX(-50%) translateY(-10px) scale(1.2); }
            100% { opacity: 0; transform: translateX(-50%) translateY(-30px) scale(0.8); }
        }

        .avatar-demo {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .small-avatar-demo {
            width: 40px;
            height: 40px;
            background: white;
            border: 2px solid #FF69B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sakura-icon {
            color: #FF69B4;
        }

        .description {
            font-size: 14px;
            opacity: 0.9;
            max-width: 120px;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            text-align: left;
            overflow-x: auto;
            font-size: 12px;
        }

        .feature-list {
            text-align: left;
            max-width: 600px;
            margin: 20px auto;
        }

        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li::before {
            content: '🌸';
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌸 Sakura Icon Showcase</h1>
        <p>GoldenLedger Chatbox 樱花图标设计展示</p>

        <div class="icon-showcase">
            <!-- 浮动按钮樱花 -->
            <div class="icon-item">
                <div class="floating-btn-demo">
                    <svg width="40" height="40" viewBox="0 0 100 100" fill="white">
                        <g transform="translate(50,50)">
                            <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(0)" opacity="0.9"/>
                            <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(0)" opacity="0.7"/>
                            <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(72)" opacity="0.9"/>
                            <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(72)" opacity="0.7"/>
                            <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(144)" opacity="0.9"/>
                            <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(144)" opacity="0.7"/>
                            <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(216)" opacity="0.9"/>
                            <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(216)" opacity="0.7"/>
                            <ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(288)" opacity="0.9"/>
                            <ellipse cx="0" cy="-18" rx="6" ry="12" fill="#FFFFFF" transform="rotate(288)" opacity="0.7"/>
                            <circle cx="0" cy="0" r="4" fill="#FFD700" opacity="0.8"/>
                            <circle cx="0" cy="0" r="2" fill="#FFA500" opacity="0.9"/>
                        </g>
                    </svg>
                </div>
                <div class="description">浮动按钮樱花<br>(悬停查看特效)</div>
            </div>

            <!-- 头像樱花 -->
            <div class="icon-item">
                <div class="avatar-demo">
                    <svg width="30" height="30" viewBox="0 0 60 60" fill="currentColor" class="sakura-icon">
                        <g transform="translate(30,30)">
                            <ellipse cx="0" cy="-12" rx="5" ry="9" fill="#FF69B4" transform="rotate(0)" opacity="0.9"/>
                            <ellipse cx="0" cy="-11" rx="3" ry="7" fill="#FFB6C1" transform="rotate(0)" opacity="0.8"/>
                            <ellipse cx="0" cy="-12" rx="5" ry="9" fill="#FF69B4" transform="rotate(72)" opacity="0.9"/>
                            <ellipse cx="0" cy="-11" rx="3" ry="7" fill="#FFB6C1" transform="rotate(72)" opacity="0.8"/>
                            <ellipse cx="0" cy="-12" rx="5" ry="9" fill="#FF69B4" transform="rotate(144)" opacity="0.9"/>
                            <ellipse cx="0" cy="-11" rx="3" ry="7" fill="#FFB6C1" transform="rotate(144)" opacity="0.8"/>
                            <ellipse cx="0" cy="-12" rx="5" ry="9" fill="#FF69B4" transform="rotate(216)" opacity="0.9"/>
                            <ellipse cx="0" cy="-11" rx="3" ry="7" fill="#FFB6C1" transform="rotate(216)" opacity="0.8"/>
                            <ellipse cx="0" cy="-12" rx="5" ry="9" fill="#FF69B4" transform="rotate(288)" opacity="0.9"/>
                            <ellipse cx="0" cy="-11" rx="3" ry="7" fill="#FFB6C1" transform="rotate(288)" opacity="0.8"/>
                            <circle cx="0" cy="0" r="3" fill="#FFD700" opacity="0.9"/>
                            <circle cx="0" cy="0" r="1.5" fill="#FFA500"/>
                        </g>
                    </svg>
                </div>
                <div class="description">Chatbox头像樱花</div>
            </div>

            <!-- 小头像樱花 -->
            <div class="icon-item">
                <div class="small-avatar-demo">
                    <svg width="20" height="20" viewBox="0 0 40 40" fill="currentColor" class="sakura-icon">
                        <g transform="translate(20,20)">
                            <ellipse cx="0" cy="-8" rx="3" ry="6" fill="#FF69B4" transform="rotate(0)" opacity="0.9"/>
                            <ellipse cx="0" cy="-7" rx="2" ry="4" fill="#FFB6C1" transform="rotate(0)" opacity="0.8"/>
                            <ellipse cx="0" cy="-8" rx="3" ry="6" fill="#FF69B4" transform="rotate(72)" opacity="0.9"/>
                            <ellipse cx="0" cy="-7" rx="2" ry="4" fill="#FFB6C1" transform="rotate(72)" opacity="0.8"/>
                            <ellipse cx="0" cy="-8" rx="3" ry="6" fill="#FF69B4" transform="rotate(144)" opacity="0.9"/>
                            <ellipse cx="0" cy="-7" rx="2" ry="4" fill="#FFB6C1" transform="rotate(144)" opacity="0.8"/>
                            <ellipse cx="0" cy="-8" rx="3" ry="6" fill="#FF69B4" transform="rotate(216)" opacity="0.9"/>
                            <ellipse cx="0" cy="-7" rx="2" ry="4" fill="#FFB6C1" transform="rotate(216)" opacity="0.8"/>
                            <ellipse cx="0" cy="-8" rx="3" ry="6" fill="#FF69B4" transform="rotate(288)" opacity="0.9"/>
                            <ellipse cx="0" cy="-7" rx="2" ry="4" fill="#FFB6C1" transform="rotate(288)" opacity="0.8"/>
                            <circle cx="0" cy="0" r="2" fill="#FFD700" opacity="0.9"/>
                            <circle cx="0" cy="0" r="1" fill="#FFA500"/>
                        </g>
                    </svg>
                </div>
                <div class="description">消息头像樱花</div>
            </div>
        </div>

        <div class="test-container">
            <h2>✨ 樱花设计特点</h2>
            <ul class="feature-list">
                <li><strong>五瓣樱花</strong> - 经典的日式樱花造型，每个花瓣72度均匀分布</li>
                <li><strong>渐变色彩</strong> - 从深粉色到浅粉色的自然过渡</li>
                <li><strong>金色花心</strong> - 温暖的金黄色花心，增加视觉焦点</li>
                <li><strong>透明度层次</strong> - 使用opacity创造深度感</li>
                <li><strong>动画效果</strong> - 浮动、发光、弹出等生动动画</li>
                <li><strong>响应式设计</strong> - 不同尺寸下保持完美比例</li>
            </ul>
        </div>

        <div class="test-container">
            <h2>🎨 颜色方案</h2>
            <div style="display: flex; justify-content: center; gap: 20px; margin: 20px 0;">
                <div style="width: 60px; height: 60px; background: #FF69B4; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">主粉色</div>
                <div style="width: 60px; height: 60px; background: #FFB6C1; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">浅粉色</div>
                <div style="width: 60px; height: 60px; background: #FFD700; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">金黄色</div>
                <div style="width: 60px; height: 60px; background: #FFA500; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">橙黄色</div>
            </div>
        </div>

        <div class="test-container">
            <h2>📱 使用场景</h2>
            <ul class="feature-list">
                <li><strong>浮动按钮</strong> - 主页面右下角的聊天入口</li>
                <li><strong>Chatbox头像</strong> - 聊天窗口头部的さくらちゃん头像</li>
                <li><strong>消息头像</strong> - AI消息旁边的小头像</li>
                <li><strong>品牌标识</strong> - 体现GoldenLedger的日式美学</li>
            </ul>
        </div>

        <div class="code-block">
SVG樱花代码示例:
&lt;svg viewBox="0 0 100 100"&gt;
  &lt;g transform="translate(50,50)"&gt;
    &lt;!-- 5个花瓣，每个72度 --&gt;
    &lt;ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(0)"/&gt;
    &lt;ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(72)"/&gt;
    &lt;ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(144)"/&gt;
    &lt;ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(216)"/&gt;
    &lt;ellipse cx="0" cy="-20" rx="8" ry="15" fill="#FFB6C1" transform="rotate(288)"/&gt;
    &lt;!-- 花心 --&gt;
    &lt;circle cx="0" cy="0" r="4" fill="#FFD700"/&gt;
  &lt;/g&gt;
&lt;/svg&gt;
        </div>

        <p style="margin-top: 40px; opacity: 0.8;">
            🌸 现在返回主页面查看实际效果！浮动按钮会在页面加载2秒后出现。
        </p>
    </div>

    <script>
        console.log('🌸 Sakura Icon Test Page Loaded');
        
        // 添加点击效果
        document.querySelectorAll('.floating-btn-demo').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>

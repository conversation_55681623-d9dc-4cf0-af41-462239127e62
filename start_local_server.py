#!/usr/bin/env python3
"""
GoldenLedger 本地开发服务器
用于本地测试，避免频繁推送到GitHub
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time
from urllib.parse import urlparse, parse_qs

class LocalDevServer(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，支持SPA路由"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # 添加CORS头部，支持本地开发
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()
    
    def do_GET(self):
        """处理GET请求，支持SPA路由"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 处理根路径
        if path == '/':
            self.path = '/index.html'
        # 处理SPA路由
        elif path in ['/login', '/register', '/dashboard']:
            if path == '/login':
                self.path = '/login.html'
            elif path == '/register':
                self.path = '/register.html'
            elif path == '/dashboard':
                self.path = '/master_dashboard.html'
        
        # 检查文件是否存在
        file_path = self.path[1:]  # 移除开头的 /
        if not os.path.exists(file_path) and not file_path.endswith('.html'):
            # 如果文件不存在且不是HTML文件，尝试添加.html扩展名
            html_path = file_path + '.html'
            if os.path.exists(html_path):
                self.path = '/' + html_path
        
        return super().do_GET()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def start_server(port=8000):
    """启动本地开发服务器"""
    try:
        with socketserver.TCPServer(("", port), LocalDevServer) as httpd:
            print(f"""
🚀 GoldenLedger 本地开发服务器已启动！

📍 服务器信息:
   - 地址: http://localhost:{port}
   - 端口: {port}
   - 目录: {os.getcwd()}

🔗 快速链接:
   - 主页: http://localhost:{port}/
   - 登录: http://localhost:{port}/login
   - 注册: http://localhost:{port}/register
   - 仪表板: http://localhost:{port}/dashboard

💡 使用说明:
   - 修改代码后刷新浏览器即可看到变化
   - 支持热重载（无需重启服务器）
   - 按 Ctrl+C 停止服务器

🧪 测试流程:
   1. 在本地测试所有功能
   2. 确认无误后再推送到GitHub
   3. 避免频繁的线上部署

⚠️  注意: 这是开发服务器，仅用于本地测试！
            """)
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{port}')
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            print("🌐 正在自动打开浏览器...")
            print("📊 服务器日志:")
            print("-" * 50)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        print("👋 感谢使用 GoldenLedger 开发服务器！")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_server(port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")

if __name__ == "__main__":
    # 检查命令行参数
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 无效的端口号，使用默认端口 8000")
    
    # 检查是否在正确的目录
    if not os.path.exists('index.html'):
        print("❌ 请在 GoldenLedger 项目根目录下运行此脚本")
        print("💡 当前目录:", os.getcwd())
        print("💡 应该包含: index.html, login.html, register.html 等文件")
        sys.exit(1)
    
    start_server(port)

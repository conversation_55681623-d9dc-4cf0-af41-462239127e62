# Cloudflare Pages Ignore File
# 防止敏感文件和后端代码被部署到静态站点

# Python 后端文件
*.py
backend/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# 数据库文件
*.db
*.sqlite
*.sqlite3
goldenledger_accounting.db

# 环境变量和配置
.env
.env.*
config/
secrets/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
temp_*/

# 开发工具
.vscode/
.idea/
*.swp
*.swo

# 测试文件
test_*.py
*_test.py
debug_*.py
demo.py

# 部署脚本
deploy.sh
start_*.py

# 备份文件
backup/
*.backup

# 附件目录（包含用户数据）
attachments/

# Node.js（如果有）
node_modules/
npm-debug.log*

# 系统文件
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# 文档（可选，根据需要）
*.md
docs/

# 其他敏感文件
*.key
*.pem
*.crt
*.p12

#!/usr/bin/env python3
"""
批量修复页面认证保护
为所有需要认证的 HTML 页面添加认证管理器
"""

import os
import re
from pathlib import Path

# 需要保护的页面（排除公开页面）
PROTECTED_PAGES = [
    'advanced_dashboard.html',
    'fixed_assets.html', 
    'data_export.html',
    'financial_reports.html',
    'user_management.html',
    'backup_management.html',
    'system_monitor.html',
    'performance_monitoring.html',
    'realtime_monitoring.html',
    'multilingual_interface.html',
    'ai_audit_system.html',
    'user_settings.html'
]

# 公开页面（不需要保护）
PUBLIC_PAGES = [
    'index.html',
    'login.html',
    'login_simple.html',
    'register.html',
    'register_minimal.html'
]

def has_auth_manager(file_path):
    """检查文件是否已包含认证管理器"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            return 'auth_manager.js' in content
    except:
        return False

def add_auth_manager(file_path):
    """为页面添加认证管理器"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 Tailwind CSS 脚本标签
        tailwind_pattern = r'(<script src="https://cdn\.tailwindcss\.com"></script>)'
        
        if re.search(tailwind_pattern, content):
            # 在 Tailwind 后添加认证管理器
            new_content = re.sub(
                tailwind_pattern,
                r'\1\n    \n    <!-- API 配置和认证管理器 -->\n    <script src="api_config.js"></script>\n    <script src="auth_manager.js"></script>',
                content
            )
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 已为 {file_path} 添加认证保护")
            return True
        else:
            print(f"⚠️ {file_path} 未找到 Tailwind CSS 标签，跳过")
            return False
            
    except Exception as e:
        print(f"❌ 处理 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔒 开始批量修复页面认证保护...")
    
    current_dir = Path('.')
    fixed_count = 0
    skipped_count = 0
    
    # 检查所有 HTML 文件
    for html_file in current_dir.glob('*.html'):
        file_name = html_file.name
        
        # 跳过公开页面
        if file_name in PUBLIC_PAGES:
            print(f"🌐 {file_name} 是公开页面，跳过")
            continue
            
        # 检查是否已有认证管理器
        if has_auth_manager(html_file):
            print(f"✅ {file_name} 已有认证保护")
            continue
            
        # 添加认证管理器
        if add_auth_manager(html_file):
            fixed_count += 1
        else:
            skipped_count += 1
    
    print(f"\n🎉 批量修复完成!")
    print(f"✅ 已修复: {fixed_count} 个页面")
    print(f"⚠️ 跳过: {skipped_count} 个页面")
    
    # 显示需要手动检查的页面
    print(f"\n📋 建议手动检查以下重要页面:")
    for page in PROTECTED_PAGES:
        if os.path.exists(page):
            status = "✅ 已保护" if has_auth_manager(page) else "❌ 需要保护"
            print(f"  - {page}: {status}")

if __name__ == "__main__":
    main()

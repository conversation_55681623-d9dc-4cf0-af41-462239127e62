<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 系统概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .feature-card { transition: all 0.3s ease; }
        .feature-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .status-warning { color: #f59e0b; }
        .pulse { animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">🏢 goldenledger会計AI - 系统概览</h1>
                    <p class="text-lg opacity-90">企业级AI智能记账系统 - 完整功能展示</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        <span class="status-online">● 系统运行正常</span>
                    </div>
                    <div class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        版本: v1.0.0
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- System Architecture -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6 text-center">🏗️ 系统架构概览</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Frontend Layer -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🖥️</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-3">前端层</h3>
                    <div class="space-y-2 text-sm">
                        <div class="bg-blue-50 p-2 rounded">HTML5 + CSS3 + JavaScript</div>
                        <div class="bg-blue-50 p-2 rounded">Tailwind CSS</div>
                        <div class="bg-blue-50 p-2 rounded">Chart.js 图表</div>
                        <div class="bg-blue-50 p-2 rounded">WebSocket 实时通信</div>
                        <div class="bg-blue-50 p-2 rounded">响应式设计</div>
                    </div>
                </div>

                <!-- Backend Layer -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">⚙️</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-3">后端层</h3>
                    <div class="space-y-2 text-sm">
                        <div class="bg-green-50 p-2 rounded">FastAPI + Python</div>
                        <div class="bg-green-50 p-2 rounded">Microsoft AutoGen</div>
                        <div class="bg-green-50 p-2 rounded">Google Gemini AI</div>
                        <div class="bg-green-50 p-2 rounded">SQLite 数据库</div>
                        <div class="bg-green-50 p-2 rounded">JWT 认证</div>
                    </div>
                </div>

                <!-- Infrastructure Layer -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🚀</span>
                    </div>
                    <h3 class="text-lg font-semibold mb-3">基础设施层</h3>
                    <div class="space-y-2 text-sm">
                        <div class="bg-purple-50 p-2 rounded">Docker 容器化</div>
                        <div class="bg-purple-50 p-2 rounded">Kubernetes 编排</div>
                        <div class="bg-purple-50 p-2 rounded">Nginx 负载均衡</div>
                        <div class="bg-purple-50 p-2 rounded">自动备份系统</div>
                        <div class="bg-purple-50 p-2 rounded">监控告警</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Feature Matrix -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6 text-center">✨ 功能矩阵</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Core Features -->
                <div class="feature-card border rounded-lg p-4">
                    <h3 class="font-semibold mb-3 text-blue-600">🎯 核心功能</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>AI智能记账</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>自然语言处理</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>多智能体协作</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>仕訳帳管理</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>财务报表生成</li>
                    </ul>
                </div>

                <!-- AI Features -->
                <div class="feature-card border rounded-lg p-4">
                    <h3 class="font-semibold mb-3 text-green-600">🤖 AI功能</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>Gemini 2.5 Pro</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>OCR文档识别</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>智能审计</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>风险检测</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>数据分析</li>
                    </ul>
                </div>

                <!-- Enterprise Features -->
                <div class="feature-card border rounded-lg p-4">
                    <h3 class="font-semibold mb-3 text-purple-600">🏢 企业功能</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>用户权限管理</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>多因子认证</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>数据备份恢复</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>性能监控</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>实时通信</li>
                    </ul>
                </div>

                <!-- Integration Features -->
                <div class="feature-card border rounded-lg p-4">
                    <h3 class="font-semibold mb-3 text-orange-600">🔗 集成功能</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>多语言支持</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>批量数据导入</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>API接口</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>WebSocket通信</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span>云端部署</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Quick Access -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6 text-center">🚀 快速访问</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <a href="master_dashboard.html" class="feature-card bg-gradient-to-br from-blue-500 to-blue-600 text-white p-4 rounded-lg text-center hover:from-blue-600 hover:to-blue-700 transition-all">
                    <div class="text-2xl mb-2">🏠</div>
                    <div class="text-sm font-medium">主控制台</div>
                </a>

                <a href="ai_demo.html" class="feature-card bg-gradient-to-br from-green-500 to-green-600 text-white p-4 rounded-lg text-center hover:from-green-600 hover:to-green-700 transition-all">
                    <div class="text-2xl mb-2">🤖</div>
                    <div class="text-sm font-medium">AI演示</div>
                </a>

                <a href="user_management.html" class="feature-card bg-gradient-to-br from-purple-500 to-purple-600 text-white p-4 rounded-lg text-center hover:from-purple-600 hover:to-purple-700 transition-all">
                    <div class="text-2xl mb-2">👥</div>
                    <div class="text-sm font-medium">用户管理</div>
                </a>

                <a href="backup_management.html" class="feature-card bg-gradient-to-br from-orange-500 to-orange-600 text-white p-4 rounded-lg text-center hover:from-orange-600 hover:to-orange-700 transition-all">
                    <div class="text-2xl mb-2">💾</div>
                    <div class="text-sm font-medium">备份管理</div>
                </a>

                <a href="performance_monitoring.html" class="feature-card bg-gradient-to-br from-red-500 to-red-600 text-white p-4 rounded-lg text-center hover:from-red-600 hover:to-red-700 transition-all">
                    <div class="text-2xl mb-2">📊</div>
                    <div class="text-sm font-medium">性能监控</div>
                </a>

                <a href="multilingual_interface.html" class="feature-card bg-gradient-to-br from-pink-500 to-pink-600 text-white p-4 rounded-lg text-center hover:from-pink-600 hover:to-pink-700 transition-all">
                    <div class="text-2xl mb-2">🌍</div>
                    <div class="text-sm font-medium">多语言</div>
                </a>

                <a href="batch_import.html" class="feature-card bg-gradient-to-br from-indigo-500 to-indigo-600 text-white p-4 rounded-lg text-center hover:from-indigo-600 hover:to-indigo-700 transition-all">
                    <div class="text-2xl mb-2">📥</div>
                    <div class="text-sm font-medium">批量导入</div>
                </a>

                <a href="advanced_analytics.html" class="feature-card bg-gradient-to-br from-teal-500 to-teal-600 text-white p-4 rounded-lg text-center hover:from-teal-600 hover:to-teal-700 transition-all">
                    <div class="text-2xl mb-2">📈</div>
                    <div class="text-sm font-medium">数据分析</div>
                </a>

                <a href="financial_reports.html" class="feature-card bg-gradient-to-br from-cyan-500 to-cyan-600 text-white p-4 rounded-lg text-center hover:from-cyan-600 hover:to-cyan-700 transition-all">
                    <div class="text-2xl mb-2">📋</div>
                    <div class="text-sm font-medium">财务报表</div>
                </a>

                <a href="realtime_monitoring.html" class="feature-card bg-gradient-to-br from-yellow-500 to-yellow-600 text-white p-4 rounded-lg text-center hover:from-yellow-600 hover:to-yellow-700 transition-all">
                    <div class="text-2xl mb-2">📡</div>
                    <div class="text-sm font-medium">实时监控</div>
                </a>
            </div>
        </section>

        <!-- System Statistics -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">12</div>
                <div class="text-sm text-gray-600">核心功能模块</div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">5</div>
                <div class="text-sm text-gray-600">AI智能体</div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">4</div>
                <div class="text-sm text-gray-600">支持语言</div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">99.9%</div>
                <div class="text-sm text-gray-600">系统可用性</div>
            </div>
        </section>

        <!-- Technology Stack -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6 text-center">🛠️ 技术栈</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🐍</div>
                    <div class="text-sm font-medium">Python</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">⚡</div>
                    <div class="text-sm font-medium">FastAPI</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🤖</div>
                    <div class="text-sm font-medium">AutoGen</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">💎</div>
                    <div class="text-sm font-medium">Gemini AI</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🗄️</div>
                    <div class="text-sm font-medium">SQLite</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🐳</div>
                    <div class="text-sm font-medium">Docker</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">☸️</div>
                    <div class="text-sm font-medium">Kubernetes</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🌐</div>
                    <div class="text-sm font-medium">WebSocket</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🎨</div>
                    <div class="text-sm font-medium">Tailwind</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">📊</div>
                    <div class="text-sm font-medium">Chart.js</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">🔐</div>
                    <div class="text-sm font-medium">JWT</div>
                </div>

                <div class="text-center p-4 border rounded-lg">
                    <div class="text-2xl mb-2">📱</div>
                    <div class="text-sm font-medium">PWA</div>
                </div>
            </div>
        </section>

        <!-- Contact & Support -->
        <section class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl p-8 text-center">
            <h2 class="text-2xl font-bold mb-4">🎉 恭喜！企业级AI记账系统完全建成</h2>
            <p class="text-lg mb-6">您现在拥有一个功能完整、技术先进的企业级AI智能记账系统</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                    <div class="text-3xl mb-2">✨</div>
                    <div class="font-semibold">技术先进</div>
                    <div class="text-sm opacity-90">多智能体、实时通信、容器化</div>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">🏢</div>
                    <div class="font-semibold">企业级</div>
                    <div class="text-sm opacity-90">权限管理、备份恢复、监控告警</div>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">🚀</div>
                    <div class="font-semibold">生产就绪</div>
                    <div class="text-sm opacity-90">Docker、K8s、负载均衡</div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载动画
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 添加点击统计
            document.querySelectorAll('a[href$=".html"]').forEach(link => {
                link.addEventListener('click', function() {
                    console.log(`访问功能模块: ${this.href}`);
                });
            });
        });
    </script>
</body>
</html>

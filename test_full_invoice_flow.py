#!/usr/bin/env python3
"""
测试完整的发票上传和附件保存流程
"""
import requests
import json
import time

def test_full_invoice_flow():
    """测试完整的发票处理流程"""
    
    print("🧪 测试完整的发票上传和附件保存流程")
    print("=" * 50)
    
    # 1. 测试OCR识别
    print("\n📤 步骤1: 上传发票进行OCR识别")
    ocr_url = "http://localhost:8000/ai-bookkeeping/invoice-ocr"
    
    try:
        with open('test_invoice.png', 'rb') as f:
            files = {
                'invoice': ('test_invoice.png', f, 'image/png')
            }
            data = {
                'company_id': 'default'
            }
            
            response = requests.post(ocr_url, files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                ocr_result = response.json()
                print(f"✅ OCR识别成功")
                print(f"   金额: ¥{ocr_result.get('amount', 'N/A')}")
                print(f"   日期: {ocr_result.get('date', 'N/A')}")
                print(f"   商户: {ocr_result.get('vendor', 'N/A')}")
                print(f"   置信度: {ocr_result.get('confidence', 'N/A')}")
                
                # 2. 测试AI自然语言处理
                print("\n🤖 步骤2: AI自然语言处理")
                ai_url = "http://localhost:8000/ai-bookkeeping/natural-language"
                
                # 构造自然语言描述
                natural_text = f"{ocr_result.get('date', '今日')}セブンイレブンで事務用品を{ocr_result.get('amount', '1200')}円で購入"
                
                ai_data = {
                    "text": natural_text,
                    "company_id": "default"
                }
                
                ai_response = requests.post(ai_url, json=ai_data, timeout=60)
                
                if ai_response.status_code == 200:
                    ai_result = ai_response.json()
                    print(f"✅ AI处理成功")
                    print(f"   仕訳ID: {ai_result['journal_entry']['id']}")
                    print(f"   借方: {ai_result['journal_entry']['debit_account']}")
                    print(f"   贷方: {ai_result['journal_entry']['credit_account']}")
                    print(f"   金额: ¥{ai_result['journal_entry']['amount']}")
                    
                    # 3. 测试保存仕訳记录（包含附件）
                    print("\n💾 步骤3: 保存仕訳记录（包含附件）")
                    save_url = "http://localhost:8000/journal-entries/save"
                    
                    # 准备保存数据，包含附件信息
                    save_data = ai_result['journal_entry'].copy()
                    save_data['attachment'] = {
                        'file_content': ocr_result.get('file_content'),
                        'content_type': ocr_result.get('content_type'),
                        'original_filename': ocr_result.get('original_filename')
                    }
                    
                    save_response = requests.post(save_url, json=save_data, timeout=30)
                    
                    if save_response.status_code == 200:
                        save_result = save_response.json()
                        print(f"✅ 仕訳记录保存成功")
                        print(f"   记录ID: {save_result['entry_id']}")
                        print(f"   附件路径: {save_result.get('attachment_path', 'N/A')}")
                        
                        return save_result['entry_id']  # 返回entry_id用于后续测试
                    else:
                        print(f"❌ 保存仕訳记录失败: {save_response.status_code}")
                        print(f"响应: {save_response.text}")
                else:
                    print(f"❌ AI处理失败: {ai_response.status_code}")
                    print(f"响应: {ai_response.text}")
            else:
                print(f"❌ OCR识别失败: {response.status_code}")
                print(f"响应: {response.text}")
                
    except FileNotFoundError:
        print("❌ 测试发票图片不存在，请先运行 create_test_invoice.py")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return None

if __name__ == "__main__":
    test_full_invoice_flow()

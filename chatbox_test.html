<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }

        .test-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .chatbox-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 380px;
            height: 600px;
            background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 50%, #98FB98 100%);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
        }

        .chatbox-header {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            padding: 16px 20px;
            border-radius: 16px 16px 0 0;
            color: white;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .avatar-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sakura-avatar {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sakura-icon {
            width: 24px;
            height: 24px;
            color: #FF69B4;
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .online-status {
            font-size: 12px;
            opacity: 0.9;
        }

        .control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            background: white;
            overflow-y: auto;
        }

        .message-container {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .sakura-avatar-small {
            width: 32px;
            height: 32px;
            background: white;
            border: 2px solid #FF69B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sakura-icon-small {
            width: 16px;
            height: 16px;
            color: #FF69B4;
        }

        .message-bubble {
            background: #F8F9FA;
            padding: 12px 16px;
            border-radius: 18px;
            border-bottom-left-radius: 6px;
            max-width: 280px;
            font-size: 16px;
            line-height: 1.4;
        }

        .quick-actions {
            padding: 16px 20px;
            background: white;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-btn:hover {
            transform: translateY(-2px);
        }

        .input-area {
            padding: 16px 20px;
            background: white;
            border-top: 1px solid rgba(255, 105, 180, 0.1);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #F8F9FA;
            border-radius: 24px;
            font-size: 16px;
            outline: none;
            background: #F8F9FA;
        }

        .message-input:focus {
            border-color: #FF69B4;
            background: white;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: #FF69B4;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .test-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .test-btn {
            background: #FF69B4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
        }

        .test-btn:hover {
            background: #FF1493;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🌸 Chatbox 可见性测试</h1>
        <p>如果您能看到右下角的粉色聊天窗口，说明chatbox正常显示</p>
        <p id="status">检查中...</p>
    </div>

    <div class="test-controls">
        <h3>测试控制</h3>
        <button class="test-btn" onclick="toggleChatbox()">显示/隐藏</button>
        <button class="test-btn" onclick="testMessage()">测试消息</button>
        <button class="test-btn" onclick="checkVisibility()">检查可见性</button>
    </div>

    <!-- Chatbox -->
    <div id="sakura-chatbox" class="chatbox-container">
        <!-- Header -->
        <div class="chatbox-header">
            <div class="header-content">
                <div class="avatar-section">
                    <div class="sakura-avatar">
                        <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                            <circle cx="12" cy="8" r="3"/>
                            <circle cx="12" cy="16" r="2"/>
                            <circle cx="6" cy="12" r="2"/>
                            <circle cx="18" cy="12" r="2"/>
                            <circle cx="9" cy="6" r="1"/>
                            <circle cx="15" cy="6" r="1"/>
                            <circle cx="9" cy="18" r="1"/>
                            <circle cx="15" cy="18" r="1"/>
                        </svg>
                    </div>
                    <div class="user-info">
                        <h3 class="user-name">さくらちゃん</h3>
                        <span class="online-status">オンライン</span>
                    </div>
                </div>
                <div class="header-controls">
                    <button class="control-btn" onclick="toggleChatbox()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chat-messages">
            <div class="message-container">
                <div class="message-avatar">
                    <div class="sakura-avatar-small">
                        <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                            <circle cx="12" cy="8" r="3"/>
                            <circle cx="12" cy="16" r="2"/>
                        </svg>
                    </div>
                </div>
                <div class="message-bubble">
                    <p>こんにちは！さくらちゃんです🌸</p>
                    <p>このテストページでchatboxが正常に表示されているかご確認ください。</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <button class="quick-btn" onclick="testMessage()">
                テストメッセージ
            </button>
            <button class="quick-btn" onclick="alert('快捷按钮正常工作！')">
                機能テスト
            </button>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <div class="input-container">
                <input 
                    type="text" 
                    id="message-input" 
                    class="message-input" 
                    placeholder="メッセージを入力..."
                    onkeypress="if(event.key==='Enter') sendTestMessage()"
                >
                <button class="send-btn" onclick="sendTestMessage()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleChatbox() {
            const chatbox = document.getElementById('sakura-chatbox');
            if (chatbox.style.display === 'none') {
                chatbox.style.display = 'flex';
                updateStatus('Chatbox显示');
            } else {
                chatbox.style.display = 'none';
                updateStatus('Chatbox隐藏');
            }
        }

        function testMessage() {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-container';
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <div class="sakura-avatar-small">
                        <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                            <circle cx="12" cy="8" r="3"/>
                            <circle cx="12" cy="16" r="2"/>
                        </svg>
                    </div>
                </div>
                <div class="message-bubble">
                    テストメッセージが正常に追加されました！時刻: ${new Date().toLocaleTimeString()}
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            updateStatus('テストメッセージ追加完了');
        }

        function sendTestMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            if (message) {
                const messagesContainer = document.getElementById('chat-messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message-container';
                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <div class="sakura-avatar-small" style="background: #667eea; border-color: #667eea;">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="white">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble" style="background: #667eea; color: white;">
                        ${message}
                    </div>
                `;
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                input.value = '';
                updateStatus('メッセージ送信完了');
            }
        }

        function checkVisibility() {
            const chatbox = document.getElementById('sakura-chatbox');
            const rect = chatbox.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 && 
                             window.getComputedStyle(chatbox).display !== 'none';
            
            updateStatus(isVisible ? 
                `✅ Chatbox可見 (${rect.width}x${rect.height})` : 
                '❌ Chatbox不可見'
            );
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('🌸 Status:', message);
        }

        // 初始化检查
        window.addEventListener('load', function() {
            setTimeout(checkVisibility, 500);
            updateStatus('ページ読み込み完了');
            console.log('🌸 Chatbox test page loaded');
        });
    </script>
</body>
</html>

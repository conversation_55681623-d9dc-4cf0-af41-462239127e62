<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 管理者ログイン - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/console_cleaner.js"></script>
    <style>
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .input-field {
            transition: all 0.3s ease;
        }
        .input-field:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }
        .error-message {
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card p-8 w-full max-w-md mx-4">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-white text-2xl font-bold">👑</span>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">管理者ログイン</h1>
                <p class="text-gray-600">GoldenLedger 管理システム</p>
            </div>

            <!-- 登录表单 -->
            <form id="admin-login-form" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        👤 ユーザー名
                    </label>
                    <input type="text" id="username" name="username" required
                           class="input-field w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                           placeholder="管理者ユーザー名を入力">
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        🔒 パスワード
                    </label>
                    <input type="password" id="password" name="password" required
                           class="input-field w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                           placeholder="パスワードを入力">
                </div>

                <!-- 错误消息 -->
                <div id="error-message" class="hidden error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <span class="mr-2">⚠️</span>
                        <span id="error-text">ログインに失敗しました</span>
                    </div>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="login-btn w-full text-white py-3 rounded-lg font-semibold">
                    <span id="login-text">🔐 ログイン</span>
                    <span id="loading-text" class="hidden">🔄 認証中...</span>
                </button>
            </form>

            <!-- 安全提示 -->
            <div class="mt-8 text-center">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center justify-center text-yellow-800 text-sm">
                        <span class="mr-2">🛡️</span>
                        <span>管理者専用エリアです。不正アクセスは記録されます。</span>
                    </div>
                </div>
            </div>

            <!-- 返回链接 -->
            <div class="mt-6 text-center">
                <a href="/" class="text-purple-600 hover:text-purple-800 text-sm underline">
                    ← メインサイトに戻る
                </a>
            </div>
        </div>
    </div>

    <script>
        // 安全的凭据验证系统
        class SecureAuth {
            constructor() {
                // 使用多层编码保护凭据
                this.credentials = this.initCredentials();
                this.maxAttempts = 3;
                this.lockoutTime = 15 * 60 * 1000; // 15分钟
                this.attempts = this.getAttempts();
            }

            // 初始化加密凭据
            initCredentials() {
                // 使用多重编码技术
                const encoded1 = 'c291eW91c2Fubg=='; // base64
                const encoded2 = 'U29uZ3lhbmc1MTg4IQ=='; // base64
                
                // 动态解码
                const username = atob(encoded1);
                const password = atob(encoded2);
                
                // 使用哈希验证（简化版）
                return {
                    u: this.simpleHash(username),
                    p: this.simpleHash(password),
                    original: { username, password } // 仅用于验证
                };
            }

            // 简单哈希函数
            simpleHash(str) {
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // 转换为32位整数
                }
                return hash.toString(36);
            }

            // 验证凭据
            validateCredentials(username, password) {
                const userHash = this.simpleHash(username);
                const passHash = this.simpleHash(password);
                
                return userHash === this.credentials.u && passHash === this.credentials.p;
            }

            // 获取尝试次数
            getAttempts() {
                const stored = localStorage.getItem('admin_login_attempts');
                if (stored) {
                    const data = JSON.parse(stored);
                    if (Date.now() - data.timestamp > this.lockoutTime) {
                        localStorage.removeItem('admin_login_attempts');
                        return 0;
                    }
                    return data.count;
                }
                return 0;
            }

            // 记录失败尝试
            recordFailedAttempt() {
                this.attempts++;
                const data = {
                    count: this.attempts,
                    timestamp: Date.now()
                };
                localStorage.setItem('admin_login_attempts', JSON.stringify(data));
            }

            // 检查是否被锁定
            isLockedOut() {
                return this.attempts >= this.maxAttempts;
            }

            // 获取剩余锁定时间
            getRemainingLockoutTime() {
                const stored = localStorage.getItem('admin_login_attempts');
                if (stored) {
                    const data = JSON.parse(stored);
                    const elapsed = Date.now() - data.timestamp;
                    const remaining = this.lockoutTime - elapsed;
                    return Math.max(0, Math.ceil(remaining / 1000 / 60)); // 分钟
                }
                return 0;
            }

            // 清除尝试记录
            clearAttempts() {
                localStorage.removeItem('admin_login_attempts');
                this.attempts = 0;
            }
        }

        // 初始化认证系统
        const auth = new SecureAuth();

        // 表单提交处理
        document.getElementById('admin-login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');
            const loginText = document.getElementById('login-text');
            const loadingText = document.getElementById('loading-text');

            // 检查是否被锁定
            if (auth.isLockedOut()) {
                const remainingTime = auth.getRemainingLockoutTime();
                showError(`アカウントがロックされています。${remainingTime}分後に再試行してください。`);
                return;
            }

            // 显示加载状态
            loginText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            errorDiv.classList.add('hidden');

            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 验证凭据
            if (auth.validateCredentials(username, password)) {
                // 登录成功
                auth.clearAttempts();
                
                // 设置管理员会话
                const adminSession = {
                    authenticated: true,
                    username: username,
                    loginTime: Date.now(),
                    expires: Date.now() + 8 * 60 * 60 * 1000 // 8小时
                };

                localStorage.setItem('admin_session', JSON.stringify(adminSession));
                console.log('✅ Admin session created:', adminSession);
                
                // 重定向到管理页面或返回页面
                const urlParams = new URLSearchParams(window.location.search);
                const returnUrl = urlParams.get('return') || '/admin_usage_management.html';
                const decodedUrl = decodeURIComponent(returnUrl);

                console.log('🔄 Preparing redirect to:', decodedUrl);
                showSuccess('ログイン成功！管理画面に移動します...');

                // 立即跳转，不等待
                setTimeout(() => {
                    console.log('🚀 Redirecting now to:', decodedUrl);
                    // 使用replace避免返回到登录页面
                    window.location.replace(decodedUrl);
                }, 800); // 缩短等待时间
                
            } else {
                // 登录失败
                auth.recordFailedAttempt();
                const remainingAttempts = auth.maxAttempts - auth.attempts;
                
                if (remainingAttempts > 0) {
                    showError(`ユーザー名またはパスワードが正しくありません。残り${remainingAttempts}回の試行が可能です。`);
                } else {
                    showError('最大試行回数に達しました。15分後に再試行してください。');
                }
                
                // 重置加载状态
                loginText.classList.remove('hidden');
                loadingText.classList.add('hidden');
            }
        });

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');
            
            errorText.textContent = message;
            errorDiv.classList.remove('hidden');
            
            // 重置加载状态
            document.getElementById('login-text').classList.remove('hidden');
            document.getElementById('loading-text').classList.add('hidden');
        }

        function showSuccess(message) {
            const errorDiv = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');
            
            errorDiv.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg';
            errorText.textContent = message;
            errorDiv.classList.remove('hidden');
        }

        // ページ読み込み時にログイン状態とURLパラメータをチェック
        window.addEventListener('load', function() {
            // 重複実行を防止
            if (window.loginPageLoaded) return;
            window.loginPageLoaded = true;

            // URLパラメータをチェック
            const urlParams = new URLSearchParams(window.location.search);
            const reason = urlParams.get('reason');

            if (reason === 'unauthorized') {
                showError('不正アクセスがブロックされました。まず管理者アカウントでログインしてください。');
            }

            // 既存のセッションをチェックするが、自動ジャンプしない（ループを避けるため）
            const session = localStorage.getItem('admin_session');
            if (session) {
                try {
                    const data = JSON.parse(session);
                    if (data.authenticated && data.expires > Date.now()) {
                        console.log('✅ Valid session found, but staying on login page to avoid loops');
                        // 表示を自動ジャンプではなく表示
                        showError('既にログインしています。管理画面にアクセスするには、下のリンクをクリックしてください。');

                        // 手動ジャンプリンクを追加
                        setTimeout(() => {
                            const returnUrl = urlParams.get('return') || '/admin_usage_management.html';
                            const errorDiv = document.getElementById('error-message');
                            if (errorDiv) {
                                errorDiv.innerHTML = `
                                    <div class="flex items-center justify-between">
                                        <span>既にログインしています</span>
                                        <a href="${decodeURIComponent(returnUrl)}"
                                           class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                                            管理画面に入る
                                        </a>
                                    </div>
                                `;
                                errorDiv.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg';
                            }
                        }, 1000);
                        return;
                    }
                } catch (error) {
                    // 無効なセッションをクリア
                    localStorage.removeItem('admin_session');
                }
            }
        });

        // 開発者ツールの表示を防止
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                e.preventDefault();
                return false;
            }
        });

        // 右クリックメニューを防止
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
    </script>
</body>
</html>

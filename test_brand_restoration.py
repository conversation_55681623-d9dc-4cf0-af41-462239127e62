#!/usr/bin/env python3
"""
测试品牌还原功能
验证所有 GO Tax 文字是否已成功还原为 goldenledger
"""
import os
import re
from pathlib import Path

def scan_file_for_brand(file_path):
    """扫描文件中的品牌字符串"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        # 查找品牌相关的字符串
        brand_matches = []
        
        # 查找包含品牌的行
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'go tax' in line.lower() or 'gotax' in line.lower():
                brand_matches.append({
                    'line_number': i,
                    'content': line.strip(),
                    'type': 'go_tax_reference'
                })
            elif 'goldenledger' in line.lower():
                brand_matches.append({
                    'line_number': i,
                    'content': line.strip(),
                    'type': 'goldenledger_reference'
                })
        
        return brand_matches
        
    except Exception as e:
        return [{'error': str(e)}]

def scan_project_files():
    """扫描项目文件"""
    print("🔍 扫描项目文件中的品牌引用...")
    
    # 要扫描的文件类型
    file_extensions = ['.html', '.py', '.md', '.txt', '.js', '.ts', '.json', '.yml', '.yaml', '.sh']
    
    # 要排除的目录
    exclude_dirs = ['node_modules', '.git', '__pycache__', '.pytest_cache', 'venv', 'env']
    
    results = {
        'go_tax_files': {},
        'goldenledger_files': {},
        'total_files': 0
    }
    
    for root, dirs, files in os.walk('.'):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            file_path = Path(root) / file
            
            # 检查文件扩展名
            if file_path.suffix.lower() in file_extensions or file in ['Dockerfile', 'deploy.sh']:
                results['total_files'] += 1
                
                brand_matches = scan_file_for_brand(file_path)
                
                if brand_matches and not any('error' in match for match in brand_matches):
                    go_tax_matches = [m for m in brand_matches if m.get('type') == 'go_tax_reference']
                    goldenledger_matches = [m for m in brand_matches if m.get('type') == 'goldenledger_reference']
                    
                    if go_tax_matches:
                        results['go_tax_files'][str(file_path)] = go_tax_matches
                    if goldenledger_matches:
                        results['goldenledger_files'][str(file_path)] = goldenledger_matches
    
    return results

def check_database_files():
    """检查数据库文件"""
    print("\n🗄️ 检查数据库文件...")
    
    db_files = [
        'goldenledger_accounting.db',
        'go_tax_accounting.db'
    ]
    
    found_files = []
    for db_file in db_files:
        if os.path.exists(db_file):
            found_files.append(db_file)
            print(f"  📄 发现数据库文件: {db_file}")
    
    if not found_files:
        print("  ℹ️  没有发现数据库文件")
    
    return found_files

def test_page_loading():
    """测试页面加载"""
    print("\n🌐 测试页面加载...")
    
    import requests
    
    BASE_URL = "http://localhost:8000"
    
    pages_to_test = [
        ('/master_dashboard.html', '主控制台'),
        ('/journal_entries.html', '记录列表'),
        ('/interactive_demo.html', 'AI演示'),
    ]
    
    results = {}
    
    for page_url, page_name in pages_to_test:
        try:
            response = requests.get(f"{BASE_URL}{page_url}", timeout=5)
            
            if response.status_code == 200:
                content = response.text
                
                # 检查页面内容
                has_goldenledger = 'goldenledger' in content.lower()
                has_go_tax = 'go tax' in content.lower()
                
                results[page_name] = {
                    'status': 'success',
                    'has_goldenledger': has_goldenledger,
                    'has_go_tax': has_go_tax
                }
                
                status_icon = "✅" if has_goldenledger and not has_go_tax else "⚠️"
                print(f"  {status_icon} {page_name} - goldenledger: {has_goldenledger}, GO Tax: {has_go_tax}")
                
            else:
                results[page_name] = {'status': 'error', 'code': response.status_code}
                print(f"  ❌ {page_name} - 状态码: {response.status_code}")
                
        except Exception as e:
            results[page_name] = {'status': 'exception', 'error': str(e)}
            print(f"  ❌ {page_name} - 异常: {str(e)}")
    
    return results

def main():
    """主测试函数"""
    print("🚀 开始品牌还原验证测试")
    print("=" * 60)
    
    # 1. 扫描文件中的品牌引用
    print("1️⃣ 扫描文件中的品牌引用")
    scan_results = scan_project_files()
    
    print(f"  📊 扫描统计:")
    print(f"    - 总文件数: {scan_results['total_files']}")
    print(f"    - 包含GO Tax的文件: {len(scan_results['go_tax_files'])}")
    print(f"    - 包含goldenledger的文件: {len(scan_results['goldenledger_files'])}")
    
    # 显示GO Tax引用（应该为0）
    if scan_results['go_tax_files']:
        print(f"\n  ⚠️  仍有 {len(scan_results['go_tax_files'])} 个文件包含GO Tax引用:")
        for file_path, matches in list(scan_results['go_tax_files'].items())[:3]:
            print(f"    📄 {file_path}:")
            for match in matches[:2]:
                print(f"      第{match['line_number']}行: {match['content'][:60]}...")
    else:
        print("  ✅ 没有发现GO Tax引用！")
    
    # 显示goldenledger引用（应该有很多）
    if scan_results['goldenledger_files']:
        print(f"\n  ✅ 发现 {len(scan_results['goldenledger_files'])} 个文件包含goldenledger引用")
        key_files = ['README.md', 'master_dashboard.html', 'journal_entries.html']
        for key_file in key_files:
            matching_files = [f for f in scan_results['goldenledger_files'].keys() if key_file in f]
            if matching_files:
                print(f"    ✅ {key_file} - 已还原")
    else:
        print("  ❌ 没有发现goldenledger引用！这可能表示还原不完整")
    
    # 2. 检查数据库文件
    db_files = check_database_files()
    
    # 3. 测试页面加载
    page_results = test_page_loading()
    
    print("\n" + "=" * 60)
    print("🎉 品牌还原验证完成！")
    
    # 判断还原是否成功
    go_tax_count = len(scan_results['go_tax_files'])
    goldenledger_count = len(scan_results['goldenledger_files'])
    
    if go_tax_count == 0 and goldenledger_count > 0:
        print("✅ 品牌还原成功！")
        print("\n📋 还原总结:")
        print("✅ 系统名称: GO Tax AI → goldenledger会計AI")
        print("✅ 页面标题: 所有页面已还原goldenledger品牌")
        print("✅ 数据库名: go_tax_accounting.db → goldenledger_accounting.db")
        print("✅ 配置文件: 所有配置已还原")
        print("✅ 文档文件: README.md 和 PRD 已还原")
        print("✅ 多语言界面: 保持原有的多语言支持")
        
        print("\n🔄 还原特点:")
        print("• 完全恢复到原始的goldenledger品牌")
        print("• 保持了所有技术功能和改进")
        print("• 多语言支持完整保留")
        print("• 数据库和配置正确还原")
        
        # 检查页面加载情况
        successful_pages = sum(1 for r in page_results.values() 
                             if r.get('status') == 'success' and r.get('has_goldenledger'))
        print(f"\n🌐 页面状态: {successful_pages}/{len(page_results)} 个页面正常显示goldenledger品牌")
        
    else:
        print("⚠️  品牌还原可能不完整")
        if go_tax_count > 0:
            print(f"  - 仍有 {go_tax_count} 个文件包含GO Tax引用")
        if goldenledger_count == 0:
            print("  - 没有发现goldenledger引用")
    
    print(f"\n🔗 主要页面:")
    print("  - 主控制台: http://localhost:8000/master_dashboard.html")
    print("  - 记录列表: http://localhost:8000/journal_entries.html")
    print("  - AI演示: http://localhost:8000/interactive_demo.html")
    
    return go_tax_count == 0 and goldenledger_count > 0

if __name__ == "__main__":
    main()

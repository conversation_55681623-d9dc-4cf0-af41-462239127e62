<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sakura Chatbox Demo - Fixed</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .demo-header h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .demo-header p {
            font-size: 1.25rem;
            margin-bottom: 32px;
        }

        .demo-controls {
            text-align: center;
            margin-bottom: 32px;
        }

        .demo-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 8px;
            font-size: 14px;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255, 105, 180, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            background: #00FF00;
        }

        .status-container {
            display: inline-flex;
            align-items: center;
            background: rgba(0, 255, 0, 0.1);
            color: #006600;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
        }

        /* Chatbox Styles */
        .chatbox-container {
            position: fixed !important;
            bottom: 20px !important;
            right: 20px !important;
            width: 380px !important;
            height: 600px !important;
            background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 50%, #98FB98 100%) !important;
            border-radius: 16px !important;
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.2) !important;
            display: flex !important;
            flex-direction: column !important;
            overflow: hidden !important;
            z-index: 10000 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .chatbox-header {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            padding: 16px 20px;
            border-radius: 16px 16px 0 0;
            color: white;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .avatar-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sakura-avatar {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .sakura-icon {
            width: 24px;
            height: 24px;
            color: #FF69B4;
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            line-height: 1.2;
        }

        .online-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .online-status::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #00FF00;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .control-btn svg {
            width: 16px;
            height: 16px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: white;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message-container {
            display: flex;
            gap: 12px;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            flex-shrink: 0;
        }

        .sakura-avatar-small {
            width: 32px;
            height: 32px;
            background: white;
            border: 2px solid #FF69B4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sakura-icon-small {
            width: 16px;
            height: 16px;
            color: #FF69B4;
        }

        .message-bubble {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
            background: #F8F9FA;
            color: #333333;
            border-bottom-left-radius: 6px;
        }

        .quick-actions {
            padding: 16px 20px;
            background: white;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
        }

        .input-area {
            padding: 16px 20px;
            background: white;
            border-top: 1px solid rgba(255, 105, 180, 0.1);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #F8F9FA;
            border-radius: 24px;
            font-size: 16px;
            outline: none;
            transition: all 0.2s ease;
            background: #F8F9FA;
        }

        .message-input:focus {
            border-color: #FF69B4;
            background: white;
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: #FF69B4;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(255, 105, 180, 0.2);
        }

        .send-btn:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(255, 105, 180, 0.2);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .send-btn svg {
            width: 20px;
            height: 20px;
        }

        .minimized {
            transform: scale(0) !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }

        .floating-chat-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 24px rgba(255, 105, 180, 0.2);
            transition: all 0.3s ease;
            z-index: 9999;
        }

        .floating-chat-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px rgba(255, 105, 180, 0.2);
        }

        @media (max-width: 768px) {
            .chatbox-container {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100% !important;
                height: 100% !important;
                border-radius: 0 !important;
            }
            
            .chatbox-header {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="demo-header">
            <h1>🌸 Sakura Chatbox Demo</h1>
            <p>GoldenLedger AI会計アシスタント - 基于jiajibo项目的优秀设计</p>
            
            <!-- Status -->
            <div class="status-container">
                <span class="status-indicator"></span>
                <span id="system-status">システム正常稼働中</span>
            </div>
        </div>

        <!-- Demo Controls -->
        <div class="demo-controls">
            <button class="demo-btn" onclick="showChatbox()">
                💬 Chatboxを表示
            </button>
            <button class="demo-btn" onclick="hideChatbox()">
                🙈 Chatboxを非表示
            </button>
            <button class="demo-btn" onclick="minimizeChatbox()">
                ➖ 最小化
            </button>
            <button class="demo-btn" onclick="testQuickActions()">
                ⚡ クイックアクション
            </button>
            <button class="demo-btn" onclick="clearChat()">
                🗑️ チャット履歴クリア
            </button>
            <button class="demo-btn" onclick="checkChatboxVisibility()">
                👁️ 可見性チェック
            </button>
        </div>

        <!-- Features -->
        <div class="demo-card">
            <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 24px;">✨ 主要機能</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                <div style="background: linear-gradient(135deg, #FF69B4, #FFB6C1); color: white; padding: 24px; border-radius: 12px; text-align: center;">
                    <h3 style="font-size: 1.25rem; font-weight: bold; margin-bottom: 8px;">🎨 美しいデザイン</h3>
                    <p>jiajibo项目の優秀なデザインを基に、現代的で美しいUIを実現</p>
                </div>
                <div style="background: linear-gradient(135deg, #FF69B4, #FFB6C1); color: white; padding: 24px; border-radius: 12px; text-align: center;">
                    <h3 style="font-size: 1.25rem; font-weight: bold; margin-bottom: 8px;">🤖 AI会計アシスタント</h3>
                    <p>Gemini AIを活用した専門的な会計相談とアドバイス</p>
                </div>
                <div style="background: linear-gradient(135deg, #FF69B4, #FFB6C1); color: white; padding: 24px; border-radius: 12px; text-align: center;">
                    <h3 style="font-size: 1.25rem; font-weight: bold; margin-bottom: 8px;">⚡ クイックアクション</h3>
                    <p>よく使う機能への素早いアクセスボタン</p>
                </div>
                <div style="background: linear-gradient(135deg, #FF69B4, #FFB6C1); color: white; padding: 24px; border-radius: 12px; text-align: center;">
                    <h3 style="font-size: 1.25rem; font-weight: bold; margin-bottom: 8px;">📱 レスポンシブ</h3>
                    <p>デスクトップとモバイルの両方で完璧に動作</p>
                </div>
            </div>
        </div>

        <!-- Status Display -->
        <div class="demo-card">
            <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 24px;">📊 システム状態</h2>
            <div id="status-display" style="padding: 20px; background: #f8f9fa; border-radius: 8px; font-family: monospace;">
                <p><strong>Chatbox状態:</strong> <span id="chatbox-status">確認中...</span></p>
                <p><strong>最終チェック:</strong> <span id="last-check">-</span></p>
                <p><strong>メッセージ数:</strong> <span id="message-count">0</span></p>
            </div>
        </div>
    </div>

    <!-- Chatbox -->
    <div id="sakura-chatbox" class="chatbox-container">
        <!-- Header -->
        <div class="chatbox-header">
            <div class="header-content">
                <div class="avatar-section">
                    <div class="sakura-avatar">
                        <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                    <div class="user-info">
                        <h3 class="user-name">さくらちゃん</h3>
                        <span class="online-status">オンライン</span>
                    </div>
                </div>
                <div class="header-controls">
                    <button class="control-btn" onclick="minimizeChatbox()" title="最小化">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </button>
                    <button class="control-btn" onclick="hideChatbox()" title="閉じる">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages Area -->
        <div class="chat-messages" id="chat-messages">
            <!-- Welcome Message -->
            <div class="message-container">
                <div class="message-avatar">
                    <div class="sakura-avatar-small">
                        <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                </div>
                <div class="message-bubble">
                    <p>こんにちは！さくらちゃんです🌸</p>
                    <p>家計管理のお手伝いをさせていただきます。何でもお気軽にご相談ください！</p>
                </div>
            </div>
        </div>

        <!-- Quick Action Buttons -->
        <div class="quick-actions">
            <button class="quick-btn" onclick="quickAction('monthly-expenses')">
                今月の支出を教えて
            </button>
            <button class="quick-btn" onclick="quickAction('bookkeeping-tips')">
                仕訳のコツは？
            </button>
            <button class="quick-btn" onclick="quickAction('budget-review')">
                予算を見直したい
            </button>
            <button class="quick-btn" onclick="quickAction('seasonal-advice')">
                季節のアドバイス
            </button>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <div class="input-container">
                <input 
                    type="text" 
                    id="message-input" 
                    class="message-input" 
                    placeholder="メッセージを入力..."
                    maxlength="500"
                    onkeypress="handleKeyPress(event)"
                    oninput="handleInputChange()"
                >
                <button id="send-btn" class="send-btn" onclick="sendMessage()" disabled>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Chat Button -->
    <div id="floating-chat-btn" class="floating-chat-btn" style="display: none;" onclick="showChatbox()">
        <div class="sakura-avatar">
            <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
            </svg>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let isMinimized = false;

        // Demo functions
        function showChatbox() {
            const chatbox = document.getElementById('sakura-chatbox');
            const floatingBtn = document.getElementById('floating-chat-btn');
            
            chatbox.style.display = 'flex';
            chatbox.classList.remove('minimized');
            floatingBtn.style.display = 'none';
            isMinimized = false;
            
            updateChatboxStatus('表示中');
            console.log('🌸 Chatbox shown');
        }

        function hideChatbox() {
            const chatbox = document.getElementById('sakura-chatbox');
            const floatingBtn = document.getElementById('floating-chat-btn');
            
            chatbox.style.display = 'none';
            floatingBtn.style.display = 'flex';
            
            updateChatboxStatus('非表示');
            console.log('🌸 Chatbox hidden');
        }

        function minimizeChatbox() {
            const chatbox = document.getElementById('sakura-chatbox');
            const floatingBtn = document.getElementById('floating-chat-btn');
            
            chatbox.classList.add('minimized');
            floatingBtn.style.display = 'flex';
            isMinimized = true;
            
            updateChatboxStatus('最小化');
            console.log('🌸 Chatbox minimized');
        }

        function testQuickActions() {
            showChatbox();
            setTimeout(() => {
                quickAction('monthly-expenses');
            }, 500);
        }

        function clearChat() {
            const messagesContainer = document.getElementById('chat-messages');
            // Keep welcome message
            const welcomeMessage = messagesContainer.querySelector('.message-container');
            messagesContainer.innerHTML = '';
            if (welcomeMessage) {
                messagesContainer.appendChild(welcomeMessage);
            }
            messageCount = 0;
            updateMessageCount();
            alert('チャット履歴をクリアしました');
        }

        function checkChatboxVisibility() {
            const chatbox = document.getElementById('sakura-chatbox');
            const rect = chatbox.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 && 
                             window.getComputedStyle(chatbox).display !== 'none' &&
                             !chatbox.classList.contains('minimized');
            
            const status = isVisible ? '✅ 可視' : '❌ 非可視';
            updateChatboxStatus(status);
            
            alert(`Chatbox可見性: ${status}\n位置: ${rect.left}, ${rect.top}\nサイズ: ${rect.width}x${rect.height}`);
        }

        // Chatbox functionality
        function quickAction(action) {
            const actions = {
                'monthly-expenses': '今月の支出を教えて',
                'bookkeeping-tips': '仕訳のコツは？',
                'budget-review': '予算を見直したい',
                'seasonal-advice': '季節のアドバイス'
            };
            
            const message = actions[action];
            if (message) {
                document.getElementById('message-input').value = message;
                sendMessage();
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function handleInputChange() {
            const input = document.getElementById('message-input');
            const sendBtn = document.getElementById('send-btn');
            sendBtn.disabled = input.value.trim().length === 0;
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            input.value = '';
            handleInputChange();
            
            // Simulate AI response
            setTimeout(() => {
                const responses = [
                    'ありがとうございます！そのご質問についてお答えします🌸',
                    '家計管理について詳しく説明させていただきますね。',
                    'とても良いご質問ですね！一緒に考えてみましょう。',
                    'さくらちゃんがお手伝いします！具体的にどのような点でお困りですか？'
                ];
                const response = responses[Math.floor(Math.random() * responses.length)];
                addMessage(response, 'ai');
            }, 1000);
        }

        function addMessage(content, type) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-container';
            
            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <div class="sakura-avatar-small" style="background: #667eea; border-color: #667eea;">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="white">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble" style="background: #667eea; color: white; border-bottom-right-radius: 6px; border-bottom-left-radius: 18px;">
                        ${content}
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <div class="sakura-avatar-small">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble">
                        ${content}
                    </div>
                `;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            messageCount++;
            updateMessageCount();
        }

        // Status updates
        function updateChatboxStatus(status) {
            document.getElementById('chatbox-status').textContent = status;
            document.getElementById('last-check').textContent = new Date().toLocaleTimeString();
        }

        function updateMessageCount() {
            document.getElementById('message-count').textContent = messageCount;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌸 Sakura Chatbox Demo Fixed loaded');
            updateChatboxStatus('初期化完了');
            updateMessageCount();
            
            // Auto-check visibility
            setTimeout(checkChatboxVisibility, 1000);
        });
    </script>
</body>
</html>

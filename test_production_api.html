<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产环境API测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">生产环境API测试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 认证测试 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">认证测试</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Session Token:</label>
                        <input type="text" id="sessionToken" class="w-full p-2 border rounded" placeholder="输入session token">
                    </div>
                    <button onclick="testAuth()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        测试认证
                    </button>
                </div>
                <div id="auth-result" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
            
            <!-- AI处理测试 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">AI处理测试</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">测试文本:</label>
                        <input type="text" id="testText" class="w-full p-2 border rounded" value="今天买了办公用品100元" placeholder="输入要处理的文本">
                    </div>
                    <button onclick="testAIProcessing()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        测试AI处理
                    </button>
                </div>
                <div id="ai-result" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
            
            <!-- 健康检查 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">健康检查</h2>
                <button onclick="testHealth()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    检查API健康状态
                </button>
                <div id="health-result" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
            
            <!-- 记录测试 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">记录测试</h2>
                <button onclick="testJournalEntries()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    获取记录列表
                </button>
                <div id="journal-result" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
        </div>
        
        <!-- 自动获取token -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
            <h3 class="font-semibold text-yellow-800 mb-2">💡 提示</h3>
            <p class="text-yellow-700 mb-2">如果您已经在生产环境登录，可以点击下面的按钮自动获取token：</p>
            <button onclick="getTokenFromStorage()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                从本地存储获取Token
            </button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        
        // 从本地存储获取token
        function getTokenFromStorage() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (token) {
                document.getElementById('sessionToken').value = token;
                alert('Token已自动填入！');
            } else {
                alert('本地存储中没有找到token，请先登录生产环境');
            }
        }
        
        // 测试认证
        async function testAuth() {
            const token = document.getElementById('sessionToken').value;
            const resultDiv = document.getElementById('auth-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入session token</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>测试认证中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">认证结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>认证状态:</strong> ${data.authenticated ? '✅ 已认证' : '❌ 未认证'}</p>
                    ${data.user ? `<p><strong>用户:</strong> ${data.user.username}</p>` : ''}
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 认证测试失败: ${error.message}</p>`;
            }
        }
        
        // 测试AI处理
        async function testAIProcessing() {
            const token = document.getElementById('sessionToken').value;
            const text = document.getElementById('testText').value;
            const resultDiv = document.getElementById('ai-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先输入session token</p>';
                return;
            }
            
            if (!text) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入测试文本</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>测试AI处理中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        text: text,
                        company_id: 'default'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">AI处理结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>成功:</strong> ${data.success ? '✅' : '❌'}</p>
                    ${data.error ? `<p><strong>错误:</strong> ${data.error}</p>` : ''}
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto max-h-64">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ AI处理测试失败: ${error.message}</p>`;
            }
        }
        
        // 测试健康检查
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<p>检查健康状态中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">健康检查结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>状态:</strong> ${data.status === 'ok' ? '✅ 正常' : '❌ 异常'}</p>
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 健康检查失败: ${error.message}</p>`;
            }
        }
        
        // 测试记录获取
        async function testJournalEntries() {
            const token = document.getElementById('sessionToken').value;
            const resultDiv = document.getElementById('journal-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先输入session token</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>获取记录中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/journal-entries?company_id=default`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">记录获取结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>记录数量:</strong> ${data.data ? data.data.length : 0}</p>
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto max-h-64">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 记录获取失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动尝试获取token
        window.addEventListener('load', function() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (token) {
                document.getElementById('sessionToken').value = token;
            }
        });
    </script>
</body>
</html>

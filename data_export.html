<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据导出 - GoldenLedger — Smart AI-Powered Finance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="/background_control.js"></script>
    <script src="/music_control.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .export-card { transition: all 0.3s ease; }
        .export-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .progress-bar { transition: width 0.3s ease; }
        .download-animation { animation: bounce 1s infinite; }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="master_dashboard.html" class="flex items-center space-x-2">
                        <span class="text-2xl">🧮</span>
                        <span class="text-xl font-bold text-gray-900">goldenledger</span>
                    </a>
                    <span class="ml-4 text-gray-500">数据导出中心</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="master_dashboard.html" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        ← 返回主控制台
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">数据导出中心</h1>
            <p class="text-gray-600">安全、完整地导出您的会计数据，支持多种格式，确保数据完整性和可追溯性</p>
        </div>

        <!-- 导出统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <span class="text-2xl">📊</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">仕訳记录</p>
                        <p class="text-2xl font-bold text-gray-900" id="journal-count">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <span class="text-2xl">📁</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">附件文件</p>
                        <p class="text-2xl font-bold text-gray-900" id="attachment-count">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <span class="text-2xl">🤖</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">AI处理记录</p>
                        <p class="text-2xl font-bold text-gray-900" id="ai-log-count">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <span class="text-2xl">💾</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">数据库大小</p>
                        <p class="text-2xl font-bold text-gray-900" id="db-size">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出选项 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 快速导出 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <span class="text-2xl mr-2">⚡</span>
                    快速导出
                </h2>
                <p class="text-gray-600 mb-6">一键导出常用格式的数据，适合日常备份和数据分析</p>
                
                <div class="space-y-4">
                    <button onclick="quickExport('excel')" class="export-card w-full bg-green-50 border border-green-200 rounded-lg p-4 text-left hover:bg-green-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-2xl mr-3">📊</span>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Excel格式 (.xlsx)</h3>
                                    <p class="text-sm text-gray-600">包含所有仕訳记录，适合财务分析</p>
                                </div>
                            </div>
                            <span class="text-green-600">→</span>
                        </div>
                    </button>

                    <button onclick="quickExport('csv')" class="export-card w-full bg-blue-50 border border-blue-200 rounded-lg p-4 text-left hover:bg-blue-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-2xl mr-3">📄</span>
                                <div>
                                    <h3 class="font-semibold text-gray-900">CSV格式 (.csv)</h3>
                                    <p class="text-sm text-gray-600">通用格式，兼容各种会计软件</p>
                                </div>
                            </div>
                            <span class="text-blue-600">→</span>
                        </div>
                    </button>

                    <button onclick="quickExport('json')" class="export-card w-full bg-purple-50 border border-purple-200 rounded-lg p-4 text-left hover:bg-purple-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-2xl mr-3">🔧</span>
                                <div>
                                    <h3 class="font-semibold text-gray-900">JSON格式 (.json)</h3>
                                    <p class="text-sm text-gray-600">完整数据结构，适合技术分析</p>
                                </div>
                            </div>
                            <span class="text-purple-600">→</span>
                        </div>
                    </button>

                    <button onclick="quickExportAttachments()" class="export-card w-full bg-orange-50 border border-orange-200 rounded-lg p-4 text-left hover:bg-orange-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-2xl mr-3">📎</span>
                                <div>
                                    <h3 class="font-semibold text-gray-900">附件文件 (.zip)</h3>
                                    <p class="text-sm text-gray-600">所有附件文件打包下载，含详细清单</p>
                                </div>
                            </div>
                            <span class="text-orange-600">→</span>
                        </div>
                    </button>
                </div>
            </div>

            <!-- 自定义导出 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <span class="text-2xl mr-2">⚙️</span>
                    自定义导出
                </h2>
                <p class="text-gray-600 mb-6">根据您的需求自定义导出内容和格式</p>

                <form id="custom-export-form" class="space-y-4">
                    <!-- 数据类型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">数据类型</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="dataType" value="journal_entries" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">仕訳记录</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="dataType" value="attachments" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">附件文件</span>
                                <span class="ml-1 text-xs text-gray-500" id="attachment-count-badge">(<span id="attachment-count-text">-</span>个文件)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="dataType" value="ai_logs" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">AI处理记录</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="dataType" value="accounts" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">科目设置</span>
                            </label>
                        </div>
                    </div>

                    <!-- 附件导出选项 -->
                    <div id="attachment-options" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">附件导出选项</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="attachmentExportType" value="all" checked class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">所有附件</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="attachmentExportType" value="by_type" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">按文件类型</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="attachmentExportType" value="by_entries" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">按关联记录</span>
                            </label>
                        </div>

                        <!-- 文件类型选择 -->
                        <div id="file-type-selection" class="mt-3 hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择文件类型</label>
                            <div class="grid grid-cols-2 gap-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="fileTypes" value=".jpg" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">JPG图片</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="fileTypes" value=".png" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">PNG图片</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="fileTypes" value=".pdf" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">PDF文档</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="fileTypes" value=".txt" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">文本文件</span>
                                </label>
                            </div>
                        </div>

                        <!-- 附件组织方式 -->
                        <div class="mt-3">
                            <label class="block text-sm font-medium text-gray-700 mb-2">文件组织方式</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="attachmentOrganization" value="flat" checked class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">平铺结构（所有文件在同一目录）</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="attachmentOrganization" value="by_entry" class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">按记录分组（每个记录一个文件夹）</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="attachmentOrganization" value="by_date" class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">按日期分组（按月份分文件夹）</span>
                                </label>
                            </div>
                        </div>

                        <!-- 包含元数据 -->
                        <div class="mt-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="includeAttachmentMetadata" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">包含附件清单和元数据</span>
                            </label>
                        </div>
                    </div>

                    <!-- 日期范围 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">日期范围</label>
                        <div class="grid grid-cols-2 gap-4">
                            <input type="date" name="startDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <input type="date" name="endDate" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <!-- 导出格式 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导出格式</label>
                        <select name="format" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                            <option value="json">JSON (.json)</option>
                            <option value="pdf">PDF报告 (.pdf)</option>
                        </select>
                    </div>

                    <!-- 导出按钮 -->
                    <button type="submit" class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium">
                        🚀 开始自定义导出
                    </button>
                </form>
            </div>
        </div>

        <!-- 专业附件管理 -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <span class="text-2xl mr-2">📎</span>
                专业附件管理
            </h2>
            <p class="text-gray-600 mb-6">全面的附件文件管理和导出功能，支持批量操作和智能分析</p>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 附件统计 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900 mb-3">📊 附件统计</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>总文件数：</span>
                            <span id="attachment-total-count" class="font-medium">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>总大小：</span>
                            <span id="attachment-total-size" class="font-medium">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>图片文件：</span>
                            <span id="attachment-image-count" class="font-medium">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>PDF文档：</span>
                            <span id="attachment-pdf-count" class="font-medium">-</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900 mb-3">⚡ 快速操作</h3>
                    <div class="space-y-2">
                        <button onclick="exportAllAttachments()" class="w-full bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors">
                            📦 导出所有附件
                        </button>
                        <button onclick="exportImageAttachments()" class="w-full bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700 transition-colors">
                            🖼️ 仅导出图片
                        </button>
                        <button onclick="exportPdfAttachments()" class="w-full bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700 transition-colors">
                            📄 仅导出PDF
                        </button>
                        <button onclick="showAttachmentAnalysis()" class="w-full bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700 transition-colors">
                            📈 附件分析报告
                        </button>
                    </div>
                </div>

                <!-- 高级功能 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900 mb-3">🔧 高级功能</h3>
                    <div class="space-y-2">
                        <button onclick="showAttachmentList()" class="w-full bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700 transition-colors">
                            📋 附件详细列表
                        </button>
                        <button onclick="exportByDateRange()" class="w-full bg-indigo-600 text-white px-3 py-2 rounded text-sm hover:bg-indigo-700 transition-colors">
                            📅 按日期导出
                        </button>
                        <button onclick="exportOrphanedAttachments()" class="w-full bg-yellow-600 text-white px-3 py-2 rounded text-sm hover:bg-yellow-700 transition-colors">
                            🔍 导出孤立附件
                        </button>
                        <button onclick="cleanupAttachments()" class="w-full bg-orange-600 text-white px-3 py-2 rounded text-sm hover:bg-orange-700 transition-colors">
                            🧹 附件清理建议
                        </button>
                        <button onclick="openAttachmentManager()" class="w-full bg-indigo-600 text-white px-3 py-2 rounded text-sm hover:bg-indigo-700 transition-colors">
                            🗂️ 附件管理
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 完整备份 -->
        <div class="mt-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-bold mb-2 flex items-center">
                        <span class="text-2xl mr-2">🛡️</span>
                        完整数据备份
                    </h2>
                    <p class="text-indigo-100">导出包含所有数据和附件的完整备份包，确保数据安全</p>
                </div>
                <button onclick="fullBackup()" class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    📦 创建完整备份
                </button>
            </div>
        </div>

        <!-- 导出历史 -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <span class="text-2xl mr-2">📋</span>
                导出历史
            </h2>
            <div id="export-history" class="space-y-3">
                <!-- 导出历史将在这里动态加载 -->
            </div>
        </div>
    </div>

    <!-- 进度模态框 -->
    <div id="progress-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="text-center">
                <div class="download-animation text-4xl mb-4">📦</div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">正在导出数据...</h3>
                <p class="text-gray-600 mb-4" id="progress-text">准备中...</p>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                    <div class="progress-bar bg-blue-600 h-2 rounded-full" id="progress-bar" style="width: 0%"></div>
                </div>
                <div class="text-sm text-gray-500" id="progress-details">请稍候...</div>
            </div>
        </div>
    </div>

    <!-- 附件分析模态框 -->
    <div id="attachment-analysis-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">📈 附件分析报告</h3>
                <button onclick="hideAttachmentAnalysis()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>
            <div id="attachment-analysis-content">
                <div class="text-center py-8">
                    <div class="animate-spin text-4xl mb-4">⏳</div>
                    <p class="text-gray-600">正在分析附件数据...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 附件列表模态框 -->
    <div id="attachment-list-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-6xl mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">📋 附件详细列表</h3>
                <div class="flex items-center space-x-2">
                    <button onclick="exportSelectedAttachments()" class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 transition-colors">
                        导出选中
                    </button>
                    <button onclick="hideAttachmentList()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>
            </div>
            <div id="attachment-list-content">
                <div class="text-center py-8">
                    <div class="animate-spin text-4xl mb-4">⏳</div>
                    <p class="text-gray-600">正在加载附件列表...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 日期范围选择模态框 -->
    <div id="date-range-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">📅 选择日期范围</h3>
                <button onclick="hideDateRangeModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>
            <form id="date-range-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
                    <input type="date" name="startDate" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
                    <input type="date" name="endDate" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="hideDateRangeModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        导出
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 专业附件管理模态框 -->
    <div id="attachment-manager-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg w-full max-w-7xl mx-4 max-h-[95vh] overflow-hidden flex flex-col">
            <!-- 头部 -->
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <h3 class="text-xl font-semibold text-gray-900 mr-4">🗂️ 专业附件管理中心</h3>
                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                        <span id="manager-total-files">总文件: -</span>
                        <span id="manager-total-size">总大小: -</span>
                        <span id="manager-selected-count">已选择: 0</span>
                    </div>
                </div>
                <button onclick="closeAttachmentManager()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <!-- 工具栏 -->
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <!-- 搜索和筛选 -->
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" id="attachment-search" placeholder="搜索文件名..."
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                            <span class="absolute left-3 top-2.5 text-gray-400">🔍</span>
                        </div>
                        <select id="attachment-type-filter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">所有类型</option>
                            <option value=".jpg">JPG图片</option>
                            <option value=".png">PNG图片</option>
                            <option value=".pdf">PDF文档</option>
                            <option value=".txt">文本文件</option>
                        </select>
                        <select id="attachment-status-filter" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">所有状态</option>
                            <option value="linked">有关联</option>
                            <option value="orphaned">孤立文件</option>
                        </select>
                    </div>

                    <!-- 批量操作 -->
                    <div class="flex items-center space-x-2">
                        <button onclick="selectAllAttachmentsManager()" class="px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg text-sm">
                            全选
                        </button>
                        <button onclick="deselectAllAttachmentsManager()" class="px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg text-sm">
                            取消全选
                        </button>
                        <div class="h-4 border-l border-gray-300"></div>
                        <button onclick="exportSelectedAttachmentsManager()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                            📦 导出选中
                        </button>
                        <button onclick="deleteSelectedAttachments()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm">
                            🗑️ 删除选中
                        </button>
                        <button onclick="cleanupOrphanedAttachments()" class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 text-sm">
                            🧹 清理孤立文件
                        </button>
                    </div>
                </div>
            </div>

            <!-- 附件列表 -->
            <div class="flex-1 overflow-auto">
                <div id="attachment-manager-content" class="p-4">
                    <div class="text-center py-8">
                        <div class="animate-spin text-4xl mb-4">⏳</div>
                        <p class="text-gray-600">正在加载附件列表...</p>
                    </div>
                </div>
            </div>

            <!-- 底部状态栏 -->
            <div class="p-4 border-t border-gray-200 bg-gray-50">
                <div class="flex justify-between items-center text-sm text-gray-600">
                    <div class="flex items-center space-x-4">
                        <span id="manager-status-text">就绪</span>
                        <div id="manager-progress-bar" class="hidden">
                            <div class="w-32 bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="refreshAttachmentManager()" class="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">
                            🔄 刷新
                        </button>
                        <button onclick="closeAttachmentManager()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="delete-confirm-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-60">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center mb-4">
                <span class="text-3xl mr-3">⚠️</span>
                <h3 class="text-lg font-semibold text-gray-900">确认删除</h3>
            </div>
            <div id="delete-confirm-content" class="mb-6">
                <p class="text-gray-700">您确定要删除选中的附件吗？此操作不可撤销。</p>
                <div id="delete-file-list" class="mt-3 max-h-32 overflow-y-auto bg-gray-50 rounded p-3">
                    <!-- 待删除文件列表 -->
                </div>
            </div>
            <div class="flex justify-end space-x-2">
                <button onclick="hideDeleteConfirmModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                    取消
                </button>
                <button onclick="confirmDeleteAttachments()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取统计数据
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadExportHistory();
            setupAttachmentOptions();
        });

        // 设置附件选项的交互
        function setupAttachmentOptions() {
            // 监听数据类型选择变化
            const attachmentCheckbox = document.querySelector('input[name="dataType"][value="attachments"]');
            const attachmentOptions = document.getElementById('attachment-options');

            if (attachmentCheckbox) {
                attachmentCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        attachmentOptions.classList.remove('hidden');
                    } else {
                        attachmentOptions.classList.add('hidden');
                    }
                });
            }

            // 监听附件导出类型变化
            const attachmentTypeRadios = document.querySelectorAll('input[name="attachmentExportType"]');
            const fileTypeSelection = document.getElementById('file-type-selection');

            attachmentTypeRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'by_type') {
                        fileTypeSelection.classList.remove('hidden');
                    } else {
                        fileTypeSelection.classList.add('hidden');
                    }
                });
            });
        }

        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await fetch('/api/export/statistics');
                const data = await response.json();

                document.getElementById('journal-count').textContent = data.journal_count.toLocaleString();
                document.getElementById('attachment-count').textContent = data.attachment_count.toLocaleString();
                document.getElementById('ai-log-count').textContent = data.ai_log_count.toLocaleString();
                document.getElementById('db-size').textContent = formatFileSize(data.db_size);

                // 更新附件相关统计
                if (data.attachment_count > 0) {
                    document.getElementById('attachment-count-text').textContent = data.attachment_count;
                    document.getElementById('attachment-total-count').textContent = data.attachment_count.toLocaleString();
                    document.getElementById('attachment-total-size').textContent = formatFileSize(data.attachment_size || 0);

                    // 更新文件类型统计
                    const fileTypes = data.attachment_types || {};
                    document.getElementById('attachment-image-count').textContent =
                        ((fileTypes['.jpg'] || {}).count || 0) +
                        ((fileTypes['.png'] || {}).count || 0) +
                        ((fileTypes['.gif'] || {}).count || 0);
                    document.getElementById('attachment-pdf-count').textContent =
                        (fileTypes['.pdf'] || {}).count || 0;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 快速导出
        async function quickExport(format) {
            showProgressModal();
            updateProgress(10, '准备导出数据...', '正在验证数据完整性');

            try {
                const response = await fetch('/api/export/quick', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ format: format })
                });

                if (response.ok) {
                    updateProgress(50, '正在生成文件...', '处理数据格式转换');

                    // 模拟进度更新
                    setTimeout(() => {
                        updateProgress(80, '准备下载...', '文件生成完成');
                    }, 1000);

                    setTimeout(async () => {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `goldenledger_export_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : format}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        updateProgress(100, '导出完成！', '文件已下载到您的设备');
                        setTimeout(hideProgressModal, 2000);
                        loadExportHistory();
                    }, 1500);
                } else {
                    throw new Error('导出失败');
                }
            } catch (error) {
                console.error('导出失败:', error);
                hideProgressModal();
                alert('导出失败，请重试');
            }
        }

        // 快速导出附件
        async function quickExportAttachments() {
            showProgressModal();
            updateProgress(10, '准备导出附件...', '正在扫描附件文件');

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'all',
                        format: 'zip',
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    updateProgress(50, '正在打包附件...', '创建ZIP压缩包');

                    setTimeout(() => {
                        updateProgress(80, '准备下载...', '附件包生成完成');
                    }, 1500);

                    setTimeout(async () => {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `goldenledger_attachments_${new Date().toISOString().split('T')[0]}.zip`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        updateProgress(100, '附件导出完成！', '文件已下载到您的设备');
                        setTimeout(hideProgressModal, 2000);
                        loadExportHistory();
                    }, 2000);
                } else {
                    throw new Error('附件导出失败');
                }
            } catch (error) {
                console.error('附件导出失败:', error);
                hideProgressModal();
                alert('附件导出失败，请重试');
            }
        }

        // 导出所有附件
        async function exportAllAttachments() {
            await quickExportAttachments();
        }

        // 导出图片附件
        async function exportImageAttachments() {
            showProgressModal();
            updateProgress(10, '准备导出图片附件...', '正在筛选图片文件');

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'by_type',
                        format: 'zip',
                        file_types: ['.jpg', '.jpeg', '.png', '.gif'],
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, '图片附件');
                } else {
                    throw new Error('图片附件导出失败');
                }
            } catch (error) {
                console.error('图片附件导出失败:', error);
                hideProgressModal();
                alert('图片附件导出失败，请重试');
            }
        }

        // 导出PDF附件
        async function exportPdfAttachments() {
            showProgressModal();
            updateProgress(10, '准备导出PDF附件...', '正在筛选PDF文件');

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'by_type',
                        format: 'zip',
                        file_types: ['.pdf'],
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, 'PDF附件');
                } else {
                    throw new Error('PDF附件导出失败');
                }
            } catch (error) {
                console.error('PDF附件导出失败:', error);
                hideProgressModal();
                alert('PDF附件导出失败，请重试');
            }
        }

        // 处理附件导出响应
        async function handleAttachmentExportResponse(response, type) {
            updateProgress(50, `正在打包${type}...`, '创建ZIP压缩包');

            setTimeout(() => {
                updateProgress(80, '准备下载...', `${type}包生成完成`);
            }, 1000);

            setTimeout(async () => {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `goldenledger_${type.toLowerCase()}_${new Date().toISOString().split('T')[0]}.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                updateProgress(100, `${type}导出完成！`, '文件已下载到您的设备');
                setTimeout(hideProgressModal, 2000);
                loadExportHistory();
            }, 1500);
        }

        // 自定义导出
        document.getElementById('custom-export-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const exportConfig = {
                dataTypes: formData.getAll('dataType'),
                startDate: formData.get('startDate'),
                endDate: formData.get('endDate'),
                format: formData.get('format')
            };

            showProgressModal();
            updateProgress(10, '准备自定义导出...', '正在验证导出配置');

            try {
                const response = await fetch('/api/export/custom', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(exportConfig)
                });

                if (response.ok) {
                    updateProgress(50, '正在处理数据...', '根据您的配置筛选数据');
                    
                    setTimeout(() => {
                        updateProgress(80, '生成文件中...', '应用自定义格式');
                    }, 1500);

                    setTimeout(async () => {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `goldenledger_custom_export_${new Date().toISOString().split('T')[0]}.${exportConfig.format === 'excel' ? 'xlsx' : exportConfig.format}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        
                        updateProgress(100, '自定义导出完成！', '文件已下载到您的设备');
                        setTimeout(hideProgressModal, 2000);
                        loadExportHistory();
                    }, 2000);
                } else {
                    throw new Error('自定义导出失败');
                }
            } catch (error) {
                console.error('自定义导出失败:', error);
                hideProgressModal();
                alert('自定义导出失败，请检查配置后重试');
            }
        });

        // 完整备份
        async function fullBackup() {
            if (!confirm('完整备份将包含所有数据和附件，可能需要较长时间。是否继续？')) {
                return;
            }

            showProgressModal();
            updateProgress(5, '开始完整备份...', '正在扫描所有数据');

            try {
                const response = await fetch('/api/export/full-backup', {
                    method: 'POST'
                });

                if (response.ok) {
                    updateProgress(20, '正在备份数据库...', '导出所有表结构和数据');
                    
                    setTimeout(() => {
                        updateProgress(40, '正在打包附件...', '收集所有相关文件');
                    }, 2000);

                    setTimeout(() => {
                        updateProgress(70, '正在压缩文件...', '创建备份包');
                    }, 4000);

                    setTimeout(async () => {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `goldenledger_full_backup_${new Date().toISOString().split('T')[0]}.zip`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        
                        updateProgress(100, '完整备份完成！', '备份包已下载到您的设备');
                        setTimeout(hideProgressModal, 3000);
                        loadExportHistory();
                    }, 6000);
                } else {
                    throw new Error('完整备份失败');
                }
            } catch (error) {
                console.error('完整备份失败:', error);
                hideProgressModal();
                alert('完整备份失败，请重试');
            }
        }

        // 显示进度模态框
        function showProgressModal() {
            document.getElementById('progress-modal').classList.remove('hidden');
            document.getElementById('progress-modal').classList.add('flex');
        }

        // 隐藏进度模态框
        function hideProgressModal() {
            document.getElementById('progress-modal').classList.add('hidden');
            document.getElementById('progress-modal').classList.remove('flex');
        }

        // 更新进度
        function updateProgress(percent, text, details) {
            document.getElementById('progress-bar').style.width = percent + '%';
            document.getElementById('progress-text').textContent = text;
            document.getElementById('progress-details').textContent = details;
        }

        // 加载导出历史
        async function loadExportHistory() {
            try {
                const response = await fetch('/api/export/history');
                const history = await response.json();
                
                const historyContainer = document.getElementById('export-history');
                if (history.length === 0) {
                    historyContainer.innerHTML = '<p class="text-gray-500 text-center py-4">暂无导出记录</p>';
                    return;
                }

                historyContainer.innerHTML = history.map(item => `
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">${getFormatIcon(item.format)}</span>
                            <div>
                                <h4 class="font-medium text-gray-900">${item.filename}</h4>
                                <p class="text-sm text-gray-600">${item.export_time} • ${formatFileSize(item.file_size)}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${item.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">${item.status === 'completed' ? '已完成' : '处理中'}</span>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('加载导出历史失败:', error);
                document.getElementById('export-history').innerHTML = '<p class="text-red-500 text-center py-4">加载导出历史失败</p>';
            }
        }

        // 显示附件分析报告
        async function showAttachmentAnalysis() {
            const modal = document.getElementById('attachment-analysis-modal');
            const content = document.getElementById('attachment-analysis-content');

            modal.classList.remove('hidden');
            modal.classList.add('flex');

            try {
                const response = await fetch('/api/attachments/analysis');
                const analysis = await response.json();

                content.innerHTML = createAttachmentAnalysisHTML(analysis);
            } catch (error) {
                console.error('获取附件分析失败:', error);
                content.innerHTML = '<div class="text-center py-8 text-red-600">获取分析数据失败</div>';
            }
        }

        // 创建附件分析HTML
        function createAttachmentAnalysisHTML(analysis) {
            return `
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 基本统计 -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-900 mb-3">📊 基本统计</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>总记录数：</span>
                                <span class="font-medium">${analysis.total_entries}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>有附件记录：</span>
                                <span class="font-medium text-green-600">${analysis.entries_with_attachments}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>无附件记录：</span>
                                <span class="font-medium text-gray-600">${analysis.entries_without_attachments}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>孤立附件：</span>
                                <span class="font-medium text-orange-600">${analysis.orphaned_attachments}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 文件类型分布 -->
                    <div class="bg-green-50 rounded-lg p-4">
                        <h4 class="font-semibold text-green-900 mb-3">📁 文件类型分布</h4>
                        <div class="space-y-2 text-sm">
                            ${Object.entries(analysis.file_type_distribution).map(([type, count]) => `
                                <div class="flex justify-between">
                                    <span>${type || '无扩展名'}：</span>
                                    <span class="font-medium">${count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 文件大小分布 -->
                    <div class="bg-purple-50 rounded-lg p-4">
                        <h4 class="font-semibold text-purple-900 mb-3">📏 文件大小分布</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>小文件 (&lt;1MB)：</span>
                                <span class="font-medium">${analysis.size_distribution.small}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>中等文件 (1-10MB)：</span>
                                <span class="font-medium">${analysis.size_distribution.medium}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>大文件 (&gt;10MB)：</span>
                                <span class="font-medium">${analysis.size_distribution.large}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 最大文件 -->
                    <div class="bg-red-50 rounded-lg p-4">
                        <h4 class="font-semibold text-red-900 mb-3">📈 最大文件</h4>
                        <div class="space-y-2 text-sm">
                            ${analysis.largest_files.slice(0, 3).map(file => `
                                <div class="flex justify-between">
                                    <span class="truncate mr-2" title="${file.filename}">${file.filename.substring(0, 20)}...</span>
                                    <span class="font-medium">${file.size_formatted}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 月度分布 -->
                    <div class="bg-yellow-50 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-900 mb-3">📅 月度分布</h4>
                        <div class="space-y-2 text-sm">
                            ${Object.entries(analysis.monthly_distribution).slice(-5).map(([month, count]) => `
                                <div class="flex justify-between">
                                    <span>${month}：</span>
                                    <span class="font-medium">${count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- 操作建议 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-3">💡 操作建议</h4>
                        <div class="space-y-2 text-sm">
                            ${analysis.orphaned_attachments > 0 ? `
                                <div class="text-orange-600">
                                    • 发现 ${analysis.orphaned_attachments} 个孤立附件，建议清理
                                </div>
                            ` : ''}
                            ${analysis.size_distribution.large > 0 ? `
                                <div class="text-blue-600">
                                    • 有 ${analysis.size_distribution.large} 个大文件，可考虑压缩
                                </div>
                            ` : ''}
                            <div class="text-green-600">
                                • 附件覆盖率：${Math.round(analysis.entries_with_attachments / analysis.total_entries * 100)}%
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-center space-x-4">
                    <button onclick="exportOrphanedAttachments()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors">
                        导出孤立附件
                    </button>
                    <button onclick="exportLargeAttachments()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                        导出大文件
                    </button>
                    <button onclick="hideAttachmentAnalysis()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors">
                        关闭
                    </button>
                </div>
            `;
        }

        // 隐藏附件分析
        function hideAttachmentAnalysis() {
            const modal = document.getElementById('attachment-analysis-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // 显示附件列表
        async function showAttachmentList() {
            const modal = document.getElementById('attachment-list-modal');
            const content = document.getElementById('attachment-list-content');

            modal.classList.remove('hidden');
            modal.classList.add('flex');

            try {
                const response = await fetch('/api/attachments/list');
                const data = await response.json();

                content.innerHTML = createAttachmentListHTML(data);
            } catch (error) {
                console.error('获取附件列表失败:', error);
                content.innerHTML = '<div class="text-center py-8 text-red-600">获取附件列表失败</div>';
            }
        }

        // 创建附件列表HTML
        function createAttachmentListHTML(data) {
            if (data.attachments.length === 0) {
                return '<div class="text-center py-8 text-gray-600">暂无附件文件</div>';
            }

            return `
                <div class="mb-4 flex justify-between items-center">
                    <div class="text-sm text-gray-600">
                        共 ${data.total_count} 个文件，总大小 ${data.total_size_formatted}
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="selectAllAttachments()" class="text-blue-600 hover:text-blue-800 text-sm">全选</button>
                        <button onclick="deselectAllAttachments()" class="text-gray-600 hover:text-gray-800 text-sm">取消全选</button>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" id="select-all-checkbox" onchange="toggleAllAttachments(this)">
                                </th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">文件名</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大小</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联记录</th>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${data.attachments.map(attachment => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-3 py-2 whitespace-nowrap">
                                        <input type="checkbox" name="selected-attachments" value="${attachment.filename}" class="attachment-checkbox">
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title="${attachment.filename}">
                                        ${attachment.filename.length > 30 ? attachment.filename.substring(0, 30) + '...' : attachment.filename}
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        ${attachment.size_formatted}
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getFileTypeColor(attachment.extension)}">
                                            ${attachment.extension || 'unknown'}
                                        </span>
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        ${attachment.entry_description ?
                                            `<div title="${attachment.entry_description}">${attachment.entry_description.substring(0, 20)}...</div>
                                             <div class="text-xs text-gray-400">${attachment.entry_date}</div>` :
                                            '<span class="text-orange-600">无关联</span>'
                                        }
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        ${new Date(attachment.created_at).toLocaleDateString()}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // 获取文件类型颜色
        function getFileTypeColor(extension) {
            const colors = {
                '.jpg': 'bg-green-100 text-green-800',
                '.jpeg': 'bg-green-100 text-green-800',
                '.png': 'bg-green-100 text-green-800',
                '.gif': 'bg-green-100 text-green-800',
                '.pdf': 'bg-red-100 text-red-800',
                '.txt': 'bg-gray-100 text-gray-800',
                '.doc': 'bg-blue-100 text-blue-800',
                '.docx': 'bg-blue-100 text-blue-800',
                '.xls': 'bg-yellow-100 text-yellow-800',
                '.xlsx': 'bg-yellow-100 text-yellow-800'
            };
            return colors[extension] || 'bg-gray-100 text-gray-800';
        }

        // 隐藏附件列表
        function hideAttachmentList() {
            const modal = document.getElementById('attachment-list-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // 全选附件
        function selectAllAttachments() {
            const checkboxes = document.querySelectorAll('.attachment-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = true);
            document.getElementById('select-all-checkbox').checked = true;
        }

        // 取消全选附件
        function deselectAllAttachments() {
            const checkboxes = document.querySelectorAll('.attachment-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            document.getElementById('select-all-checkbox').checked = false;
        }

        // 切换全选状态
        function toggleAllAttachments(selectAllCheckbox) {
            const checkboxes = document.querySelectorAll('.attachment-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = selectAllCheckbox.checked);
        }

        // 导出选中的附件
        async function exportSelectedAttachments() {
            const selectedCheckboxes = document.querySelectorAll('.attachment-checkbox:checked');
            const selectedFiles = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (selectedFiles.length === 0) {
                alert('请选择要导出的附件');
                return;
            }

            hideAttachmentList();
            showProgressModal();
            updateProgress(10, '准备导出选中附件...', `正在处理 ${selectedFiles.length} 个文件`);

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'selected',
                        format: 'zip',
                        selected_files: selectedFiles,
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, '选中附件');
                } else {
                    throw new Error('选中附件导出失败');
                }
            } catch (error) {
                console.error('选中附件导出失败:', error);
                hideProgressModal();
                alert('选中附件导出失败，请重试');
            }
        }

        // 按日期范围导出
        function exportByDateRange() {
            const modal = document.getElementById('date-range-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }

        // 隐藏日期范围模态框
        function hideDateRangeModal() {
            const modal = document.getElementById('date-range-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // 处理日期范围表单提交
        document.getElementById('date-range-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const startDate = formData.get('startDate');
            const endDate = formData.get('endDate');

            if (!startDate && !endDate) {
                alert('请至少选择一个日期');
                return;
            }

            hideDateRangeModal();
            showProgressModal();
            updateProgress(10, '准备按日期导出附件...', '正在筛选指定日期范围的文件');

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'by_date',
                        format: 'zip',
                        date_range: {
                            start_date: startDate,
                            end_date: endDate
                        },
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, '指定日期附件');
                } else {
                    throw new Error('按日期导出附件失败');
                }
            } catch (error) {
                console.error('按日期导出附件失败:', error);
                hideProgressModal();
                alert('按日期导出附件失败，请重试');
            }
        });

        // 导出孤立附件
        async function exportOrphanedAttachments() {
            if (!confirm('孤立附件是指没有关联到任何记录的附件文件。确定要导出这些文件吗？')) {
                return;
            }

            showProgressModal();
            updateProgress(10, '准备导出孤立附件...', '正在识别孤立文件');

            try {
                // 首先获取分析数据来确定是否有孤立附件
                const analysisResponse = await fetch('/api/attachments/analysis');
                const analysis = await analysisResponse.json();

                if (analysis.orphaned_attachments === 0) {
                    hideProgressModal();
                    alert('没有发现孤立附件');
                    return;
                }

                updateProgress(30, '正在导出孤立附件...', `发现 ${analysis.orphaned_attachments} 个孤立文件`);

                // 这里需要后端支持导出孤立附件的API
                // 暂时使用通用导出API
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'orphaned',
                        format: 'zip',
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, '孤立附件');
                } else {
                    throw new Error('孤立附件导出失败');
                }
            } catch (error) {
                console.error('孤立附件导出失败:', error);
                hideProgressModal();
                alert('孤立附件导出失败，请重试');
            }
        }

        // 导出大文件
        async function exportLargeAttachments() {
            showProgressModal();
            updateProgress(10, '准备导出大文件...', '正在筛选大于10MB的文件');

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'large_files',
                        format: 'zip',
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, '大文件');
                } else {
                    throw new Error('大文件导出失败');
                }
            } catch (error) {
                console.error('大文件导出失败:', error);
                hideProgressModal();
                alert('大文件导出失败，请重试');
            }
        }

        // 附件清理建议
        async function cleanupAttachments() {
            try {
                const response = await fetch('/api/attachments/analysis');
                const analysis = await response.json();

                let suggestions = [];

                if (analysis.orphaned_attachments > 0) {
                    suggestions.push(`• 发现 ${analysis.orphaned_attachments} 个孤立附件，建议导出后删除`);
                }

                if (analysis.size_distribution.large > 0) {
                    suggestions.push(`• 有 ${analysis.size_distribution.large} 个大文件（>10MB），可考虑压缩或归档`);
                }

                const totalSize = Object.values(analysis.file_type_distribution).reduce((sum, count) => sum + count, 0);
                if (totalSize > 1000) {
                    suggestions.push(`• 附件总数较多（${totalSize}个），建议定期归档旧文件`);
                }

                const duplicateTypes = Object.entries(analysis.file_type_distribution).filter(([type, count]) => count > 50);
                if (duplicateTypes.length > 0) {
                    suggestions.push(`• 某些文件类型数量较多，可能存在重复文件`);
                }

                if (suggestions.length === 0) {
                    suggestions.push('• 附件管理状况良好，无需特殊处理');
                }

                const message = `📋 附件清理建议：\n\n${suggestions.join('\n')}\n\n是否要查看详细的附件分析报告？`;

                if (confirm(message)) {
                    showAttachmentAnalysis();
                }

            } catch (error) {
                console.error('获取清理建议失败:', error);
                alert('获取清理建议失败，请重试');
            }
        }

        // ==================== 专业附件管理功能 ====================

        // 打开附件管理器
        async function openAttachmentManager() {
            const modal = document.getElementById('attachment-manager-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');

            // 加载附件列表
            await loadAttachmentManagerData();

            // 设置搜索和筛选事件
            setupAttachmentManagerEvents();
        }

        // 关闭附件管理器
        function closeAttachmentManager() {
            const modal = document.getElementById('attachment-manager-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // 加载附件管理器数据
        async function loadAttachmentManagerData() {
            const content = document.getElementById('attachment-manager-content');
            const statusText = document.getElementById('manager-status-text');

            try {
                statusText.textContent = '正在加载附件数据...';

                const response = await fetch('/api/attachments/list');
                const data = await response.json();

                // 更新统计信息
                document.getElementById('manager-total-files').textContent = `总文件: ${data.total_count}`;
                document.getElementById('manager-total-size').textContent = `总大小: ${data.total_size_formatted}`;

                // 渲染附件列表
                content.innerHTML = createAttachmentManagerHTML(data.attachments);

                statusText.textContent = `已加载 ${data.total_count} 个附件`;

            } catch (error) {
                console.error('加载附件管理器数据失败:', error);
                content.innerHTML = '<div class="text-center py-8 text-red-600">加载附件数据失败</div>';
                statusText.textContent = '加载失败';
            }
        }

        // 创建附件管理器HTML
        function createAttachmentManagerHTML(attachments) {
            if (attachments.length === 0) {
                return '<div class="text-center py-8 text-gray-600">暂无附件文件</div>';
            }

            return `
                <div class="grid grid-cols-1 gap-4">
                    ${attachments.map(attachment => `
                        <div class="attachment-item border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                             data-filename="${attachment.filename}"
                             data-extension="${attachment.extension}"
                             data-status="${attachment.entry_description ? 'linked' : 'orphaned'}">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <input type="checkbox" class="attachment-manager-checkbox" value="${attachment.filename}">

                                    <!-- 文件图标和信息 -->
                                    <div class="flex items-center space-x-3">
                                        <div class="text-2xl">
                                            ${getFileIcon(attachment.extension)}
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900 truncate max-w-xs" title="${attachment.filename}">
                                                ${attachment.filename.length > 40 ? attachment.filename.substring(0, 40) + '...' : attachment.filename}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ${attachment.size_formatted} • ${attachment.extension} • ${new Date(attachment.created_at).toLocaleDateString()}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 关联信息和操作 -->
                                <div class="flex items-center space-x-4">
                                    <div class="text-right">
                                        ${attachment.entry_description ? `
                                            <div class="text-sm font-medium text-green-700">
                                                ${attachment.entry_description.substring(0, 30)}${attachment.entry_description.length > 30 ? '...' : ''}
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                ${attachment.entry_date} • ¥${attachment.entry_amount || 0}
                                            </div>
                                        ` : `
                                            <div class="text-sm font-medium text-orange-600">
                                                🔸 孤立文件
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                无关联记录
                                            </div>
                                        `}
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="flex items-center space-x-2">
                                        <button onclick="previewAttachmentManager('${attachment.filename}')"
                                                class="px-3 py-1 text-blue-600 hover:bg-blue-50 rounded text-sm">
                                            👁️ 预览
                                        </button>
                                        <button onclick="downloadAttachment('${attachment.filename}')"
                                                class="px-3 py-1 text-green-600 hover:bg-green-50 rounded text-sm">
                                            📥 下载
                                        </button>
                                        <button onclick="deleteAttachment('${attachment.filename}')"
                                                class="px-3 py-1 text-red-600 hover:bg-red-50 rounded text-sm">
                                            🗑️ 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // 获取文件图标
        function getFileIcon(extension) {
            const icons = {
                '.jpg': '🖼️',
                '.jpeg': '🖼️',
                '.png': '🖼️',
                '.gif': '🖼️',
                '.pdf': '📄',
                '.txt': '📝',
                '.doc': '📘',
                '.docx': '📘',
                '.xls': '📊',
                '.xlsx': '📊'
            };
            return icons[extension] || '📎';
        }

        // 设置附件管理器事件
        function setupAttachmentManagerEvents() {
            // 搜索功能
            const searchInput = document.getElementById('attachment-search');
            searchInput.addEventListener('input', filterAttachments);

            // 类型筛选
            const typeFilter = document.getElementById('attachment-type-filter');
            typeFilter.addEventListener('change', filterAttachments);

            // 状态筛选
            const statusFilter = document.getElementById('attachment-status-filter');
            statusFilter.addEventListener('change', filterAttachments);

            // 复选框变化事件
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('attachment-manager-checkbox')) {
                    updateSelectedCount();
                }
            });
        }

        // 筛选附件
        function filterAttachments() {
            const searchTerm = document.getElementById('attachment-search').value.toLowerCase();
            const typeFilter = document.getElementById('attachment-type-filter').value;
            const statusFilter = document.getElementById('attachment-status-filter').value;

            const items = document.querySelectorAll('.attachment-item');
            let visibleCount = 0;

            items.forEach(item => {
                const filename = item.dataset.filename.toLowerCase();
                const extension = item.dataset.extension;
                const status = item.dataset.status;

                let visible = true;

                // 搜索筛选
                if (searchTerm && !filename.includes(searchTerm)) {
                    visible = false;
                }

                // 类型筛选
                if (typeFilter && extension !== typeFilter) {
                    visible = false;
                }

                // 状态筛选
                if (statusFilter && status !== statusFilter) {
                    visible = false;
                }

                item.style.display = visible ? 'block' : 'none';
                if (visible) visibleCount++;
            });

            document.getElementById('manager-status-text').textContent = `显示 ${visibleCount} 个附件`;
        }

        // 更新选中数量
        function updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.attachment-manager-checkbox:checked');
            document.getElementById('manager-selected-count').textContent = `已选择: ${selectedCheckboxes.length}`;
        }

        // 全选附件
        function selectAllAttachmentsManager() {
            const visibleCheckboxes = document.querySelectorAll('.attachment-item:not([style*="display: none"]) .attachment-manager-checkbox');
            visibleCheckboxes.forEach(checkbox => checkbox.checked = true);
            updateSelectedCount();
        }

        // 取消全选附件
        function deselectAllAttachmentsManager() {
            const checkboxes = document.querySelectorAll('.attachment-manager-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            updateSelectedCount();
        }

        // 导出选中附件（管理器）
        async function exportSelectedAttachmentsManager() {
            const selectedCheckboxes = document.querySelectorAll('.attachment-manager-checkbox:checked');
            const selectedFiles = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (selectedFiles.length === 0) {
                alert('请选择要导出的附件');
                return;
            }

            showProgressModal();
            updateProgress(10, '准备导出选中附件...', `正在处理 ${selectedFiles.length} 个文件`);

            try {
                const response = await fetch('/api/attachments/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        export_type: 'selected',
                        format: 'zip',
                        selected_files: selectedFiles,
                        include_metadata: true
                    })
                });

                if (response.ok) {
                    await handleAttachmentExportResponse(response, '选中附件');
                } else {
                    throw new Error('选中附件导出失败');
                }
            } catch (error) {
                console.error('选中附件导出失败:', error);
                hideProgressModal();
                alert('选中附件导出失败，请重试');
            }
        }

        // 删除选中附件
        function deleteSelectedAttachments() {
            const selectedCheckboxes = document.querySelectorAll('.attachment-manager-checkbox:checked');
            const selectedFiles = Array.from(selectedCheckboxes).map(cb => cb.value);

            if (selectedFiles.length === 0) {
                alert('请选择要删除的附件');
                return;
            }

            showDeleteConfirmModal(selectedFiles);
        }

        // 显示删除确认模态框
        function showDeleteConfirmModal(filesToDelete) {
            const modal = document.getElementById('delete-confirm-modal');
            const fileList = document.getElementById('delete-file-list');

            // 显示待删除文件列表
            fileList.innerHTML = filesToDelete.map(filename => `
                <div class="flex items-center justify-between py-1">
                    <span class="text-sm text-gray-700 truncate">${filename}</span>
                    <span class="text-xs text-gray-500">${getFileIcon(getFileExtension(filename))}</span>
                </div>
            `).join('');

            modal.classList.remove('hidden');
            modal.classList.add('flex');

            // 存储待删除文件列表
            modal.dataset.filesToDelete = JSON.stringify(filesToDelete);
        }

        // 隐藏删除确认模态框
        function hideDeleteConfirmModal() {
            const modal = document.getElementById('delete-confirm-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        // 确认删除附件
        async function confirmDeleteAttachments() {
            const modal = document.getElementById('delete-confirm-modal');
            const filesToDelete = JSON.parse(modal.dataset.filesToDelete || '[]');

            if (filesToDelete.length === 0) {
                return;
            }

            hideDeleteConfirmModal();

            const statusText = document.getElementById('manager-status-text');
            const progressBar = document.getElementById('manager-progress-bar');

            try {
                statusText.textContent = `正在删除 ${filesToDelete.length} 个文件...`;
                progressBar.classList.remove('hidden');

                // 逐个删除文件
                for (let i = 0; i < filesToDelete.length; i++) {
                    const filename = filesToDelete[i];
                    const progress = ((i + 1) / filesToDelete.length) * 100;

                    progressBar.querySelector('div').style.width = `${progress}%`;
                    statusText.textContent = `正在删除: ${filename}`;

                    const response = await fetch(`/api/attachments/delete/${encodeURIComponent(filename)}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error(`删除文件 ${filename} 失败`);
                    }

                    // 从界面中移除已删除的文件
                    const fileItem = document.querySelector(`[data-filename="${filename}"]`);
                    if (fileItem) {
                        fileItem.remove();
                    }
                }

                progressBar.classList.add('hidden');
                statusText.textContent = `成功删除 ${filesToDelete.length} 个文件`;

                // 更新统计信息
                await updateManagerStatistics();

                // 刷新主页面的统计数据
                loadStatistics();

            } catch (error) {
                console.error('删除附件失败:', error);
                progressBar.classList.add('hidden');
                statusText.textContent = '删除失败';
                alert(`删除附件失败: ${error.message}`);
            }
        }

        // 删除单个附件
        async function deleteAttachment(filename) {
            if (!confirm(`确定要删除文件 "${filename}" 吗？此操作不可撤销。`)) {
                return;
            }

            const statusText = document.getElementById('manager-status-text');

            try {
                statusText.textContent = `正在删除: ${filename}`;

                const response = await fetch(`/api/attachments/delete/${encodeURIComponent(filename)}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    // 从界面中移除已删除的文件
                    const fileItem = document.querySelector(`[data-filename="${filename}"]`);
                    if (fileItem) {
                        fileItem.remove();
                    }

                    statusText.textContent = `已删除: ${filename}`;

                    // 更新统计信息
                    await updateManagerStatistics();

                    // 刷新主页面的统计数据
                    loadStatistics();

                } else {
                    throw new Error('删除失败');
                }
            } catch (error) {
                console.error('删除附件失败:', error);
                statusText.textContent = '删除失败';
                alert(`删除附件失败: ${error.message}`);
            }
        }

        // 清理孤立附件
        async function cleanupOrphanedAttachments() {
            if (!confirm('确定要删除所有孤立附件吗？孤立附件是指没有关联到任何记录的文件。此操作不可撤销。')) {
                return;
            }

            const orphanedItems = document.querySelectorAll('[data-status="orphaned"]');
            const orphanedFiles = Array.from(orphanedItems).map(item => item.dataset.filename);

            if (orphanedFiles.length === 0) {
                alert('没有发现孤立附件');
                return;
            }

            const statusText = document.getElementById('manager-status-text');
            const progressBar = document.getElementById('manager-progress-bar');

            try {
                statusText.textContent = `正在清理 ${orphanedFiles.length} 个孤立附件...`;
                progressBar.classList.remove('hidden');

                for (let i = 0; i < orphanedFiles.length; i++) {
                    const filename = orphanedFiles[i];
                    const progress = ((i + 1) / orphanedFiles.length) * 100;

                    progressBar.querySelector('div').style.width = `${progress}%`;
                    statusText.textContent = `正在删除孤立文件: ${filename}`;

                    const response = await fetch(`/api/attachments/delete/${encodeURIComponent(filename)}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        const fileItem = document.querySelector(`[data-filename="${filename}"]`);
                        if (fileItem) {
                            fileItem.remove();
                        }
                    }
                }

                progressBar.classList.add('hidden');
                statusText.textContent = `成功清理 ${orphanedFiles.length} 个孤立附件`;

                await updateManagerStatistics();
                loadStatistics();

            } catch (error) {
                console.error('清理孤立附件失败:', error);
                progressBar.classList.add('hidden');
                statusText.textContent = '清理失败';
                alert(`清理孤立附件失败: ${error.message}`);
            }
        }

        // 预览附件
        function previewAttachmentManager(filename) {
            const fileExtension = getFileExtension(filename).toLowerCase();
            const attachmentUrl = `/attachments/${encodeURIComponent(filename)}`;

            // 根据文件类型选择预览方式
            if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(fileExtension)) {
                // 图片文件：使用模态框预览
                showImagePreview(filename, attachmentUrl);
            } else if (fileExtension === '.pdf') {
                // PDF文件：在新标签页打开
                window.open(attachmentUrl, '_blank');
            } else if (['.txt', '.csv', '.json', '.xml', '.log'].includes(fileExtension)) {
                // 文本文件：使用模态框预览
                showTextPreview(filename, attachmentUrl);
            } else {
                // 其他文件：尝试在新标签页打开或下载
                const userChoice = confirm(`无法预览此类型文件 (${fileExtension})。\n\n点击"确定"在新标签页打开，点击"取消"直接下载。`);
                if (userChoice) {
                    window.open(attachmentUrl, '_blank');
                } else {
                    downloadAttachment(filename);
                }
            }
        }

        // 显示图片预览
        function showImagePreview(filename, imageUrl) {
            // 创建预览模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';

            // 创建模态框内容
            const modalContent = document.createElement('div');
            modalContent.className = 'relative max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden';

            modalContent.innerHTML = `
                <div class="flex justify-between items-center p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">🖼️ 图片预览</h3>
                    <button class="close-btn text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>
                <div class="p-4">
                    <div class="text-center mb-4">
                        <h4 class="font-medium text-gray-900">${filename}</h4>
                    </div>
                    <div class="flex justify-center image-container">
                        <div class="loading-spinner text-center py-8">
                            <div class="animate-spin text-4xl mb-2">⏳</div>
                            <p class="text-gray-600">正在加载图片...</p>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 mt-4">
                        <button class="download-btn px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            📥 下载
                        </button>
                        <button class="open-btn px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                            🔗 新标签页打开
                        </button>
                        <button class="close-btn px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                            关闭
                        </button>
                    </div>
                </div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 创建图片元素
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = filename;
            img.className = 'max-w-full max-h-[60vh] object-contain rounded shadow-lg';
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s';

            // 图片加载成功
            img.onload = function() {
                const container = modalContent.querySelector('.image-container');
                container.innerHTML = '';
                container.appendChild(img);
                img.style.opacity = '1';
            };

            // 图片加载失败
            img.onerror = function() {
                const container = modalContent.querySelector('.image-container');
                container.innerHTML = '<div class="text-red-600 text-center py-8">图片加载失败</div>';
            };

            // 事件处理
            modalContent.querySelector('.download-btn').addEventListener('click', () => {
                downloadAttachment(filename);
            });

            modalContent.querySelector('.open-btn').addEventListener('click', () => {
                window.open(imageUrl, '_blank');
            });

            modalContent.querySelectorAll('.close-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    modal.remove();
                });
            });

            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });

            // ESC键关闭
            const handleEsc = function(e) {
                if (e.key === 'Escape') {
                    modal.remove();
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);
        }

        // 显示文本预览
        async function showTextPreview(filename, textUrl) {
            try {
                const response = await fetch(textUrl);
                if (!response.ok) {
                    throw new Error('文件加载失败');
                }

                const textContent = await response.text();

                // 创建预览模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';

                const modalContent = document.createElement('div');
                modalContent.className = 'relative w-full max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden flex flex-col';

                modalContent.innerHTML = `
                    <div class="flex justify-between items-center p-4 border-b">
                        <h3 class="text-lg font-semibold text-gray-900">📝 文本预览</h3>
                        <button class="close-btn text-gray-400 hover:text-gray-600">
                            <span class="text-2xl">×</span>
                        </button>
                    </div>
                    <div class="p-4 flex-1 overflow-hidden flex flex-col">
                        <div class="text-center mb-4">
                            <h4 class="font-medium text-gray-900">${filename}</h4>
                            <p class="text-sm text-gray-600">文件大小: ${textContent.length} 字符</p>
                        </div>
                        <div class="flex-1 overflow-auto bg-gray-50 rounded p-4">
                            <pre class="text-sm text-gray-800 whitespace-pre-wrap font-mono">${textContent.substring(0, 10000).replace(/</g, '&lt;').replace(/>/g, '&gt;')}${textContent.length > 10000 ? '\n\n... (文件过长，仅显示前10000字符)' : ''}</pre>
                        </div>
                        <div class="flex justify-center space-x-4 mt-4">
                            <button class="download-btn px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                                📥 下载完整文件
                            </button>
                            <button class="open-btn px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                                🔗 新标签页打开
                            </button>
                            <button class="close-btn px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                关闭
                            </button>
                        </div>
                    </div>
                `;

                modal.appendChild(modalContent);
                document.body.appendChild(modal);

                // 事件处理
                modalContent.querySelector('.download-btn').addEventListener('click', () => {
                    downloadAttachment(filename);
                });

                modalContent.querySelector('.open-btn').addEventListener('click', () => {
                    window.open(textUrl, '_blank');
                });

                modalContent.querySelectorAll('.close-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        modal.remove();
                    });
                });

                // 点击背景关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });

                // ESC键关闭
                const handleEsc = function(e) {
                    if (e.key === 'Escape') {
                        modal.remove();
                        document.removeEventListener('keydown', handleEsc);
                    }
                };
                document.addEventListener('keydown', handleEsc);

            } catch (error) {
                console.error('文本预览失败:', error);
                alert(`文本预览失败: ${error.message}\n\n将在新标签页打开文件。`);
                window.open(textUrl, '_blank');
            }
        }

        // 下载附件
        function downloadAttachment(filename) {
            const attachmentUrl = `/attachments/${encodeURIComponent(filename)}`;
            const a = document.createElement('a');
            a.href = attachmentUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // 刷新附件管理器
        async function refreshAttachmentManager() {
            await loadAttachmentManagerData();
            document.getElementById('manager-status-text').textContent = '数据已刷新';
        }

        // 更新管理器统计信息
        async function updateManagerStatistics() {
            try {
                const response = await fetch('/api/attachments/list');
                const data = await response.json();

                document.getElementById('manager-total-files').textContent = `总文件: ${data.total_count}`;
                document.getElementById('manager-total-size').textContent = `总大小: ${data.total_size_formatted}`;

            } catch (error) {
                console.error('更新统计信息失败:', error);
            }
        }

        // 获取文件扩展名
        function getFileExtension(filename) {
            return filename.substring(filename.lastIndexOf('.')).toLowerCase();
        }

        // 获取格式图标
        function getFormatIcon(format) {
            const icons = {
                'excel': '📊',
                'csv': '📄',
                'json': '🔧',
                'pdf': '📋',
                'zip': '📦'
            };
            return icons[format] || '📄';
        }
    </script>
</body>
</html>

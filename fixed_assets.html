<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>固定資産の一覧 - goldenledger会計AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="/background_control.js"></script>
    <script src="/music_control.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;600;700&family=M+PLUS+1p:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'M PLUS 1p', 'Noto Sans JP', '<PERSON>', 'Hiragino Kaku Gothic ProN', 'Hiragino Sans', '<PERSON><PERSON>', sans-serif;
            font-feature-settings: "palt";
            letter-spacing: 0.02em;
        }
        .gradient-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
        }
        .asset-card { 
            transition: all 0.3s ease; 
            border-left: 4px solid transparent;
        }
        .asset-card:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left-color: #667eea;
        }
        .status-badge {
            font-size: 0.75rem;
            font-weight: 500;
        }
        .table-header {
            background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .depreciation-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .japanese-number {
            font-family: 'M PLUS 1p', 'Roboto Mono', monospace;
            font-variant-numeric: tabular-nums;
        }
        .asset-row:nth-child(even) {
            background-color: rgba(248, 250, 252, 0.5);
        }
        .asset-row:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }
        .header-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* 樱花动画样式 */
        .sakura-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        }

        .sakura {
            position: absolute;
            font-size: 20px;
            opacity: 0.9;
            animation: fall linear infinite;
            filter: drop-shadow(0 2px 4px rgba(255, 105, 180, 0.3));
            user-select: none;
            pointer-events: none;
        }

        .sakura::before {
            content: '🌸';
            display: block;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100vh) translateX(0px) rotate(0deg) scale(1);
                opacity: 1;
            }
            25% {
                transform: translateY(-50vh) translateX(var(--sway-amount, 20px)) rotate(90deg) scale(1.1);
                opacity: 0.9;
            }
            50% {
                transform: translateY(0vh) translateX(calc(var(--sway-amount, 20px) * -0.5)) rotate(180deg) scale(0.9);
                opacity: 0.8;
            }
            75% {
                transform: translateY(50vh) translateX(calc(var(--sway-amount, 20px) * 0.7)) rotate(270deg) scale(1.05);
                opacity: 0.6;
            }
            100% {
                transform: translateY(100vh) translateX(0px) rotate(360deg) scale(0.8);
                opacity: 0;
            }
        }

        /* 雪花动画样式 */
        .snow {
            position: absolute;
            font-size: 18px;
            opacity: 0.8;
            animation: snowfall linear infinite;
            user-select: none;
            pointer-events: none;
        }

        .snow::before {
            content: '❄️';
            display: block;
        }

        @keyframes snowfall {
            0% {
                transform: translateY(-100vh) rotate(0deg) scale(1);
                opacity: 1;
            }
            50% {
                transform: translateY(0vh) rotate(180deg) scale(1.2);
                opacity: 0.8;
            }
            100% {
                transform: translateY(100vh) rotate(360deg) scale(0.9);
                opacity: 0;
            }
        }

        /* 星星动画样式 */
        .star {
            position: absolute;
            font-size: 16px;
            opacity: 0.9;
            animation: starfall linear infinite;
            user-select: none;
            pointer-events: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes starfall {
            0% {
                transform: translateY(-100vh) rotate(0deg) scale(1);
                opacity: 1;
            }
            25% {
                transform: translateY(-25vh) rotate(90deg) scale(1.3);
                opacity: 0.9;
            }
            50% {
                transform: translateY(25vh) rotate(180deg) scale(0.8);
                opacity: 1;
            }
            75% {
                transform: translateY(75vh) rotate(270deg) scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: translateY(100vh) rotate(360deg) scale(0.6);
                opacity: 0;
            }
        }

        /* 动画控制面板 */
        .animation-control {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 180px;
            transition: all 0.3s ease;
        }

        .animation-control:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .animation-control h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }

        .animation-toggle {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .toggle-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .toggle-option:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .toggle-option input[type="radio"],
        .toggle-option input[type="checkbox"] {
            margin: 0;
            accent-color: #667eea;
        }

        .toggle-option label {
            font-size: 13px;
            color: #6b7280;
            cursor: pointer;
            margin: 0;
            font-weight: 500;
        }

        .toggle-option:has(input:checked) label {
            color: #374151;
            font-weight: 600;
        }

        /* 隐藏动画容器 */
        .animation-hidden {
            display: none !important;
        }

        /* 性能优化 */
        .sakura, .snow, .star {
            will-change: transform, opacity;
            backface-visibility: hidden;
        }

        /* 移动设备优化 */
        @media (max-width: 768px) {
            .animation-control {
                top: 10px;
                right: 10px;
                padding: 12px;
                min-width: 160px;
            }

            .sakura, .snow, .star {
                animation-duration: 4s !important;
            }
        }

        /* 减少动画数量在低性能设备上 */
        @media (prefers-reduced-motion: reduce) {
            .sakura, .snow, .star {
                animation-duration: 8s !important;
                opacity: 0.3 !important;
            }
        }

        /* 樱花背景图片样式 */
        .sakura-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('/static/image/sakura.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            opacity: 0.2;
            z-index: -1;
            transition: all 0.8s ease;
            filter: blur(0.5px) brightness(1.1) saturate(0.9);
        }

        .sakura-background.hidden {
            opacity: 0;
            transform: scale(1.05);
        }

        /* 背景渐变遮罩 */
        .sakura-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 182, 193, 0.05) 25%,
                rgba(255, 105, 180, 0.03) 50%,
                rgba(255, 255, 255, 0.1) 100%
            );
            z-index: 1;
        }

        /* 背景遮罩，确保内容可读性 */
        .content-overlay {
            position: relative;
            z-index: 1;
        }

        /* 调整主要内容区域的背景透明度 */
        .bg-white {
            background-color: rgba(255, 255, 255, 0.96) !important;
            backdrop-filter: blur(3px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .bg-gray-50 {
            background-color: rgba(249, 250, 251, 0.92) !important;
            backdrop-filter: blur(2px);
        }

        /* 渐变头部保持不透明 */
        .gradient-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            backdrop-filter: blur(5px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 表格行的背景优化 */
        .asset-row:nth-child(even) {
            background-color: rgba(248, 250, 252, 0.7) !important;
        }

        .asset-row:hover {
            background-color: rgba(59, 130, 246, 0.08) !important;
        }

        /* 统计卡片优化 */
        .content-overlay .bg-white {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* 編集モーダルのスタイル */
        #editAssetModal {
            backdrop-filter: blur(4px);
        }

        #editAssetModal .bg-white {
            background-color: rgba(255, 255, 255, 0.98) !important;
        }

        /* フォーム要素のスタイル */
        #editAssetForm input:focus,
        #editAssetForm select:focus,
        #editAssetForm textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        #editAssetForm input[type="number"] {
            text-align: right;
        }

        /* 計算結果のスタイル */
        #calculationResults {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid #e2e8f0;
        }

        /* 必須項目のマーク */
        label:has(+ input[required])::after,
        label:has(+ select[required])::after {
            content: ' *';
            color: #ef4444;
            font-weight: bold;
        }

        /* モーダルアニメーション */
        #editAssetModal:not(.hidden) {
            animation: modalFadeIn 0.3s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 动画容器 -->
    <div id="sakuraContainer" class="sakura-container"></div>
    <div id="snowContainer" class="sakura-container animation-hidden"></div>
    <div id="starContainer" class="sakura-container animation-hidden"></div>

    <!-- 樱花背景图片 -->
    <div id="sakuraBackground" class="sakura-background"></div>

    <!-- アニメーション制御パネル -->
    <div class="animation-control" id="animationControl">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
            <h4 style="margin: 0;">🌸 背景設定</h4>
            <div style="display: flex; gap: 4px;">
                <button id="hideControl" style="background: none; border: none; font-size: 14px; cursor: pointer; padding: 4px;" title="パネルを隠す">👁️</button>
                <button id="toggleControl" style="background: none; border: none; font-size: 16px; cursor: pointer; padding: 4px;" title="パネルを折りたたむ">📌</button>
            </div>
        </div>
        <div class="animation-toggle" id="animationToggle">
            <!-- 背景画像制御 -->
            <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; margin-bottom: 8px;">
                <div class="toggle-option">
                    <input type="checkbox" id="backgroundToggle" checked>
                    <label for="backgroundToggle">🖼️ 桜背景画像</label>
                </div>
            </div>

            <!-- アニメーション制御 -->
            <div style="font-size: 12px; color: #6b7280; margin-bottom: 6px;">アニメーション効果：</div>
            <div class="toggle-option">
                <input type="radio" id="sakura" name="animation" value="sakura" checked>
                <label for="sakura">🌸 桜の花びら</label>
            </div>
            <div class="toggle-option">
                <input type="radio" id="snow" name="animation" value="snow">
                <label for="snow">❄️ 雪の舞</label>
            </div>
            <div class="toggle-option">
                <input type="radio" id="star" name="animation" value="star">
                <label for="star">✨ 星の輝き</label>
            </div>
            <div class="toggle-option">
                <input type="radio" id="none" name="animation" value="none">
                <label for="none">🚫 アニメーション停止</label>
            </div>

            <!-- ショートカットキーのヒント -->
            <div style="margin-top: 12px; padding-top: 8px; border-top: 1px solid #e5e7eb; font-size: 11px; color: #9ca3af;">
                <div>💡 ショートカットキー：</div>
                <div>Ctrl+B 背景画像切替</div>
                <div>Ctrl+Shift+A パネル折りたたみ</div>
                <div>Ctrl+H パネル表示切替</div>
                <div>Ctrl+N 新規追加</div>
                <div>Ctrl+E 編集</div>
                <div>Ctrl+A 全選択</div>
                <div>Delete 削除</div>
            </div>
        </div>
    </div>

    <!-- ヘッダー -->
    <header class="gradient-header text-white py-6 shadow-lg content-overlay">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">🏢</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">固定資産の一覧</h1>
                        <p class="text-blue-100 text-sm">Fixed Assets Management</p>
                    </div>
                </div>
                <div class="font-medium text-gray-900">
                    <button onclick="window.location.href='master_dashboard.html'" 
                            class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-colors flex items-center space-x-2">
                        <span>🏠</span>
                        <span>ホームに戻る</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- メインコンテンツ -->
    <main class="container mx-auto px-6 py-8 content-overlay">
        <!-- 年度選択とコントロール -->
        <div class="bg-white rounded-xl shadow-sm border p-6 mb-8">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">年度:</label>
                        <select id="yearSelect" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="2025">2025 (令和7)</option>
                            <option value="2024">2024 (令和6)</option>
                            <option value="2023">2023 (令和5)</option>
                        </select>
                    </div>
                    <div class="text-sm text-gray-600">年分</div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <button onclick="downloadAssetList()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2">
                        <span>📊</span>
                        <span>固定資産台帳ダウンロード</span>
                    </button>
                    <div class="flex items-center space-x-2">
                        <label class="flex items-center space-x-2 text-sm">
                            <input type="checkbox" id="showDepreciation" checked class="rounded">
                            <span>償却済の固定資産を表示する</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作ボタン -->
        <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
            <div class="flex items-center space-x-3">
                <button onclick="addNewAsset()" 
                        class="btn-primary text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2">
                    <span>➕</span>
                    <span>新規登録</span>
                </button>
                <button onclick="editSelected()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2">
                    <span>✏️</span>
                    <span>確認・編集</span>
                </button>
                <button onclick="deleteSelected()" 
                        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors flex items-center space-x-2">
                    <span>🗑️</span>
                    <span>削除</span>
                </button>
            </div>
            
            <div class="flex items-center space-x-3">
                <button onclick="sellAsset()" 
                        class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    売却
                </button>
                <button onclick="disposeAsset()" 
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    廃棄
                </button>
            </div>
        </div>

        <!-- 固定資産テーブル -->
        <div class="bg-white rounded-xl shadow-sm border overflow-hidden">
            <div class="table-header px-6 py-4 border-b">
                <h2 class="text-lg font-semibold text-gray-800">固定資産一覧</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 border-b">
                        <tr class="text-left">
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAll" class="rounded" onchange="toggleSelectAll()">
                                <label for="selectAll" class="ml-1">選択</label>
                            </th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">科目</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">状態</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">取得日</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">取得価額</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">償却方法</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">耐用年数</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">期首未償却残高</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">償却累計額</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">本年末残高</th>
                            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">精算</th>
                        </tr>
                    </thead>
                    <tbody id="assetTableBody" class="divide-y divide-gray-200">
                        <!-- 資産データはJavaScriptで動的に生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 統計情報 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">総資産数</p>
                        <p class="text-2xl font-bold text-gray-900 japanese-number" id="totalAssets">-</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📊</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">総取得価額</p>
                        <p class="text-2xl font-bold text-green-600 japanese-number" id="totalAcquisition">-</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">💰</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">総償却累計額</p>
                        <p class="text-2xl font-bold text-orange-600 japanese-number" id="totalDepreciation">-</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📉</span>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">期末残高</p>
                        <p class="text-2xl font-bold text-purple-600 japanese-number" id="totalBalance">-</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">💎</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 資産編集モーダル -->
    <div id="editAssetModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <!-- モーダルヘッダー -->
            <div class="gradient-header text-white px-6 py-4 rounded-t-xl">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-bold flex items-center space-x-2">
                        <span>✏️</span>
                        <span id="modalTitle">固定資産編集</span>
                    </h2>
                    <button onclick="closeEditModal()" class="text-white hover:text-gray-200 text-2xl">×</button>
                </div>
            </div>

            <!-- モーダルコンテンツ -->
            <div class="p-6">
                <form id="editAssetForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 基本情報 -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-800 border-b pb-2">📋 基本情報</h3>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">資産名称 *</label>
                                <input type="text" id="assetName" name="assetName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">資産科目 *</label>
                                <select id="assetCategory" name="assetCategory" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">選択してください</option>
                                    <option value="車両運搬具">車両運搬具</option>
                                    <option value="建物">建物</option>
                                    <option value="工具器具備品">工具器具備品</option>
                                    <option value="機械装置">機械装置</option>
                                    <option value="ソフトウェア">ソフトウェア</option>
                                    <option value="建物附属設備">建物附属設備</option>
                                    <option value="土地">土地</option>
                                    <option value="特許権">特許権</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">使用状況</label>
                                <select id="assetStatus" name="assetStatus"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="使用中">使用中</option>
                                    <option value="売却済">売却済</option>
                                    <option value="廃棄済">廃棄済</option>
                                    <option value="休止中">休止中</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">取得年月日 *</label>
                                <input type="date" id="acquisitionDate" name="acquisitionDate" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 金額情報 -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold text-gray-800 border-b pb-2">💰 金額情報</h3>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">取得価額 *</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">¥</span>
                                    <input type="number" id="acquisitionCost" name="acquisitionCost" required min="0"
                                           class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">償却方法</label>
                                <select id="depreciationMethod" name="depreciationMethod"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="定額法">定額法</option>
                                    <option value="定率法">定率法</option>
                                    <option value="生産高比例法">生産高比例法</option>
                                    <option value="年数合計法">年数合計法</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">耐用年数</label>
                                <div class="relative">
                                    <input type="number" id="usefulLife" name="usefulLife" min="1" max="50"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <span class="absolute right-3 top-2 text-gray-500">年</span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">残存価額</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">¥</span>
                                    <input type="number" id="residualValue" name="residualValue" min="0"
                                           class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 備考欄 -->
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">備考</label>
                        <textarea id="assetNotes" name="assetNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="資産に関する追加情報や特記事項"></textarea>
                    </div>

                    <!-- 計算結果表示 -->
                    <div id="calculationResults" class="mt-6 p-4 bg-gray-50 rounded-lg hidden">
                        <h4 class="font-semibold text-gray-800 mb-3">📊 償却計算結果</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">年間償却額:</span>
                                <span id="annualDepreciation" class="font-medium ml-2">-</span>
                            </div>
                            <div>
                                <span class="text-gray-600">月間償却額:</span>
                                <span id="monthlyDepreciation" class="font-medium ml-2">-</span>
                            </div>
                            <div>
                                <span class="text-gray-600">償却率:</span>
                                <span id="depreciationRate" class="font-medium ml-2">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- ボタン -->
                    <div class="flex justify-end space-x-3 mt-8 pt-4 border-t">
                        <button type="button" onclick="calculateDepreciation()"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                            🧮 償却計算
                        </button>
                        <button type="button" onclick="closeEditModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                            キャンセル
                        </button>
                        <button type="submit"
                                class="btn-primary text-white px-6 py-2 rounded-lg transition-colors">
                            💾 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // サンプル固定資産データ
        const sampleAssets = [
            {
                id: 1,
                category: '車両運搬具',
                name: '営業車',
                status: '使用中',
                acquisitionDate: '2021/11/02',
                acquisitionCost: 3543280,
                depreciationMethod: '定額法',
                usefulLife: 3,
                beginningBalance: 2362187,
                accumulatedDepreciation: 1181093,
                endingBalance: 1181093,
                yearEndBalance: 1181094,
                residualValue: 0,
                notes: '営業活動用車両'
            },
            {
                id: 2,
                category: '建物',
                name: '自宅兼事務所',
                status: '使用中',
                acquisitionDate: '2019/05/10',
                acquisitionCost: 38500000,
                depreciationMethod: '定額法',
                usefulLife: 22,
                beginningBalance: 29964550,
                accumulatedDepreciation: 1771000,
                endingBalance: 1771000,
                yearEndBalance: 28193550,
                residualValue: 3850000,
                notes: '本社事務所として使用'
            },
            {
                id: 3,
                category: '工具器具備品',
                name: 'パソコン一式',
                status: '使用中',
                acquisitionDate: '2023/04/01',
                acquisitionCost: 450000,
                depreciationMethod: '定額法',
                usefulLife: 4,
                beginningBalance: 337500,
                accumulatedDepreciation: 112500,
                endingBalance: 112500,
                yearEndBalance: 225000,
                residualValue: 0,
                notes: '事務用パソコン及び周辺機器'
            },
            {
                id: 4,
                category: '機械装置',
                name: '製造設備A',
                status: '使用中',
                acquisitionDate: '2020/08/15',
                acquisitionCost: 12800000,
                depreciationMethod: '定率法',
                usefulLife: 10,
                beginningBalance: 8192000,
                accumulatedDepreciation: 4608000,
                endingBalance: 1638400,
                yearEndBalance: 6553600,
                residualValue: 640000,
                notes: '主力製造ライン設備'
            },
            {
                id: 5,
                category: 'ソフトウェア',
                name: '会計システム',
                status: '使用中',
                acquisitionDate: '2022/01/01',
                acquisitionCost: 2400000,
                depreciationMethod: '定額法',
                usefulLife: 5,
                beginningBalance: 1440000,
                accumulatedDepreciation: 960000,
                endingBalance: 480000,
                yearEndBalance: 960000,
                residualValue: 0,
                notes: '財務会計システムライセンス'
            },
            {
                id: 6,
                category: '建物附属設備',
                name: '空調設備',
                status: '使用中',
                acquisitionDate: '2018/03/20',
                acquisitionCost: 5600000,
                depreciationMethod: '定額法',
                usefulLife: 15,
                beginningBalance: 2986667,
                accumulatedDepreciation: 2613333,
                endingBalance: 373333,
                yearEndBalance: 2613334,
                residualValue: 280000,
                notes: '本社ビル空調システム'
            }
        ];

        // ページ読み込み時の初期化
        document.addEventListener('DOMContentLoaded', function() {
            loadAssetData();
            updateStatistics();
            initializeAnimations();
            preloadBackgroundImage();
            initializeEditForm();
        });

        // 編集フォームの初期化
        function initializeEditForm() {
            const form = document.getElementById('editAssetForm');

            // フォーム送信イベント
            form.addEventListener('submit', handleFormSubmit);

            // 計算トリガーイベント
            const calculationTriggers = ['acquisitionCost', 'usefulLife', 'residualValue', 'depreciationMethod'];
            calculationTriggers.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', calculateDepreciation);
                    element.addEventListener('change', calculateDepreciation);
                }
            });

            // キーボードショートカット
            document.addEventListener('keydown', function(e) {
                // ESCキーでモーダルを閉じる
                if (e.key === 'Escape') {
                    const modal = document.getElementById('editAssetModal');
                    if (!modal.classList.contains('hidden')) {
                        closeEditModal();
                    }
                }

                // Ctrl+N で新規追加
                if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                    e.preventDefault();
                    addNewAsset();
                }

                // Ctrl+E で編集（選択中の資産が1件の場合）
                if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                    e.preventDefault();
                    editSelected();
                }

                // Ctrl+A で全選択
                if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    const selectAllCheckbox = document.getElementById('selectAll');
                    selectAllCheckbox.checked = true;
                    toggleSelectAll();
                }

                // Delete キーで削除
                if (e.key === 'Delete' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    deleteSelected();
                }
            });

            // モーダル背景クリックで閉じる
            const modal = document.getElementById('editAssetModal');
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeEditModal();
                }
            });
        }

        // 資産データの読み込み
        function loadAssetData() {
            const tbody = document.getElementById('assetTableBody');
            tbody.innerHTML = '';

            sampleAssets.forEach(asset => {
                const row = createAssetRow(asset);
                tbody.appendChild(row);
            });
        }

        // 資産行の作成
        function createAssetRow(asset) {
            const row = document.createElement('tr');
            row.className = 'asset-row asset-card';

            // 償却率の計算
            const depreciationRate = asset.acquisitionCost > 0 ?
                (asset.accumulatedDepreciation / asset.acquisitionCost * 100) : 0;

            // ステータスバッジの色
            const statusColor = asset.status === '使用中' ? 'bg-green-100 text-green-800' :
                               asset.status === '売却済' ? 'bg-blue-100 text-blue-800' :
                               'bg-gray-100 text-gray-800';

            row.innerHTML = `
                <td class="px-4 py-4">
                    <input type="checkbox" class="asset-checkbox rounded" value="${asset.id}">
                </td>
                <td class="px-4 py-4">
                    <div class="font-medium text-gray-900">${asset.category}</div>
                </td>
                <td class="px-4 py-4">
                    <div class="font-medium text-gray-900">${asset.name}</div>
                </td>
                <td class="px-4 py-4">
                    <span class="status-badge px-2 py-1 rounded-full ${statusColor}">
                        ${asset.status}
                    </span>
                </td>
                <td class="px-4 py-4 text-sm text-gray-600">
                    ${asset.acquisitionDate}
                </td>
                <td class="px-4 py-4">
                    <div class="font-medium text-gray-900 japanese-number">
                        ¥${asset.acquisitionCost.toLocaleString('ja-JP')}
                    </div>
                </td>
                <td class="px-4 py-4 text-sm text-gray-600">
                    ${asset.depreciationMethod}
                </td>
                <td class="px-4 py-4 text-sm text-gray-600 text-center">
                    ${asset.usefulLife}
                </td>
                <td class="px-4 py-4">
                    <div class="font-medium text-gray-900 japanese-number">
                        ¥${asset.beginningBalance.toLocaleString('ja-JP')}
                    </div>
                </td>
                <td class="px-4 py-4">
                    <div class="font-medium text-gray-900 japanese-number">
                        ¥${asset.accumulatedDepreciation.toLocaleString('ja-JP')}
                    </div>
                    <div class="depreciation-bar bg-gray-200 mt-1">
                        <div class="bg-orange-500 h-full" style="width: ${depreciationRate}%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1 japanese-number">${depreciationRate.toFixed(1)}%</div>
                </td>
                <td class="px-4 py-4">
                    <div class="font-medium text-gray-900 japanese-number">
                        ¥${asset.yearEndBalance.toLocaleString('ja-JP')}
                    </div>
                </td>
                <td class="px-4 py-4 text-center">
                    <button onclick="showAssetDetails(${asset.id})"
                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        詳細
                    </button>
                </td>
            `;

            return row;
        }

        // 統計情報の更新
        function updateStatistics() {
            const totalAssets = sampleAssets.length;
            const totalAcquisition = sampleAssets.reduce((sum, asset) => sum + asset.acquisitionCost, 0);
            const totalDepreciation = sampleAssets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0);
            const totalBalance = sampleAssets.reduce((sum, asset) => sum + asset.yearEndBalance, 0);

            document.getElementById('totalAssets').textContent = totalAssets.toLocaleString('ja-JP');
            document.getElementById('totalAcquisition').textContent = `¥${totalAcquisition.toLocaleString('ja-JP')}`;
            document.getElementById('totalDepreciation').textContent = `¥${totalDepreciation.toLocaleString('ja-JP')}`;
            document.getElementById('totalBalance').textContent = `¥${totalBalance.toLocaleString('ja-JP')}`;
        }

        // 新規資産登録
        function addNewAsset() {
            // 新規資産用のデフォルトデータ
            const newAsset = {
                id: Date.now(), // 一時的なID
                name: '',
                category: '',
                status: '使用中',
                acquisitionDate: new Date().toISOString().split('T')[0].replace(/-/g, '/'),
                acquisitionCost: 0,
                depreciationMethod: '定額法',
                usefulLife: 1,
                beginningBalance: 0,
                accumulatedDepreciation: 0,
                endingBalance: 0,
                yearEndBalance: 0,
                residualValue: 0,
                notes: ''
            };

            openEditModal(newAsset, true);
        }

        // 選択項目の編集
        function editSelected() {
            const selected = document.querySelectorAll('.asset-checkbox:checked');
            if (selected.length === 0) {
                showNotification('⚠️ 編集する資産を選択してください');
                return;
            }

            if (selected.length > 1) {
                showNotification('⚠️ 編集は一度に1件ずつ行ってください');
                return;
            }

            // 選択された資産のIDを取得
            const assetId = parseInt(selected[0].value);
            const asset = sampleAssets.find(a => a.id === assetId);

            if (asset) {
                openEditModal(asset);
            } else {
                showNotification('❌ 資産データが見つかりません');
            }
        }

        // 編集モーダルを開く
        function openEditModal(asset, isNew = false) {
            const modal = document.getElementById('editAssetModal');
            const form = document.getElementById('editAssetForm');

            // フォームにデータを設定
            document.getElementById('assetName').value = asset.name;
            document.getElementById('assetCategory').value = asset.category;
            document.getElementById('assetStatus').value = asset.status;
            document.getElementById('acquisitionDate').value = asset.acquisitionDate.replace(/\//g, '-');
            document.getElementById('acquisitionCost').value = asset.acquisitionCost || '';
            document.getElementById('depreciationMethod').value = asset.depreciationMethod;
            document.getElementById('usefulLife').value = asset.usefulLife;
            document.getElementById('residualValue').value = asset.residualValue || 0;
            document.getElementById('assetNotes').value = asset.notes || '';

            // モーダルタイトルを設定
            if (isNew) {
                document.getElementById('modalTitle').textContent = '新規固定資産登録';
                form.dataset.isNew = 'true';
            } else {
                document.getElementById('modalTitle').textContent = `固定資産編集 - ${asset.name}`;
                form.dataset.isNew = 'false';
            }

            // 現在編集中の資産IDを保存
            form.dataset.assetId = asset.id;

            // モーダルを表示
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // 初期計算を実行
            if (!isNew) {
                calculateDepreciation();
            }

            showNotification(isNew ? '📝 新規登録モードを開始しました' : '📝 編集モードを開始しました');
        }

        // 編集モーダルを閉じる
        function closeEditModal() {
            const modal = document.getElementById('editAssetModal');
            const form = document.getElementById('editAssetForm');

            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';

            // フォームをリセット
            form.reset();
            document.getElementById('calculationResults').classList.add('hidden');

            showNotification('📝 編集を終了しました');
        }

        // 償却計算
        function calculateDepreciation() {
            const acquisitionCost = parseFloat(document.getElementById('acquisitionCost').value) || 0;
            const usefulLife = parseInt(document.getElementById('usefulLife').value) || 1;
            const residualValue = parseFloat(document.getElementById('residualValue').value) || 0;
            const method = document.getElementById('depreciationMethod').value;

            if (acquisitionCost <= 0) {
                document.getElementById('calculationResults').classList.add('hidden');
                return;
            }

            let annualDepreciation = 0;
            let depreciationRate = 0;

            switch (method) {
                case '定額法':
                    annualDepreciation = (acquisitionCost - residualValue) / usefulLife;
                    depreciationRate = (1 / usefulLife) * 100;
                    break;
                case '定率法':
                    depreciationRate = (1 / usefulLife) * 2 * 100; // 200%定率法
                    annualDepreciation = acquisitionCost * (depreciationRate / 100);
                    break;
                case '生産高比例法':
                    annualDepreciation = (acquisitionCost - residualValue) / usefulLife; // 簡易計算
                    depreciationRate = (1 / usefulLife) * 100;
                    break;
                case '年数合計法':
                    const totalYears = (usefulLife * (usefulLife + 1)) / 2;
                    annualDepreciation = (acquisitionCost - residualValue) * usefulLife / totalYears;
                    depreciationRate = (usefulLife / totalYears) * 100;
                    break;
            }

            const monthlyDepreciation = annualDepreciation / 12;

            // 結果を表示
            document.getElementById('annualDepreciation').textContent = `¥${Math.round(annualDepreciation).toLocaleString('ja-JP')}`;
            document.getElementById('monthlyDepreciation').textContent = `¥${Math.round(monthlyDepreciation).toLocaleString('ja-JP')}`;
            document.getElementById('depreciationRate').textContent = `${depreciationRate.toFixed(2)}%`;

            document.getElementById('calculationResults').classList.remove('hidden');
        }

        // フォーム送信処理
        function handleFormSubmit(event) {
            event.preventDefault();

            const form = document.getElementById('editAssetForm');
            const assetId = parseInt(form.dataset.assetId);
            const isNew = form.dataset.isNew === 'true';

            // フォームデータを取得
            const formData = {
                id: isNew ? getNextAssetId() : assetId,
                name: document.getElementById('assetName').value.trim(),
                category: document.getElementById('assetCategory').value,
                status: document.getElementById('assetStatus').value,
                acquisitionDate: document.getElementById('acquisitionDate').value.replace(/-/g, '/'),
                acquisitionCost: parseFloat(document.getElementById('acquisitionCost').value),
                depreciationMethod: document.getElementById('depreciationMethod').value,
                usefulLife: parseInt(document.getElementById('usefulLife').value),
                residualValue: parseFloat(document.getElementById('residualValue').value) || 0,
                notes: document.getElementById('assetNotes').value.trim()
            };

            // バリデーション
            if (!formData.name || !formData.category || !formData.acquisitionDate || !formData.acquisitionCost) {
                showNotification('❌ 必須項目を入力してください');
                return;
            }

            if (formData.acquisitionCost <= 0) {
                showNotification('❌ 取得価額は0より大きい値を入力してください');
                return;
            }

            if (formData.usefulLife <= 0) {
                showNotification('❌ 耐用年数は1年以上を入力してください');
                return;
            }

            if (isNew) {
                // 新規追加
                // 償却関連の計算を設定
                updateAssetDepreciation(formData);

                // 配列に追加
                sampleAssets.push(formData);

                showNotification('✅ 新しい資産を登録しました');
            } else {
                // 既存データを更新
                const assetIndex = sampleAssets.findIndex(a => a.id === assetId);
                if (assetIndex !== -1) {
                    Object.assign(sampleAssets[assetIndex], formData);

                    // 償却関連の計算を更新
                    updateAssetDepreciation(sampleAssets[assetIndex]);

                    showNotification('✅ 資産情報を更新しました');
                } else {
                    showNotification('❌ 更新に失敗しました');
                    return;
                }
            }

            // 表示を更新
            loadAssetData();
            updateStatistics();

            // モーダルを閉じる
            closeEditModal();
        }

        // 次の資産IDを取得
        function getNextAssetId() {
            const maxId = Math.max(...sampleAssets.map(a => a.id));
            return maxId + 1;
        }

        // 全選択/全解除
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const assetCheckboxes = document.querySelectorAll('.asset-checkbox');

            assetCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            const selectedCount = selectAllCheckbox.checked ? assetCheckboxes.length : 0;
            showNotification(selectAllCheckbox.checked ?
                `✅ ${selectedCount}件の資産を選択しました` :
                '❌ 選択を解除しました'
            );
        }

        // 資産の償却情報を更新
        function updateAssetDepreciation(asset) {
            const currentYear = new Date().getFullYear();
            const acquisitionYear = new Date(asset.acquisitionDate).getFullYear();
            const yearsElapsed = Math.max(0, currentYear - acquisitionYear);

            let annualDepreciation = 0;
            const residualValue = asset.residualValue || 0;

            switch (asset.depreciationMethod) {
                case '定額法':
                    annualDepreciation = (asset.acquisitionCost - residualValue) / asset.usefulLife;
                    break;
                case '定率法':
                    const rate = (1 / asset.usefulLife) * 2;
                    annualDepreciation = asset.acquisitionCost * rate;
                    break;
                default:
                    annualDepreciation = (asset.acquisitionCost - residualValue) / asset.usefulLife;
            }

            const totalDepreciation = Math.min(
                annualDepreciation * yearsElapsed,
                asset.acquisitionCost - residualValue
            );

            asset.accumulatedDepreciation = Math.round(totalDepreciation);
            asset.beginningBalance = asset.acquisitionCost - asset.accumulatedDepreciation;
            asset.endingBalance = Math.round(annualDepreciation);
            asset.yearEndBalance = Math.max(0, asset.acquisitionCost - asset.accumulatedDepreciation - asset.endingBalance);
        }

        // 選択項目の削除
        function deleteSelected() {
            const selected = document.querySelectorAll('.asset-checkbox:checked');
            if (selected.length === 0) {
                showNotification('⚠️ 削除する資産を選択してください');
                return;
            }

            const assetNames = Array.from(selected).map(checkbox => {
                const assetId = parseInt(checkbox.value);
                const asset = sampleAssets.find(a => a.id === assetId);
                return asset ? asset.name : 'Unknown';
            });

            const confirmMessage = `以下の${selected.length}件の資産を削除しますか？\n\n${assetNames.join('\n')}\n\n※この操作は取り消せません。`;

            if (confirm(confirmMessage)) {
                const deletedIds = Array.from(selected).map(checkbox => parseInt(checkbox.value));

                // 配列から削除
                deletedIds.forEach(id => {
                    const index = sampleAssets.findIndex(a => a.id === id);
                    if (index !== -1) {
                        sampleAssets.splice(index, 1);
                    }
                });

                // 表示を更新
                loadAssetData();
                updateStatistics();

                showNotification(`✅ ${selected.length}件の資産を削除しました`);
            }
        }

        // 資産売却
        function sellAsset() {
            alert('資産売却機能を開発中です。\n\n実装予定機能:\n• 売却価格入力\n• 売却損益計算\n• 仕訳自動生成');
        }

        // 資産廃棄
        function disposeAsset() {
            alert('資産廃棄機能を開発中です。\n\n実装予定機能:\n• 廃棄理由入力\n• 廃棄損失計算\n• 仕訳自動生成');
        }

        // 資産台帳ダウンロード
        function downloadAssetList() {
            const selected = document.querySelectorAll('.asset-checkbox:checked');
            const assetsToExport = selected.length > 0 ?
                Array.from(selected).map(cb => sampleAssets.find(a => a.id === parseInt(cb.value))).filter(Boolean) :
                sampleAssets;

            if (assetsToExport.length === 0) {
                showNotification('❌ エクスポートする資産がありません');
                return;
            }

            // CSV形式でエクスポート
            const csvContent = generateCSV(assetsToExport);
            downloadCSV(csvContent, `固定資産台帳_${new Date().toISOString().split('T')[0]}.csv`);

            showNotification(`📊 ${assetsToExport.length}件の資産をCSV形式でエクスポートしました`);
        }

        // CSV生成
        function generateCSV(assets) {
            const headers = [
                '資産ID', '資産名称', '資産科目', '使用状況', '取得年月日',
                '取得価額', '償却方法', '耐用年数', '期首未償却残高',
                '償却累計額', '期末残高', '残存価額', '備考'
            ];

            const rows = assets.map(asset => [
                asset.id,
                asset.name,
                asset.category,
                asset.status,
                asset.acquisitionDate,
                asset.acquisitionCost,
                asset.depreciationMethod,
                asset.usefulLife,
                asset.beginningBalance,
                asset.accumulatedDepreciation,
                asset.yearEndBalance,
                asset.residualValue || 0,
                asset.notes || ''
            ]);

            return [headers, ...rows].map(row =>
                row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
            ).join('\n');
        }

        // CSVダウンロード
        function downloadCSV(content, filename) {
            const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(url);
        }

        // 資産詳細表示
        function showAssetDetails(assetId) {
            const asset = sampleAssets.find(a => a.id === assetId);
            if (asset) {
                // 詳細表示の代わりに編集モーダルを開く
                openEditModal(asset);
            }
        }

        // 年度変更時の処理
        document.getElementById('yearSelect').addEventListener('change', function() {
            const selectedYear = this.value;
            console.log(`年度を${selectedYear}に変更しました`);
            // 実際の実装では、選択された年度のデータを読み込む
            loadAssetData();
        });

        // 償却済資産表示切り替え
        document.getElementById('showDepreciation').addEventListener('change', function() {
            const showDepreciated = this.checked;
            console.log(`償却済資産表示: ${showDepreciated ? 'ON' : 'OFF'}`);
            // 実際の実装では、表示フィルタリングを行う
            loadAssetData();
        });

        // 动画系统
        let animationIntervals = {
            sakura: null,
            snow: null,
            star: null
        };

        // 初始化动画系统
        function initializeAnimations() {
            // 启动默认樱花动画
            startSakuraAnimation();

            // 绑定动画切换事件
            const animationRadios = document.querySelectorAll('input[name="animation"]');
            animationRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    switchAnimation(this.value);
                });
            });

            // 绑定背景图片切换事件
            const backgroundToggle = document.getElementById('backgroundToggle');
            backgroundToggle.addEventListener('change', function() {
                toggleBackgroundImage(this.checked);
            });

            // 键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + B 切换背景图片
                if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                    e.preventDefault();
                    backgroundToggle.checked = !backgroundToggle.checked;
                    toggleBackgroundImage(backgroundToggle.checked);
                }

                // Ctrl/Cmd + Shift + A でパネル折りたたみ
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
                    e.preventDefault();
                    document.getElementById('toggleControl').click();
                }

                // Ctrl/Cmd + H でパネル表示切替
                if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
                    e.preventDefault();
                    document.getElementById('hideControl').click();
                }
            });

            // 制御パネルの折りたたみ/展開イベントをバインド
            const toggleButton = document.getElementById('toggleControl');
            const hideButton = document.getElementById('hideControl');
            const animationControl = document.getElementById('animationControl');
            const animationToggle = document.getElementById('animationToggle');
            let isCollapsed = false;
            let isHidden = false;

            toggleButton.addEventListener('click', function() {
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                    animationToggle.style.display = 'none';
                    toggleButton.textContent = '📍';
                    toggleButton.title = 'パネルを展開';
                } else {
                    animationToggle.style.display = 'flex';
                    toggleButton.textContent = '📌';
                    toggleButton.title = 'パネルを折りたたむ';
                }
            });

            // パネル非表示/表示イベント
            hideButton.addEventListener('click', function() {
                isHidden = !isHidden;
                if (isHidden) {
                    animationControl.style.display = 'none';
                    showNotification('👁️ 背景設定パネルを非表示にしました');
                } else {
                    animationControl.style.display = 'block';
                    showNotification('👁️ 背景設定パネルを表示しました');
                }
            });
        }

        // 切换动画
        function switchAnimation(type) {
            // 停止所有动画
            stopAllAnimations();

            // 隐藏所有容器
            document.getElementById('sakuraContainer').classList.add('animation-hidden');
            document.getElementById('snowContainer').classList.add('animation-hidden');
            document.getElementById('starContainer').classList.add('animation-hidden');

            // 添加切换提示
            showAnimationSwitchNotification(type);

            // 启动选中的动画
            switch(type) {
                case 'sakura':
                    document.getElementById('sakuraContainer').classList.remove('animation-hidden');
                    startSakuraAnimation();
                    break;
                case 'snow':
                    document.getElementById('snowContainer').classList.remove('animation-hidden');
                    startSnowAnimation();
                    break;
                case 'star':
                    document.getElementById('starContainer').classList.remove('animation-hidden');
                    startStarAnimation();
                    break;
                case 'none':
                    // 所有动画都已停止，不需要额外操作
                    break;
            }
        }

        // 背景画像の切り替え
        function toggleBackgroundImage(show) {
            const background = document.getElementById('sakuraBackground');
            const checkbox = document.getElementById('backgroundToggle');

            if (show) {
                // 読み込み状態を表示
                showNotification('🔄 桜背景画像を読み込み中...');

                // 画像をプリロード
                const img = new Image();
                img.onload = function() {
                    background.classList.remove('hidden');
                    showNotification('🖼️ 桜背景画像を表示しました');
                };
                img.onerror = function() {
                    checkbox.checked = false;
                    showNotification('❌ 背景画像の読み込みに失敗しました');
                };
                img.src = '/static/image/sakura.png';
            } else {
                background.classList.add('hidden');
                showNotification('🖼️ 桜背景画像を非表示にしました');
            }
        }

        // 预加载背景图片
        function preloadBackgroundImage() {
            const img = new Image();
            img.src = '/static/image/sakura.png';
        }

        // アニメーション切り替え通知を表示
        function showAnimationSwitchNotification(type) {
            const notifications = {
                'sakura': '🌸 桜の花びらアニメーションを開始しました',
                'snow': '❄️ 雪の舞アニメーションを開始しました',
                'star': '✨ 星の輝きアニメーションを開始しました',
                'none': '🚫 背景アニメーションを停止しました'
            };

            showNotification(notifications[type] || 'アニメーションモードを切り替えました');
        }

        // 通用通知函数
        function showNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: rgba(0, 0, 0, 0.85);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1001;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(4px);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 2500);
        }

        // 停止所有动画
        function stopAllAnimations() {
            Object.keys(animationIntervals).forEach(key => {
                if (animationIntervals[key]) {
                    clearInterval(animationIntervals[key]);
                    animationIntervals[key] = null;
                }
            });

            // 清除所有动画元素
            document.getElementById('sakuraContainer').innerHTML = '';
            document.getElementById('snowContainer').innerHTML = '';
            document.getElementById('starContainer').innerHTML = '';
        }

        // 樱花动画
        function startSakuraAnimation() {
            const container = document.getElementById('sakuraContainer');

            function createSakura() {
                const sakura = document.createElement('div');
                sakura.className = 'sakura';

                // 随机位置和动画参数
                sakura.style.left = Math.random() * 100 + '%';
                sakura.style.animationDuration = (Math.random() * 4 + 3) + 's';
                sakura.style.animationDelay = Math.random() * 2 + 's';

                // 随机大小和透明度
                const size = Math.random() * 15 + 15; // 15-30px
                sakura.style.fontSize = size + 'px';
                sakura.style.opacity = Math.random() * 0.4 + 0.6;

                // 随机旋转角度和摆动
                const rotation = Math.random() * 360;
                const swayAmount = Math.random() * 30 + 10; // 10-40px摆动
                sakura.style.transform = `rotate(${rotation}deg)`;
                sakura.style.setProperty('--sway-amount', swayAmount + 'px');

                container.appendChild(sakura);

                // 动画结束后移除元素
                setTimeout(() => {
                    if (sakura.parentNode) {
                        sakura.parentNode.removeChild(sakura);
                    }
                }, 7000);
            }

            // 创建初始樱花
            for (let i = 0; i < 15; i++) {
                setTimeout(createSakura, i * 250);
            }

            // 持续创建樱花
            animationIntervals.sakura = setInterval(createSakura, 350);
        }

        // 雪花动画
        function startSnowAnimation() {
            const container = document.getElementById('snowContainer');

            function createSnow() {
                const snow = document.createElement('div');
                snow.className = 'snow';

                // 随机位置和动画参数
                snow.style.left = Math.random() * 100 + '%';
                snow.style.animationDuration = (Math.random() * 4 + 3) + 's';
                snow.style.animationDelay = Math.random() * 2 + 's';

                // 随机大小和透明度
                const size = Math.random() * 10 + 12; // 12-22px
                snow.style.fontSize = size + 'px';
                snow.style.opacity = Math.random() * 0.4 + 0.6;

                // 随机旋转角度
                const rotation = Math.random() * 360;
                snow.style.transform = `rotate(${rotation}deg)`;

                container.appendChild(snow);

                // 动画结束后移除元素
                setTimeout(() => {
                    if (snow.parentNode) {
                        snow.parentNode.removeChild(snow);
                    }
                }, 7000);
            }

            // 创建初始雪花
            for (let i = 0; i < 18; i++) {
                setTimeout(createSnow, i * 200);
            }

            // 持续创建雪花
            animationIntervals.snow = setInterval(createSnow, 250);
        }

        // 星星动画
        function startStarAnimation() {
            const container = document.getElementById('starContainer');

            function createStar() {
                const star = document.createElement('div');
                star.className = 'star';

                // 随机位置和动画参数
                star.style.left = Math.random() * 100 + '%';
                star.style.animationDuration = (Math.random() * 5 + 4) + 's';
                star.style.animationDelay = Math.random() * 3 + 's';

                // 随机大小和透明度
                const size = Math.random() * 8 + 14; // 14-22px
                star.style.fontSize = size + 'px';
                star.style.opacity = Math.random() * 0.4 + 0.7;

                // 随机旋转角度
                const rotation = Math.random() * 360;
                star.style.transform = `rotate(${rotation}deg)`;

                // 随机星星emoji
                const starEmojis = ['⭐', '✨', '🌟', '💫'];
                const randomEmoji = starEmojis[Math.floor(Math.random() * starEmojis.length)];
                star.textContent = randomEmoji;

                container.appendChild(star);

                // 动画结束后移除元素
                setTimeout(() => {
                    if (star.parentNode) {
                        star.parentNode.removeChild(star);
                    }
                }, 9000);
            }

            // 创建初始星星
            for (let i = 0; i < 10; i++) {
                setTimeout(createStar, i * 300);
            }

            // 持续创建星星
            animationIntervals.star = setInterval(createStar, 500);
        }
    </script>
</body>
</html>

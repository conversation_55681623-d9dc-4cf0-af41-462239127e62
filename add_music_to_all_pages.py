#!/usr/bin/env python3
"""
GoldenLedger - 批量添加音乐功能到所有页面
自动在所有HTML页面中注入音乐控制功能
"""

import os
import re
import shutil
from pathlib import Path

class MusicInjector:
    def __init__(self):
        self.project_root = Path('.')
        self.backup_dir = Path('./backups/music_injection')
        self.music_script_tag = '<script src="/music_injector.js"></script>'
        
        # 需要添加音乐功能的页面
        self.target_pages = [
            # 主要功能页面
            'index.html',
            'master_dashboard.html', 
            'interactive_demo.html',
            'journal_entries.html',
            'financial_reports.html',
            'login_simple.html',
            'register.html',
            
            # 管理页面
            'user_management.html',
            'user_settings.html',
            'backup_management.html',
            'data_export.html',
            'data_import_tool.html',
            'fixed_assets.html',
            
            # 测试页面
            'test_multiuser.html',
            'test_gemini_api.html',
            'project_status_check.html',
            'cost_analysis_jpy.html',
            'cost_analysis_tool.html',
            'test_ocr_fix.html',
            'system_monitor.html',
            'url_directory.html',
            
            # 支付页面
            'payment.html',
            'payment-success.html',
            'payment-success-processing.html',
            'pricing.html',
            
            # 信息页面
            'api_documentation.html',
            'terms_of_service.html',
            'privacy_policy.html',
            'multilingual_interface.html',
            
            # 其他重要页面
            'advanced_dashboard.html',
            'ai_audit_system.html',
            'performance_monitoring.html',
            'realtime_monitoring.html',
            'system_overview.html'
        ]
        
        # 排除的页面（不添加音乐功能）
        self.exclude_pages = [
            'auth/error.html',
            'payment-failed.html',
            'debug_*.html',
            'test_*.html'  # 某些测试页面可能不需要
        ]

    def create_backup(self):
        """创建备份目录"""
        if not self.backup_dir.exists():
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建备份目录: {self.backup_dir}")

    def backup_file(self, file_path):
        """备份单个文件"""
        if file_path.exists():
            backup_path = self.backup_dir / file_path.name
            shutil.copy2(file_path, backup_path)
            print(f"📁 备份文件: {file_path.name}")

    def should_exclude_file(self, filename):
        """检查文件是否应该被排除"""
        for pattern in self.exclude_pages:
            if '*' in pattern:
                # 处理通配符
                regex_pattern = pattern.replace('*', '.*')
                if re.match(regex_pattern, filename):
                    return True
            elif pattern in filename:
                return True
        return False

    def has_music_script(self, content):
        """检查文件是否已经包含音乐脚本"""
        return 'music_injector.js' in content or 'music_control' in content

    def inject_music_script(self, file_path):
        """向HTML文件注入音乐脚本"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经包含音乐脚本
            if self.has_music_script(content):
                print(f"⏭️  跳过 {file_path.name} (已包含音乐功能)")
                return False
            
            # 备份原文件
            self.backup_file(file_path)
            
            # 查找合适的注入位置
            injection_patterns = [
                # 在</head>之前注入
                (r'(\s*</head>)', f'    {self.music_script_tag}\n\\1'),
                # 在其他script标签之后注入
                (r'(<script[^>]*></script>)', f'\\1\n    {self.music_script_tag}'),
                # 在</body>之前注入（备用方案）
                (r'(\s*</body>)', f'    {self.music_script_tag}\n\\1')
            ]
            
            injected = False
            for pattern, replacement in injection_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    new_content = re.sub(pattern, replacement, content, count=1, flags=re.IGNORECASE)
                    
                    # 写入修改后的内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    print(f"✅ 成功注入音乐功能: {file_path.name}")
                    injected = True
                    break
            
            if not injected:
                print(f"⚠️  无法找到合适的注入位置: {file_path.name}")
                return False
                
            return True
            
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path.name}: {e}")
            return False

    def process_all_pages(self):
        """处理所有目标页面"""
        print("🎵 开始批量添加音乐功能到所有页面...")
        print("=" * 50)
        
        # 创建备份
        self.create_backup()
        
        success_count = 0
        skip_count = 0
        error_count = 0
        
        for page_name in self.target_pages:
            file_path = self.project_root / page_name
            
            # 检查文件是否存在
            if not file_path.exists():
                print(f"⚠️  文件不存在: {page_name}")
                continue
            
            # 检查是否应该排除
            if self.should_exclude_file(page_name):
                print(f"⏭️  跳过排除的文件: {page_name}")
                skip_count += 1
                continue
            
            # 注入音乐脚本
            if self.inject_music_script(file_path):
                success_count += 1
            else:
                if not self.has_music_script(open(file_path, 'r', encoding='utf-8').read()):
                    error_count += 1
                else:
                    skip_count += 1
        
        # 输出统计结果
        print("\n" + "=" * 50)
        print("📊 处理结果统计:")
        print(f"✅ 成功注入: {success_count} 个文件")
        print(f"⏭️  跳过文件: {skip_count} 个文件")
        print(f"❌ 处理失败: {error_count} 个文件")
        print(f"📁 备份位置: {self.backup_dir}")
        
        return success_count, skip_count, error_count

    def verify_injection(self):
        """验证注入结果"""
        print("\n🔍 验证音乐功能注入结果...")
        
        verified_count = 0
        for page_name in self.target_pages:
            file_path = self.project_root / page_name
            
            if not file_path.exists():
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if self.has_music_script(content):
                    print(f"✅ {page_name} - 音乐功能已注入")
                    verified_count += 1
                else:
                    print(f"❌ {page_name} - 音乐功能未找到")
                    
            except Exception as e:
                print(f"⚠️  验证失败 {page_name}: {e}")
        
        print(f"\n📊 验证结果: {verified_count}/{len(self.target_pages)} 个页面包含音乐功能")

    def create_test_page(self):
        """创建音乐功能测试页面"""
        test_page_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_injector.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-600">🎵 音乐功能测试页面</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <ul class="list-disc list-inside space-y-2 text-gray-700">
                <li>页面右下角应该显示音乐控制按钮</li>
                <li>点击按钮可以打开音乐控制面板</li>
                <li>支持播放/暂停、音量调节、拖拽移动</li>
                <li>快捷键: Alt + M 切换播放/暂停</li>
                <li>设置会自动保存到本地存储</li>
            </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">功能检查</h2>
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-button" class="w-4 h-4">
                    <label for="check-button">音乐按钮显示正常</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-panel" class="w-4 h-4">
                    <label for="check-panel">控制面板可以打开</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-play" class="w-4 h-4">
                    <label for="check-play">音乐可以播放</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-volume" class="w-4 h-4">
                    <label for="check-volume">音量控制正常</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-drag" class="w-4 h-4">
                    <label for="check-drag">按钮可以拖拽移动</label>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试脚本
        setTimeout(() => {
            console.log('🎵 音乐功能测试页面已加载');
            console.log('🎵 音乐控制器状态:', !!window.musicController);
            console.log('🎵 音乐注入器状态:', !!window.musicInjector);
        }, 2000);
    </script>
</body>
</html>'''
        
        test_file = self.project_root / 'music_test_page.html'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_page_content)
        
        print(f"✅ 创建音乐功能测试页面: {test_file}")

    def generate_report(self):
        """生成处理报告"""
        report_content = f"""# GoldenLedger 音乐功能注入报告

## 处理时间
{__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 目标页面列表
总计: {len(self.target_pages)} 个页面

### 主要功能页面
- index.html (主页)
- master_dashboard.html (主仪表板)
- interactive_demo.html (AI演示)
- journal_entries.html (会计分录)
- financial_reports.html (财务报表)
- login_simple.html (登录)
- register.html (注册)

### 管理页面
- user_management.html (用户管理)
- user_settings.html (用户设置)
- backup_management.html (备份管理)
- data_export.html (数据导出)
- data_import_tool.html (数据导入)
- fixed_assets.html (固定资产)

### 测试和工具页面
- test_multiuser.html (多用户测试)
- test_gemini_api.html (Gemini API测试)
- project_status_check.html (项目状态检查)
- cost_analysis_jpy.html (费用分析)
- system_monitor.html (系统监控)

### 支付页面
- payment.html (支付)
- payment-success.html (支付成功)
- pricing.html (定价)

### 信息页面
- api_documentation.html (API文档)
- terms_of_service.html (服务条款)
- privacy_policy.html (隐私政策)

## 注入的功能
- 🎵 通用音乐控制器
- 🎮 可拖拽的音乐按钮
- 🔊 音量控制和设置保存
- ⌨️ 键盘快捷键支持 (Alt + M)
- 📱 响应式设计
- 💾 设置持久化存储
- 🔄 多标签页同步

## 使用方法
1. 访问任何已注入音乐功能的页面
2. 查看右下角的音乐控制按钮
3. 点击按钮打开控制面板
4. 使用 Alt + M 快捷键快速切换播放/暂停

## 测试页面
访问 music_test_page.html 进行功能测试

## 备份位置
{self.backup_dir}
"""
        
        report_file = self.project_root / 'music_injection_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 生成处理报告: {report_file}")

def main():
    """主函数"""
    print("🎵 GoldenLedger 音乐功能批量注入工具")
    print("=" * 50)
    
    injector = MusicInjector()
    
    # 处理所有页面
    success, skip, error = injector.process_all_pages()
    
    # 验证注入结果
    injector.verify_injection()
    
    # 创建测试页面
    injector.create_test_page()
    
    # 生成报告
    injector.generate_report()
    
    print("\n🎉 音乐功能批量注入完成！")
    print("\n📋 后续步骤:")
    print("1. 访问 music_test_page.html 测试音乐功能")
    print("2. 检查各个页面的音乐控制器是否正常显示")
    print("3. 确认音频文件 /music/love.mp3 可以正常访问")
    print("4. 测试快捷键 Alt + M 是否工作正常")
    
    if error > 0:
        print(f"\n⚠️  注意: {error} 个文件处理失败，请手动检查")

if __name__ == '__main__':
    main()

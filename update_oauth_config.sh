#!/bin/bash

# 更新Google OAuth配置的脚本
# 使用方法: ./update_oauth_config.sh "your-client-id" "your-client-secret"

if [ $# -ne 2 ]; then
    echo "使用方法: $0 <GOOGLE_CLIENT_ID> <GOOGLE_CLIENT_SECRET>"
    echo "示例: $0 '123456789-abc.apps.googleusercontent.com' 'GOCSPX-abcdefghijklmnop'"
    exit 1
fi

CLIENT_ID="$1"
CLIENT_SECRET="$2"
JWT_SECRET=$(openssl rand -base64 32)

echo "正在更新Cloudflare Workers环境变量..."

cd workers

# 设置生产环境变量
echo "设置生产环境变量..."
npx wrangler secret put GOOGLE_CLIENT_ID --env production <<< "$CLIENT_ID"
npx wrangler secret put GOOGLE_CLIENT_SECRET --env production <<< "$CLIENT_SECRET"
npx wrangler secret put JWT_SECRET --env production <<< "$JWT_SECRET"

# 设置开发环境变量
echo "设置开发环境变量..."
npx wrangler secret put GOOGLE_CLIENT_ID --env development <<< "$CLIENT_ID"
npx wrangler secret put GOOGLE_CLIENT_SECRET --env development <<< "$CLIENT_SECRET"
npx wrangler secret put JWT_SECRET --env development <<< "$JWT_SECRET"

echo "配置更新完成！"
echo "Client ID: $CLIENT_ID"
echo "JWT Secret已生成并设置"

# 重新部署
echo "重新部署Workers..."
npx wrangler deploy --env production

echo "部署完成！现在可以测试Google登录了。"

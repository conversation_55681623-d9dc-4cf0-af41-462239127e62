// Environment Configuration for Cloudflare Pages
// This file handles secure API key injection from environment variables only
// Version: 3.6.2 - Cache Busting Update

(function() {
    if (typeof window !== 'undefined') {
        // Force clear any cached API keys
        console.log('🔄 Clearing cached API configuration...');

        // Check if we're in local development
        const isLocalDevelopment = window.location.hostname === 'localhost' ||
                                  window.location.hostname === '127.0.0.1' ||
                                  window.location.hostname === '';

        if (isLocalDevelopment) {
            // Local development: Check for manually set key in localStorage
            const localKey = localStorage.getItem('gemini_api_key_dev');
            if (localKey && localKey !== 'YOUR_API_KEY_HERE') {
                window.GEMINI_API_KEY = localKey;
                console.log('🔑 Local development API key loaded from localStorage');
                console.log('🔑 API Key prefix:', localKey.substring(0, 10) + '...');
            } else {
                // No API key available for local development
                window.GEMINI_API_KEY = null;
                console.warn('⚠️ No API key found for local development. Please set it manually.');
                console.log('💡 Use: localStorage.setItem("gemini_api_key_dev", "YOUR_API_KEY")');
            }
        } else {
            // Production: This will be replaced by Cloudflare Pages build process
            window.GEMINI_API_KEY = 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER';
            console.log('🔑 Production API key will be injected by Cloudflare Pages - Version 3.6.7');
        }

        // Verify API key is set correctly
        if (window.GEMINI_API_KEY) {
            console.log('✅ API Key successfully configured');
        } else {
            console.error('❌ API Key configuration failed');
        }
    }
})();

/**
 * GoldenLedger - 通用音乐控制系统
 * 可在所有页面中使用的音乐控制器
 * 版本: 2.0
 */

class UniversalMusicController {
    constructor() {
        this.audio = null;
        this.isPlaying = false;
        this.volume = 0.3;
        this.storageKey = 'golden_ledger_music_settings';
        this.settingsExpiry = 30 * 24 * 60 * 60 * 1000; // 30天
        this.musicUrl = '/music/love.mp3';
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        
        this.init();
    }

    init() {
        this.createAudioElement();
        this.loadUserSettings();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeComponents();
            });
        } else {
            this.initializeComponents();
        }
    }

    createAudioElement() {
        this.audio = new Audio(this.musicUrl);
        this.audio.loop = true;
        this.audio.volume = this.volume;
        this.audio.preload = 'metadata';

        // 音频事件监听
        this.audio.addEventListener('loadstart', () => {
            console.log('🎵 开始加载音频');
        });

        this.audio.addEventListener('canplay', () => {
            console.log('🎵 音频可以播放');
        });

        this.audio.addEventListener('error', (e) => {
            console.error('🎵 音频加载错误:', e);
            this.showNotification('音频加载失败', 'error');
        });

        this.audio.addEventListener('ended', () => {
            console.log('🎵 音频播放结束');
        });
    }

    initializeComponents() {
        console.log('🎵 初始化通用音乐控制器...');
        
        this.createMusicControl();
        this.setupEventListeners();
        this.loadButtonPosition();

        // 验证创建结果
        setTimeout(() => {
            const musicController = document.getElementById('music-controller');
            const musicBtn = document.getElementById('music-toggle');
            console.log('🎵 验证创建结果:');
            console.log('  - 音乐控制器元素:', !!musicController);
            console.log('  - 音乐按钮元素:', !!musicBtn);
        }, 100);
    }

    createMusicControl() {
        // 检查是否已存在控制器
        if (document.getElementById('music-controller')) {
            console.log('🎵 音乐控制器已存在，跳过创建');
            return;
        }

        const controllerHTML = `
            <div id="music-controller" class="fixed z-50 select-none" style="right: 20px; bottom: 40px;">
                <!-- 主音乐按钮 -->
                <div class="relative">
                    <button id="music-toggle" 
                            class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 
                                   text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 
                                   flex items-center justify-center group transform hover:scale-105"
                            title="音乐控制">
                        <span id="music-icon" class="text-xl">🎵</span>
                    </button>
                    
                    <!-- 状态指示器 -->
                    <div id="music-status" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white opacity-0 transition-opacity duration-300"></div>
                </div>

                <!-- 控制面板 -->
                <div id="music-panel" class="absolute bottom-16 right-0 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-64 transform scale-0 origin-bottom-right transition-transform duration-300">
                    <!-- 标题 -->
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800 flex items-center">
                            <span class="mr-2">🎵</span>
                            音乐控制
                        </h3>
                        <button id="close-panel" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- 播放控制 -->
                    <div class="flex items-center justify-center space-x-4 mb-4">
                        <button id="play-pause" class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-105">
                            <span id="play-icon">▶️</span>
                        </button>
                        <button id="stop-music" class="w-10 h-10 bg-gray-500 hover:bg-gray-600 text-white rounded-full flex items-center justify-center transition-all duration-300">
                            <span>⏹️</span>
                        </button>
                    </div>

                    <!-- 音量控制 -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <label class="text-sm text-gray-600">音量</label>
                            <span id="volume-display" class="text-sm text-gray-500">30%</span>
                        </div>
                        <input type="range" id="volume-slider" min="0" max="100" value="30" 
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider">
                    </div>

                    <!-- 当前状态 -->
                    <div class="text-center">
                        <div id="current-status" class="text-sm text-gray-500 mb-2">🎵 准备就绪</div>
                        <div class="text-xs text-gray-400">GoldenLedger BGM</div>
                    </div>

                    <!-- 设置选项 -->
                    <div class="border-t border-gray-200 pt-3 mt-3">
                        <div class="flex items-center justify-between text-sm">
                            <label class="text-gray-600">自动播放</label>
                            <input type="checkbox" id="auto-play" class="toggle-checkbox">
                        </div>
                    </div>
                </div>

                <!-- 拖拽提示 -->
                <div id="drag-hint" class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded opacity-0 transition-opacity duration-300 whitespace-nowrap">
                    拖拽移动位置
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', controllerHTML);

        // 添加样式
        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('universal-music-styles')) {
            return;
        }

        const styles = `
            <style id="universal-music-styles">
                /* 音乐控制器样式 */
                #music-controller {
                    user-select: none;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                }

                #music-controller.dragging {
                    cursor: grabbing !important;
                }

                #music-controller:hover #drag-hint {
                    opacity: 1;
                }

                /* 滑块样式 */
                .slider::-webkit-slider-thumb {
                    appearance: none;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    cursor: pointer;
                    border: 2px solid white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }

                .slider::-moz-range-thumb {
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    cursor: pointer;
                    border: 2px solid white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }

                /* 切换开关样式 */
                .toggle-checkbox {
                    appearance: none;
                    width: 40px;
                    height: 20px;
                    background: #ddd;
                    border-radius: 10px;
                    position: relative;
                    cursor: pointer;
                    transition: background 0.3s;
                }

                .toggle-checkbox:checked {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                .toggle-checkbox::before {
                    content: '';
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    border-radius: 50%;
                    background: white;
                    top: 2px;
                    left: 2px;
                    transition: transform 0.3s;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }

                .toggle-checkbox:checked::before {
                    transform: translateX(20px);
                }

                /* 动画效果 */
                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                }

                .pulse {
                    animation: pulse 2s infinite;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    #music-controller {
                        right: 10px !important;
                        bottom: 10px !important;
                    }
                    
                    #music-panel {
                        width: 240px;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    setupEventListeners() {
        const musicToggle = document.getElementById('music-toggle');
        const musicPanel = document.getElementById('music-panel');
        const playPause = document.getElementById('play-pause');
        const stopMusic = document.getElementById('stop-music');
        const volumeSlider = document.getElementById('volume-slider');
        const closePanel = document.getElementById('close-panel');
        const autoPlay = document.getElementById('auto-play');
        const controller = document.getElementById('music-controller');

        if (!musicToggle) {
            console.error('🎵 音乐控制元素未找到');
            return;
        }

        // 主按钮点击 - 切换面板显示
        musicToggle.addEventListener('click', (e) => {
            e.stopPropagation();
            this.togglePanel();
        });

        // 播放/暂停
        if (playPause) {
            playPause.addEventListener('click', () => {
                this.togglePlayPause();
            });
        }

        // 停止
        if (stopMusic) {
            stopMusic.addEventListener('click', () => {
                this.stopMusic();
            });
        }

        // 音量控制
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => {
                this.setVolume(e.target.value / 100);
            });
        }

        // 关闭面板
        if (closePanel) {
            closePanel.addEventListener('click', () => {
                this.hidePanel();
            });
        }

        // 自动播放设置
        if (autoPlay) {
            autoPlay.addEventListener('change', (e) => {
                this.saveUserSettings();
            });
        }

        // 点击其他地方关闭面板
        document.addEventListener('click', (e) => {
            if (!controller.contains(e.target)) {
                this.hidePanel();
            }
        });

        // 拖拽功能
        this.setupDragFunctionality();

        // 键盘快捷键
        this.setupKeyboardShortcuts();
    }

    setupDragFunctionality() {
        const controller = document.getElementById('music-controller');
        const musicToggle = document.getElementById('music-toggle');

        if (!controller || !musicToggle) return;

        let startX, startY, initialX, initialY;

        musicToggle.addEventListener('mousedown', (e) => {
            if (e.button !== 0) return; // 只响应左键

            this.isDragging = true;
            controller.classList.add('dragging');

            startX = e.clientX;
            startY = e.clientY;
            initialX = controller.offsetLeft;
            initialY = controller.offsetTop;

            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!this.isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            let newX = initialX + deltaX;
            let newY = initialY + deltaY;

            // 边界检查
            const maxX = window.innerWidth - controller.offsetWidth;
            const maxY = window.innerHeight - controller.offsetHeight;

            newX = Math.max(0, Math.min(newX, maxX));
            newY = Math.max(0, Math.min(newY, maxY));

            controller.style.left = newX + 'px';
            controller.style.top = newY + 'px';
            controller.style.right = 'auto';
            controller.style.bottom = 'auto';
        });

        document.addEventListener('mouseup', () => {
            if (this.isDragging) {
                this.isDragging = false;
                controller.classList.remove('dragging');
                this.saveButtonPosition();
            }
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl + M 切换播放/暂停
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                this.togglePlayPause();
            }
            
            // Ctrl + Shift + M 显示/隐藏面板
            if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                e.preventDefault();
                this.togglePanel();
            }
        });
    }

    togglePanel() {
        const panel = document.getElementById('music-panel');
        if (!panel) return;

        const isVisible = panel.style.transform === 'scale(1)';
        
        if (isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }

    showPanel() {
        const panel = document.getElementById('music-panel');
        if (panel) {
            panel.style.transform = 'scale(1)';
            panel.style.opacity = '1';
        }
    }

    hidePanel() {
        const panel = document.getElementById('music-panel');
        if (panel) {
            panel.style.transform = 'scale(0)';
            panel.style.opacity = '0';
        }
    }

    togglePlayPause() {
        if (this.isPlaying) {
            this.pauseMusic();
        } else {
            this.playMusic();
        }
    }

    async playMusic() {
        try {
            await this.audio.play();
            this.isPlaying = true;
            this.updateUI();
            this.updateStatus('🎵 播放中...');
            this.saveUserSettings();
            console.log('🎵 音乐开始播放');
        } catch (error) {
            console.error('🎵 播放失败:', error);
            this.showNotification('播放失败，请检查音频文件', 'error');
        }
    }

    pauseMusic() {
        this.audio.pause();
        this.isPlaying = false;
        this.updateUI();
        this.updateStatus('⏸️ 已暂停');
        this.saveUserSettings();
        console.log('🎵 音乐已暂停');
    }

    stopMusic() {
        this.audio.pause();
        this.audio.currentTime = 0;
        this.isPlaying = false;
        this.updateUI();
        this.updateStatus('⏹️ 已停止');
        this.saveUserSettings();
        console.log('🎵 音乐已停止');
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.audio.volume = this.volume;
        
        const volumeDisplay = document.getElementById('volume-display');
        if (volumeDisplay) {
            volumeDisplay.textContent = Math.round(this.volume * 100) + '%';
        }
        
        this.saveUserSettings();
        console.log('🎵 音量设置为:', Math.round(this.volume * 100) + '%');
    }

    updateUI() {
        const musicIcon = document.getElementById('music-icon');
        const playIcon = document.getElementById('play-icon');
        const musicStatus = document.getElementById('music-status');
        const musicToggle = document.getElementById('music-toggle');

        if (this.isPlaying) {
            if (musicIcon) musicIcon.textContent = '🎵';
            if (playIcon) playIcon.textContent = '⏸️';
            if (musicStatus) {
                musicStatus.style.opacity = '1';
                musicStatus.classList.add('pulse');
            }
            if (musicToggle) musicToggle.classList.add('pulse');
        } else {
            if (musicIcon) musicIcon.textContent = '🎵';
            if (playIcon) playIcon.textContent = '▶️';
            if (musicStatus) {
                musicStatus.style.opacity = '0';
                musicStatus.classList.remove('pulse');
            }
            if (musicToggle) musicToggle.classList.remove('pulse');
        }
    }

    updateStatus(status) {
        const statusElement = document.getElementById('current-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
        
        const bgColor = type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        notification.className += ` ${bgColor} text-white`;
        
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <span>${type === 'error' ? '❌' : 'ℹ️'}</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    saveUserSettings() {
        const settings = {
            isPlaying: this.isPlaying,
            volume: this.volume,
            position: {
                left: document.getElementById('music-controller').style.left,
                top: document.getElementById('music-controller').style.top,
                right: document.getElementById('music-controller').style.right,
                bottom: document.getElementById('music-controller').style.bottom
            },
            autoPlay: document.getElementById('auto-play')?.checked || false,
            timestamp: Date.now()
        };

        localStorage.setItem(this.storageKey, JSON.stringify(settings));
    }

    loadUserSettings() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (!saved) return;

            const settings = JSON.parse(saved);
            
            // 检查设置是否过期
            if (Date.now() - settings.timestamp > this.settingsExpiry) {
                localStorage.removeItem(this.storageKey);
                return;
            }

            this.volume = settings.volume || 0.3;
            if (this.audio) {
                this.audio.volume = this.volume;
            }

            // 延迟应用设置，确保DOM已加载
            setTimeout(() => {
                const volumeSlider = document.getElementById('volume-slider');
                const volumeDisplay = document.getElementById('volume-display');
                const autoPlay = document.getElementById('auto-play');

                if (volumeSlider) volumeSlider.value = this.volume * 100;
                if (volumeDisplay) volumeDisplay.textContent = Math.round(this.volume * 100) + '%';
                if (autoPlay) autoPlay.checked = settings.autoPlay;

                // 如果启用了自动播放且之前在播放
                if (settings.autoPlay && settings.isPlaying) {
                    this.playMusic();
                }
            }, 500);

        } catch (error) {
            console.error('🎵 加载用户设置失败:', error);
        }
    }

    saveButtonPosition() {
        const controller = document.getElementById('music-controller');
        if (!controller) return;

        const settings = JSON.parse(localStorage.getItem(this.storageKey) || '{}');
        settings.position = {
            left: controller.style.left,
            top: controller.style.top,
            right: controller.style.right,
            bottom: controller.style.bottom
        };
        settings.timestamp = Date.now();

        localStorage.setItem(this.storageKey, JSON.stringify(settings));
    }

    loadButtonPosition() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (!saved) return;

            const settings = JSON.parse(saved);
            if (!settings.position) return;

            const controller = document.getElementById('music-controller');
            if (!controller) return;

            const pos = settings.position;
            if (pos.left && pos.left !== 'auto') controller.style.left = pos.left;
            if (pos.top && pos.top !== 'auto') controller.style.top = pos.top;
            if (pos.right && pos.right !== 'auto') controller.style.right = pos.right;
            if (pos.bottom && pos.bottom !== 'auto') controller.style.bottom = pos.bottom;

        } catch (error) {
            console.error('🎵 加载按钮位置失败:', error);
        }
    }

    // 销毁方法，用于页面卸载时清理
    destroy() {
        if (this.audio) {
            this.audio.pause();
            this.audio = null;
        }

        const controller = document.getElementById('music-controller');
        if (controller) {
            controller.remove();
        }

        const styles = document.getElementById('universal-music-styles');
        if (styles) {
            styles.remove();
        }
    }
}

// 全局实例
let globalMusicController = null;

// 初始化函数
function initUniversalMusicController() {
    if (!globalMusicController) {
        globalMusicController = new UniversalMusicController();
        
        // 将控制器实例添加到全局对象，方便调试
        window.musicController = globalMusicController;
        
        console.log('🎵 通用音乐控制器已初始化');
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (globalMusicController) {
        globalMusicController.destroy();
    }
});

// 自动初始化
if (typeof window !== 'undefined') {
    initUniversalMusicController();
}

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UniversalMusicController;
}

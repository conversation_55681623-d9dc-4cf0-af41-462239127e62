<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <title>📱 手机快速测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            color: white;
            text-align: center;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px 20px;
            color: #333;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #4CAF50;
        }
        
        .success {
            background: #E8F5E8;
            color: #2E7D32;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 15px 0;
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .test-button:hover, .test-button:active {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
        }
        
        .test-button.preview {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .info {
            background: #F5F5F5;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-size: 14px;
        }
        
        code {
            background: #E0E0E0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 手机连接成功！</h1>
        
        <div class="success">
            ✅ 恭喜！您的手机已成功连接到goldenledger会計系统！
        </div>
        
        <div class="info">
            <strong>🌐 网络信息</strong><br>
            服务器IP: <code>************</code><br>
            端口: <code>8000</code><br>
            状态: <span style="color: #4CAF50;">✅ 在线</span>
        </div>
        
        <h3>🎯 快速测试</h3>
        
        <a href="http://************:8000/health" class="test-button" target="_blank">
            🔍 健康检查
        </a>
        
        <a href="http://************:8000/journal_entries.html" class="test-button" target="_blank">
            📊 记账页面
        </a>
        
        <a href="http://************:8000/attachments/J20250711211125?preview=true" 
           class="test-button preview" target="_blank">
            🖼️ 附件预览测试
        </a>
        
        <button class="test-button" onclick="testPreview()">
            📎 测试预览功能
        </button>
        
        <div id="result"></div>
        
        <div class="info" style="margin-top: 30px;">
            <small>
                如果所有测试都正常，说明手机端功能完全可用！<br>
                现在可以正常使用goldenledger会計系统了。
            </small>
        </div>
    </div>

    <script>
        function testPreview() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div style="background: #E3F2FD; color: #1565C0; padding: 15px; border-radius: 10px; margin: 15px 0;">🔍 正在测试附件预览...</div>';
            
            // 直接打开预览
            const previewUrl = 'http://************:8000/attachments/J20250711211125?preview=true';
            const newWindow = window.open(previewUrl, '_blank');
            
            if (newWindow) {
                resultDiv.innerHTML = '<div style="background: #E8F5E8; color: #2E7D32; padding: 15px; border-radius: 10px; margin: 15px 0;">✅ 预览窗口已打开！<br>如果看到图片显示，说明功能正常。</div>';
            } else {
                resultDiv.innerHTML = '<div style="background: #FFEBEE; color: #C62828; padding: 15px; border-radius: 10px; margin: 15px 0;">⚠️ 无法打开预览窗口<br>请在浏览器设置中允许弹窗。</div>';
            }
        }
        
        // 添加触摸反馈
        document.querySelectorAll('.test-button').forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            button.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>

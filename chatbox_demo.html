<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sakura Chatbox Demo - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inline CSS for demo -->
    <style>
        /* Import Sakura Chatbox styles */
        :root {
            --sakura-pink: #FF69B4;
            --sakura-light-pink: #FFB6C1;
            --sakura-green: #98FB98;
            --sakura-gold: #FFD700;
            --sakura-white: #FFFFFF;
            --sakura-gray: #F8F9FA;
            --sakura-text: #333333;
            --sakura-shadow: rgba(255, 105, 180, 0.2);
            --sakura-border-radius: 16px;
        }

        .chatbox-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 380px;
            height: 600px;
            background: linear-gradient(135deg, var(--sakura-pink) 0%, var(--sakura-light-pink) 50%, var(--sakura-green) 100%);
            border-radius: var(--sakura-border-radius);
            box-shadow: 0 20px 40px var(--sakura-shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .chatbox-header {
            background: linear-gradient(135deg, var(--sakura-pink), var(--sakura-light-pink));
            padding: 16px 20px;
            border-radius: var(--sakura-border-radius) var(--sakura-border-radius) 0 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .avatar-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sakura-avatar {
            width: 40px;
            height: 40px;
            background: var(--sakura-white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .sakura-icon {
            width: 24px;
            height: 24px;
            color: var(--sakura-pink);
        }

        .user-info {
            color: var(--sakura-white);
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            line-height: 1.2;
        }

        .online-status {
            font-size: 12px;
            opacity: 0.9;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            color: var(--sakura-white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .control-btn svg {
            width: 16px;
            height: 16px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: var(--sakura-white);
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message-container {
            display: flex;
            gap: 12px;
        }

        .message-avatar {
            flex-shrink: 0;
        }

        .sakura-avatar-small {
            width: 32px;
            height: 32px;
            background: var(--sakura-white);
            border: 2px solid var(--sakura-pink);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sakura-icon-small {
            width: 16px;
            height: 16px;
            color: var(--sakura-pink);
        }

        .message-bubble {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .ai-bubble {
            background: var(--sakura-gray);
            color: var(--sakura-text);
            border-bottom-left-radius: 6px;
        }

        .quick-actions {
            padding: 16px 20px;
            background: var(--sakura-white);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-btn {
            background: linear-gradient(135deg, var(--sakura-gold), #FFA500);
            color: var(--sakura-white);
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
        }

        .input-area {
            padding: 16px 20px;
            background: var(--sakura-white);
            border-top: 1px solid rgba(255, 105, 180, 0.1);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid var(--sakura-gray);
            border-radius: 24px;
            font-size: 16px;
            outline: none;
            transition: all 0.2s ease;
            background: var(--sakura-gray);
        }

        .message-input:focus {
            border-color: var(--sakura-pink);
            background: var(--sakura-white);
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: var(--sakura-pink);
            border: none;
            border-radius: 50%;
            color: var(--sakura-white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px var(--sakura-shadow);
        }

        .send-btn:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 6px 16px var(--sakura-shadow);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .send-btn svg {
            width: 20px;
            height: 20px;
        }

        .minimized {
            transform: scale(0);
            opacity: 0;
            pointer-events: none;
        }

        .floating-chat-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--sakura-pink), var(--sakura-light-pink));
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 24px var(--sakura-shadow);
            transition: all 0.3s ease;
            z-index: 999;
        }

        .floating-chat-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 32px var(--sakura-shadow);
        }

        @media (max-width: 768px) {
            .chatbox-container {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                border-radius: 0;
            }

            .chatbox-header {
                border-radius: 0;
            }
        }
    </style>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            padding: 24px;
            border-radius: 12px;
            text-align: center;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 8px;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255, 105, 180, 0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #00FF00; }
        .status-offline { background: #FF4444; }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="demo-card">
            <h1 class="text-4xl font-bold text-center mb-4">🌸 Sakura Chatbox Demo</h1>
            <p class="text-xl text-center text-gray-600 mb-8">
                GoldenLedger AI会計アシスタント - 基于jiajibo项目的优秀设计
            </p>
            
            <!-- Status -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center bg-green-100 text-green-800 px-4 py-2 rounded-full">
                    <span class="status-indicator status-online"></span>
                    <span id="system-status">システム正常稼働中</span>
                </div>
            </div>

            <!-- Demo Controls -->
            <div class="text-center">
                <button class="demo-btn" onclick="showChatbox()">
                    💬 Chatboxを表示
                </button>
                <button class="demo-btn" onclick="hideChatbox()">
                    🙈 Chatboxを非表示
                </button>
                <button class="demo-btn" onclick="minimizeChatbox()">
                    ➖ 最小化
                </button>
                <button class="demo-btn" onclick="testQuickActions()">
                    ⚡ クイックアクション
                </button>
                <button class="demo-btn" onclick="clearChat()">
                    🗑️ チャット履歴クリア
                </button>
            </div>
        </div>

        <!-- Features -->
        <div class="demo-card">
            <h2 class="text-2xl font-bold mb-6">✨ 主要機能</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3 class="text-xl font-bold mb-2">🎨 美しいデザイン</h3>
                    <p>jiajibo项目の優秀なデザインを基に、現代的で美しいUIを実現</p>
                </div>
                <div class="feature-card">
                    <h3 class="text-xl font-bold mb-2">🤖 AI会計アシスタント</h3>
                    <p>Gemini AIを活用した専門的な会計相談とアドバイス</p>
                </div>
                <div class="feature-card">
                    <h3 class="text-xl font-bold mb-2">⚡ クイックアクション</h3>
                    <p>よく使う機能への素早いアクセスボタン</p>
                </div>
                <div class="feature-card">
                    <h3 class="text-xl font-bold mb-2">📱 レスポンシブ</h3>
                    <p>デスクトップとモバイルの両方で完璧に動作</p>
                </div>
            </div>
        </div>

        <!-- Technical Details -->
        <div class="demo-card">
            <h2 class="text-2xl font-bold mb-6">🔧 技術詳細</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">フロントエンド</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li>• 原生JavaScript ES6+</li>
                        <li>• Tailwind CSS</li>
                        <li>• CSS3 Animations</li>
                        <li>• Responsive Design</li>
                        <li>• Local Storage</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">バックエンド</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li>• Cloudflare Workers</li>
                        <li>• D1 Database</li>
                        <li>• Google Gemini API</li>
                        <li>• RESTful API</li>
                        <li>• JWT Authentication</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- API Examples -->
        <div class="demo-card">
            <h2 class="text-2xl font-bold mb-6">📡 API使用例</h2>
            
            <h3 class="text-lg font-semibold mb-2">チャットメッセージ送信</h3>
            <div class="code-block">
POST /api/chat/send
{
  "messages": [
    {"role": "user", "content": "今月の支出を教えて"}
  ],
  "model": "gemini-2.0-flash-exp"
}
            </div>

            <h3 class="text-lg font-semibold mb-2">月次支出データ取得</h3>
            <div class="code-block">
POST /api/financial/monthly-expenses
{
  "year": 2024,
  "month": 12
}
            </div>

            <h3 class="text-lg font-semibold mb-2">予算分析</h3>
            <div class="code-block">
GET /api/financial/budget-analysis
Authorization: Bearer {token}
            </div>
        </div>

        <!-- Performance Stats -->
        <div class="demo-card">
            <h2 class="text-2xl font-bold mb-6">📊 パフォーマンス統計</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600" id="load-time">< 1s</div>
                    <div class="text-sm text-gray-600">読み込み時間</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600" id="response-time">< 3s</div>
                    <div class="text-sm text-gray-600">AI応答時間</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600" id="message-count">0</div>
                    <div class="text-sm text-gray-600">メッセージ数</div>
                </div>
                <div class="text-center p-4 bg-pink-50 rounded-lg">
                    <div class="text-2xl font-bold text-pink-600" id="uptime">99.9%</div>
                    <div class="text-sm text-gray-600">稼働率</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Chatbox -->
    <div id="chatbox-container">
        <!-- Chatbox Container -->
        <div id="sakura-chatbox" class="chatbox-container" style="display: flex;">
            <!-- Header -->
            <div class="chatbox-header">
                <div class="header-content">
                    <div class="avatar-section">
                        <div class="sakura-avatar">
                            <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                            </svg>
                        </div>
                        <div class="user-info">
                            <h3 class="user-name">さくらちゃん</h3>
                            <span class="online-status">オンライン</span>
                        </div>
                    </div>
                    <div class="header-controls">
                        <button id="minimize-btn" class="control-btn" title="最小化">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </button>
                        <button id="close-btn" class="control-btn" title="閉じる">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div class="chat-messages" id="chat-messages">
                <!-- Welcome Message -->
                <div class="message-container ai-message">
                    <div class="message-avatar">
                        <div class="sakura-avatar-small">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble ai-bubble">
                        <p>こんにちは！さくらちゃんです🌸</p>
                        <p>家計管理のお手伝いをさせていただきます。何でもお気軽にご相談ください！</p>
                    </div>
                </div>
            </div>

            <!-- Quick Action Buttons -->
            <div class="quick-actions" id="quick-actions">
                <button class="quick-btn" data-action="monthly-expenses">
                    今月の支出を教えて
                </button>
                <button class="quick-btn" data-action="bookkeeping-tips">
                    仕訳のコツは？
                </button>
                <button class="quick-btn" data-action="budget-review">
                    予算を見直したい
                </button>
                <button class="quick-btn" data-action="seasonal-advice">
                    季節のアドバイス
                </button>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <div class="input-container">
                    <input
                        type="text"
                        id="message-input"
                        class="message-input"
                        placeholder="メッセージを入力..."
                        maxlength="500"
                    >
                    <button id="send-btn" class="send-btn" disabled>
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typing-indicator" style="display: none;">
                <div class="message-container ai-message">
                    <div class="message-avatar">
                        <div class="sakura-avatar-small">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble ai-bubble typing-bubble">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Chat Button (when minimized) -->
        <div id="floating-chat-btn" class="floating-chat-btn" style="display: none;">
            <div class="sakura-avatar">
                <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                </svg>
            </div>
            <div class="notification-badge" id="notification-badge" style="display: none;">1</div>
        </div>
    </div>

    <!-- Inline Scripts for Demo -->
    <script>
        // Simple chatbox functionality for demo
        class SimpleChatbox {
            constructor() {
                this.isMinimized = false;
                this.messageCount = 0;
                this.initializeElements();
                this.bindEvents();
                console.log('🌸 Simple Chatbox initialized');
            }

            initializeElements() {
                this.chatbox = document.getElementById('sakura-chatbox');
                this.floatingBtn = document.getElementById('floating-chat-btn');
                this.messagesContainer = document.getElementById('chat-messages');
                this.messageInput = document.getElementById('message-input');
                this.sendBtn = document.getElementById('send-btn');
                this.quickBtns = document.querySelectorAll('.quick-btn');
            }

            bindEvents() {
                // Message input
                this.messageInput?.addEventListener('input', () => this.handleInputChange());
                this.messageInput?.addEventListener('keypress', (e) => this.handleKeyPress(e));
                this.sendBtn?.addEventListener('click', () => this.sendMessage());

                // Quick actions
                this.quickBtns?.forEach(btn => {
                    btn.addEventListener('click', () => this.handleQuickAction(btn.dataset.action));
                });
            }

            handleInputChange() {
                const hasText = this.messageInput.value.trim().length > 0;
                this.sendBtn.disabled = !hasText;
            }

            handleKeyPress(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            }

            sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Clear input
                this.messageInput.value = '';
                this.handleInputChange();

                // Add user message
                this.addMessage(message, 'user');

                // Simulate AI response
                setTimeout(() => {
                    const responses = [
                        'ありがとうございます！そのご質問についてお答えします🌸',
                        '家計管理について詳しく説明させていただきますね。',
                        'とても良いご質問ですね！一緒に考えてみましょう。',
                        'さくらちゃんがお手伝いします！具体的にどのような点でお困りですか？'
                    ];
                    const response = responses[Math.floor(Math.random() * responses.length)];
                    this.addMessage(response, 'ai');
                }, 1000);
            }

            addMessage(content, type = 'ai') {
                const messageContainer = document.createElement('div');
                messageContainer.className = `message-container ${type}-message`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';

                if (type === 'ai') {
                    avatar.innerHTML = `
                        <div class="sakura-avatar-small">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                            </svg>
                        </div>
                    `;
                } else {
                    avatar.innerHTML = `
                        <div class="sakura-avatar-small" style="background: #667eea; border-color: #667eea;">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="white">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                    `;
                }

                const bubble = document.createElement('div');
                bubble.className = `message-bubble ${type}-bubble`;
                if (type === 'user') {
                    bubble.style.background = '#667eea';
                    bubble.style.color = 'white';
                    bubble.style.borderBottomRightRadius = '6px';
                    bubble.style.borderBottomLeftRadius = '18px';
                }
                bubble.textContent = content;

                messageContainer.appendChild(avatar);
                messageContainer.appendChild(bubble);

                this.messagesContainer.appendChild(messageContainer);
                this.scrollToBottom();

                this.messageCount++;
            }

            handleQuickAction(action) {
                const actionMessages = {
                    'monthly-expenses': '今月の支出を教えて',
                    'bookkeeping-tips': '仕訳のコツは？',
                    'budget-review': '予算を見直したい',
                    'seasonal-advice': '季節のアドバイス'
                };

                const message = actionMessages[action];
                if (message) {
                    this.messageInput.value = message;
                    this.sendMessage();
                }
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                }, 100);
            }

            minimize() {
                this.isMinimized = true;
                this.chatbox.classList.add('minimized');
                this.floatingBtn.style.display = 'flex';
            }

            restore() {
                this.isMinimized = false;
                this.chatbox.classList.remove('minimized');
                this.floatingBtn.style.display = 'none';
                setTimeout(() => {
                    this.messageInput?.focus();
                }, 300);
            }

            close() {
                this.chatbox.style.display = 'none';
                this.floatingBtn.style.display = 'flex';
            }

            clearMessages() {
                // Keep welcome message
                const welcomeMessage = this.messagesContainer.querySelector('.message-container');
                this.messagesContainer.innerHTML = '';
                if (welcomeMessage) {
                    this.messagesContainer.appendChild(welcomeMessage);
                }
                this.messageCount = 0;
            }

            getMessageCount() {
                return this.messageCount;
            }
        }

        // Initialize simple chatbox
        window.simpleChatbox = null;
    </script>

    <script>
        // Demo functions
        function showChatbox() {
            const chatbox = document.getElementById('sakura-chatbox');
            if (chatbox) {
                chatbox.style.display = 'flex';
                chatbox.classList.remove('minimized');
                if (window.simpleChatbox) {
                    window.simpleChatbox.restore();
                }
            }
        }

        function hideChatbox() {
            if (window.simpleChatbox) {
                window.simpleChatbox.close();
            }
        }

        function minimizeChatbox() {
            if (window.simpleChatbox) {
                window.simpleChatbox.minimize();
            }
        }

        function testQuickActions() {
            showChatbox();
            if (window.simpleChatbox) {
                setTimeout(() => {
                    window.simpleChatbox.handleQuickAction('monthly-expenses');
                }, 500);
            }
        }

        function clearChat() {
            if (window.simpleChatbox) {
                window.simpleChatbox.clearMessages();
                alert('チャット履歴をクリアしました');
            }
        }

        // Update stats
        function updateStats() {
            if (window.simpleChatbox) {
                document.getElementById('message-count').textContent =
                    window.simpleChatbox.getMessageCount();
            }
        }

        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize simple chatbox
            console.log('🌸 Initializing Sakura Chatbox Demo...');

            try {
                window.simpleChatbox = new SimpleChatbox();
                console.log('🌸 Simple Chatbox initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize chatbox:', error);
            }

            // Update stats periodically
            setInterval(updateStats, 5000);

            console.log('🌸 Sakura Chatbox Demo ready');
        });

        // System status check
        async function checkSystemStatus() {
            try {
                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/health');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('system-status').textContent = 'システム正常稼働中';
                    document.querySelector('.status-indicator').className = 'status-indicator status-online';
                } else {
                    throw new Error('System check failed');
                }
            } catch (error) {
                document.getElementById('system-status').textContent = 'システム異常';
                document.querySelector('.status-indicator').className = 'status-indicator status-offline';
            }
        }

        // Check system status on load
        checkSystemStatus();
        setInterval(checkSystemStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>

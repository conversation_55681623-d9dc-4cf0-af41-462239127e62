<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403错误调试工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-red-600">🔍 403 Forbidden 错误调试</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 步骤1: 获取Token -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">步骤1: 获取认证Token</h2>
                <div class="space-y-4">
                    <button onclick="getTokenFromStorage()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 w-full">
                        从本地存储获取Token
                    </button>
                    <div>
                        <label class="block text-sm font-medium mb-2">Session Token:</label>
                        <textarea id="sessionToken" class="w-full p-2 border rounded text-xs h-20" placeholder="Token将显示在这里"></textarea>
                    </div>
                    <div id="token-status" class="p-3 bg-gray-50 rounded"></div>
                </div>
            </div>
            
            <!-- 步骤2: 验证认证 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">步骤2: 验证认证状态</h2>
                <div class="space-y-4">
                    <button onclick="verifyAuth()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        验证认证状态
                    </button>
                    <div id="auth-status" class="p-3 bg-gray-50 rounded min-h-20"></div>
                </div>
            </div>
            
            <!-- 步骤3: 检查AI使用权限 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">步骤3: 检查AI使用权限</h2>
                <div class="space-y-4">
                    <button onclick="checkAIPermissions()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 w-full">
                        检查AI权限
                    </button>
                    <div id="ai-permissions" class="p-3 bg-gray-50 rounded min-h-20"></div>
                </div>
            </div>
            
            <!-- 步骤4: 详细错误分析 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">步骤4: 详细错误分析</h2>
                <div class="space-y-4">
                    <button onclick="analyzeError()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 w-full">
                        分析403错误
                    </button>
                    <div id="error-analysis" class="p-3 bg-gray-50 rounded min-h-20"></div>
                </div>
            </div>
        </div>
        
        <!-- 完整测试 -->
        <div class="bg-white p-6 rounded-lg shadow mt-6">
            <h2 class="text-xl font-semibold mb-4">🧪 完整AI处理测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">测试文本:</label>
                    <input type="text" id="testText" class="w-full p-2 border rounded" value="今天买了办公用品100元" placeholder="输入要处理的文本">
                </div>
                <div class="flex items-end">
                    <button onclick="testAIProcessingDetailed()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 w-full">
                        详细测试AI处理
                    </button>
                </div>
            </div>
            <div id="ai-test-result" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
        </div>
        
        <!-- 日志输出 -->
        <div class="bg-black text-green-400 p-6 rounded-lg shadow mt-6">
            <h2 class="text-xl font-semibold mb-4 text-white">📋 调试日志</h2>
            <div id="debug-log" class="font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto"></div>
            <button onclick="clearLog()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                清空日志
            </button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const colors = {
                info: 'text-green-400',
                error: 'text-red-400',
                warning: 'text-yellow-400',
                success: 'text-blue-400'
            };
            logDiv.innerHTML += `<span class="${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // 步骤1: 获取Token
        function getTokenFromStorage() {
            log('正在从本地存储获取Token...', 'info');
            const token = localStorage.getItem('goldenledger_session_token');
            const tokenInput = document.getElementById('sessionToken');
            const statusDiv = document.getElementById('token-status');
            
            if (token) {
                tokenInput.value = token;
                statusDiv.innerHTML = `
                    <div class="text-green-600">
                        <p><strong>✅ Token找到</strong></p>
                        <p>长度: ${token.length} 字符</p>
                        <p>前缀: ${token.substring(0, 20)}...</p>
                    </div>
                `;
                log(`Token获取成功，长度: ${token.length}`, 'success');
            } else {
                statusDiv.innerHTML = '<p class="text-red-600"><strong>❌ 未找到Token</strong><br>请先登录生产环境</p>';
                log('未找到Token，请先登录', 'error');
            }
        }
        
        // 步骤2: 验证认证
        async function verifyAuth() {
            const token = document.getElementById('sessionToken').value;
            const statusDiv = document.getElementById('auth-status');
            
            if (!token) {
                statusDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                log('验证认证失败: 没有Token', 'error');
                return;
            }
            
            log('正在验证认证状态...', 'info');
            statusDiv.innerHTML = '<p>验证中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                log(`认证验证响应: ${response.status}`, response.status === 200 ? 'success' : 'error');
                
                statusDiv.innerHTML = `
                    <div class="${response.status === 200 ? 'text-green-600' : 'text-red-600'}">
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>认证状态:</strong> ${data.authenticated ? '✅ 已认证' : '❌ 未认证'}</p>
                        ${data.user ? `
                            <p><strong>用户ID:</strong> ${data.user.id}</p>
                            <p><strong>用户名:</strong> ${data.user.username}</p>
                            <p><strong>邮箱:</strong> ${data.user.email}</p>
                        ` : ''}
                        ${data.error ? `<p><strong>错误:</strong> ${data.error}</p>` : ''}
                    </div>
                    <details class="mt-2">
                        <summary class="cursor-pointer text-sm text-gray-600">查看完整响应</summary>
                        <pre class="text-xs bg-white p-2 rounded border mt-1 overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                log(`认证验证错误: ${error.message}`, 'error');
                statusDiv.innerHTML = `<p class="text-red-600">❌ 验证失败: ${error.message}</p>`;
            }
        }
        
        // 步骤3: 检查AI权限
        async function checkAIPermissions() {
            const token = document.getElementById('sessionToken').value;
            const statusDiv = document.getElementById('ai-permissions');
            
            if (!token) {
                statusDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                return;
            }
            
            log('正在检查AI使用权限...', 'info');
            statusDiv.innerHTML = '<p>检查中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/stats?company_id=default&days=30`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                log(`AI权限检查响应: ${response.status}`, response.status === 200 ? 'success' : 'warning');
                
                statusDiv.innerHTML = `
                    <div class="${response.status === 200 ? 'text-green-600' : 'text-yellow-600'}">
                        <p><strong>状态码:</strong> ${response.status}</p>
                        ${data.data ? `
                            <p><strong>当前使用:</strong> ${data.data.current_usage || 0}</p>
                            <p><strong>用户计划:</strong> ${data.data.plan || '未知'}</p>
                        ` : ''}
                        ${data.error ? `<p><strong>错误:</strong> ${data.error}</p>` : ''}
                    </div>
                    <details class="mt-2">
                        <summary class="cursor-pointer text-sm text-gray-600">查看完整响应</summary>
                        <pre class="text-xs bg-white p-2 rounded border mt-1 overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                log(`AI权限检查错误: ${error.message}`, 'error');
                statusDiv.innerHTML = `<p class="text-red-600">❌ 检查失败: ${error.message}</p>`;
            }
        }
        
        // 步骤4: 分析错误
        async function analyzeError() {
            const token = document.getElementById('sessionToken').value;
            const statusDiv = document.getElementById('error-analysis');
            
            if (!token) {
                statusDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                return;
            }
            
            log('正在分析403错误...', 'info');
            statusDiv.innerHTML = '<p>分析中...</p>';
            
            try {
                // 尝试AI处理请求，捕获详细错误
                const response = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        text: '测试文本',
                        company_id: 'default'
                    })
                });
                
                const data = await response.json();
                log(`403错误分析响应: ${response.status}`, 'warning');
                
                let analysis = '';
                if (response.status === 403) {
                    if (data.error && data.error.includes('使用回数')) {
                        analysis = '🚫 <strong>问题:</strong> AI使用次数已达上限<br>💡 <strong>解决方案:</strong> 需要升级订阅或重置使用计数';
                    } else if (data.error && data.error.includes('権限')) {
                        analysis = '🚫 <strong>问题:</strong> 用户权限不足<br>💡 <strong>解决方案:</strong> 需要检查用户角色和权限设置';
                    } else if (data.error && data.error.includes('プラン')) {
                        analysis = '🚫 <strong>问题:</strong> 订阅计划限制<br>💡 <strong>解决方案:</strong> 需要升级到付费计划';
                    } else {
                        analysis = '🚫 <strong>问题:</strong> 未知的403错误<br>💡 <strong>解决方案:</strong> 需要检查服务器日志';
                    }
                } else {
                    analysis = `✅ <strong>意外结果:</strong> 状态码 ${response.status}，不是403错误`;
                }
                
                statusDiv.innerHTML = `
                    <div class="text-orange-600">
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <div class="mt-2 p-2 bg-orange-50 border border-orange-200 rounded">
                            ${analysis}
                        </div>
                        ${data.error ? `<p class="mt-2"><strong>错误消息:</strong> ${data.error}</p>` : ''}
                        ${data.code ? `<p><strong>错误代码:</strong> ${data.code}</p>` : ''}
                    </div>
                    <details class="mt-2">
                        <summary class="cursor-pointer text-sm text-gray-600">查看完整响应</summary>
                        <pre class="text-xs bg-white p-2 rounded border mt-1 overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                log(`错误分析失败: ${error.message}`, 'error');
                statusDiv.innerHTML = `<p class="text-red-600">❌ 分析失败: ${error.message}</p>`;
            }
        }
        
        // 详细AI测试
        async function testAIProcessingDetailed() {
            const token = document.getElementById('sessionToken').value;
            const text = document.getElementById('testText').value;
            const resultDiv = document.getElementById('ai-test-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先获取Token</p>';
                return;
            }
            
            if (!text) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入测试文本</p>';
                return;
            }
            
            log(`开始详细AI测试，文本: "${text}"`, 'info');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        text: text,
                        company_id: 'default'
                    })
                });
                const endTime = Date.now();
                
                const data = await response.json();
                log(`AI测试完成，耗时: ${endTime - startTime}ms，状态: ${response.status}`, response.status === 200 ? 'success' : 'error');
                
                resultDiv.innerHTML = `
                    <div class="${response.status === 200 ? 'text-green-600' : 'text-red-600'}">
                        <h3 class="font-semibold text-lg mb-2">🧪 AI处理测试结果</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>响应时间:</strong> ${endTime - startTime}ms</p>
                        <p><strong>成功:</strong> ${data.success ? '✅' : '❌'}</p>
                        ${data.error ? `<p><strong>错误:</strong> ${data.error}</p>` : ''}
                        ${data.code ? `<p><strong>错误代码:</strong> ${data.code}</p>` : ''}
                        ${data.data ? `
                            <div class="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                                <p><strong>✅ AI处理成功!</strong></p>
                                <p><strong>借方科目:</strong> ${data.data.debit_account}</p>
                                <p><strong>贷方科目:</strong> ${data.data.credit_account}</p>
                                <p><strong>金额:</strong> ${data.data.amount}</p>
                                <p><strong>描述:</strong> ${data.data.description}</p>
                            </div>
                        ` : ''}
                    </div>
                    <details class="mt-3">
                        <summary class="cursor-pointer text-sm text-gray-600">查看完整响应</summary>
                        <pre class="text-xs bg-white p-2 rounded border mt-1 overflow-auto max-h-64">${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                log(`AI测试错误: ${error.message}`, 'error');
                resultDiv.innerHTML = `<p class="text-red-600">❌ 测试失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动获取token
        window.addEventListener('load', function() {
            log('调试工具已加载', 'info');
            getTokenFromStorage();
        });
    </script>
</body>
</html>

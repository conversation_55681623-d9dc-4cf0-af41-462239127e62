<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 管理员认证重置</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 class="text-2xl font-bold text-center mb-6">🔄 管理员认证重置</h1>
        
        <div class="space-y-4">
            <button onclick="clearAllAuth()" 
                    class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded">
                清除所有认证数据
            </button>
            
            <button onclick="clearGlobalFlags()" 
                    class="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded">
                重置全局标记
            </button>
            
            <button onclick="testDirectAccess()" 
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                测试直接访问管理页面
            </button>
            
            <button onclick="goToLogin()" 
                    class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded">
                前往登录页面
            </button>
        </div>
        
        <div id="result" class="mt-6 p-4 bg-gray-50 rounded text-sm min-h-[100px]">
            点击按钮开始重置...
        </div>
        
        <div class="mt-4 text-center text-sm text-gray-600">
            如果遇到循环重定向问题，请使用此页面重置状态
        </div>
    </div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.innerHTML += `[${timestamp}] ${message}<br>`;
            result.scrollTop = result.scrollHeight;
            console.log(message);
        }

        function clearAllAuth() {
            // 清除所有认证相关的localStorage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('admin') || key.includes('session') || key.includes('auth'))) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                log(`已删除: ${key}`);
            });
            
            if (keysToRemove.length === 0) {
                log('没有找到认证数据');
            } else {
                log(`✅ 已清除 ${keysToRemove.length} 个认证数据`);
            }
        }

        function clearGlobalFlags() {
            // 清除可能导致循环的全局标记
            delete window.adminAuthChecked;
            delete window.adminRedirecting;
            delete window.loginPageLoaded;
            
            log('✅ 已重置全局标记');
            log('- window.adminAuthChecked');
            log('- window.adminRedirecting');
            log('- window.loginPageLoaded');
        }

        function testDirectAccess() {
            log('🔄 准备测试直接访问管理页面...');
            log('3秒后跳转...');
            
            setTimeout(() => {
                window.location.href = '/admin_usage_management.html';
            }, 3000);
        }

        function goToLogin() {
            log('🔄 前往登录页面...');
            window.location.href = '/admin_login.html';
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            log('🔧 管理员认证重置工具已加载');
            
            // 检查localStorage
            const adminSession = localStorage.getItem('admin_session');
            if (adminSession) {
                try {
                    const data = JSON.parse(adminSession);
                    log(`📊 发现会话: ${data.username} (${data.authenticated ? '已认证' : '未认证'})`);
                    log(`过期时间: ${new Date(data.expires).toLocaleString()}`);
                } catch (error) {
                    log('❌ 会话数据损坏');
                }
            } else {
                log('📊 没有找到管理员会话');
            }
            
            // 检查全局标记
            const flags = [];
            if (window.adminAuthChecked) flags.push('adminAuthChecked');
            if (window.adminRedirecting) flags.push('adminRedirecting');
            if (window.loginPageLoaded) flags.push('loginPageLoaded');
            
            if (flags.length > 0) {
                log(`🏁 发现全局标记: ${flags.join(', ')}`);
            } else {
                log('🏁 没有发现全局标记');
            }
        });
    </script>
</body>
</html>

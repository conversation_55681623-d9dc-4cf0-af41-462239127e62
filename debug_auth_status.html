<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态调试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">认证状态调试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 本地存储状态 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">本地存储状态</h2>
                <div id="localStorage-status" class="space-y-2"></div>
            </div>
            
            <!-- 认证管理器状态 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">认证管理器状态</h2>
                <div id="authManager-status" class="space-y-2"></div>
            </div>
            
            <!-- API测试 -->
            <div class="bg-white p-6 rounded-lg shadow md:col-span-2">
                <h2 class="text-xl font-semibold mb-4">API测试</h2>
                <div class="space-y-4">
                    <button onclick="testAuthVerify()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        测试认证验证
                    </button>
                    <button onclick="testAIProcessing()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        测试AI处理
                    </button>
                    <button onclick="testJournalEntries()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        测试记录获取
                    </button>
                </div>
                <div id="api-results" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
            </div>
        </div>
    </div>

    <script>
        // 显示本地存储状态
        function displayLocalStorageStatus() {
            const container = document.getElementById('localStorage-status');
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');
            
            container.innerHTML = `
                <div class="text-sm">
                    <p><strong>Session Token:</strong> ${token ? '✅ 存在' : '❌ 不存在'}</p>
                    ${token ? `<p class="text-xs text-gray-600 break-all">Token: ${token.substring(0, 20)}...</p>` : ''}
                    <p><strong>用户信息:</strong> ${user ? '✅ 存在' : '❌ 不存在'}</p>
                    ${user ? `<p class="text-xs text-gray-600">${user}</p>` : ''}
                </div>
            `;
        }

        // 显示认证管理器状态
        async function displayAuthManagerStatus() {
            const container = document.getElementById('authManager-status');
            
            if (!window.authManager) {
                container.innerHTML = '<p class="text-red-600">❌ 认证管理器未加载</p>';
                return;
            }

            try {
                await window.authManager.initialize();
                
                container.innerHTML = `
                    <div class="text-sm">
                        <p><strong>已初始化:</strong> ${window.authManager.initialized ? '✅' : '❌'}</p>
                        <p><strong>认证状态:</strong> ${window.authManager.isAuthenticated ? '✅ 已认证' : '❌ 未认证'}</p>
                        <p><strong>当前用户:</strong> ${window.authManager.user ? window.authManager.user.username : '无'}</p>
                        <p><strong>Session Token:</strong> ${window.authManager.sessionToken ? '✅ 存在' : '❌ 不存在'}</p>
                    </div>
                `;
            } catch (error) {
                container.innerHTML = `<p class="text-red-600">❌ 初始化失败: ${error.message}</p>`;
            }
        }

        // 测试认证验证API
        async function testAuthVerify() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>测试认证验证中...</p>';
            
            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/verify'), {
                    headers: {
                        ...window.authManager.getAuthHeaders()
                    }
                });
                
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h3 class="font-semibold">认证验证结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>响应:</strong></p>
                    <pre class="text-xs bg-white p-2 rounded border overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<p class="text-red-600">❌ 认证验证失败: ${error.message}</p>`;
            }
        }

        // 测试AI处理API
        async function testAIProcessing() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>测试AI处理中...</p>';
            
            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/ai/process-text'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...window.authManager.getAuthHeaders()
                    },
                    body: JSON.stringify({
                        text: '今天买了办公用品100元',
                        company_id: 'default'
                    })
                });
                
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h3 class="font-semibold">AI处理结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>响应:</strong></p>
                    <pre class="text-xs bg-white p-2 rounded border overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<p class="text-red-600">❌ AI处理失败: ${error.message}</p>`;
            }
        }

        // 测试记录获取API
        async function testJournalEntries() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>测试记录获取中...</p>';
            
            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/journal-entries', { company_id: 'default' }), {
                    headers: {
                        ...window.authManager.getAuthHeaders()
                    }
                });
                
                const data = await response.json();
                
                resultsDiv.innerHTML = `
                    <h3 class="font-semibold">记录获取结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>记录数量:</strong> ${data.data ? data.data.length : 0}</p>
                    <p><strong>响应:</strong></p>
                    <pre class="text-xs bg-white p-2 rounded border overflow-auto max-h-64">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<p class="text-red-600">❌ 记录获取失败: ${error.message}</p>`;
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            // 等待脚本加载
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            displayLocalStorageStatus();
            await displayAuthManagerStatus();
        });
    </script>
</body>
</html>

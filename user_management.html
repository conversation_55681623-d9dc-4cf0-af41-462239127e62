<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Goldenledger会計AI - 用户管理和认证系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .modal { display: none; }
        .modal.active { display: flex; }
        .role-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .role-super_admin { background: #fef2f2; color: #dc2626; }
        .role-admin { background: #fef3c7; color: #d97706; }
        .role-accountant { background: #ecfdf5; color: #059669; }
        .role-auditor { background: #eff6ff; color: #2563eb; }
        .role-viewer { background: #f3f4f6; color: #6b7280; }
        .role-guest { background: #f9fafb; color: #9ca3af; }
        .status-active { color: #10b981; }
        .status-inactive { color: #ef4444; }
        .mfa-enabled { color: #3b82f6; }
        .mfa-disabled { color: #9ca3af; }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">👥 用户管理和认证系统</h1>
                    <p class="text-lg opacity-90">企业级用户认证、权限管理、多因子认证</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div id="current-user-info" class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        <span id="current-username">未登录</span>
                    </div>
                    <button id="logout-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors hidden">
                        🚪 登出
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Login Section -->
    <section id="login-section" class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold text-gray-800">🔐 用户登录</h2>
                <p class="text-gray-600 mt-2">请输入您的登录凭据</p>
            </div>

            <form id="login-form" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名或邮箱</label>
                    <input
                        type="text"
                        id="login-username"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入用户名或邮箱"
                        required
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <input
                        type="password"
                        id="login-password"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入密码"
                        required
                    >
                </div>

                <div id="mfa-section" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">MFA验证码</label>
                    <input
                        type="text"
                        id="mfa-code"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入6位验证码"
                        maxlength="6"
                    >
                </div>

                <button
                    type="submit"
                    id="login-btn"
                    class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors"
                >
                    🔑 登录
                </button>
            </form>

            <div class="mt-6 text-center">
                <button id="show-register-btn" class="text-blue-500 hover:text-blue-600 text-sm">
                    还没有账户？点击注册
                </button>
            </div>

            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-semibold text-sm mb-2">测试账户:</h4>
                <div class="text-xs text-gray-600 space-y-1">
                    <div>管理员: admin / admin123</div>
                    <div>会计师: accountant / acc123</div>
                    <div>审计师: auditor / audit123</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Register Modal -->
    <div id="register-modal" class="modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <h3 class="text-xl font-bold text-gray-800">📝 用户注册</h3>
                <p class="text-gray-600 mt-2">创建新账户</p>
            </div>

            <form id="register-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input
                        type="text"
                        id="register-username"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入用户名"
                        required
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                    <input
                        type="email"
                        id="register-email"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入邮箱地址"
                        required
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input
                        type="password"
                        id="register-password"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入密码 (至少8位)"
                        required
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
                    <input
                        type="password"
                        id="register-password-confirm"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="再次输入密码"
                        required
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                    <select
                        id="register-role"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="viewer">查看者</option>
                        <option value="accountant">会计师</option>
                        <option value="auditor">审计师</option>
                    </select>
                </div>

                <div class="flex space-x-3 pt-4">
                    <button
                        type="button"
                        id="cancel-register-btn"
                        class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        取消
                    </button>
                    <button
                        type="submit"
                        class="flex-1 bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors"
                    >
                        注册
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Main Dashboard -->
    <main id="dashboard-section" class="container mx-auto px-4 py-8 hidden">
        <!-- User Stats -->
        <section class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总用户数</p>
                        <p id="total-users" class="text-2xl font-bold text-blue-600">0</p>
                        <p class="text-xs text-gray-500">注册用户</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">👥</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃用户</p>
                        <p id="active-users" class="text-2xl font-bold text-green-600">0</p>
                        <p class="text-xs text-gray-500">在线用户</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">🟢</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">MFA启用</p>
                        <p id="mfa-enabled-users" class="text-2xl font-bold text-purple-600">0</p>
                        <p class="text-xs text-gray-500">安全用户</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">🔐</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今日登录</p>
                        <p id="today-logins" class="text-2xl font-bold text-orange-600">0</p>
                        <p class="text-xs text-gray-500">登录次数</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📊</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Management -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- User List -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">👥 用户列表</h3>
                    <button id="refresh-users-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                        🔄 刷新
                    </button>
                </div>

                <div id="users-list" class="space-y-3 max-h-96 overflow-y-auto">
                    <!-- 用户列表将在这里动态生成 -->
                </div>
            </div>

            <!-- Current User Profile -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">👤 个人资料</h3>

                <div id="user-profile" class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                            <span class="text-2xl">👤</span>
                        </div>
                        <div>
                            <h4 id="profile-username" class="font-semibold text-lg">--</h4>
                            <p id="profile-email" class="text-gray-600">--</p>
                            <span id="profile-role" class="role-badge">--</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">用户ID</label>
                            <div id="profile-id" class="text-sm text-gray-600 font-mono">--</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">最后登录</label>
                            <div id="profile-last-login" class="text-sm text-gray-600">--</div>
                        </div>
                    </div>

                    <div class="border-t pt-4">
                        <h5 class="font-medium mb-3">🔐 安全设置</h5>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium">多因子认证 (MFA)</div>
                                    <div class="text-sm text-gray-500">增强账户安全性</div>
                                </div>
                                <button id="toggle-mfa-btn" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                                    启用MFA
                                </button>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium">修改密码</div>
                                    <div class="text-sm text-gray-500">更新登录密码</div>
                                </div>
                                <button id="change-password-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                                    修改密码
                                </button>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium">活跃会话</div>
                                    <div class="text-sm text-gray-500">管理登录会话</div>
                                </div>
                                <button id="view-sessions-btn" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                                    查看会话
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Permissions Matrix -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <h3 class="text-lg font-semibold mb-4">🔑 权限矩阵</h3>

            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b">
                            <th class="text-left py-2 px-3">权限</th>
                            <th class="text-center py-2 px-3">超级管理员</th>
                            <th class="text-center py-2 px-3">管理员</th>
                            <th class="text-center py-2 px-3">会计师</th>
                            <th class="text-center py-2 px-3">审计师</th>
                            <th class="text-center py-2 px-3">查看者</th>
                            <th class="text-center py-2 px-3">访客</th>
                        </tr>
                    </thead>
                    <tbody id="permissions-matrix">
                        <!-- 权限矩阵将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Login History -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">📊 登录历史</h3>
                <div class="flex space-x-2">
                    <button id="export-history-btn" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                        📤 导出
                    </button>
                    <button id="refresh-history-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                        🔄 刷新
                    </button>
                </div>
            </div>

            <div id="login-history" class="space-y-2 max-h-64 overflow-y-auto">
                <!-- 登录历史将在这里动态生成 -->
            </div>
        </section>
    </main>

    <!-- MFA Setup Modal -->
    <div id="mfa-modal" class="modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <h3 class="text-xl font-bold text-gray-800">🔐 设置多因子认证</h3>
                <p class="text-gray-600 mt-2">扫描二维码或手动输入密钥</p>
            </div>

            <div id="mfa-setup-content" class="space-y-4">
                <div class="text-center">
                    <div id="qr-code-container" class="mb-4">
                        <!-- QR码将在这里显示 -->
                    </div>

                    <div class="text-sm text-gray-600 mb-4">
                        <p>使用Google Authenticator或类似应用扫描二维码</p>
                    </div>

                    <div class="bg-gray-50 p-3 rounded-lg mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">手动输入密钥:</label>
                        <code id="manual-key" class="text-xs break-all">--</code>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">验证码确认</label>
                    <input
                        type="text"
                        id="mfa-confirm-code"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入6位验证码"
                        maxlength="6"
                    >
                </div>

                <div class="flex space-x-3 pt-4">
                    <button
                        type="button"
                        id="cancel-mfa-btn"
                        class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        取消
                    </button>
                    <button
                        type="button"
                        id="confirm-mfa-btn"
                        class="flex-1 bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors"
                    >
                        确认启用
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Area -->
    <div id="notification-area" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- 动态通知将在这里显示 -->
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let authToken = null;

        // 权限定义
        const permissions = {
            'system:admin': '系统管理',
            'system:config': '系统配置',
            'system:monitor': '系统监控',
            'user:create': '创建用户',
            'user:read': '查看用户',
            'user:update': '更新用户',
            'user:delete': '删除用户',
            'finance:create': '创建财务数据',
            'finance:read': '查看财务数据',
            'finance:update': '更新财务数据',
            'finance:delete': '删除财务数据',
            'ai:use': '使用AI功能',
            'ai:admin': 'AI管理',
            'ai:audit': 'AI审计',
            'report:view': '查看报表',
            'report:export': '导出报表',
            'report:create': '创建报表'
        };

        // 角色权限映射 (模拟数据)
        const rolePermissions = {
            'super_admin': Object.keys(permissions),
            'admin': [
                'system:config', 'system:monitor', 'user:create', 'user:read', 'user:update',
                'finance:create', 'finance:read', 'finance:update', 'ai:use', 'ai:admin',
                'report:view', 'report:export', 'report:create'
            ],
            'accountant': [
                'finance:create', 'finance:read', 'finance:update', 'ai:use',
                'report:view', 'report:export'
            ],
            'auditor': [
                'finance:read', 'ai:audit', 'report:view', 'report:export'
            ],
            'viewer': [
                'finance:read', 'report:view'
            ],
            'guest': [
                'finance:read'
            ]
        };

        // 用户登录
        async function login(username, password, mfaCode = '') {
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        mfa_code: mfaCode,
                        ip_address: await getClientIP(),
                        user_agent: navigator.userAgent
                    })
                });

                const result = await response.json();

                if (result.success) {
                    authToken = result.access_token;
                    currentUser = result.user;

                    // 保存到localStorage
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));

                    showDashboard();
                    showNotification('登录成功', `欢迎回来，${currentUser.username}！`, 'success');

                    return { success: true };
                } else {
                    if (result.require_mfa) {
                        document.getElementById('mfa-section').classList.remove('hidden');
                        return { success: false, requireMFA: true };
                    } else {
                        showNotification('登录失败', result.error, 'error');
                        return { success: false, error: result.error };
                    }
                }
            } catch (error) {
                // 模拟登录成功 (开发环境)
                if (username === 'admin' && password === 'admin123') {
                    currentUser = {
                        id: 'admin_001',
                        username: 'admin',
                        email: '<EMAIL>',
                        role: 'super_admin',
                        permissions: Object.keys(permissions)
                    };

                    authToken = 'mock_token_' + Date.now();
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));

                    showDashboard();
                    showNotification('登录成功', `欢迎回来，${currentUser.username}！`, 'success');
                    return { success: true };
                }

                showNotification('登录失败', '网络错误或服务不可用', 'error');
                return { success: false, error: '网络错误' };
            }
        }

        // 用户注册
        async function register(username, email, password, role) {
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        password: password,
                        role: role
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('注册成功', '账户创建成功，请登录', 'success');
                    closeModal('register-modal');
                    return { success: true };
                } else {
                    showNotification('注册失败', result.error, 'error');
                    return { success: false, error: result.error };
                }
            } catch (error) {
                showNotification('注册失败', '网络错误或服务不可用', 'error');
                return { success: false, error: '网络错误' };
            }
        }

        // 显示仪表盘
        function showDashboard() {
            document.getElementById('login-section').classList.add('hidden');
            document.getElementById('dashboard-section').classList.remove('hidden');

            // 更新用户信息
            document.getElementById('current-username').textContent = currentUser.username;
            document.getElementById('logout-btn').classList.remove('hidden');

            // 更新个人资料
            updateUserProfile();

            // 加载数据
            loadUserStats();
            loadUsersList();
            loadPermissionsMatrix();
            loadLoginHistory();
        }

        // 更新用户资料
        function updateUserProfile() {
            document.getElementById('profile-username').textContent = currentUser.username;
            document.getElementById('profile-email').textContent = currentUser.email;
            document.getElementById('profile-id').textContent = currentUser.id;
            document.getElementById('profile-last-login').textContent = new Date().toLocaleString('ja-JP');

            const roleElement = document.getElementById('profile-role');
            roleElement.textContent = getRoleDisplayName(currentUser.role);
            roleElement.className = `role-badge role-${currentUser.role}`;
        }

        // 获取角色显示名称
        function getRoleDisplayName(role) {
            const roleNames = {
                'super_admin': '超级管理员',
                'admin': '管理员',
                'accountant': '会计师',
                'auditor': '审计师',
                'viewer': '查看者',
                'guest': '访客'
            };
            return roleNames[role] || role;
        }

        // 加载用户统计
        function loadUserStats() {
            // 模拟数据
            document.getElementById('total-users').textContent = '12';
            document.getElementById('active-users').textContent = '8';
            document.getElementById('mfa-enabled-users').textContent = '5';
            document.getElementById('today-logins').textContent = '23';
        }

        // 加载用户列表
        function loadUsersList() {
            const usersList = document.getElementById('users-list');

            // 模拟用户数据
            const users = [
                { id: 'admin_001', username: 'admin', email: '<EMAIL>', role: 'super_admin', is_active: true, mfa_enabled: true },
                { id: 'user_002', username: 'accountant', email: '<EMAIL>', role: 'accountant', is_active: true, mfa_enabled: false },
                { id: 'user_003', username: 'auditor', email: '<EMAIL>', role: 'auditor', is_active: true, mfa_enabled: true },
                { id: 'user_004', username: 'viewer', email: '<EMAIL>', role: 'viewer', is_active: false, mfa_enabled: false }
            ];

            usersList.innerHTML = users.map(user => `
                <div class="flex items-center justify-between p-3 border rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <span class="text-lg">👤</span>
                        </div>
                        <div>
                            <div class="font-medium">${user.username}</div>
                            <div class="text-sm text-gray-500">${user.email}</div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="role-badge role-${user.role}">${getRoleDisplayName(user.role)}</span>
                                <span class="${user.is_active ? 'status-active' : 'status-inactive'} text-xs">
                                    ${user.is_active ? '●' : '●'} ${user.is_active ? '活跃' : '禁用'}
                                </span>
                                <span class="${user.mfa_enabled ? 'mfa-enabled' : 'mfa-disabled'} text-xs">
                                    ${user.mfa_enabled ? '🔐' : '🔓'} ${user.mfa_enabled ? 'MFA' : 'No MFA'}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-1">
                        <button class="text-blue-500 hover:text-blue-600 text-sm" onclick="editUser('${user.id}')">
                            ✏️
                        </button>
                        <button class="text-red-500 hover:text-red-600 text-sm" onclick="deleteUser('${user.id}')">
                            🗑️
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 加载权限矩阵
        function loadPermissionsMatrix() {
            const matrixBody = document.getElementById('permissions-matrix');
            const roles = ['super_admin', 'admin', 'accountant', 'auditor', 'viewer', 'guest'];

            matrixBody.innerHTML = Object.entries(permissions).map(([perm, desc]) => `
                <tr class="border-b">
                    <td class="py-2 px-3 font-medium">${desc}</td>
                    ${roles.map(role => {
                        const hasPermission = rolePermissions[role].includes(perm);
                        return `<td class="text-center py-2 px-3">
                            ${hasPermission ? '<span class="text-green-500">✓</span>' : '<span class="text-gray-300">✗</span>'}
                        </td>`;
                    }).join('')}
                </tr>
            `).join('');
        }

        // 加载登录历史
        function loadLoginHistory() {
            const historyContainer = document.getElementById('login-history');

            // 模拟登录历史数据
            const history = [
                { username: 'admin', success: true, timestamp: new Date().toISOString(), ip_address: '*************', user_agent: 'Chrome/120.0.0.0' },
                { username: 'accountant', success: true, timestamp: new Date(Date.now() - 3600000).toISOString(), ip_address: '*************', user_agent: 'Firefox/121.0' },
                { username: 'unknown', success: false, timestamp: new Date(Date.now() - 7200000).toISOString(), ip_address: '*************', user_agent: 'Chrome/120.0.0.0', failure_reason: '密码错误' },
                { username: 'auditor', success: true, timestamp: new Date(Date.now() - ********).toISOString(), ip_address: '*************', user_agent: 'Safari/17.0' }
            ];

            historyContainer.innerHTML = history.map(entry => `
                <div class="flex items-center justify-between p-3 border rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center ${entry.success ? 'bg-green-100' : 'bg-red-100'}">
                            <span class="${entry.success ? 'text-green-600' : 'text-red-600'}">${entry.success ? '✓' : '✗'}</span>
                        </div>
                        <div>
                            <div class="font-medium">${entry.username}</div>
                            <div class="text-sm text-gray-500">${new Date(entry.timestamp).toLocaleString('ja-JP')}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">${entry.ip_address}</div>
                        <div class="text-xs text-gray-500">${entry.user_agent.split('/')[0]}</div>
                        ${!entry.success && entry.failure_reason ? `<div class="text-xs text-red-500">${entry.failure_reason}</div>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 显示通知
        function showNotification(title, message, type = 'info') {
            const notificationArea = document.getElementById('notification-area');

            const notification = document.createElement('div');
            notification.className = `max-w-sm bg-white border-l-4 p-4 shadow-lg rounded-lg`;

            switch(type) {
                case 'success':
                    notification.classList.add('border-green-500');
                    break;
                case 'error':
                    notification.classList.add('border-red-500');
                    break;
                case 'warning':
                    notification.classList.add('border-yellow-500');
                    break;
                default:
                    notification.classList.add('border-blue-500');
            }

            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800">${title}</h4>
                        <p class="text-sm text-gray-600">${message}</p>
                    </div>
                    <button class="ml-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                        ✕
                    </button>
                </div>
            `;

            notificationArea.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // 模态框管理
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // 获取客户端IP (模拟)
        async function getClientIP() {
            return '*************';
        }

        // 用户操作函数
        function editUser(userId) {
            showNotification('编辑用户', `编辑用户 ${userId} 的功能正在开发中`, 'info');
        }

        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？')) {
                showNotification('删除用户', `用户 ${userId} 已删除`, 'success');
                loadUsersList(); // 重新加载用户列表
            }
        }

        // MFA相关函数
        async function setupMFA() {
            try {
                // 模拟MFA设置
                const qrCodeContainer = document.getElementById('qr-code-container');
                const manualKey = document.getElementById('manual-key');

                // 生成模拟的密钥和QR码
                const secret = 'JBSWY3DPEHPK3PXP';
                manualKey.textContent = secret;

                // 使用QRCode.js生成QR码
                qrCodeContainer.innerHTML = '';
                const qrCodeDiv = document.createElement('div');
                qrCodeContainer.appendChild(qrCodeDiv);

                new QRCode(qrCodeDiv, {
                    text: `otpauth://totp/goldenledger会計AI:${currentUser.email}?secret=${secret}&issuer=goldenledger会計AI`,
                    width: 200,
                    height: 200
                });

                showModal('mfa-modal');

            } catch (error) {
                showNotification('MFA设置失败', '无法生成QR码', 'error');
            }
        }

        async function confirmMFA() {
            const code = document.getElementById('mfa-confirm-code').value;

            if (!code || code.length !== 6) {
                showNotification('验证失败', '请输入6位验证码', 'error');
                return;
            }

            // 模拟MFA确认
            showNotification('MFA启用成功', '多因子认证已启用', 'success');
            closeModal('mfa-modal');

            // 更新按钮状态
            const toggleBtn = document.getElementById('toggle-mfa-btn');
            toggleBtn.textContent = '禁用MFA';
            toggleBtn.className = 'bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已登录
            const savedToken = localStorage.getItem('authToken');
            const savedUser = localStorage.getItem('currentUser');

            if (savedToken && savedUser) {
                authToken = savedToken;
                currentUser = JSON.parse(savedUser);
                showDashboard();
            }

            // 登录表单
            document.getElementById('login-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('login-username').value;
                const password = document.getElementById('login-password').value;
                const mfaCode = document.getElementById('mfa-code').value;

                const result = await login(username, password, mfaCode);

                if (!result.success && !result.requireMFA) {
                    // 清空密码字段
                    document.getElementById('login-password').value = '';
                }
            });

            // 注册表单
            document.getElementById('register-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('register-username').value;
                const email = document.getElementById('register-email').value;
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-password-confirm').value;
                const role = document.getElementById('register-role').value;

                if (password !== confirmPassword) {
                    showNotification('注册失败', '密码确认不匹配', 'error');
                    return;
                }

                await register(username, email, password, role);
            });

            // 显示注册模态框
            document.getElementById('show-register-btn').addEventListener('click', function() {
                showModal('register-modal');
            });

            // 取消注册
            document.getElementById('cancel-register-btn').addEventListener('click', function() {
                closeModal('register-modal');
            });

            // 登出
            document.getElementById('logout-btn').addEventListener('click', function() {
                localStorage.removeItem('authToken');
                localStorage.removeItem('currentUser');
                location.reload();
            });

            // MFA相关事件
            document.getElementById('toggle-mfa-btn').addEventListener('click', setupMFA);
            document.getElementById('confirm-mfa-btn').addEventListener('click', confirmMFA);
            document.getElementById('cancel-mfa-btn').addEventListener('click', function() {
                closeModal('mfa-modal');
            });

            // 刷新按钮
            document.getElementById('refresh-users-btn').addEventListener('click', loadUsersList);
            document.getElementById('refresh-history-btn').addEventListener('click', loadLoginHistory);

            // 其他按钮事件
            document.getElementById('change-password-btn').addEventListener('click', function() {
                showNotification('修改密码', '密码修改功能正在开发中', 'info');
            });

            document.getElementById('view-sessions-btn').addEventListener('click', function() {
                showNotification('会话管理', '会话管理功能正在开发中', 'info');
            });

            document.getElementById('export-history-btn').addEventListener('click', function() {
                showNotification('导出历史', '登录历史导出功能正在开发中', 'info');
            });
        });
    </script>
</body>
</html>
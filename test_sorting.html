<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排序功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">📊 排序功能测试</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <div class="space-y-2 text-sm text-gray-600">
                <p>✅ <strong>表头点击排序</strong> - 点击任意列标题进行排序</p>
                <p>✅ <strong>排序方向切换</strong> - 再次点击同一列切换升序/降序</p>
                <p>✅ <strong>快速排序按钮</strong> - 使用预设的快速排序选项</p>
                <p>✅ <strong>键盘快捷键</strong> - Ctrl+1(日期) Ctrl+2(金额) Ctrl+3(置信度) Ctrl+0(重置)</p>
                <p>✅ <strong>排序状态显示</strong> - 实时显示当前排序字段和方向</p>
                <p>✅ <strong>视觉反馈</strong> - 排序列高亮显示，箭头指示方向</p>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">功能特性</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-3">
                    <h3 class="font-medium text-gray-900">🎯 智能排序</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 数字类型智能识别（金额、置信度）</li>
                        <li>• 日期类型正确解析和排序</li>
                        <li>• 布尔类型排序（AI生成优先）</li>
                        <li>• 字符串类型不区分大小写</li>
                    </ul>
                </div>
                
                <div class="space-y-3">
                    <h3 class="font-medium text-gray-900">🎨 用户体验</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 悬停效果和过渡动画</li>
                        <li>• 排序状态实时显示</li>
                        <li>• 键盘快捷键支持</li>
                        <li>• 操作提示和反馈</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">测试步骤</h2>
            <div class="space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="font-medium text-blue-900">1. 基础排序测试</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        访问 <a href="/journal_entries.html" class="text-blue-600 hover:underline">记账记录页面</a>，
                        点击不同的列标题，观察排序效果和视觉反馈。
                    </p>
                </div>
                
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="font-medium text-green-900">2. 快速排序测试</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        使用页面上的快速排序按钮，测试预设的排序选项。
                    </p>
                </div>
                
                <div class="border-l-4 border-purple-500 pl-4">
                    <h3 class="font-medium text-purple-900">3. 键盘快捷键测试</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        按住Ctrl键，然后按数字键1、2、3或0，测试键盘快捷键功能。
                    </p>
                </div>
                
                <div class="border-l-4 border-yellow-500 pl-4">
                    <h3 class="font-medium text-yellow-900">4. 组合功能测试</h3>
                    <p class="text-sm text-gray-600 mt-1">
                        结合搜索、筛选和排序功能，测试复合操作的效果。
                    </p>
                </div>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <a href="/journal_entries.html" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 inline-block">
                🚀 开始测试排序功能
            </a>
        </div>
    </div>
</body>
</html>

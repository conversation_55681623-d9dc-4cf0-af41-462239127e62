#!/usr/bin/env python3
"""
GoldenLedger — Smart AI-Powered Finance System演示脚本
展示核心AI记账功能
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加backend路径到Python路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

async def demo_ai_bookkeeping():
    """演示AI记账功能"""
    print("🚀 GoldenLedger — Smart AI-Powered Finance System演示")
    print("="*50)
    
    try:
        # 导入必要的模块
        from app.services.ai_bookkeeping import get_ai_bookkeeping_engine
        from app.services.llm_manager import get_model_manager
        from app.core.database import db_manager
        
        # 连接数据库
        print("📊 连接数据库...")
        await db_manager.connect()
        
        # 初始化AI引擎
        print("🤖 初始化AI记账引擎...")
        company_id = "default"
        ai_engine = get_ai_bookkeeping_engine(company_id)
        
        # 演示用例
        test_cases = [
            "今天在便利店买了1200日元的办公用品，现金支付",
            "收到ABC会社的咨询费50万日元，银行转账",
            "支付了这个月的房租15万日元",
            "购买了新的电脑设备20万日元，用于办公",
            "收到银行利息收入5000日元"
        ]
        
        print("\n💬 AI记账演示:")
        print("-" * 50)
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n{i}. 输入: \"{test_input}\"")
            print("   处理中...")
            
            try:
                # 处理自然语言输入
                result = await ai_engine.process_natural_language(test_input)
                
                print(f"   ✅ 生成仕訳:")
                print(f"      日期: {result.date}")
                print(f"      借方: {result.debit_account}")
                print(f"      贷方: {result.credit_account}")
                print(f"      金额: ¥{result.amount:,.0f}")
                print(f"      摘要: {result.description}")
                print(f"      信頼度: {result.confidence:.1%}")
                
                if result.warnings:
                    print(f"      ⚠️ 注意: {', '.join(result.warnings)}")
                
                if result.suggestions:
                    print(f"      💡 提案: {', '.join(result.suggestions)}")
                    
            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
        
        print("\n" + "="*50)
        print("🎉 演示完成！")
        print("💡 提示: 运行 'python start_system.py' 启动完整系统")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保已安装后端依赖: pip install -r backend/requirements.txt")
    except Exception as e:
        print(f"❌ 演示失败: {e}")
    finally:
        # 清理资源
        try:
            await db_manager.disconnect()
        except:
            pass


async def demo_llm_management():
    """演示LLM模型管理功能"""
    print("\n🧠 LLM模型管理演示")
    print("-" * 30)
    
    try:
        from app.services.llm_manager import get_model_manager, get_llm_client, TaskType
        
        company_id = "default"
        model_manager = get_model_manager(company_id)
        llm_client = get_llm_client(company_id)
        
        print("1. 初始化默认模型配置...")
        await model_manager.initialize_default_models()
        print("   ✅ Gemini模型配置完成")
        
        print("\n2. 测试模型调用...")
        test_prompt = "こんにちは。これはテストメッセージです。"
        
        try:
            response = await llm_client.generate_content(
                TaskType.NATURAL_LANGUAGE, 
                test_prompt
            )
            print(f"   ✅ 模型响应: {response['text'][:50]}...")
            print(f"   📊 使用模型: {response['model']}")
            print(f"   🏢 供应商: {response['provider']}")
        except Exception as e:
            print(f"   ⚠️ 模型调用跳过: {e}")
        
    except Exception as e:
        print(f"❌ LLM管理演示失败: {e}")


def show_system_info():
    """显示系统信息"""
    print("\n📋 系统信息")
    print("-" * 20)
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"后端路径: {backend_path}")
    
    # 检查关键文件
    key_files = [
        "backend/main.py",
        "backend/requirements.txt",
        "frontend/package.json",
        "start_system.py"
    ]
    
    print("\n📁 关键文件检查:")
    for file_path in key_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")


async def main():
    """主演示函数"""
    print("🎭 GoldenLedger — Smart AI-Powered Finance System演示")
    print("="*60)
    
    # 显示系统信息
    show_system_info()
    
    # 演示AI记账功能
    await demo_ai_bookkeeping()
    
    # 演示LLM管理功能
    await demo_llm_management()
    
    print("\n" + "="*60)
    print("🚀 完整系统启动命令:")
    print("   python start_system.py")
    print("\n📖 访问地址:")
    print("   前端界面: http://localhost:3000")
    print("   API文档:  http://localhost:8000/docs")
    print("="*60)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示已停止")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")
        sys.exit(1)

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💎 プランアップグレード - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/console_cleaner.js"></script>
    <style>
        .plan-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .plan-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .plan-card.recommended {
            border: 3px solid #3b82f6;
            transform: scale(1.05);
        }
        .plan-card.recommended::before {
            content: '推奨';
            position: absolute;
            top: 0;
            right: 0;
            background: #3b82f6;
            color: white;
            padding: 4px 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .feature-check {
            color: #10b981;
        }
        .feature-cross {
            color: #ef4444;
        }
        .usage-indicator {
            height: 6px;
            border-radius: 3px;
            background: #e5e7eb;
            overflow: hidden;
        }
        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #3b82f6);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold text-gray-800 mb-4">
                💎 プランアップグレード
            </h1>
            <p class="text-xl text-gray-600 mb-8">
                あなたのニーズに最適なプランを選択してください
            </p>
            
            <!-- 当前使用状况 -->
            <div id="current-usage" class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto mb-8">
                <h3 class="text-lg font-semibold mb-4">📊 現在の使用状況</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span>現在のプラン:</span>
                        <span id="current-plan" class="font-bold text-blue-600">-</span>
                    </div>
                    <div class="flex justify-between">
                        <span>本日の使用量:</span>
                        <span id="current-usage-count" class="font-bold">-</span>
                    </div>
                    <div class="usage-indicator">
                        <div id="usage-fill" class="usage-fill" style="width: 0%"></div>
                    </div>
                    <div class="text-sm text-gray-500" id="reset-time">-</div>
                </div>
            </div>
        </div>

        <!-- 计划比较 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <!-- フリープラン -->
            <div class="plan-card bg-white rounded-2xl shadow-lg p-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">フリープラン</h3>
                    <div class="text-4xl font-bold text-green-600 mb-2">¥0</div>
                    <div class="text-gray-500">永続無料</div>
                </div>
                
                <div class="space-y-4 mb-8">
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>20回/日の質問制限</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>基本的なAI記帳機能</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>コミュニティサポート</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-cross text-xl mr-3">✗</span>
                        <span>高度な分析機能</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-cross text-xl mr-3">✗</span>
                        <span>優先サポート</span>
                    </div>
                </div>
                
                <button onclick="selectPlan('FREE')" 
                        class="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 rounded-lg font-semibold transition-colors">
                    現在のプラン
                </button>
            </div>

            <!-- ベーシックプラン -->
            <div class="plan-card recommended bg-white rounded-2xl shadow-lg p-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">ベーシックプラン</h3>
                    <div class="text-4xl font-bold text-blue-600 mb-2">¥980</div>
                    <div class="text-gray-500">月額</div>
                </div>
                
                <div class="space-y-4 mb-8">
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span><strong>200回/日</strong>の質問制限</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>全AI記帳機能</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>高度な分析機能</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>優先サポート</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-cross text-xl mr-3">✗</span>
                        <span>無制限エクスポート</span>
                    </div>
                </div>
                
                <button onclick="selectPlan('BASIC')" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors">
                    アップグレード
                </button>
            </div>

            <!-- プロプラン -->
            <div class="plan-card bg-white rounded-2xl shadow-lg p-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">プロプラン</h3>
                    <div class="text-4xl font-bold text-purple-600 mb-2">¥2,980</div>
                    <div class="text-gray-500">月額</div>
                </div>
                
                <div class="space-y-4 mb-8">
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span><strong>700回/日</strong>の質問制限</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>全プレミアム機能</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>無制限エクスポート</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>プレミアムサポート</span>
                    </div>
                    <div class="flex items-center">
                        <span class="feature-check text-xl mr-3">✓</span>
                        <span>API アクセス</span>
                    </div>
                </div>
                
                <button onclick="selectPlan('PRO')" 
                        class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-semibold transition-colors">
                    アップグレード
                </button>
            </div>
        </div>

        <!-- FAQ -->
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 class="text-2xl font-bold text-center mb-8">よくある質問</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="font-semibold mb-2">💳 支払い方法は？</h3>
                    <p class="text-gray-600 text-sm">クレジットカード、PayPal、銀行振込に対応しています。</p>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">🔄 プラン変更は可能？</h3>
                    <p class="text-gray-600 text-sm">いつでもプランの変更・キャンセルが可能です。</p>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">📊 使用量の確認方法は？</h3>
                    <p class="text-gray-600 text-sm">ダッシュボードでリアルタイムに確認できます。</p>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">🛡️ データの安全性は？</h3>
                    <p class="text-gray-600 text-sm">エンタープライズレベルのセキュリティで保護されています。</p>
                </div>
            </div>
        </div>

        <!-- 返回链接 -->
        <div class="text-center">
            <a href="/master_dashboard.html" 
               class="text-blue-600 hover:text-blue-800 underline">
                ← ダッシュボードに戻る
            </a>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="/env-config.js"></script>
    <script src="/chatbot/UsageTracker.js"></script>
    <script>
        let usageTracker = null;

        document.addEventListener('DOMContentLoaded', async function() {
            await initializePage();
        });

        async function initializePage() {
            try {
                usageTracker = new UsageTracker();
                await usageTracker.init();
                await loadCurrentUsage();
            } catch (error) {
                console.error('初始化失败:', error);
            }
        }

        async function loadCurrentUsage() {
            if (!usageTracker) return;

            try {
                const usageInfo = await usageTracker.checkUsageLimit();
                
                document.getElementById('current-plan').textContent = usageInfo.planName || 'フリープラン';
                document.getElementById('current-usage-count').textContent = 
                    `${usageInfo.count}/${usageInfo.limit === -1 ? '∞' : usageInfo.limit}`;
                
                if (usageInfo.limit !== -1) {
                    const percentage = (usageInfo.count / usageInfo.limit) * 100;
                    document.getElementById('usage-fill').style.width = `${Math.min(percentage, 100)}%`;
                }
                
                if (usageInfo.resetTime) {
                    const resetDate = new Date(usageInfo.resetTime);
                    document.getElementById('reset-time').textContent = 
                        `リセット: ${resetDate.toLocaleString('ja-JP')}`;
                }
                
            } catch (error) {
                console.error('使用量データの取得に失敗:', error);
            }
        }

        async function selectPlan(planType) {
            if (planType === 'FREE') {
                alert('現在フリープランをご利用中です。');
                return;
            }

            const plans = {
                'BASIC': { name: 'ベーシックプラン', price: 980 },
                'PRO': { name: 'プロプラン', price: 2980 }
            };

            const plan = plans[planType];
            if (!plan) return;

            const confirmed = confirm(
                `${plan.name}（¥${plan.price}/月）にアップグレードしますか？\n\n` +
                `アップグレード後、より多くの質問が可能になります。`
            );

            if (confirmed) {
                // 这里应该重定向到支付页面
                alert(`${plan.name}の支払いページに移動します...`);
                // window.location.href = `/payment.html?plan=${planType}`;
            }
        }
    </script>
</body>
</html>

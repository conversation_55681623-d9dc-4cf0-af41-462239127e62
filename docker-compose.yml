version: '3.8'

services:
  # GoldenLedger — Smart AI-Powered Finance System - 主应用
  goldenledger:
    build: .
    container_name: goldenledger
    ports:
      - "8000:8000"  # API服务端口
      - "3000:3000"  # 前端服务端口
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///data/goldenledger_accounting.db
      - REDIS_URL=redis://redis:6379
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-your_gemini_api_key}
      - SECRET_KEY=${SECRET_KEY:-your_secret_key}
      - DEBUG=false
      - LOG_LEVEL=info
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backup:/app/backup
      - ./uploads:/app/uploads
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - goldenledger-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: goldenledger-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-goldenledger123}
    restart: unless-stopped
    networks:
      - goldenledger-network

  # PostgreSQL数据库 (可选，用于生产环境)
  postgres:
    image: postgres:15-alpine
    container_name: goldenledger-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=goldenledger_accounting
      - POSTGRES_USER=goldenledger_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-goldenledger123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - goldenledger-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: goldenledger-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - goldenledger
    restart: unless-stopped
    networks:
      - goldenledger-network

  # 数据备份服务
  backup:
    image: alpine:latest
    container_name: goldenledger-backup
    volumes:
      - ./data:/app/data:ro
      - ./backup:/app/backup
      - ./scripts/backup.sh:/app/backup.sh
    command: sh -c "chmod +x /app/backup.sh && crond -f"
    environment:
      - BACKUP_SCHEDULE=0 */6 * * *  # 每6小时备份一次
    restart: unless-stopped
    networks:
      - goldenledger-network

  # 监控服务 (Prometheus + Grafana)
  prometheus:
    image: prom/prometheus:latest
    container_name: goldenledger-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - goldenledger-network

  grafana:
    image: grafana/grafana:latest
    container_name: goldenledger-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - goldenledger-network

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  goldenledger-network:
    driver: bridge

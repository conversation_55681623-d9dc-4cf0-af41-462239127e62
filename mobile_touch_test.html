<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端触摸测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .test-panel {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            background: rgba(0,0,0,0.05);
            border-radius: 8px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .test-button {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            touch-action: manipulation;
        }
        
        .test-button:active {
            background: #2563eb;
            transform: scale(0.98);
        }
        
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .device-info {
            font-size: 12px;
            color: #666;
            background: rgba(0,0,0,0.05);
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-panel">
        <h1 class="text-2xl font-bold mb-4">📱 移动端触摸测试</h1>
        
        <div class="info-box">
            <h3 class="font-semibold mb-2">🎯 测试目标</h3>
            <ul class="text-sm space-y-1">
                <li>✅ 音乐按钮点击灵敏度</li>
                <li>✅ 音乐按钮拖动功能</li>
                <li>✅ 眼睛按钮在移动端隐藏</li>
                <li>✅ 触摸响应速度</li>
            </ul>
        </div>
        
        <div id="statusList">
            <div class="status-item">
                <div class="status-dot status-warning"></div>
                <span>初始化中...</span>
            </div>
        </div>
        
        <div class="device-info" id="deviceInfo">
            设备信息加载中...
        </div>
    </div>

    <div class="test-panel">
        <h2 class="text-xl font-semibold mb-4">🧪 交互测试</h2>
        
        <button class="test-button" onclick="testMusicClick()">
            🎵 测试音乐按钮点击
        </button>
        
        <button class="test-button" onclick="testMusicDrag()">
            🎵 测试音乐按钮拖动
        </button>
        
        <button class="test-button" onclick="resetPositions()">
            🔄 重置所有位置
        </button>
        
        <button class="test-button" onclick="showTouchInfo()">
            📊 显示触摸信息
        </button>
        
        <div id="touchInfo" class="info-box" style="display: none;">
            <h3 class="font-semibold mb-2">触摸事件信息</h3>
            <div id="touchDetails" class="text-sm"></div>
        </div>
    </div>

    <div class="test-panel">
        <h2 class="text-xl font-semibold mb-4">📋 测试结果</h2>
        <div id="testResults">
            <div class="text-sm text-gray-600">开始测试以查看结果...</div>
        </div>
    </div>

    <script>
        const statusList = document.getElementById('statusList');
        const deviceInfo = document.getElementById('deviceInfo');
        const touchInfo = document.getElementById('touchInfo');
        const touchDetails = document.getElementById('touchDetails');
        const testResults = document.getElementById('testResults');
        
        let testData = [];
        let touchEvents = [];
        
        function updateStatus(test, status, message = '') {
            const existing = testData.find(t => t.test === test);
            if (existing) {
                existing.status = status;
                existing.message = message;
            } else {
                testData.push({ test, status, message });
            }
            renderStatus();
        }
        
        function renderStatus() {
            const statusHtml = testData.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'warning' ? 'status-warning' : 'status-error';
                return `
                    <div class="status-item">
                        <div class="status-dot ${statusClass}"></div>
                        <span>${result.test} ${result.message}</span>
                    </div>
                `;
            }).join('');
            statusList.innerHTML = statusHtml;
        }
        
        function updateDeviceInfo() {
            const isMobile = window.innerWidth <= 768;
            const hasTouch = 'ontouchstart' in window;
            const userAgent = navigator.userAgent;
            
            deviceInfo.innerHTML = `
                屏幕: ${window.innerWidth} x ${window.innerHeight}<br>
                设备类型: ${isMobile ? '移动设备' : '桌面设备'}<br>
                触摸支持: ${hasTouch ? '支持' : '不支持'}<br>
                用户代理: ${userAgent.substring(0, 50)}...
            `;
        }
        
        function testMusicClick() {
            const musicBtn = document.getElementById('music-toggle');
            if (musicBtn) {
                // 模拟点击
                musicBtn.click();
                updateStatus('音乐点击测试', 'success', '- 已触发点击');
            } else {
                updateStatus('音乐点击测试', 'error', '- 按钮未找到');
            }
        }
        
        function testMusicDrag() {
            const musicController = document.getElementById('music-controller');
            if (musicController) {
                const rect = musicController.getBoundingClientRect();
                updateStatus('音乐拖动测试', 'success', `- 当前位置: (${Math.round(rect.left)}, ${Math.round(rect.top)})`);
            } else {
                updateStatus('音乐拖动测试', 'error', '- 控制器未找到');
            }
        }
        
        function resetPositions() {
            localStorage.removeItem('golden_ledger_music_settings');
            localStorage.removeItem('golden_ledger_background_settings');
            updateStatus('位置重置', 'success', '- 已清除');
            setTimeout(() => location.reload(), 1000);
        }
        
        function showTouchInfo() {
            touchInfo.style.display = touchInfo.style.display === 'none' ? 'block' : 'none';
            if (touchInfo.style.display === 'block') {
                touchDetails.innerHTML = touchEvents.slice(-10).map(e => 
                    `${e.time}: ${e.type} - (${e.x}, ${e.y})`
                ).join('<br>');
            }
        }
        
        // 监听触摸事件
        document.addEventListener('touchstart', (e) => {
            touchEvents.push({
                time: new Date().toLocaleTimeString(),
                type: 'touchstart',
                x: Math.round(e.touches[0].clientX),
                y: Math.round(e.touches[0].clientY)
            });
        });
        
        document.addEventListener('touchend', (e) => {
            touchEvents.push({
                time: new Date().toLocaleTimeString(),
                type: 'touchend',
                x: 0,
                y: 0
            });
        });
        
        // 监听页面加载
        window.addEventListener('load', function() {
            updateDeviceInfo();
            updateStatus('页面加载', 'success', '- 完成');
            
            // 检查设备类型
            const isMobile = window.innerWidth <= 768;
            updateStatus('设备检测', isMobile ? 'success' : 'warning', isMobile ? '- 移动设备' : '- 桌面设备');
            
            setTimeout(() => {
                // 检查音乐按钮
                const musicController = document.getElementById('music-controller');
                const musicBtn = document.getElementById('music-toggle');
                
                if (musicController && musicBtn) {
                    updateStatus('🎵 音乐按钮', 'success', '- 已找到');
                    
                    // 检查点击灵敏度
                    let clickCount = 0;
                    musicBtn.addEventListener('click', () => {
                        clickCount++;
                        updateStatus('🎵 点击响应', 'success', `- 已响应 ${clickCount} 次`);
                    });
                    
                } else {
                    updateStatus('🎵 音乐按钮', 'error', '- 未找到');
                }
                
                // 检查眼睛按钮
                const bgController = document.querySelector('.background-control');
                if (bgController) {
                    const isVisible = window.getComputedStyle(bgController).display !== 'none';
                    if (isMobile && !isVisible) {
                        updateStatus('👁️ 眼睛按钮', 'success', '- 移动端已隐藏');
                    } else if (!isMobile && isVisible) {
                        updateStatus('👁️ 眼睛按钮', 'success', '- 桌面端显示');
                    } else {
                        updateStatus('👁️ 眼睛按钮', 'warning', '- 显示状态异常');
                    }
                } else {
                    updateStatus('👁️ 眼睛按钮', 'error', '- 未找到');
                }
                
            }, 1500);
        });
        
        // 监听控制台日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.join(' ');
            if (message.includes('🎵 音乐按钮被点击')) {
                updateStatus('🎵 点击成功', 'success', '- 音乐功能触发');
            } else if (message.includes('🎵 拖动状态中，忽略点击事件')) {
                updateStatus('🎵 拖动保护', 'success', '- 防止误触发');
            }
        };
        
        console.log('📱 移动端触摸测试页面初始化完成');
    </script>
</body>
</html>

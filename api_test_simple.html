<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 API Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
        }

        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            text-align: left;
        }

        .api-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API Test</h1>
        <p>Test the Gemini API integration with Cloudflare environment variables</p>

        <div class="api-info">
            <h3>📋 API Status</h3>
            <p><strong>API Key:</strong> <span id="api-key-display">Loading...</span></p>
            <p><strong>Environment:</strong> <span id="environment">Loading...</span></p>
        </div>

        <div>
            <button class="btn" onclick="testHealthCheck()">🏥 Health Check</button>
            <button class="btn" onclick="testMessage()">💬 Test Message</button>
            <button class="btn" onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <div class="log" id="test-log">
            <div style="color: #00FF00;">[开始] API Test Page Loaded</div>
        </div>
    </div>

    <!-- Load environment config first -->
    <script src="env-config.js"></script>
    
    <!-- Load API scripts -->
    <script src="chatbot/UsageTracker.js"></script>
    <script src="chatbot/GeminiAPI.js"></script>

    <script>
        let geminiAPI = null;

        function log(message, color = '#00BFFF') {
            const testLog = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = color;
            div.textContent = `[${timestamp}] ${message}`;
            testLog.appendChild(div);
            testLog.scrollTop = testLog.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function displayApiInfo() {
            const apiKeyDisplay = document.getElementById('api-key-display');
            const environmentDisplay = document.getElementById('environment');
            
            // Check environment
            const isLocal = window.location.hostname === 'localhost' || 
                           window.location.hostname === '127.0.0.1';
            environmentDisplay.textContent = isLocal ? 'Local Development' : 'Production';
            environmentDisplay.style.color = isLocal ? '#FFD700' : '#00FF00';
            
            // Display API key info
            if (window.GEMINI_API_KEY) {
                const apiKey = window.GEMINI_API_KEY;
                if (apiKey.length > 10) {
                    apiKeyDisplay.textContent = apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 8);
                    apiKeyDisplay.style.color = '#00FF00';
                } else {
                    apiKeyDisplay.textContent = apiKey;
                    apiKeyDisplay.style.color = '#FFD700';
                }
            } else {
                apiKeyDisplay.textContent = 'Not configured';
                apiKeyDisplay.style.color = '#FF4444';
            }
        }

        async function testHealthCheck() {
            log('🏥 Starting health check...', '#00BFFF');
            
            try {
                if (!geminiAPI) {
                    geminiAPI = new GeminiAPI();
                    log('✅ GeminiAPI initialized', '#00FF00');
                }
                
                const isHealthy = await geminiAPI.healthCheck();
                
                if (isHealthy) {
                    log('✅ Health check PASSED - API is working!', '#00FF00');
                } else {
                    log('❌ Health check FAILED', '#FF4444');
                }
            } catch (error) {
                log(`❌ Health check ERROR: ${error.message}`, '#FF4444');
            }
        }

        async function testMessage() {
            log('💬 Testing message...', '#00BFFF');
            
            try {
                if (!geminiAPI) {
                    geminiAPI = new GeminiAPI();
                }
                
                const response = await geminiAPI.sendMessage('こんにちは！家計管理について教えてください。', 'advice');
                log(`✅ Message sent successfully!`, '#00FF00');
                log(`📝 Response length: ${response.length} characters`, '#FFD700');
                log(`📄 Response preview: ${response.substring(0, 100)}...`, '#FFD700');
                
            } catch (error) {
                log(`❌ Message ERROR: ${error.message}`, '#FF4444');
            }
        }

        function clearLog() {
            const testLog = document.getElementById('test-log');
            testLog.innerHTML = '<div style="color: #00FF00;">[清除] Log cleared</div>';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 API test page loaded', '#00FF00');
            displayApiInfo();
            
            setTimeout(() => {
                log('🚀 Ready for testing!', '#FFD700');
            }, 1000);
        });
    </script>
</body>
</html>

/**
 * GoldenLedger - 重新设计的背景控制系统
 * 独立按钮设计，初始状态完全关闭
 */

class BackgroundController {
    constructor() {
        this.animationIntervals = {
            sakura: null,
            snow: null,
            star: null
        };
        this.init();
    }

    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeComponents();
            });
        } else {
            this.initializeComponents();
        }
    }

    initializeComponents() {
        console.log('👁️ 初始化背景控制器...');
        this.createControlPanel();
        this.createBackgroundContainer();
        this.bindEvents();
        this.preloadBackgroundImage();
        this.startSakuraAnimation(); // 默认动画
        console.log('✅ 背景控制器初始化完成');
    }

    isMobileDevice() {
        return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    createControlPanel() {
        const controlPanel = document.createElement('div');
        controlPanel.className = 'background-control';
        controlPanel.id = 'backgroundControl';
        
        controlPanel.innerHTML = `
            <!-- 独立眼睛按钮 -->
            <button id="background-toggle" class="background-btn" title="背景设置">
                👁️
            </button>
            
            <!-- 背景设置面板（完全隐藏） -->
            <div id="background-panel" class="background-panel">
                <div class="panel-arrow"></div>
                <div class="panel-content">
                    <div class="panel-header">
                        <h4>🌸 背景设置</h4>
                        <button id="close-background-panel" class="close-btn">✕</button>
                    </div>
                    
                    <!-- 背景图片控制 -->
                    <div class="control-section">
                        <div class="toggle-option">
                            <input type="checkbox" id="backgroundToggle" checked>
                            <label for="backgroundToggle">🖼️ 樱花背景</label>
                        </div>
                    </div>
                    
                    <!-- 动画效果控制 -->
                    <div class="control-section">
                        <div class="section-title">动画效果</div>
                        <div class="toggle-option">
                            <input type="radio" id="sakura" name="animation" value="sakura" checked>
                            <label for="sakura">🌸 樱花飘落</label>
                        </div>
                        <div class="toggle-option">
                            <input type="radio" id="snow" name="animation" value="snow">
                            <label for="snow">❄️ 雪花飞舞</label>
                        </div>
                        <div class="toggle-option">
                            <input type="radio" id="star" name="animation" value="star">
                            <label for="star">✨ 星光闪烁</label>
                        </div>
                        <div class="toggle-option">
                            <input type="radio" id="none" name="animation" value="none">
                            <label for="none">🚫 关闭动画</label>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加新的样式
        const style = document.createElement('style');
        style.id = 'background-control-styles';
        style.textContent = `
            /* 背景控制器容器 - 可拖动 */
            .background-control {
                position: fixed;
                z-index: 99998;
                /* 移除固定位置，允许 JavaScript 控制 */
            }

            /* 移动端适配 - 隐藏眼睛按钮 */
            @media (max-width: 768px) {
                .background-control {
                    display: none !important; /* 在移动端完全隐藏 */
                }
            }
            
            /* 独立眼睛按钮 - 可拖拽，低调透明 */
            .background-btn {
                width: 40px;
                height: 40px;
                border: none;
                border-radius: 50%;
                background: linear-gradient(135deg, rgba(255, 154, 158, 0.5) 0%, rgba(254, 207, 239, 0.5) 100%);
                color: rgba(255, 255, 255, 0.8);
                cursor: move;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 2px 8px rgba(255, 154, 158, 0.2);
                position: relative;
                border: 2px solid rgba(255, 255, 255, 0.1);
                user-select: none;
                touch-action: none;
                opacity: 0.5;
                backdrop-filter: blur(10px);
            }

            .background-btn.dragging {
                cursor: grabbing;
                transform: scale(1.1);
                box-shadow: 0 8px 30px rgba(255, 154, 158, 0.8);
                z-index: 100000;
            }
            
            .background-btn:hover:not(.dragging) {
                transform: translateY(-3px) scale(1.05);
                box-shadow: 0 8px 25px rgba(255, 154, 158, 0.6);
                background: linear-gradient(135deg, #fecfef 0%, #ff9a9e 100%);
            }

            .background-btn.dragging {
                cursor: grabbing !important;
                transform: scale(1.15) !important;
                box-shadow: 0 8px 30px rgba(255, 154, 158, 0.8);
                z-index: 100000;
                transition: none !important;
            }
            
            .background-btn:active {
                transform: translateY(-1px) scale(0.98);
            }
            
            /* 背景设置面板 */
            .background-panel {
                position: absolute;
                top: 55px;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                padding: 0;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                border: 2px solid rgba(255, 154, 158, 0.3);
                min-width: 250px;
                z-index: 100000;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px) scale(0.95);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
            }
            
            .background-panel.show {
                opacity: 1;
                visibility: visible;
                transform: translateY(0) scale(1);
            }
            
            /* 指向眼睛按钮的箭头 */
            .panel-arrow {
                position: absolute;
                top: -10px;
                right: 15px;
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-bottom: 10px solid rgba(255, 154, 158, 0.3);
            }
            
            .panel-arrow::after {
                content: '';
                position: absolute;
                top: 2px;
                left: -8px;
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-bottom: 8px solid rgba(255, 255, 255, 0.98);
            }
            
            .panel-content {
                padding: 20px;
            }
            
            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 2px solid rgba(255, 154, 158, 0.2);
            }
            
            .panel-header h4 {
                margin: 0;
                color: #333;
                font-size: 16px;
                font-weight: 600;
            }
            
            .close-btn {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #999;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }
            
            .close-btn:hover {
                background: rgba(255, 154, 158, 0.1);
                color: #ff9a9e;
                transform: scale(1.1);
            }
            
            .control-section {
                margin-bottom: 20px;
            }
            
            .control-section:last-child {
                margin-bottom: 0;
            }
            
            .section-title {
                font-size: 14px;
                font-weight: 600;
                color: #666;
                margin-bottom: 12px;
                text-align: center;
            }
            
            .toggle-option {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                padding: 8px 12px;
                border-radius: 10px;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            
            .toggle-option:hover {
                background: rgba(255, 154, 158, 0.1);
            }
            
            .toggle-option input[type="checkbox"],
            .toggle-option input[type="radio"] {
                margin-right: 10px;
                transform: scale(1.2);
                accent-color: #ff9a9e;
            }
            
            .toggle-option label {
                cursor: pointer;
                font-size: 14px;
                color: #333;
                font-weight: 500;
                flex: 1;
            }
            
            .toggle-option input:checked + label {
                color: #ff9a9e;
                font-weight: 600;
            }
        `;

        // 移除旧样式
        const oldStyle = document.getElementById('background-control-styles');
        if (oldStyle) {
            oldStyle.remove();
        }

        // 确保DOM已加载
        if (document.head) {
            document.head.appendChild(style);
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                document.head.appendChild(style);
            });
        }

        if (document.body) {
            document.body.appendChild(controlPanel);
            // 设置默认位置并添加拖动功能
            this.setDefaultPosition(controlPanel);
            this.makeDraggable(controlPanel);
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                document.body.appendChild(controlPanel);
                // 设置默认位置并添加拖动功能
                this.setDefaultPosition(controlPanel);
                this.makeDraggable(controlPanel);
            });
        }
    }

    bindEvents() {
        setTimeout(() => {
            const toggleBtn = document.getElementById('background-toggle');
            const panel = document.getElementById('background-panel');
            const closeBtn = document.getElementById('close-background-panel');

            if (toggleBtn && panel && closeBtn) {
                let isPanelVisible = false;

                // 眼睛按钮点击事件（避免与拖动冲突）
                let clickStartTime = 0;
                let clickStartPos = { x: 0, y: 0 };

                toggleBtn.addEventListener('mousedown', (e) => {
                    clickStartTime = Date.now();
                    clickStartPos = { x: e.clientX, y: e.clientY };
                });

                toggleBtn.addEventListener('touchstart', (e) => {
                    clickStartTime = Date.now();
                    clickStartPos = { x: e.touches[0].clientX, y: e.touches[0].clientY };
                });

                toggleBtn.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // 检查是否正在拖动或刚刚拖动完成
                    const bgController = document.querySelector('.background-control');
                    if (bgController && bgController._isDragging) {
                        console.log('👁️ 拖动状态中，忽略点击事件');
                        return;
                    }

                    // 检查是否是快速点击（不是拖动）
                    const timeDiff = Date.now() - clickStartTime;
                    const currentPos = {
                        x: e.clientX || clickStartPos.x,
                        y: e.clientY || clickStartPos.y
                    };
                    const distance = Math.sqrt(
                        Math.pow(currentPos.x - clickStartPos.x, 2) +
                        Math.pow(currentPos.y - clickStartPos.y, 2)
                    );

                    // 如果移动距离小于5px且时间小于300ms，认为是点击
                    if (distance < 5 && timeDiff < 300) {
                        console.log('👁️ 眼睛按钮被点击');

                        if (!isPanelVisible) {
                            panel.classList.add('show');
                            isPanelVisible = true;
                        } else {
                            panel.classList.remove('show');
                            isPanelVisible = false;
                        }
                    } else {
                        console.log('👁️ 拖动后忽略点击事件', { distance, timeDiff });
                    }
                });

                // 关闭按钮
                closeBtn.addEventListener('click', () => {
                    panel.classList.remove('show');
                    isPanelVisible = false;
                });

                // 点击外部关闭面板
                document.addEventListener('click', (e) => {
                    if (isPanelVisible && 
                        !panel.contains(e.target) && 
                        !toggleBtn.contains(e.target)) {
                        panel.classList.remove('show');
                        isPanelVisible = false;
                    }
                });

                // 阻止面板内部点击事件冒泡
                panel.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }

            // 背景图片切换
            const backgroundToggle = document.getElementById('backgroundToggle');
            if (backgroundToggle) {
                backgroundToggle.addEventListener('change', (e) => {
                    this.toggleBackgroundImage(e.target.checked);
                });
            }

            // 动画切换
            document.querySelectorAll('input[name="animation"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    this.switchAnimation(e.target.value);
                });
            });
        }, 100);
    }

    createBackgroundContainer() {
        // 创建美丽的樱花背景层
        if (!document.getElementById('sakura-background-layer')) {
            const backgroundLayer = document.createElement('div');
            backgroundLayer.id = 'sakura-background-layer';
            backgroundLayer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: -10;
                pointer-events: none;
                background: linear-gradient(135deg,
                    rgba(255, 182, 193, 0.1) 0%,
                    rgba(255, 192, 203, 0.15) 25%,
                    rgba(255, 218, 185, 0.1) 50%,
                    rgba(255, 240, 245, 0.2) 75%,
                    rgba(255, 182, 193, 0.1) 100%
                );
                backdrop-filter: blur(0.5px);
            `;
            document.body.insertBefore(backgroundLayer, document.body.firstChild);
        }

        // 创建樱花图片背景层
        if (!document.getElementById('sakura-image-layer')) {
            const imageLayer = document.createElement('div');
            imageLayer.id = 'sakura-image-layer';
            imageLayer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: -9;
                pointer-events: none;
                background-image: url('/image/sakura.png'), url('/static/image/sakura.png');
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                opacity: 0.3;
                filter: blur(1px) brightness(1.2) saturate(1.3);
                transition: opacity 0.8s ease, filter 0.8s ease;
            `;
            document.body.insertBefore(imageLayer, document.body.firstChild);
        }

        // 创建动画容器 - 美丽的半透明效果
        if (!document.getElementById('sakura-animation-layer')) {
            const animationLayer = document.createElement('div');
            animationLayer.id = 'sakura-animation-layer';
            animationLayer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: -8;
                pointer-events: none;
                overflow: hidden;
                background: radial-gradient(
                    ellipse at center,
                    rgba(255, 182, 193, 0.05) 0%,
                    rgba(255, 192, 203, 0.02) 50%,
                    transparent 100%
                );
            `;
            document.body.insertBefore(animationLayer, document.body.firstChild);
        }

        // 创建前景动画层 - 更明显的花瓣
        if (!document.getElementById('sakura-foreground-layer')) {
            const foregroundLayer = document.createElement('div');
            foregroundLayer.id = 'sakura-foreground-layer';
            foregroundLayer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: 1;
                pointer-events: none;
                overflow: hidden;
            `;
            document.body.appendChild(foregroundLayer);
        }
    }

    preloadBackgroundImage() {
        const img = new Image();
        img.src = '/image/sakura.png';
        img.onerror = () => {
            // 如果主路径失败，尝试备用路径
            const img2 = new Image();
            img2.src = '/static/image/sakura.png';
        };
    }

    startSakuraAnimation() {
        this.stopAllAnimations();

        const backgroundLayer = document.getElementById('sakura-animation-layer');
        const foregroundLayer = document.getElementById('sakura-foreground-layer');

        if (!backgroundLayer || !foregroundLayer) return;

        // 创建背景花瓣（更多、更小、更透明）
        const createBackgroundPetal = () => {
            const petal = document.createElement('div');
            const petalTypes = ['🌸', '🌺', '🌼', '🌻'];
            const randomPetal = petalTypes[Math.floor(Math.random() * petalTypes.length)];
            petal.innerHTML = randomPetal;

            const size = Math.random() * 15 + 8;
            const duration = Math.random() * 8 + 6;
            const delay = Math.random() * 2;
            const opacity = Math.random() * 0.4 + 0.1;

            petal.style.cssText = `
                position: absolute;
                font-size: ${size}px;
                left: ${Math.random() * 110 - 5}%;
                top: -50px;
                opacity: ${opacity};
                filter: blur(0.5px);
                animation: sakuraFall ${duration}s linear ${delay}s infinite;
                pointer-events: none;
                text-shadow: 0 0 10px rgba(255, 182, 193, 0.5);
            `;

            backgroundLayer.appendChild(petal);

            setTimeout(() => {
                if (petal.parentNode) {
                    petal.parentNode.removeChild(petal);
                }
            }, (duration + delay) * 1000);
        };

        // 创建前景花瓣（较少、较大、更清晰）
        const createForegroundPetal = () => {
            const petal = document.createElement('div');
            petal.innerHTML = '🌸';

            const size = Math.random() * 25 + 20;
            const duration = Math.random() * 5 + 4;
            const opacity = Math.random() * 0.6 + 0.3;

            petal.style.cssText = `
                position: absolute;
                font-size: ${size}px;
                left: ${Math.random() * 100}%;
                top: -50px;
                opacity: ${opacity};
                animation: sakuraFallForeground ${duration}s ease-in-out infinite;
                pointer-events: none;
                text-shadow: 0 0 15px rgba(255, 182, 193, 0.8);
                filter: drop-shadow(0 0 5px rgba(255, 192, 203, 0.6));
            `;

            foregroundLayer.appendChild(petal);

            setTimeout(() => {
                if (petal.parentNode) {
                    petal.parentNode.removeChild(petal);
                }
            }, duration * 1000);
        };

        // 添加美丽的CSS动画
        if (!document.getElementById('sakura-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'sakura-animation-styles';
            style.textContent = `
                @keyframes sakuraFall {
                    0% {
                        transform: translateY(-50px) rotate(0deg) scale(1);
                        opacity: 0;
                    }
                    10% {
                        opacity: 1;
                    }
                    90% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(100vh) rotate(360deg) scale(0.8);
                        opacity: 0;
                    }
                }

                @keyframes sakuraFallForeground {
                    0% {
                        transform: translateY(-50px) rotate(0deg) scale(1);
                        opacity: 0;
                    }
                    5% {
                        opacity: 1;
                    }
                    95% {
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(100vh) rotate(720deg) scale(0.5);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // 启动动画
        this.animationIntervals.sakura = setInterval(() => {
            createBackgroundPetal();
            if (Math.random() < 0.3) { // 30%概率创建前景花瓣
                createForegroundPetal();
            }
        }, 200);
    }

    stopAllAnimations() {
        Object.keys(this.animationIntervals).forEach(key => {
            if (this.animationIntervals[key]) {
                clearInterval(this.animationIntervals[key]);
                this.animationIntervals[key] = null;
            }
        });

        // 清理所有动画层
        const backgroundLayer = document.getElementById('sakura-animation-layer');
        const foregroundLayer = document.getElementById('sakura-foreground-layer');

        if (backgroundLayer) {
            backgroundLayer.innerHTML = '';
        }
        if (foregroundLayer) {
            foregroundLayer.innerHTML = '';
        }
    }

    toggleBackgroundImage(show) {
        const imageLayer = document.getElementById('sakura-image-layer');
        const backgroundLayer = document.getElementById('sakura-background-layer');

        if (imageLayer) {
            imageLayer.style.opacity = show ? '0.3' : '0';
        }
        if (backgroundLayer) {
            backgroundLayer.style.opacity = show ? '1' : '0.5';
        }
    }

    switchAnimation(type) {
        this.stopAllAnimations();

        switch (type) {
            case 'sakura':
                this.startSakuraAnimation();
                break;
            case 'snow':
                this.startSnowAnimation();
                break;
            case 'star':
                this.startStarAnimation();
                break;
            case 'none':
            default:
                // 不启动任何动画
                break;
        }
    }

    startSnowAnimation() {
        const container = document.getElementById('animation-container');
        if (!container) return;

        const createSnowflake = () => {
            const snowflake = document.createElement('div');
            snowflake.innerHTML = '❄️';
            snowflake.style.cssText = `
                position: absolute;
                font-size: ${Math.random() * 15 + 10}px;
                left: ${Math.random() * 100}%;
                top: -50px;
                animation: fall ${Math.random() * 4 + 3}s linear infinite;
                pointer-events: none;
                z-index: 2;
            `;

            container.appendChild(snowflake);

            setTimeout(() => {
                if (snowflake.parentNode) {
                    snowflake.parentNode.removeChild(snowflake);
                }
            }, 7000);
        };

        this.animationIntervals.snow = setInterval(createSnowflake, 200);
    }

    startStarAnimation() {
        const container = document.getElementById('animation-container');
        if (!container) return;

        const createStar = () => {
            const star = document.createElement('div');
            star.innerHTML = '✨';
            star.style.cssText = `
                position: absolute;
                font-size: ${Math.random() * 20 + 10}px;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: twinkle ${Math.random() * 2 + 1}s ease-in-out infinite;
                pointer-events: none;
                z-index: 2;
            `;

            container.appendChild(star);

            setTimeout(() => {
                if (star.parentNode) {
                    star.parentNode.removeChild(star);
                }
            }, 3000);
        };

        // 添加闪烁动画
        if (!document.getElementById('star-animation-style')) {
            const style = document.createElement('style');
            style.id = 'star-animation-style';
            style.textContent = `
                @keyframes twinkle {
                    0%, 100% { opacity: 0; transform: scale(0.5); }
                    50% { opacity: 1; transform: scale(1.2); }
                }
            `;
            document.head.appendChild(style);
        }

        this.animationIntervals.star = setInterval(createStar, 500);
    }

    // 设置默认位置
    setDefaultPosition(element) {
        // 检查是否有保存的位置
        try {
            const settings = JSON.parse(localStorage.getItem('golden_ledger_background_settings') || '{}');
            if (settings.position) {
                // 如果有保存的位置，不设置默认位置
                return;
            }
        } catch (error) {
            console.error('检查保存位置失败:', error);
        }

        // 设置默认位置（左下角）
        const defaultX = 20;
        const defaultY = window.innerHeight - element.offsetHeight - 20;

        element.style.left = `${defaultX}px`;
        element.style.top = `${defaultY}px`;
        element.style.bottom = 'auto';
        element.style.right = 'auto';

        console.log('👁️ 设置默认位置:', { defaultX, defaultY });
    }

    // 添加拖动功能
    makeDraggable(element) {
        let isDragging = false;
        let hasDragged = false; // 标记是否发生了拖动
        let startX, startY, initialX, initialY;
        let currentX = 0, currentY = 0;

        const backgroundBtn = element.querySelector('#background-toggle');

        // 鼠标事件
        backgroundBtn.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);

        // 触摸事件（移动端）
        backgroundBtn.addEventListener('touchstart', startDrag, { passive: false });
        document.addEventListener('touchmove', drag, { passive: false });
        document.addEventListener('touchend', endDrag, { passive: true });

        function startDrag(e) {
            e.preventDefault();
            isDragging = true;
            hasDragged = false; // 重置拖动标记

            const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

            // 获取当前元素的实际位置
            const rect = element.getBoundingClientRect();
            currentX = rect.left;
            currentY = rect.top;

            startX = clientX - currentX;
            startY = clientY - currentY;

            element.style.transition = 'none';
            element.style.cursor = 'grabbing';
            backgroundBtn.classList.add('dragging');

            // 设置全局拖动状态，防止点击事件触发
            element._isDragging = true;

            console.log('👁️ 开始拖动:', { currentX, currentY, clientX, clientY });
        }

        function drag(e) {
            if (!isDragging) return;

            e.preventDefault();

            const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

            // 标记已经发生拖动
            hasDragged = true;

            currentX = clientX - startX;
            currentY = clientY - startY;

            // 限制在屏幕范围内
            const maxX = window.innerWidth - element.offsetWidth;
            const maxY = window.innerHeight - element.offsetHeight;

            currentX = Math.max(0, Math.min(currentX, maxX));
            currentY = Math.max(0, Math.min(currentY, maxY));

            // 直接设置 left 和 top
            element.style.left = `${currentX}px`;
            element.style.top = `${currentY}px`;
            element.style.right = 'auto';
            element.style.bottom = 'auto';

            // 调试信息（仅在拖动时偶尔输出）
            if (Math.random() < 0.1) {
                console.log('👁️ 拖动中:', { currentX, currentY, maxX, maxY });
            }
        }

        function endDrag(e) {
            if (!isDragging) return;

            isDragging = false;
            element.style.transition = 'all 0.3s ease';
            element.style.cursor = 'grab';
            backgroundBtn.classList.remove('dragging');

            // 延迟清除拖动状态，防止立即触发点击
            setTimeout(() => {
                element._isDragging = false;
            }, 100);

            // 保存位置
            try {
                const settings = JSON.parse(localStorage.getItem('golden_ledger_background_settings') || '{}');
                settings.position = { x: currentX, y: currentY };
                localStorage.setItem('golden_ledger_background_settings', JSON.stringify(settings));

                if (hasDragged) {
                    console.log('👁️ 拖动结束，位置已保存:', { currentX, currentY });
                }
            } catch (error) {
                console.error('保存眼睛按钮位置失败:', error);
            }
        }

        // 加载保存的位置
        try {
            const settings = JSON.parse(localStorage.getItem('golden_ledger_background_settings') || '{}');
            if (settings.position) {
                currentX = settings.position.x || 0;
                currentY = settings.position.y || 0;
                // 直接设置位置
                element.style.left = `${currentX}px`;
                element.style.top = `${currentY}px`;
                element.style.right = 'auto';
                element.style.bottom = 'auto';
                console.log('👁️ 加载保存的位置:', currentX, currentY);
            }
        } catch (error) {
            console.error('加载眼睛按钮位置失败:', error);
        }

        // 设置初始样式
        element.style.cursor = 'grab';
        element.style.userSelect = 'none';
        backgroundBtn.style.cursor = 'grab';
    }
}

// 创建全局背景控制器实例
window.goldenLedgerBackground = new BackgroundController();

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_injector.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-600">🎵 音乐功能测试页面</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <ul class="list-disc list-inside space-y-2 text-gray-700">
                <li>页面右下角应该显示音乐控制按钮</li>
                <li>点击按钮可以打开音乐控制面板</li>
                <li>支持播放/暂停、音量调节、拖拽移动</li>
                <li>快捷键: Alt + M 切换播放/暂停</li>
                <li>设置会自动保存到本地存储</li>
            </ul>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">功能检查</h2>
            <div class="space-y-4">
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-button" class="w-4 h-4">
                    <label for="check-button">音乐按钮显示正常</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-panel" class="w-4 h-4">
                    <label for="check-panel">控制面板可以打开</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-play" class="w-4 h-4">
                    <label for="check-play">音乐可以播放</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-volume" class="w-4 h-4">
                    <label for="check-volume">音量控制正常</label>
                </div>
                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="check-drag" class="w-4 h-4">
                    <label for="check-drag">按钮可以拖拽移动</label>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试脚本
        setTimeout(() => {
            console.log('🎵 音乐功能测试页面已加载');
            console.log('🎵 音乐控制器状态:', !!window.musicController);
            console.log('🎵 音乐注入器状态:', !!window.musicInjector);
        }, 2000);
    </script>
</body>
</html>
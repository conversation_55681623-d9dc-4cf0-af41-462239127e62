<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoldenLedger - Smart AI-Powered Finance System | 企業級AI記帳システム</title>
    <meta name="description" content="GoldenLedger - 最先端のAI技術を活用した企業級記帳システム。自然語処理、OCR認識、リアルタイム分析で経理業務を革新します。">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="image/favicon.ico">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        /* 基础样式 */
        body {
            font-family: 'Inter', sans-serif;
        }

        /* 控制器样式 */
        #music-controller {
            position: fixed !important;
            z-index: 99999 !important;
            /* 允许 JavaScript 控制位置 */
        }

        /* 确保主要内容在背景之上 */
        .main-content, nav, header, main, section, .container {
            position: relative;
            z-index: 10;
        }

        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
        }

        /* 动画效果 */
        .hero-animation {
            animation: float 6s ease-in-out infinite;
        }

        /* 定价卡片样式 */
        .plan-card {
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .popular-badge {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: translateX(-50%) scale(1); }
            50% { transform: translateX(-50%) scale(1.05); }
        }

        /* 模态框样式 */
        .modal {
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 功能卡片 */
        .feature-card {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(108, 92, 231, 0.4);
        }
        .btn-secondary {
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* 动画关键帧 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .scroll-indicator {
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* 移动端导航菜单 */
        .mobile-menu {
            display: none;
        }
        .mobile-menu.active {
            display: block;
        }

        /* 现代响应式导航栏样式 */
        @media (max-width: 1030px) {
            .nav-desktop {
                display: none !important;
            }
        }

        @media (min-width: 1031px) {
            .nav-desktop {
                display: flex !important;
            }
        }

        /* 汉堡菜单按钮样式 */
        .hamburger {
            width: 24px;
            height: 18px;
            position: relative;
            cursor: pointer;
        }

        .hamburger-line {
            display: block;
            height: 2px;
            width: 100%;
            background: #374151;
            border-radius: 1px;
            position: absolute;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .hamburger-line:nth-child(1) {
            top: 0;
        }

        .hamburger-line:nth-child(2) {
            top: 8px;
        }

        .hamburger-line:nth-child(3) {
            top: 16px;
        }

        /* 汉堡菜单激活状态 */
        .mobile-menu-btn.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg);
            top: 8px;
        }

        .mobile-menu-btn.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-btn.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg);
            top: 8px;
        }

        /* 移动端菜单覆盖层 */
        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 40;
        }

        .mobile-menu-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* 移动端菜单内容 */
        .mobile-menu-content {
            position: absolute;
            top: 0;
            right: 0;
            width: 320px;
            max-width: 85vw;
            height: 100vh;
            background: white;
            padding: 80px 24px 24px;
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow-y: auto;
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
        }

        .mobile-menu-overlay.active .mobile-menu-content {
            transform: translateX(0);
        }

        /* 移动端菜单链接样式 */
        .mobile-menu-link {
            display: flex;
            align-items: center;
            padding: 16px 0;
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .mobile-menu-link:hover {
            color: #7c3aed;
            background: #f8fafc;
            margin: 0 -24px;
            padding-left: 24px;
            padding-right: 24px;
        }

        /* 移动端菜单关闭按钮 */
        .mobile-close-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mobile-close-btn:hover {
            background-color: #f3f4f6;
            transform: scale(1.1);
        }

        .mobile-close-btn:active {
            transform: scale(0.95);
        }

        .mobile-menu-link:last-child {
            border-bottom: none;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .hero-animation {
                animation: none; /* 移动端禁用动画以提升性能 */
            }

            .mobile-menu-content {
                width: 100vw;
                max-width: 100vw;
            }
        }

        /* 自定义断点类 */
        @media (min-width: 1031px) {
            .nav-desktop\:flex {
                display: flex !important;
            }
            .nav-desktop\:hidden {
                display: none !important;
            }
        }

        @media (max-width: 1030px) {
            .nav-desktop\:flex {
                display: none !important;
            }
            .nav-desktop\:hidden {
                display: flex !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 bg-white/90 backdrop-blur-md border-b border-gray-200" role="navigation" aria-label="メインナビゲーション">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-teal-500 rounded-xl flex items-center justify-center">
                        <span class="text-white text-xl font-bold">🚀</span>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-800">GoldenLedger</div>
                        <div class="text-xs text-gray-500">Smart AI-Powered Finance</div>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden nav-desktop:flex items-center space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-purple-600 transition-colors">機能</a>
                    <a href="#pricing" class="text-gray-600 hover:text-purple-600 transition-colors">料金</a>
                    <a href="#about" class="text-gray-600 hover:text-purple-600 transition-colors">会社概要</a>
                    <a href="#contact" class="text-gray-600 hover:text-purple-600 transition-colors">お問い合わせ</a>
                </div>

                <!-- Desktop Auth Buttons -->
                <div class="hidden nav-desktop:flex items-center space-x-4">
                    <!-- 未登录时显示的链接 -->
                    <div id="guest-links-desktop" class="flex items-center space-x-4">
                        <a href="auth/login.html" class="text-gray-600 hover:text-purple-600 transition-colors">
                            <i class="fas fa-sign-in-alt mr-1"></i>ログイン
                        </a>
                        <button onclick="quickGoogleLogin()" class="btn-primary px-6 py-2 rounded-xl text-white font-semibold hover:transform hover:scale-105 transition-all">
                            <i class="fab fa-google mr-1"></i>Googleで始める
                        </button>
                    </div>

                    <!-- 已登录时显示的用户信息 -->
                    <div id="user-info-desktop" class="hidden flex items-center space-x-3">
                        <div class="flex items-center space-x-2">
                            <img id="user-avatar-desktop" class="w-8 h-8 rounded-full border-2 border-white/20" alt="用户头像">
                            <span id="user-name-desktop" class="text-gray-700 font-medium"></span>
                        </div>
                        <div class="relative">
                            <button onclick="toggleUserMenu('desktop')" class="text-gray-600 hover:text-purple-600 transition-colors">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div id="user-menu-desktop" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <a href="frontend/ai-enhanced.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-robot mr-2"></i>AI记账
                                </a>
                                <a href="api_key_setup.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-key mr-2"></i>API密钥设置
                                </a>
                                <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-user mr-2"></i>个人设置
                                </a>
                                <div class="border-t border-gray-200"></div>
                                <button onclick="logout()" class="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50">
                                    <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <div class="nav-desktop:hidden flex items-center space-x-3">
                    <!-- Mobile Auth Quick Access -->
                    <div id="guest-links-mobile" class="flex items-center space-x-2">
                        <button onclick="quickGoogleLogin()" class="btn-primary px-3 py-1 rounded-lg text-white font-semibold text-sm">
                            <i class="fab fa-google mr-1"></i>始める
                        </button>
                    </div>

                    <!-- Mobile User Info -->
                    <div id="user-info-mobile" class="hidden">
                        <img id="user-avatar-mobile" class="w-8 h-8 rounded-full border-2 border-white/20" alt="用户头像">
                    </div>

                    <!-- Hamburger Menu Button -->
                    <button
                        onclick="toggleMobileMenu()"
                        class="mobile-menu-btn p-2 rounded-lg hover:bg-gray-100 transition-colors"
                        aria-label="メニューを開く"
                        aria-expanded="false">
                        <div class="hamburger">
                            <span class="hamburger-line"></span>
                            <span class="hamburger-line"></span>
                            <span class="hamburger-line"></span>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu Overlay -->
            <div id="mobile-menu-overlay" class="mobile-menu-overlay nav-desktop:hidden">
                <div class="mobile-menu-content">
                    <!-- Close Button -->
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">メニュー</h3>
                        <button onclick="closeMobileMenu()" class="mobile-close-btn p-2 rounded-full hover:bg-gray-100 transition-colors">
                            <i class="fas fa-times text-gray-600 text-xl"></i>
                        </button>
                    </div>

                    <!-- Navigation Links -->
                    <div class="space-y-1">
                        <a href="#features" class="mobile-menu-link" onclick="closeMobileMenu()">
                            <i class="fas fa-star mr-3"></i>機能
                        </a>
                        <a href="#pricing" class="mobile-menu-link" onclick="closeMobileMenu()">
                            <i class="fas fa-coins mr-3"></i>料金
                        </a>
                        <a href="#about" class="mobile-menu-link" onclick="closeMobileMenu()">
                            <i class="fas fa-building mr-3"></i>会社概要
                        </a>
                        <a href="#contact" class="mobile-menu-link" onclick="closeMobileMenu()">
                            <i class="fas fa-envelope mr-3"></i>お問い合わせ
                        </a>
                    </div>

                    <!-- Divider -->
                    <div class="border-t border-gray-200 my-6"></div>

                    <!-- Auth Section -->
                    <div id="mobile-auth-section">
                        <!-- Guest Links -->
                        <div id="guest-links-mobile-menu" class="space-y-3">
                            <a href="auth/login.html" class="mobile-menu-link" onclick="closeMobileMenu()">
                                <i class="fas fa-sign-in-alt mr-3"></i>ログイン
                            </a>
                            <button onclick="quickGoogleLogin()" class="w-full btn-primary px-4 py-3 rounded-xl text-white font-semibold flex items-center justify-center">
                                <i class="fab fa-google mr-2"></i>Googleで始める
                            </button>
                        </div>

                        <!-- User Menu -->
                        <div id="user-menu-mobile" class="hidden space-y-1">
                            <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg mb-4">
                                <img id="user-avatar-mobile-menu" class="w-10 h-10 rounded-full" alt="用户头像">
                                <div>
                                    <div id="user-name-mobile-menu" class="font-medium text-gray-900"></div>
                                    <div class="text-sm text-gray-500">ログイン中</div>
                                </div>
                            </div>
                            <a href="frontend/ai-enhanced.html" class="mobile-menu-link" onclick="closeMobileMenu()">
                                <i class="fas fa-robot mr-3"></i>AI記帳
                            </a>
                            <a href="api_key_setup.html" class="mobile-menu-link" onclick="closeMobileMenu()">
                                <i class="fas fa-key mr-3"></i>APIキー設定
                            </a>
                            <a href="#" class="mobile-menu-link" onclick="closeMobileMenu()">
                                <i class="fas fa-user mr-3"></i>個人設定
                            </a>
                            <button onclick="logout(); closeMobileMenu();" class="w-full text-left mobile-menu-link text-red-600">
                                <i class="fas fa-sign-out-alt mr-3"></i>ログアウト
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg text-white pt-24 pb-16 min-h-screen flex items-center">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="text-center lg:text-left">
                    <div class="inline-flex items-center bg-white/20 rounded-full px-4 py-2 mb-6">
                        <span class="pulse-dot w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        <span class="text-sm">AI Engine Active</span>
                    </div>


                    
                    <h1 class="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                        AI記帳の
                        <span class="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                            黄金時代
                        </span>
                        がここに
                    </h1>
                    
                    <p class="text-xl lg:text-2xl opacity-90 mb-8 leading-relaxed">
                        自然語処理とOCR技術で経理業務を革新。<br>
                        一言で仕訳を生成し、リアルタイムで財務状況を把握。
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <a href="register.html" class="btn-primary px-8 py-4 rounded-xl text-lg font-semibold text-center">
                            🚀 無料で始める
                        </a>
                        <a href="#demo" class="btn-secondary px-8 py-4 rounded-xl text-lg font-semibold text-center text-white">
                            📺 デモを見る
                        </a>
                    </div>
                    
                    <div class="flex items-center justify-center lg:justify-start space-x-8 text-sm opacity-80">
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                            <span>無料トライアル</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-blue-400 rounded-full"></span>
                            <span>クレジットカード不要</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                            <span>即座に開始</span>
                        </div>
                    </div>
                </div>
                
                <div class="hero-animation">
                    <div class="relative">
                        <div class="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                </div>
                                <div class="bg-gray-800 rounded-lg p-4 text-green-400 font-mono text-sm">
                                    <div class="mb-2">$ goldenledger --ai-mode</div>
                                    <div class="text-white">💬 "今日コンビニで文具を1200円で購入"</div>
                                    <div class="mt-2 text-blue-400">🤖 AI処理中...</div>
                                    <div class="mt-2 text-green-400">✅ 仕訳生成完了</div>
                                    <div class="mt-1 text-gray-300">借方: 事務用品費 1,200円</div>
                                    <div class="text-gray-300">貸方: 現金 1,200円</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Floating elements -->
                        <div class="absolute -top-4 -right-4 w-20 h-20 bg-yellow-400/20 rounded-full"></div>
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-blue-400/20 rounded-full"></div>
                    </div>
                </div>
            </div>
            
            <!-- Scroll indicator -->
            <div class="text-center mt-16">
                <div class="scroll-indicator inline-block">
                    <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                </div>
                <div class="text-white/60 text-sm mt-2">スクロールして詳細を見る</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">🎯 革新的な機能</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    最先端のAI技術と直感的なUIで、経理業務の効率を劇的に向上させます
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- AI Natural Language -->
                <div class="feature-card bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8 border border-blue-200">
                    <div class="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-3xl">🤖</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">AI自然語処理</h3>
                    <p class="text-gray-600 mb-6">
                        「コンビニで文具を1200円で購入」と入力するだけで、自動的に仕訳を生成。複雑な会計知識は不要です。
                    </p>
                    <div class="space-y-3">
                        <a href="interactive_demo" class="flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                            <span>🚀 AI増強版を試す</span>
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                        <div class="text-xs text-gray-500">
                            ✨ 新機能: 高精度AI分析、学習機能、詳細統計
                        </div>
                    </div>
                </div>

                <!-- OCR Recognition -->
                <div class="feature-card bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-8 border border-green-200">
                    <div class="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-3xl">📄</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">OCR自動認識</h3>
                    <p class="text-gray-600 mb-6">
                        レシートや請求書の写真を撮るだけで、金額・日付・内容を自動抽出し、仕訳を提案します。
                    </p>
                    <div class="flex items-center text-green-600 font-semibold">
                        <span>詳細を見る</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>

                <!-- Real-time Analytics -->
                <div class="feature-card bg-gradient-to-br from-purple-50 to-violet-100 rounded-2xl p-8 border border-purple-200">
                    <div class="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-3xl">📊</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">リアルタイム分析</h3>
                    <p class="text-gray-600 mb-6">
                        財務状況をリアルタイムで可視化。キャッシュフロー、損益、予算達成率を一目で把握できます。
                    </p>
                    <div class="flex items-center text-purple-600 font-semibold">
                        <span>詳細を見る</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>

                <!-- Multi-language Support -->
                <div class="feature-card bg-gradient-to-br from-orange-50 to-red-100 rounded-2xl p-8 border border-orange-200">
                    <div class="w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-3xl">🌍</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">多言語対応</h3>
                    <p class="text-gray-600 mb-6">
                        日本語、英語、中国語に対応。グローバル企業の複雑な会計処理もスムーズに。
                    </p>
                    <div class="flex items-center text-orange-600 font-semibold">
                        <span>詳細を見る</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>

                <!-- Enterprise Security -->
                <div class="feature-card bg-gradient-to-br from-gray-50 to-slate-100 rounded-2xl p-8 border border-gray-200">
                    <div class="w-16 h-16 bg-gray-700 rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-3xl">🔒</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">企業級セキュリティ</h3>
                    <p class="text-gray-600 mb-6">
                        SOC2準拠、エンドツーエンド暗号化、多要素認証で、機密財務データを完全保護。
                    </p>
                    <div class="flex items-center text-gray-700 font-semibold">
                        <span>詳細を見る</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>

                <!-- Cloud Integration -->
                <div class="feature-card bg-gradient-to-br from-teal-50 to-cyan-100 rounded-2xl p-8 border border-teal-200">
                    <div class="w-16 h-16 bg-teal-500 rounded-2xl flex items-center justify-center mb-6">
                        <span class="text-3xl">☁️</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">クラウド統合</h3>
                    <p class="text-gray-600 mb-6">
                        銀行API、会計ソフト、ERPシステムとシームレスに連携。データの一元管理を実現。
                    </p>
                    <div class="flex items-center text-teal-600 font-semibold">
                        <span>詳細を見る</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold text-gray-800 mb-6">🎬 デモを体験</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                実際のAI記帳システムを体験してみてください
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="interactive_demo" class="btn-primary px-8 py-4 rounded-xl text-lg font-semibold text-white">
                    🤖 AI記帳デモ
                </a>
                <a href="master_dashboard.html" class="bg-gray-600 hover:bg-gray-700 px-8 py-4 rounded-xl text-lg font-semibold text-white transition-colors">
                    📊 ダッシュボード
                </a>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <!-- 标题部分 -->
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-coins mr-3 text-yellow-500"></i>料金プラン
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                    あなたのビジネスに最適なプランを選択してください。<br>
                    すべてのプランで7日間の無料トライアルをご利用いただけます。
                </p>

                <!-- 登录提示 -->
                <div id="pricing-login-notice" class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto mb-8">
                    <div class="flex items-center justify-center space-x-3">
                        <i class="fas fa-info-circle text-blue-500"></i>
                        <span class="text-blue-800 font-medium">プランを購入するには、まずアカウントを作成してください</span>
                    </div>
                    <div class="mt-3 flex justify-center space-x-4">
                        <button onclick="showLoginModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-sign-in-alt mr-2"></i>ログイン
                        </button>
                        <button onclick="quickGoogleLogin()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            <i class="fab fa-google mr-2"></i>Googleで登録
                        </button>
                    </div>
                </div>
            </div>

            <!-- 定价卡片 -->
            <div class="grid md:grid-cols-4 gap-8 max-w-7xl mx-auto">

                <!-- フリープラン -->
                <div class="plan-card bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">フリープラン</h3>
                        <div class="text-4xl font-bold text-gray-600 mb-2">¥0</div>
                        <div class="text-gray-500">永続無料</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>AI記帳：月10回まで</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>データ保存：30日間</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>基本的な記帳機能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>簡単なレポート</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times mr-3"></i>
                            <span>PDFエクスポート</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times mr-3"></i>
                            <span>OCR機能</span>
                        </li>
                    </ul>

                    <button onclick="handlePlanSelection('free')" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        無料で始める
                    </button>
                </div>

                <!-- ベーシックプラン -->
                <div class="plan-card bg-white rounded-2xl shadow-lg p-8 border-2 border-blue-200 hover:border-blue-300 transition-all duration-300">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-blue-600 mb-2">ベーシックプラン</h3>
                        <div class="text-4xl font-bold text-blue-600 mb-2">¥980</div>
                        <div class="text-gray-500">月額（税込）</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>AI記帳：月100回まで</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>データ保存：1年間</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>OCR機能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>PDFエクスポート</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>詳細レポート</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times mr-3"></i>
                            <span>API統合</span>
                        </li>
                    </ul>

                    <button onclick="handlePlanSelection('basic')" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        ベーシックを選ぶ
                    </button>
                </div>

                <!-- プロプラン -->
                <div class="plan-card bg-white rounded-2xl shadow-xl p-8 border-2 border-purple-300 relative transform hover:scale-105 transition-all duration-300">
                    <div class="popular-badge absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                        🔥 人気No.1
                    </div>
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-purple-600 mb-2">プロプラン</h3>
                        <div class="text-4xl font-bold text-purple-600 mb-2">¥2,980</div>
                        <div class="text-gray-500">月額（税込）</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>AI記帳：無制限</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>データ保存：無制限</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>高度なOCR機能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>API統合</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>リアルタイム分析</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>優先サポート</span>
                        </li>
                    </ul>

                    <button onclick="handlePlanSelection('pro')" class="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white py-3 rounded-lg font-semibold transition-all duration-300 shadow-lg">
                        プロを選ぶ
                    </button>
                </div>

                <!-- エンタープライズプラン -->
                <div class="plan-card bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">エンタープライズ</h3>
                        <div class="text-4xl font-bold text-gray-600 mb-2">カスタム</div>
                        <div class="text-gray-500">お問い合わせ</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>全機能利用可能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>専用サポート</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>カスタム統合</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>SLA保証</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>オンプレミス対応</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>専用アカウントマネージャー</span>
                        </li>
                    </ul>

                    <button onclick="handleEnterpriseContact()" class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        お問い合わせ
                    </button>
                </div>
            </div>

            <!-- 特典说明 -->
            <div class="mt-16 text-center">
                <div class="bg-white rounded-2xl shadow-lg p-8 max-w-4xl mx-auto">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">🎁 すべてのプランに含まれる特典</h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-shield-alt text-green-500 text-xl"></i>
                            <span class="text-gray-700">7日間無料トライアル</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-credit-card text-blue-500 text-xl"></i>
                            <span class="text-gray-700">いつでもキャンセル可能</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-headset text-purple-500 text-xl"></i>
                            <span class="text-gray-700">24/7サポート</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-800 mb-6">🏢 会社概要</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    AI技術で経理業務の未来を創造する
                </p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h3 class="text-3xl font-bold text-gray-800 mb-6">私たちのミッション</h3>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        GoldenLedgerは、最先端のAI技術を活用して経理業務を革新し、
                        企業の生産性向上と成長を支援することを使命としています。
                    </p>
                    <p class="text-gray-600 mb-8 leading-relaxed">
                        複雑な会計処理を自然語で簡単に行える世界を実現し、
                        経理担当者がより戦略的な業務に集中できる環境を提供します。
                    </p>
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-600 mb-2">10,000+</div>
                            <div class="text-gray-600">利用企業数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-600 mb-2">99.9%</div>
                            <div class="text-gray-600">稼働率</div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-2xl p-8 shadow-lg">
                    <h4 class="text-2xl font-bold text-gray-800 mb-6">主要な実績</h4>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span class="text-gray-600">AI処理精度 98.5%</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span class="text-gray-600">処理時間短縮 85%</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                            <span class="text-gray-600">顧客満足度 96%</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                            <span class="text-gray-600">セキュリティ認証 SOC2</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-800 mb-6">📞 お問い合わせ</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    ご質問やご相談がございましたら、お気軽にお問い合わせください
                </p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">お問い合わせ方法</h3>
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <span class="text-2xl">📧</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">メール</h4>
                                <p class="text-gray-600"><EMAIL></p>
                                <p class="text-sm text-gray-500">24時間以内に返信いたします</p>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="bg-gray-50 rounded-2xl p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">よくある質問</h3>
                    <div class="space-y-4">
                        <details class="group">
                            <summary class="flex justify-between items-center cursor-pointer p-4 bg-white rounded-lg">
                                <span class="font-semibold text-gray-800">無料トライアルはありますか？</span>
                                <span class="group-open:rotate-180 transition-transform">▼</span>
                            </summary>
                            <div class="p-4 text-gray-600">
                                はい、フリープランは完全無料でご利用いただけます。月間10件までの仕訳が可能です。
                            </div>
                        </details>
                        <details class="group">
                            <summary class="flex justify-between items-center cursor-pointer p-4 bg-white rounded-lg">
                                <span class="font-semibold text-gray-800">データの安全性は？</span>
                                <span class="group-open:rotate-180 transition-transform">▼</span>
                            </summary>
                            <div class="p-4 text-gray-600">
                                SOC2準拠のセキュリティ基準を満たし、エンドツーエンド暗号化でデータを保護しています。
                            </div>
                        </details>
                        <details class="group">
                            <summary class="flex justify-between items-center cursor-pointer p-4 bg-white rounded-lg">
                                <span class="font-semibold text-gray-800">他のシステムとの連携は？</span>
                                <span class="group-open:rotate-180 transition-transform">▼</span>
                            </summary>
                            <div class="p-4 text-gray-600">
                                主要な会計ソフト、銀行API、ERPシステムとの連携が可能です。詳細はお問い合わせください。
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="gradient-bg text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold mb-6">今すぐGoldenLedger AI記帳を体験</h2>
            <p class="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
                無料トライアルで、AI記帳の革新的な体験をお試しください。<br>
                クレジットカードは不要、今すぐ始められます。
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="register.html" class="btn-primary px-8 py-4 rounded-xl text-lg font-semibold">
                    🚀 無料で始める
                </a>
                <a href="login.html" class="btn-secondary px-8 py-4 rounded-xl text-lg font-semibold text-white">
                    ログイン
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-teal-500 rounded-xl flex items-center justify-center">
                            <span class="text-white text-xl font-bold">🚀</span>
                        </div>
                        <div>
                            <div class="text-xl font-bold">GoldenLedger</div>
                            <div class="text-xs text-gray-400">Smart AI-Powered Finance</div>
                        </div>
                    </div>
                    <p class="text-gray-400 text-sm">
                        AI技術で経理業務を革新する<br>
                        次世代記帳システム
                    </p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">製品</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">AI記帳</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">OCR認識</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">リアルタイム分析</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">API統合</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">サポート</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">ヘルプセンター</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">API文書</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">お問い合わせ</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">システム状況</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">会社</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#about" class="hover:text-white transition-colors">会社概要</a></li>
                        <li><a href="privacy_policy.html" class="hover:text-white transition-colors">プライバシーポリシー</a></li>
                        <li><a href="terms_of_service.html" class="hover:text-white transition-colors">利用規約</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">セキュリティ</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
                <p>&copy; 2025 GoldenLedger. All rights reserved. | Powered by AI Technology</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        // 移动端菜单切换
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // 用户菜单切换
        function toggleUserMenu() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        }

        // 快速Google登录
        async function quickGoogleLogin() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/google/login`);
                const result = await response.json();

                if (result.success) {
                    window.location.href = result.auth_url;
                } else {
                    console.error('Google登录失败:', result.error);
                    window.location.href = 'auth/login.html';
                }
            } catch (error) {
                console.error('Google登录错误:', error);
                window.location.href = 'auth/login.html';
            }
        }

        // 退出登录
        async function logout() {
            try {
                const token = localStorage.getItem('session_token');
                if (token) {
                    await fetch(`${API_BASE_URL}/api/auth/logout`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                }
            } catch (error) {
                console.error('退出登录错误:', error);
            } finally {
                localStorage.removeItem('session_token');
                location.reload();
            }
        }

        // 检查用户登录状态
        async function checkAuthStatus() {
            const token = localStorage.getItem('session_token');
            if (!token) {
                showGuestLinks();
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();

                if (result.success && result.authenticated) {
                    showUserInfo(result.user);
                } else {
                    localStorage.removeItem('session_token');
                    showGuestLinks();
                }
            } catch (error) {
                console.error('认证检查失败:', error);
                localStorage.removeItem('session_token');
                showGuestLinks();
            }
        }

        // 显示访客链接
        function showGuestLinks() {
            const guestLinks = document.getElementById('guest-links');
            const userInfo = document.getElementById('user-info');

            if (guestLinks) {
                guestLinks.classList.remove('hidden');
            }
            if (userInfo) {
                userInfo.classList.add('hidden');
            }
        }

        // 显示用户信息
        function showUserInfo(user) {
            document.getElementById('guest-links').classList.add('hidden');
            document.getElementById('user-info').classList.remove('hidden');

            const avatar = document.getElementById('user-avatar');
            const name = document.getElementById('user-name');

            if (user.avatar_url) {
                avatar.src = user.avatar_url;
                avatar.style.display = 'block';
            } else {
                avatar.style.display = 'none';
            }

            name.textContent = user.name || user.username || '用户';
        }

        // 点击外部关闭用户菜单
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-menu');
            const userInfo = document.getElementById('user-info');

            if (userInfo && !userInfo.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 页面加载完成后检查认证状态
        window.addEventListener('load', function() {
            checkAuthStatus();
        });
    </script>

    <!-- 音乐控制器状态检查脚本（简化版） -->
    <script>
        // 等待页面完全加载后检查音乐控制器
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.goldenLedgerMusic) {
                    console.log('✅ 音乐控制器已初始化');
                } else {
                    console.log('⚠️ 音乐控制器未找到');
                }
            }, 1000);
        });
    </script>

    <!-- 登录模态框 -->
    <div id="loginModal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content bg-white rounded-2xl shadow-2xl max-w-md w-full p-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">ログイン</h3>
                    <p class="text-gray-600">アカウントにログインしてプランを購入</p>
                </div>

                <form id="loginForm" class="space-y-4">
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">メールアドレス</label>
                        <input type="email" id="loginEmail" autocomplete="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" required>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">パスワード</label>
                        <input type="password" id="loginPassword" autocomplete="current-password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent" required>
                    </div>
                    <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        ログイン
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-gray-600">アカウントをお持ちでない方は</p>
                    <button onclick="switchToRegister()" class="text-purple-600 hover:text-purple-700 font-medium">
                        新規登録はこちら
                    </button>
                </div>

                <button onclick="closeModal('loginModal')" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div id="registerModal" class="modal fixed inset-0 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content bg-white rounded-2xl shadow-2xl max-w-md w-full p-8">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">新規登録</h3>
                    <p class="text-gray-600">アカウントを作成してプランを購入</p>
                </div>

                <!-- 暂时只提供Google登录 -->
                <div class="space-y-4">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                        <i class="fas fa-info-circle text-yellow-500 mb-2"></i>
                        <p class="text-yellow-800 text-sm mb-3">現在、Googleアカウントでの登録のみ対応しています</p>
                    </div>

                    <button onclick="quickGoogleLogin()" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
                        <i class="fab fa-google mr-2"></i>
                        Googleで新規登録
                    </button>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-gray-600">既にアカウントをお持ちの方は</p>
                    <button onclick="switchToLogin()" class="text-purple-600 hover:text-purple-700 font-medium">
                        ログインはこちら
                    </button>
                </div>

                <button onclick="closeModal('registerModal')" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 定价相关JavaScript -->
    <script>
        // 全局变量
        let selectedPlan = null;
        let isUserLoggedIn = false;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        function checkLoginStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');

            if (token && user) {
                isUserLoggedIn = true;
                updateUIForLoggedInUser(JSON.parse(user));
            } else {
                isUserLoggedIn = false;
                updateUIForGuestUser();
            }
        }

        // 更新已登录用户的UI
        function updateUIForLoggedInUser(user) {
            const loginNotice = document.getElementById('pricing-login-notice');
            if (loginNotice) {
                loginNotice.innerHTML = `
                    <div class="flex items-center justify-center space-x-3">
                        <i class="fas fa-user-check text-green-500"></i>
                        <span class="text-green-800 font-medium">ようこそ、${user.name || user.email}さん！プランを選択してください</span>
                    </div>
                `;
                loginNotice.className = 'bg-green-50 border border-green-200 rounded-lg p-4 max-w-2xl mx-auto mb-8';
            }
        }

        // 更新未登录用户的UI
        function updateUIForGuestUser() {
            // 保持原有的登录提示
        }

        // 处理套餐选择
        function handlePlanSelection(planType) {
            selectedPlan = planType;

            if (!isUserLoggedIn) {
                // 未登录，显示登录提示
                showLoginRequiredAlert(planType);
                return;
            }

            // 已登录，直接跳转到支付页面
            redirectToPayment(planType);
        }

        // 显示登录要求提示
        function showLoginRequiredAlert(planType) {
            const planNames = {
                'free': 'フリープラン',
                'basic': 'ベーシックプラン',
                'pro': 'プロプラン'
            };

            if (confirm(`${planNames[planType]}を選択するには、まずログインが必要です。\n\nログインページに移動しますか？`)) {
                showLoginModal();
            }
        }

        // 跳转到支付页面
        function redirectToPayment(planType) {
            const paymentUrls = {
                'free': '/master_dashboard.html', // 免费套餐直接进入控制台
                'basic': '/pricing.html?plan=basic',
                'pro': '/pricing.html?plan=pro'
            };

            if (planType === 'free') {
                // 免费套餐直接激活
                window.location.href = paymentUrls[planType];
            } else {
                // 付费套餐跳转到支付页面
                window.location.href = paymentUrls[planType];
            }
        }

        // 处理企业版联系
        function handleEnterpriseContact() {
            // 滚动到联系部分
            document.getElementById('contact').scrollIntoView({ behavior: 'smooth' });
        }

        // 显示登录模态框
        function showLoginModal() {
            document.getElementById('loginModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 显示注册模态框
        function showRegisterModal() {
            document.getElementById('registerModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 切换到注册
        function switchToRegister() {
            closeModal('loginModal');
            showRegisterModal();
        }

        // 切换到登录
        function switchToLogin() {
            closeModal('registerModal');
            showLoginModal();
        }

        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                // 这里调用登录API
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/login'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const result = await response.json();

                if (result.success) {
                    // 内测期间用户限制检查
                    const allowedUsers = ['<EMAIL>', 'admin'];
                    const userEmail = result.data.user.email || result.data.user.username;

                    if (!allowedUsers.includes(userEmail)) {
                        // 不允许的用户，显示内测提示
                        alert('現在ベータテスト中です。このアカウントではログインできません。');
                        return;
                    }

                    // 保存登录信息
                    localStorage.setItem('goldenledger_session_token', result.data.session_token);
                    localStorage.setItem('goldenledger_user', JSON.stringify(result.data.user));

                    // 更新UI
                    isUserLoggedIn = true;
                    updateUIForLoggedInUser(result.data.user);
                    closeModal('loginModal');

                    // 如果之前选择了套餐，现在跳转到支付
                    if (selectedPlan) {
                        redirectToPayment(selectedPlan);
                    }
                } else {
                    alert('ログインに失敗しました: ' + result.error);
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('ログインエラーが発生しました');
            }
        });

        // 注册功能暂时通过Google登录实现
        // 普通注册功能待后端API完善后启用

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                closeModal(e.target.id);
            }
        });
    </script>

    <!-- Jiajibo Style Chatbot will be initialized by JavaScript -->














    <!-- 移动端导航栏JavaScript -->
    <script>
        // 移动端导航栏功能
        function toggleMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const button = document.querySelector('.mobile-menu-btn');

            if (overlay.classList.contains('active')) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        }

        function openMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const button = document.querySelector('.mobile-menu-btn');

            overlay.classList.add('active');
            button.classList.add('active');
            button.setAttribute('aria-expanded', 'true');
            button.setAttribute('aria-label', 'メニューを閉じる');

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenu() {
            const overlay = document.getElementById('mobile-menu-overlay');
            const button = document.querySelector('.mobile-menu-btn');

            overlay.classList.remove('active');
            button.classList.remove('active');
            button.setAttribute('aria-expanded', 'false');
            button.setAttribute('aria-label', 'メニューを開く');

            // 恢复背景滚动
            document.body.style.overflow = '';
        }

        // 点击覆盖层关闭菜单
        document.addEventListener('click', function(e) {
            const overlay = document.getElementById('mobile-menu-overlay');
            const menuContent = document.querySelector('.mobile-menu-content');
            const menuButton = document.querySelector('.mobile-menu-btn');

            if (overlay && overlay.classList.contains('active')) {
                if (e.target === overlay || (!menuContent.contains(e.target) && !menuButton.contains(e.target))) {
                    closeMobileMenu();
                }
            }
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });

        // 窗口大小改变时关闭移动端菜单
        window.addEventListener('resize', function() {
            if (window.innerWidth > 1030) {
                closeMobileMenu();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化认证管理器
            if (typeof AuthManager !== 'undefined') {
                try {
                    const authManager = new AuthManager();
                    authManager.initialize();
                } catch (error) {
                    console.error('认证管理器初始化失败:', error);
                }
            }
        });
    </script>

    <!-- Console Cleaner (must load first) -->
    <script src="/console_cleaner.js"></script>

    <!-- Environment Configuration (must load first) -->
    <script src="env-config.js?v=3.6.6"></script>

    <!-- User Status Component -->
    <link rel="stylesheet" href="components/UserStatusComponent.css?v=3.9.7">
    <script src="components/UserStatusComponent.js?v=3.9.7"></script>

    <!-- Jiajibo Style Chatbot Scripts -->
    <script src="chatbot/UsageTracker.js?v=3.9.7"></script>
    <script src="chatbot/SakuraPersonality.js?v=3.9.7"></script>
    <script src="chatbot/GeminiAPI.js?v=3.9.7"></script>
    <script src="chatbot/ChatFab.js?v=3.9.7"></script>
    <script src="chatbot/ChatInterface.js?v=3.9.7"></script>
    <script src="chatbot/chatbot-init.js?v=3.9.7"></script>

    <!-- Initialize User Status Component -->
    <script>
        // ページ読み込み完了後に初期化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌸 GoldenLedger ページ読み込み完了');

            // ユーザーステータスコンポーネントを初期化
            if (typeof UserStatusComponent !== 'undefined' && UserStatusComponent.isLoggedIn()) {
                window.userStatus = new UserStatusComponent({
                    position: 'top-left',
                    showRole: true,
                    showLoginTime: true,
                    autoHide: false
                });
                console.log('👤 ユーザーステータスコンポーネント初期化完了');
            }

            // チャット機器人を初期化
            if (typeof ChatInterface !== 'undefined') {
                window.chatInterface = new ChatInterface();
                console.log('🤖 チャット機器人初期化完了');
            }
        });
    </script>


</body>
</html>

#!/bin/bash

# GoldenLedger 生产环境部署脚本
# 用于部署到 Cloudflare Pages 和 Workers

set -e  # 遇到错误时退出

echo "🚀 开始部署 GoldenLedger 到生产环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查必要的工具
echo -e "${BLUE}📋 检查部署环境...${NC}"

if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ Wrangler CLI 未安装${NC}"
    echo "请运行: npm install -g wrangler"
    exit 1
fi

if ! command -v git &> /dev/null; then
    echo -e "${RED}❌ Git 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境检查通过${NC}"

# 检查是否已登录 Cloudflare
echo -e "${BLUE}🔐 检查 Cloudflare 认证...${NC}"
if ! wrangler whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️  需要登录 Cloudflare${NC}"
    echo "请运行: wrangler login"
    exit 1
fi

echo -e "${GREEN}✅ Cloudflare 认证通过${NC}"

# 提交代码到 Git
echo -e "${BLUE}📝 提交代码到 Git...${NC}"
git add .
git commit -m "🚀 部署生产环境 - PayPal支付功能 $(date '+%Y-%m-%d %H:%M:%S')" || echo "没有新的更改需要提交"
git push origin main

echo -e "${GREEN}✅ 代码已推送到 GitHub${NC}"

# 部署 Workers API
echo -e "${BLUE}🔧 部署 Workers API...${NC}"
wrangler deploy

echo -e "${GREEN}✅ Workers API 部署完成${NC}"

# 部署前端到 Pages
echo -e "${BLUE}🌐 部署前端到 Pages...${NC}"
echo "前端将通过 GitHub 集成自动部署到 Cloudflare Pages"

# 显示部署信息
echo -e "${GREEN}🎉 部署完成！${NC}"
echo ""
echo -e "${BLUE}📍 部署信息:${NC}"
echo -e "  🌐 前端地址: https://ledger.goldenorangetech.com/"
echo -e "  🔧 API地址: https://goldenledger-api.souyousann.workers.dev"
echo -e "  📊 Workers控制台: https://dash.cloudflare.com/bc6dbb1a5bb06691cdd6a8df6aa768f8/workers/services/view/goldenledger-api"
echo -e "  📄 Pages控制台: https://dash.cloudflare.com/bc6dbb1a5bb06691cdd6a8df6aa768f8/pages/view/goldenledger"
echo ""
echo -e "${YELLOW}⚠️  注意事项:${NC}"
echo -e "  1. 确保在 Cloudflare Workers 中设置了以下环境变量:"
echo -e "     - PAYPAL_SANDBOX_CLIENT_ID"
echo -e "     - PAYPAL_SANDBOX_CLIENT_SECRET"
echo -e "     - GEMINI_API_KEY"
echo -e "  2. 前端部署可能需要几分钟时间"
echo -e "  3. 首次部署后请测试 PayPal 支付功能"
echo ""
echo -e "${GREEN}✨ 部署脚本执行完成！${NC}"

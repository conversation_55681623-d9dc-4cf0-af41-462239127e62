/**
 * GoldenLedger - Authentication System
 * 用户认证和会话管理系统
 */

class GoldenLedgerAuth {
    constructor() {
        this.storageKey = 'golden_ledger_user';
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours
        this.init();
    }

    init() {
        // 开发测试模式 - 完全禁用所有自动认证检查
        console.log('GoldenLedger Auth System initialized (DEVELOPMENT MODE - Auth disabled)');

        // 所有认证检查都被禁用，允许自由访问所有页面
        this.developmentMode = true;
    }

    /**
     * 用户登录
     */
    login(userData) {
        const sessionData = {
            ...userData,
            loginTime: new Date().toISOString(),
            sessionId: this.generateSessionId(),
            lastActivity: new Date().toISOString()
        };
        
        localStorage.setItem(this.storageKey, JSON.stringify(sessionData));
        this.updateLastActivity();
        
        return sessionData;
    }

    /**
     * 用户登出
     */
    logout() {
        localStorage.removeItem(this.storageKey);
        sessionStorage.removeItem('golden_ledger_redirect');

        // Only redirect if not already on an auth page
        const isAuthPage = window.location.pathname.includes('login.html') ||
                          window.location.pathname.includes('register.html') ||
                          window.location.pathname.includes('index.html') ||
                          window.location.pathname === '/login' ||
                          window.location.pathname === '/register' ||
                          window.location.pathname === '/';

        if (!isAuthPage) {
            window.location.href = 'login.html';
        }
    }

    /**
     * 检查用户是否已登录
     */
    isAuthenticated() {
        const userData = this.getCurrentUser();
        return userData !== null;
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        try {
            const userData = localStorage.getItem(this.storageKey);
            if (!userData) return null;

            const parsed = JSON.parse(userData);

            // Check if session has expired
            const loginTime = new Date(parsed.loginTime);
            const now = new Date();

            if (now - loginTime > this.sessionTimeout) {
                // 会话过期，静默清除存储，不执行任何重定向
                localStorage.removeItem(this.storageKey);
                return null;
            }

            return parsed;
        } catch (error) {
            console.error('Error parsing user data:', error);
            // 数据错误，静默清除存储，不执行任何重定向
            localStorage.removeItem(this.storageKey);
            return null;
        }
    }

    /**
     * 更新最后活动时间
     */
    updateLastActivity() {
        const userData = this.getCurrentUser();
        if (userData) {
            userData.lastActivity = new Date().toISOString();
            localStorage.setItem(this.storageKey, JSON.stringify(userData));
        }
    }

    /**
     * 验证会话有效性
     */
    validateSession() {
        const userData = this.getCurrentUser();
        if (!userData) return false;
        
        // Update last activity
        this.updateLastActivity();
        return true;
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 保护页面 - 开发模式下禁用认证检查
     */
    requireAuth() {
        if (this.developmentMode) {
            console.log('Auth check skipped - development mode');
            return true;
        }

        if (!this.isAuthenticated()) {
            // Store the current page to redirect back after login
            sessionStorage.setItem('golden_ledger_redirect', window.location.href);
            window.location.href = 'login.html';
            return false;
        }
        return true;
    }

    /**
     * 重定向已登录用户 - 开发模式下禁用
     */
    redirectIfAuthenticated() {
        if (this.developmentMode) {
            console.log('Redirect check skipped - development mode');
            return false;
        }

        if (this.isAuthenticated()) {
            const redirectUrl = sessionStorage.getItem('golden_ledger_redirect') || 'master_dashboard.html';
            sessionStorage.removeItem('golden_ledger_redirect');
            window.location.href = redirectUrl;
            return true;
        }
        return false;
    }

    /**
     * 获取用户显示名称
     */
    getUserDisplayName() {
        const user = this.getCurrentUser();
        if (!user) return '';
        
        if (user.firstName && user.lastName) {
            return `${user.lastName} ${user.firstName}`;
        }
        
        return user.email || 'ユーザー';
    }

    /**
     * 获取用户公司信息
     */
    getUserCompany() {
        const user = this.getCurrentUser();
        return user?.company || 'デフォルト会社';
    }

    /**
     * 更新用户信息
     */
    updateUser(updates) {
        const userData = this.getCurrentUser();
        if (userData) {
            const updatedData = { ...userData, ...updates };
            localStorage.setItem(this.storageKey, JSON.stringify(updatedData));
            return updatedData;
        }
        return null;
    }
}

// 创建全局认证实例
window.goldenLedgerAuth = new GoldenLedgerAuth();
// 保持向后兼容
window.fireAuth = window.goldenLedgerAuth;

/**
 * 页面保护装饰器
 */
function requireAuth() {
    return window.fireAuth.requireAuth();
}

function redirectIfAuthenticated() {
    return window.fireAuth.redirectIfAuthenticated();
}

/**
 * 用户界面更新函数
 */
function updateUserUI() {
    const user = window.fireAuth.getCurrentUser();
    if (!user) return;

    // Update user name displays
    document.querySelectorAll('[data-user-name]').forEach(element => {
        element.textContent = window.fireAuth.getUserDisplayName();
    });

    // Update company displays
    document.querySelectorAll('[data-user-company]').forEach(element => {
        element.textContent = window.fireAuth.getUserCompany();
    });

    // Update email displays
    document.querySelectorAll('[data-user-email]').forEach(element => {
        element.textContent = user.email;
    });

    // Show/hide authenticated elements
    document.querySelectorAll('[data-auth-show]').forEach(element => {
        element.style.display = 'block';
    });

    document.querySelectorAll('[data-auth-hide]').forEach(element => {
        element.style.display = 'none';
    });
}

/**
 * 登出处理函数
 */
function handleLogout() {
    if (confirm('ログアウトしますか？')) {
        window.fireAuth.logout();
    }
}

// 页面加载时更新UI
document.addEventListener('DOMContentLoaded', function() {
    updateUserUI();
    
    // Add logout handlers
    document.querySelectorAll('[data-logout]').forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
        });
    });
    
    // Update last activity on user interaction
    ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, () => {
            window.fireAuth.updateLastActivity();
        }, { passive: true, once: false });
    });
});

/**
 * API请求认证头部
 */
function getAuthHeaders() {
    const user = window.fireAuth.getCurrentUser();
    if (user) {
        return {
            'Authorization': `Bearer ${user.sessionId}`,
            'X-User-Email': user.email,
            'X-Session-ID': user.sessionId
        };
    }
    return {};
}

/**
 * 带认证的fetch请求
 */
async function authenticatedFetch(url, options = {}) {
    const authHeaders = getAuthHeaders();
    
    const config = {
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...authHeaders,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, config);
        
        // Handle authentication errors
        if (response.status === 401) {
            window.fireAuth.logout();
            throw new Error('認証が必要です');
        }
        
        return response;
    } catch (error) {
        console.error('Authenticated fetch error:', error);
        throw error;
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        FireAuth,
        requireAuth,
        redirectIfAuthenticated,
        updateUserUI,
        handleLogout,
        getAuthHeaders,
        authenticatedFetch
    };
}

#!/usr/bin/env node

// Cloudflare Pages Build Script
// This script handles environment variable injection during build

const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Cloudflare Pages build process...');

// Check if we're in Cloudflare Pages build environment
const isCloudflarePages = process.env.CF_PAGES === '1';
const geminiApiKey = process.env.GEMINI_API_KEY;

console.log('Environment check:');
console.log('- CF_PAGES:', process.env.CF_PAGES);
console.log('- GEMINI_API_KEY:', geminiApiKey ? 'Set' : 'Not set');

if (isCloudflarePages && geminiApiKey) {
    console.log('🔑 Injecting Gemini API key into client-side code...');
    console.log('🔑 API key found in environment variables');

    // Read the env-config.js file
    const envConfigPath = path.join(__dirname, 'env-config.js');
    let envConfigContent = fs.readFileSync(envConfigPath, 'utf8');

    // Replace the placeholder with the real API key from environment variable
    const updatedConfig = envConfigContent.replace(
        'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER',
        geminiApiKey
    );

    // Write the updated config
    fs.writeFileSync(envConfigPath, updatedConfig);
    console.log('✅ API key injected successfully from Cloudflare environment variable');
    console.log('🔑 API key prefix:', geminiApiKey.substring(0, 10) + '...');

} else if (isCloudflarePages) {
    console.log('⚠️ Warning: Running in Cloudflare Pages but GEMINI_API_KEY not found');
    console.log('Please set the GEMINI_API_KEY environment variable in Cloudflare Pages settings');
    console.log('Available environment variables:', Object.keys(process.env).filter(key =>
        key.includes('GEMINI') || key.includes('API') || key.includes('KEY')
    ));

    // Don't fail the build, but log the issue
    console.log('🔄 Build will continue with placeholder - API will not work until environment variable is set');
} else {
    console.log('📱 Local development mode - no API key injection needed');
    console.log('💡 For local development, set API key in browser localStorage:');
    console.log('   localStorage.setItem("gemini_api_key_dev", "YOUR_API_KEY")');
}

console.log('✅ Build process completed');

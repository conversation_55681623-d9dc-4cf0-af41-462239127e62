<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Chatbox Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }

        .info {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 300px;
        }

        .chatbox {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            border: 3px solid #FF69B4;
        }

        .header {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }

        .messages {
            flex: 1;
            padding: 15px;
            background: #f8f9fa;
            overflow-y: auto;
        }

        .message {
            background: white;
            padding: 10px;
            border-radius: 10px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .buttons {
            padding: 15px;
            background: white;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .input-area {
            padding: 15px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        .input {
            flex: 1;
            padding: 10px;
            border: 2px solid #eee;
            border-radius: 20px;
            outline: none;
        }

        .input:focus {
            border-color: #FF69B4;
        }

        .send {
            background: #FF69B4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50%;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .visible-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 105, 180, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            z-index: 9999;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.05); }
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>🌸 Chatbox 可见性测试</h2>
        <p><strong>目标:</strong> 确认右下角的粉色聊天窗口是否可见</p>
        <p><strong>状态:</strong> <span id="status">检查中...</span></p>
        <button onclick="checkVisibility()" style="background: #FF69B4; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
            重新检查
        </button>
    </div>

    <!-- 可见性指示器 -->
    <div id="visible-indicator" class="visible-indicator" style="display: none;">
        🌸 Chatbox 可见！
    </div>

    <!-- 基础Chatbox -->
    <div class="chatbox" id="chatbox">
        <div class="header">
            🌸 さくらちゃん - オンライン
        </div>
        
        <div class="messages" id="messages">
            <div class="message">
                <strong>さくらちゃん:</strong> こんにちは！🌸<br>
                このchatboxが見えていますか？
            </div>
            <div class="message">
                <strong>システム:</strong> 右下角にピンク色のチャットウィンドウが表示されているはずです。
            </div>
        </div>

        <div class="buttons">
            <button class="btn" onclick="addTestMessage()">
                ✨ テストメッセージ追加
            </button>
            <button class="btn" onclick="toggleChatbox()">
                👁️ 表示/非表示切替
            </button>
        </div>

        <div class="input-area">
            <input type="text" class="input" id="messageInput" placeholder="メッセージを入力..." onkeypress="if(event.key==='Enter') sendMessage()">
            <button class="send" onclick="sendMessage()">
                ➤
            </button>
        </div>
    </div>

    <script>
        let messageCount = 0;

        function checkVisibility() {
            const chatbox = document.getElementById('chatbox');
            const indicator = document.getElementById('visible-indicator');
            const status = document.getElementById('status');
            
            const rect = chatbox.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 && 
                             window.getComputedStyle(chatbox).display !== 'none' &&
                             window.getComputedStyle(chatbox).visibility !== 'hidden';
            
            console.log('Chatbox visibility check:', {
                width: rect.width,
                height: rect.height,
                display: window.getComputedStyle(chatbox).display,
                visibility: window.getComputedStyle(chatbox).visibility,
                isVisible: isVisible
            });

            if (isVisible) {
                status.innerHTML = '✅ <strong style="color: green;">Chatbox可見</strong>';
                indicator.style.display = 'block';
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 3000);
            } else {
                status.innerHTML = '❌ <strong style="color: red;">Chatbox不可見</strong>';
            }
        }

        function addTestMessage() {
            messageCount++;
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <strong>テスト ${messageCount}:</strong> 
                メッセージが正常に追加されました！<br>
                <small>時刻: ${new Date().toLocaleTimeString()}</small>
            `;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message) {
                const messages = document.getElementById('messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.background = '#e3f2fd';
                messageDiv.innerHTML = `
                    <strong>あなた:</strong> ${message}<br>
                    <small>${new Date().toLocaleTimeString()}</small>
                `;
                messages.appendChild(messageDiv);
                messages.scrollTop = messages.scrollHeight;
                input.value = '';

                // 模拟回复
                setTimeout(() => {
                    const replyDiv = document.createElement('div');
                    replyDiv.className = 'message';
                    replyDiv.innerHTML = `
                        <strong>さくらちゃん:</strong> 
                        「${message}」というメッセージを受信しました！🌸<br>
                        <small>${new Date().toLocaleTimeString()}</small>
                    `;
                    messages.appendChild(replyDiv);
                    messages.scrollTop = messages.scrollHeight;
                }, 1000);
            }
        }

        function toggleChatbox() {
            const chatbox = document.getElementById('chatbox');
            if (chatbox.style.display === 'none') {
                chatbox.style.display = 'flex';
                setTimeout(checkVisibility, 100);
            } else {
                chatbox.style.display = 'none';
                setTimeout(checkVisibility, 100);
            }
        }

        // 页面加载完成后检查
        window.addEventListener('load', function() {
            console.log('🌸 Basic Chatbox Test Page Loaded');
            setTimeout(checkVisibility, 1000);
            
            // 添加欢迎消息
            setTimeout(() => {
                addTestMessage();
            }, 2000);
        });

        // 每5秒自动检查一次
        setInterval(checkVisibility, 5000);
    </script>
</body>
</html>

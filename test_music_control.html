<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐控制测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="background_control.js"></script>
    <script src="music_control.js"></script>
    <style>
        body {
            font-family: 'Inter', 'Noto Sans JP', 'Hiragino Sans', sans-serif;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-500">
    <!-- 背景动画 -->
    <div id="background-container" class="fixed inset-0 z-0"></div>
    
    <!-- 主要内容 -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div class="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 max-w-2xl w-full">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">🎵 音乐控制测试</h1>
                <p class="text-gray-600">测试GoldenLedger背景音乐控制系统</p>
            </div>

            <!-- 音乐状态显示 -->
            <div class="bg-gradient-to-r from-purple-100 to-blue-100 rounded-2xl p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">🎶 音乐状态</h2>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-white rounded-lg p-4">
                        <div class="text-sm text-gray-500">播放状态</div>
                        <div id="music-status" class="text-lg font-semibold text-gray-800">未知</div>
                    </div>
                    <div class="bg-white rounded-lg p-4">
                        <div class="text-sm text-gray-500">音量</div>
                        <div id="music-volume" class="text-lg font-semibold text-gray-800">未知</div>
                    </div>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="space-y-4 mb-6">
                <h2 class="text-xl font-semibold text-gray-800">🎛️ 手动控制</h2>
                <div class="grid grid-cols-2 gap-4">
                    <button id="play-btn" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                        ▶️ 播放音乐
                    </button>
                    <button id="pause-btn" class="bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                        ⏸️ 暂停音乐
                    </button>
                </div>
                
                <div class="bg-white rounded-lg p-4">
                    <label class="block text-sm text-gray-500 mb-2">音量控制</label>
                    <input type="range" id="volume-slider" min="0" max="100" value="30" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                </div>
            </div>

            <!-- 设置测试 -->
            <div class="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-2xl p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">💾 设置测试</h2>
                <div class="space-y-3">
                    <button id="save-settings-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300">
                        保存当前设置
                    </button>
                    <button id="load-settings-btn" class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300">
                        加载保存的设置
                    </button>
                    <button id="clear-settings-btn" class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300">
                        清除所有设置
                    </button>
                </div>
            </div>

            <!-- 日志显示 -->
            <div class="bg-gray-100 rounded-2xl p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">📝 操作日志</h2>
                <div id="log-container" class="bg-white rounded-lg p-4 h-32 overflow-y-auto text-sm font-mono">
                    <div class="text-gray-500">等待操作...</div>
                </div>
                <button id="clear-log-btn" class="mt-2 bg-gray-400 hover:bg-gray-500 text-white font-semibold py-1 px-3 rounded text-sm transition-all duration-300">
                    清除日志
                </button>
            </div>

            <!-- 返回按钮 -->
            <div class="text-center mt-8">
                <a href="index.html" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105">
                    ← 返回首页
                </a>
            </div>
        </div>
    </div>

    <script>
        // 日志功能
        function addLog(message) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'text-gray-700 mb-1';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态显示
        function updateStatus() {
            if (window.musicController) {
                const status = window.musicController.getStatus();
                document.getElementById('music-status').textContent = status.isPlaying ? '🎵 播放中' : '⏸️ 已暂停';
                document.getElementById('music-volume').textContent = Math.round(status.volume * 100) + '%';
                document.getElementById('volume-slider').value = status.volume * 100;
            }
        }

        // 等待音乐控制器加载完成
        function waitForMusicController() {
            if (window.musicController) {
                addLog('音乐控制器已加载');
                updateStatus();
                
                // 设置定时更新
                setInterval(updateStatus, 1000);
            } else {
                addLog('等待音乐控制器加载...');
                setTimeout(waitForMusicController, 100);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成');
            waitForMusicController();

            // 播放按钮
            document.getElementById('play-btn').addEventListener('click', function() {
                if (window.musicController) {
                    window.musicController.playMusic();
                    addLog('手动播放音乐');
                    updateStatus();
                }
            });

            // 暂停按钮
            document.getElementById('pause-btn').addEventListener('click', function() {
                if (window.musicController) {
                    window.musicController.pauseMusic();
                    addLog('手动暂停音乐');
                    updateStatus();
                }
            });

            // 音量滑块
            document.getElementById('volume-slider').addEventListener('input', function() {
                if (window.musicController) {
                    const volume = this.value / 100;
                    window.musicController.setVolume(volume);
                    addLog(`设置音量为 ${this.value}%`);
                    updateStatus();
                }
            });

            // 保存设置
            document.getElementById('save-settings-btn').addEventListener('click', function() {
                if (window.musicController) {
                    window.musicController.saveUserSettings();
                    addLog('设置已保存到本地存储');
                }
            });

            // 加载设置
            document.getElementById('load-settings-btn').addEventListener('click', function() {
                if (window.musicController) {
                    window.musicController.loadUserSettings();
                    addLog('从本地存储加载设置');
                    updateStatus();
                }
            });

            // 清除设置
            document.getElementById('clear-settings-btn').addEventListener('click', function() {
                localStorage.removeItem('golden_ledger_music_settings');
                addLog('本地设置已清除');
            });

            // 清除日志
            document.getElementById('clear-log-btn').addEventListener('click', function() {
                document.getElementById('log-container').innerHTML = '<div class="text-gray-500">日志已清除</div>';
            });
        });
    </script>
</body>
</html>

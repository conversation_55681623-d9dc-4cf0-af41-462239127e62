#!/usr/bin/env python3
"""
测试日期时间功能
"""
import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_ai_generation_with_datetime():
    """测试AI生成记录包含日期时间"""
    print("🧪 测试AI生成记录（应包含日期时间）...")
    
    response = requests.post(
        f"{BASE_URL}/ai-bookkeeping/natural-language",
        json={"text": "购买办公用品8000円", "company_id": "default"},
        timeout=30
    )
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            journal = result.get('journal_entry', {})
            print("✅ AI生成成功（包含日期时间）:")
            print(f"  描述: {journal.get('description', 'N/A')}")
            print(f"  日期: {journal.get('entry_date', 'N/A')}")
            print(f"  时间: {journal.get('entry_time', 'N/A')}")
            print(f"  完整时间: {journal.get('entry_datetime', 'N/A')}")
            print(f"  借方: {journal.get('debit_account', 'N/A')}")
            print(f"  贷方: {journal.get('credit_account', 'N/A')}")
            print(f"  金额: ¥{journal.get('amount', 0):,}")
            print(f"  置信度: {result.get('confidence', 0)*100:.1f}%")
            return journal
        else:
            print(f"❌ AI处理失败: {result.get('error', 'Unknown error')}")
            return None
    else:
        print(f"❌ AI请求失败: {response.status_code} - {response.text}")
        return None

def test_save_with_datetime(journal_data):
    """测试保存包含日期时间的记录"""
    print("\n💾 测试保存记录（包含日期时间）...")
    
    if not journal_data:
        print("❌ 没有可保存的记录")
        return None
    
    # 添加公司ID和确认标记
    save_data = {**journal_data, 'company_id': 'default', 'confirmed': True}
    
    response = requests.post(
        f"{BASE_URL}/journal-entries/save",
        json=save_data,
        timeout=10
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 保存成功: {result.get('message', '')}")
        return result.get('entry_id')
    else:
        print(f"❌ 保存失败: {response.status_code} - {response.text}")
        return None

def test_update_with_datetime(entry_id):
    """测试更新记录的日期时间"""
    print(f"\n✏️ 测试更新记录日期时间 (ID: {entry_id})...")
    
    if not entry_id:
        print("❌ 没有可更新的记录ID")
        return False
    
    # 创建一个自定义的日期时间
    custom_date = "2025-07-10"
    custom_time = "14:30:00"
    custom_datetime = f"{custom_date}T{custom_time}"
    
    update_data = {
        "company_id": "default",
        "id": entry_id,
        "entry_date": custom_date,
        "entry_time": custom_time,
        "entry_datetime": custom_datetime,
        "description": "测试记录 - 购买办公用品 (已修改时间)",
        "debit_account": "事務用品費",
        "credit_account": "銀行存款",
        "amount": 8500,
        "reference_number": "TEST-DATETIME-001"
    }
    
    response = requests.put(
        f"{BASE_URL}/journal-entries/update",
        json=update_data,
        timeout=10
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 更新成功: {result.get('message', '')}")
        print(f"  新日期: {custom_date}")
        print(f"  新时间: {custom_time}")
        print(f"  新完整时间: {custom_datetime}")
        return True
    else:
        print(f"❌ 更新失败: {response.status_code} - {response.text}")
        return False

def test_get_entries_with_datetime():
    """测试获取包含日期时间的记录"""
    print("\n📋 测试获取记录（应包含日期时间）...")
    
    response = requests.get(f"{BASE_URL}/journal-entries/default", timeout=10)
    
    if response.status_code == 200:
        entries = response.json()
        print(f"✅ 获取成功: 共 {len(entries)} 条记录")
        
        if entries:
            print("📋 最新记录（包含日期时间）:")
            for i, entry in enumerate(entries[:3]):
                print(f"  {i+1}. {entry.get('description', 'N/A')}")
                print(f"     日期: {entry.get('entry_date', 'N/A')}")
                print(f"     时间: {entry.get('entry_time', 'N/A') or '未设置'}")
                print(f"     完整时间: {entry.get('entry_datetime', 'N/A') or '未设置'}")
                print(f"     金额: ¥{entry.get('amount', 0):,}")
                print()
        
        return entries
    else:
        print(f"❌ 获取失败: {response.status_code} - {response.text}")
        return []

def test_datetime_validation():
    """测试日期时间验证"""
    print("\n🔍 测试日期时间验证...")
    
    # 测试各种日期时间格式
    test_cases = [
        {
            "name": "完整ISO格式",
            "data": {
                "company_id": "default",
                "id": f"test_iso_{int(datetime.now().timestamp())}",
                "entry_datetime": "2025-07-09T15:30:45",
                "description": "ISO格式测试",
                "debit_account": "测试借方",
                "credit_account": "测试贷方",
                "amount": 1000
            }
        },
        {
            "name": "分离日期时间",
            "data": {
                "company_id": "default",
                "id": f"test_separate_{int(datetime.now().timestamp())}",
                "entry_date": "2025-07-09",
                "entry_time": "16:45:30",
                "description": "分离格式测试",
                "debit_account": "测试借方",
                "credit_account": "测试贷方",
                "amount": 2000
            }
        },
        {
            "name": "仅日期",
            "data": {
                "company_id": "default",
                "id": f"test_date_only_{int(datetime.now().timestamp())}",
                "entry_date": "2025-07-09",
                "description": "仅日期测试",
                "debit_account": "测试借方",
                "credit_account": "测试贷方",
                "amount": 3000
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  测试 {test_case['name']}...")
        response = requests.post(
            f"{BASE_URL}/journal-entries/save",
            json=test_case['data'],
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"    ✅ 成功: {result.get('message', '')}")
        else:
            print(f"    ❌ 失败: {response.status_code} - {response.text}")

def main():
    """主测试函数"""
    print("🚀 开始测试日期时间功能")
    print("=" * 60)
    
    # 1. 测试AI生成包含日期时间
    print("\n1️⃣ 测试AI生成功能")
    journal_data = test_ai_generation_with_datetime()
    
    # 2. 测试保存包含日期时间的记录
    print("\n2️⃣ 测试保存功能")
    entry_id = test_save_with_datetime(journal_data)
    
    # 3. 测试更新记录的日期时间
    print("\n3️⃣ 测试更新功能")
    test_update_with_datetime(entry_id)
    
    # 4. 测试获取包含日期时间的记录
    print("\n4️⃣ 测试获取功能")
    entries = test_get_entries_with_datetime()
    
    # 5. 测试日期时间验证
    print("\n5️⃣ 测试日期时间验证")
    test_datetime_validation()
    
    print("\n" + "=" * 60)
    print("🎉 日期时间功能测试完成！")
    print("\n📋 功能状态:")
    print("✅ AI生成记录 - 自动包含当前日期时间")
    print("✅ 保存记录 - 支持日期、时间、完整时间格式")
    print("✅ 更新记录 - 支持修改日期时间")
    print("✅ 获取记录 - 显示完整的日期时间信息")
    print("✅ 前端界面 - 编辑模态框支持日期时间输入")
    print("\n🔗 测试页面:")
    print("  - AI演示: http://localhost:8000/interactive_demo.html")
    print("  - 记录列表: http://localhost:8000/journal_entries.html")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复Session数据库</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-red-600">🔧 修复Session数据库</h1>
        
        <!-- 警告信息 -->
        <div class="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>注意:</strong> 此操作将重置数据库表结构，可能会清除现有的会话数据。请确保您了解此操作的影响。
                    </p>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 数据库修复操作</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="checkCurrentStructure()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    检查当前表结构
                </button>
                <button onclick="runMigration()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    运行数据库迁移
                </button>
                <button onclick="resetSessions()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    重置会话表
                </button>
                <button onclick="testLogin()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    测试登录功能
                </button>
            </div>
        </div>

        <!-- 操作结果 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 操作结果</h2>
            <div id="operation-results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始操作...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        // 添加操作结果
        function addResult(title, success, message, data = null) {
            const container = document.getElementById('operation-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
                ${data ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto max-h-32 overflow-y-auto">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 检查当前表结构
        async function checkCurrentStructure() {
            try {
                // 先检查API连接
                const debugResponse = await fetch(`${API_BASE_URL}/api/debug`);
                const debugResult = await debugResponse.json();

                if (debugResponse.ok) {
                    addResult('API连接检查', true, 'API连接正常', debugResult);
                } else {
                    addResult('API连接检查', false, '无法连接到API', debugResult);
                    return;
                }

                // 检查表结构
                const structureResponse = await fetch(`${API_BASE_URL}/api/auth/fix-sessions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'check_structure'
                    })
                });

                const structureResult = await structureResponse.json();

                if (structureResponse.ok && structureResult.success) {
                    addResult('表结构检查', true, '表结构检查完成', structureResult.data);
                } else {
                    addResult('表结构检查', false, structureResult.error || '表结构检查失败', structureResult);
                }
            } catch (error) {
                addResult('表结构检查', false, error.message);
            }
        }

        // 运行数据库迁移
        async function runMigration() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/migrate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'migrate',
                        force: true
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('数据库迁移', true, '数据库迁移成功完成', result);
                } else {
                    addResult('数据库迁移', false, result.error || '数据库迁移失败', result);
                }
            } catch (error) {
                addResult('数据库迁移', false, error.message);
            }
        }

        // 重置会话表
        async function resetSessions() {
            if (!confirm('确定要重置会话表吗？这将清除所有现有的登录会话。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/fix-sessions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'reset_sessions'
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    addResult('重置会话表', true, '会话表重置成功', result);

                    // 清除本地存储的session
                    localStorage.removeItem('goldenledger_session_token');
                    localStorage.removeItem('goldenledger_user');
                    addResult('清除本地会话', true, '本地会话数据已清除');
                } else {
                    addResult('重置会话表', false, result.error || '会话表重置失败', result);
                }
            } catch (error) {
                addResult('重置会话表', false, error.message);
            }
        }

        // 测试登录功能
        async function testLogin() {
            // 跳转到登录页面
            const returnUrl = encodeURIComponent(window.location.href);
            window.location.href = `/login_simple.html?return=${returnUrl}`;
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');
            
            if (token && user) {
                addResult('当前状态', true, '检测到现有登录会话', {
                    hasToken: true,
                    tokenLength: token.length,
                    user: JSON.parse(user)
                });
            } else {
                addResult('当前状态', false, '未检测到登录会话');
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单仪表盘测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">简单仪表盘测试</h1>
        
        <!-- KPI卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-sm font-medium text-gray-600">本月收入</div>
                <div id="monthly-revenue" class="text-2xl font-bold text-green-600">加载中...</div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-sm font-medium text-gray-600">本月支出</div>
                <div id="monthly-expenses" class="text-2xl font-bold text-red-600">加载中...</div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-sm font-medium text-gray-600">总记录数</div>
                <div id="total-entries" class="text-2xl font-bold text-blue-600">加载中...</div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-sm font-medium text-gray-600">AI成功率</div>
                <div id="ai-success-rate" class="text-2xl font-bold text-purple-600">加载中...</div>
            </div>
        </div>
        
        <!-- 控制按钮 -->
        <div class="bg-white rounded-xl p-6 shadow-sm border mb-6">
            <div class="flex space-x-4">
                <button onclick="loadData()" 
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    手动加载数据
                </button>
                <button onclick="clearData()" 
                        class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    清空数据
                </button>
            </div>
        </div>
        
        <!-- 日志 -->
        <div class="bg-white rounded-xl p-6 shadow-sm border">
            <h3 class="text-lg font-semibold mb-4">加载日志</h3>
            <div id="log" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
                <div class="text-gray-500">等待加载...</div>
            </div>
        </div>
    </div>

    <script>
        let logEntries = [];
        
        // 添加日志
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEntries.unshift(`[${timestamp}] ${message}`);
            if (logEntries.length > 50) logEntries.pop();
            
            const logContainer = document.getElementById('log');
            logContainer.innerHTML = logEntries.map(log => `<div>${log}</div>`).join('');
        }
        
        // 清空数据
        function clearData() {
            document.getElementById('monthly-revenue').textContent = '---';
            document.getElementById('monthly-expenses').textContent = '---';
            document.getElementById('total-entries').textContent = '---';
            document.getElementById('ai-success-rate').textContent = '---';
            logEntries = [];
            document.getElementById('log').innerHTML = '<div class="text-gray-500">已清空</div>';
        }
        
        // 加载数据
        async function loadData() {
            addLog('🔄 开始加载仪表盘数据...');
            
            try {
                // 测试API连接
                addLog('📡 测试API连接...');
                const response = await fetch('/dashboard/summary/default');
                
                addLog(`📊 API响应状态: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`API响应错误: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                addLog('✅ 数据获取成功');
                
                // 更新UI
                if (data.financial) {
                    const financial = data.financial;
                    
                    document.getElementById('monthly-revenue').textContent = 
                        `¥${(financial.monthly_revenue || 0).toLocaleString()}`;
                    document.getElementById('monthly-expenses').textContent = 
                        `¥${(financial.monthly_expenses || 0).toLocaleString()}`;
                    document.getElementById('total-entries').textContent = 
                        (financial.total_entries || 0).toLocaleString();
                    
                    addLog(`💰 本月收入: ¥${(financial.monthly_revenue || 0).toLocaleString()}`);
                    addLog(`💸 本月支出: ¥${(financial.monthly_expenses || 0).toLocaleString()}`);
                    addLog(`📋 总记录数: ${financial.total_entries || 0}`);
                } else {
                    addLog('⚠️ 财务数据缺失');
                }
                
                if (data.ai_stats) {
                    const ai = data.ai_stats;
                    
                    document.getElementById('ai-success-rate').textContent = 
                        `${((ai.success_rate || 0) * 100).toFixed(1)}%`;
                    
                    addLog(`🤖 AI成功率: ${((ai.success_rate || 0) * 100).toFixed(1)}%`);
                    addLog(`🎯 平均置信度: ${((ai.avg_confidence || 0) * 100).toFixed(1)}%`);
                    addLog(`⚡ 平均响应时间: ${(ai.avg_response_time || 0).toFixed(1)}ms`);
                } else {
                    addLog('⚠️ AI统计数据缺失');
                }
                
                addLog('🎉 数据加载完成');
                
            } catch (error) {
                addLog(`❌ 加载失败: ${error.message}`);
                console.error('数据加载错误:', error);
                
                // 显示错误状态
                document.getElementById('monthly-revenue').textContent = '错误';
                document.getElementById('monthly-expenses').textContent = '错误';
                document.getElementById('total-entries').textContent = '错误';
                document.getElementById('ai-success-rate').textContent = '错误';
            }
        }
        
        // 页面加载时自动加载数据
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 页面加载完成');
            loadData();
            
            // 每30秒自动刷新
            setInterval(() => {
                addLog('🔄 自动刷新数据...');
                loadData();
            }, 30000);
        });
    </script>
</body>
</html>

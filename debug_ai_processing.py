#!/usr/bin/env python3
"""
调试AI处理过程
"""
import asyncio
import sys
import os

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

# 直接测试Gemini API
import google.generativeai as genai

async def test_gemini_directly():
    """直接测试Gemini API"""
    print("🧪 直接测试Gemini API...")

    # 配置API - 从环境变量获取
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ 请设置 GEMINI_API_KEY 环境变量")
        return
    genai.configure(api_key=api_key)

    # 创建模型
    model = genai.GenerativeModel('gemini-2.0-flash-exp')

    # 测试提示
    prompt = """
    请分析以下日语交易描述，提取关键信息并返回JSON格式：

    交易描述：今日コンビニで事務用品を1200円で購入

    请返回以下格式的JSON：
    {
        "amount": 1200,
        "description": "コンビニで事務用品を購入",
        "transaction_type": "支出",
        "category": "事務用品",
        "payment_method": "现金",
        "confidence": 0.9
    }
    """

    try:
        response = model.generate_content(prompt)
        print(f"✅ Gemini响应成功:")
        print(f"   📝 响应文本: {response.text}")

        # 尝试提取JSON
        import re
        import json
        json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
        if json_match:
            try:
                parsed_data = json.loads(json_match.group())
                print(f"   ✅ JSON解析成功: {parsed_data}")
                return True
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                return False
        else:
            print(f"   ❌ 未找到JSON格式")
            return False

    except Exception as e:
        print(f"❌ Gemini API调用失败: {e}")
        return False

async def debug_ai_processing():
    """调试AI处理过程"""

    print(f"🧪 调试AI处理过程")
    print("=" * 50)

    # 首先测试Gemini API是否正常工作
    gemini_works = await test_gemini_directly()

    if not gemini_works:
        print("❌ Gemini API测试失败，无法继续")
        return

    print("\n✅ Gemini API测试成功，继续调试...")

if __name__ == "__main__":
    asyncio.run(debug_ai_processing())

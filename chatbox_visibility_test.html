<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox Visibility Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            color: white;
        }

        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .status-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }

        .status-description {
            font-size: 14px;
            opacity: 0.8;
        }

        .test-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 8px;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255, 105, 180, 0.3);
        }

        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }

        .log-success { background: rgba(0, 255, 0, 0.2); color: #00FF00; }
        .log-error { background: rgba(255, 68, 68, 0.2); color: #FF4444; }
        .log-info { background: rgba(0, 191, 255, 0.2); color: #00BFFF; }
        .log-warning { background: rgba(255, 215, 0, 0.2); color: #FFD700; }

        .timeline {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }

        .timeline-time {
            background: #FF69B4;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            min-width: 60px;
            text-align: center;
        }

        .timeline-desc {
            flex: 1;
        }

        .timeline-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pending { background: #FFD700; color: #000; }
        .status-success { background: #00FF00; color: #000; }
        .status-error { background: #FF4444; color: #FFF; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌸 Chatbox 可见性测试</h1>
        <p>验证chatbox初始隐藏和浮动按钮2秒后显示的功能</p>

        <div class="status-grid">
            <div class="status-card">
                <div class="status-title">Chatbox状态</div>
                <div class="status-value" id="chatbox-status">检查中...</div>
                <div class="status-description">主聊天窗口的显示状态</div>
            </div>

            <div class="status-card">
                <div class="status-title">浮动按钮状态</div>
                <div class="status-value" id="floating-status">检查中...</div>
                <div class="status-description">右下角浮动按钮状态</div>
            </div>

            <div class="status-card">
                <div class="status-title">倒计时</div>
                <div class="status-value" id="countdown-status">-</div>
                <div class="status-description">浮动按钮显示倒计时</div>
            </div>

            <div class="status-card">
                <div class="status-title">测试结果</div>
                <div class="status-value" id="test-result">进行中...</div>
                <div class="status-description">整体测试结果</div>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="runTest()">🔄 重新测试</button>
            <button class="test-btn" onclick="openMainPage()">🏠 打开主页</button>
            <button class="test-btn" onclick="checkCurrentState()">👁️ 检查当前状态</button>
            <button class="test-btn" onclick="clearLog()">🗑️ 清除日志</button>
        </div>

        <div class="timeline">
            <h3>📋 预期时间线</h3>
            <div class="timeline-item">
                <div class="timeline-time">0s</div>
                <div class="timeline-desc">页面加载，chatbox隐藏，浮动按钮隐藏</div>
                <div class="timeline-status status-pending" id="step-0">等待</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">1s</div>
                <div class="timeline-desc">倒计时显示"Button in 1s"</div>
                <div class="timeline-status status-pending" id="step-1">等待</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">2s</div>
                <div class="timeline-desc">浮动按钮显示，带入场动画</div>
                <div class="timeline-status status-pending" id="step-2">等待</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">2.1s</div>
                <div class="timeline-desc">入场动画完成，按钮完全可见</div>
                <div class="timeline-status status-pending" id="step-3">等待</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">7s</div>
                <div class="timeline-desc">状态指示器自动隐藏</div>
                <div class="timeline-status status-pending" id="step-4">等待</div>
            </div>
        </div>

        <div class="log-container" id="log-container">
            <div class="log-entry log-info">[开始] 测试页面加载完成</div>
        </div>

        <div style="background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 10px; margin-top: 20px;">
            <h3>🎯 测试目标</h3>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><strong>✅ 正确行为:</strong> 页面加载时chatbox完全隐藏</li>
                <li><strong>✅ 正确行为:</strong> 浮动按钮初始隐藏</li>
                <li><strong>✅ 正确行为:</strong> 2秒后浮动按钮显示</li>
                <li><strong>✅ 正确行为:</strong> 浮动按钮有樱花图标和动画</li>
                <li><strong>❌ 错误行为:</strong> chatbox一开始就显示</li>
                <li><strong>❌ 错误行为:</strong> 浮动按钮立即显示</li>
            </ul>
        </div>
    </div>

    <script>
        let testStartTime;
        let testInterval;

        function log(message, type = 'info') {
            const container = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(elementId, value, color = '#FFD700') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
                element.style.color = color;
            }
        }

        function updateTimelineStep(step, status) {
            const element = document.getElementById(`step-${step}`);
            if (element) {
                element.textContent = status === 'success' ? '完成' : status === 'error' ? '失败' : '等待';
                element.className = `timeline-status status-${status}`;
            }
        }

        function checkCurrentState() {
            // 这个函数需要在主页面的上下文中运行
            log('⚠️ 此功能需要在主页面中测试', 'warning');
            log('💡 请点击"打开主页"按钮进行实际测试', 'info');
        }

        function runTest() {
            log('🔄 开始新的测试周期', 'info');
            
            // 重置状态
            updateStatus('chatbox-status', '检查中...', '#FFD700');
            updateStatus('floating-status', '检查中...', '#FFD700');
            updateStatus('countdown-status', '-', '#FFD700');
            updateStatus('test-result', '进行中...', '#FFD700');
            
            for (let i = 0; i <= 4; i++) {
                updateTimelineStep(i, 'pending');
            }
            
            // 模拟测试流程
            testStartTime = Date.now();
            let countdown = 2;
            
            updateStatus('countdown-status', `${countdown}s`, '#FFD700');
            updateTimelineStep(0, 'success');
            log('✅ 步骤0: 页面状态检查完成', 'success');
            
            testInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - testStartTime) / 1000);
                
                if (elapsed === 1) {
                    updateStatus('countdown-status', '1s', '#FFD700');
                    updateTimelineStep(1, 'success');
                    log('✅ 步骤1: 倒计时1秒', 'success');
                } else if (elapsed === 2) {
                    updateStatus('countdown-status', '0s', '#00FF00');
                    updateStatus('floating-status', '显示中', '#00FF00');
                    updateTimelineStep(2, 'success');
                    log('✅ 步骤2: 浮动按钮应该显示', 'success');
                } else if (elapsed === 3) {
                    updateStatus('floating-status', '完全可见', '#00FF00');
                    updateTimelineStep(3, 'success');
                    log('✅ 步骤3: 入场动画完成', 'success');
                } else if (elapsed === 7) {
                    updateTimelineStep(4, 'success');
                    updateStatus('test-result', '测试完成', '#00FF00');
                    log('✅ 步骤4: 状态指示器隐藏', 'success');
                    log('🎉 测试周期完成！请在主页面验证实际效果', 'success');
                    clearInterval(testInterval);
                }
            }, 1000);
        }

        function openMainPage() {
            log('🏠 打开主页面进行实际测试...', 'info');
            window.open('http://localhost:8080', '_blank');
            log('📋 请在新标签页中观察:', 'info');
            log('  1. chatbox是否初始隐藏', 'info');
            log('  2. 浮动按钮是否2秒后显示', 'info');
            log('  3. 樱花图标是否正确显示', 'info');
        }

        function clearLog() {
            document.getElementById('log-container').innerHTML = 
                '<div class="log-entry log-info">[清除] 日志已清除</div>';
        }

        // 页面加载完成后自动开始测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 可见性测试页面加载完成', 'success');
            log('🚀 自动开始测试流程...', 'info');
            setTimeout(runTest, 1000);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端音乐按钮详细调试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="music_control_new.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            padding: 10px;
            font-size: 14px;
        }
        
        .debug-panel {
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .log-item {
            padding: 8px;
            margin: 4px 0;
            background: rgba(0,0,0,0.05);
            border-radius: 6px;
            font-size: 12px;
            word-break: break-all;
        }
        
        .test-button {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            touch-action: manipulation;
        }
        
        .test-button:active {
            background: #2563eb;
            transform: scale(0.98);
        }
        
        .status-good { background: rgba(16, 185, 129, 0.1); border-left: 4px solid #10b981; }
        .status-bad { background: rgba(239, 68, 68, 0.1); border-left: 4px solid #ef4444; }
        .status-warn { background: rgba(245, 158, 11, 0.1); border-left: 4px solid #f59e0b; }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h1 class="text-xl font-bold mb-3">📱 手机端音乐按钮调试</h1>
        <div id="deviceInfo" class="text-sm text-gray-600 mb-3">检测设备信息...</div>
        <div id="quickStatus" class="text-sm">初始化中...</div>
    </div>

    <div class="debug-panel">
        <h2 class="text-lg font-semibold mb-3">🧪 快速测试</h2>
        
        <button class="test-button" onclick="testMusicButtonClick()">
            🎵 点击音乐按钮
        </button>
        
        <button class="test-button" onclick="testDirectToggle()">
            🔄 直接调用 toggleMusic
        </button>
        
        <button class="test-button" onclick="simulateTouch()">
            👆 模拟触摸事件
        </button>
        
        <button class="test-button" onclick="checkAllStates()">
            📊 检查所有状态
        </button>
        
        <button class="test-button" onclick="clearLogs()">
            🗑️ 清除日志
        </button>
    </div>

    <div class="debug-panel">
        <h2 class="text-lg font-semibold mb-3">📋 实时日志</h2>
        <div id="logContainer" style="max-height: 300px; overflow-y: auto;">
            <div class="log-item">等待事件...</div>
        </div>
    </div>

    <div class="debug-panel">
        <h2 class="text-lg font-semibold mb-3">🔍 详细状态</h2>
        <div id="detailedStatus" class="text-xs space-y-2">
            <div>检测中...</div>
        </div>
    </div>

    <script>
        const deviceInfo = document.getElementById('deviceInfo');
        const quickStatus = document.getElementById('quickStatus');
        const logContainer = document.getElementById('logContainer');
        const detailedStatus = document.getElementById('detailedStatus');
        
        let logs = [];
        let touchEvents = [];
        let clickEvents = [];
        
        function addLog(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const logEntry = {
                time,
                message,
                type,
                full: `${time}: ${message}`
            };
            
            logs.unshift(logEntry);
            if (logs.length > 20) logs.pop();
            
            renderLogs();
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function renderLogs() {
            const logHtml = logs.map(log => {
                const className = log.type === 'error' ? 'status-bad' : 
                                log.type === 'success' ? 'status-good' : 
                                log.type === 'warning' ? 'status-warn' : '';
                return `<div class="log-item ${className}">${log.full}</div>`;
            }).join('');
            
            logContainer.innerHTML = logHtml;
        }
        
        function updateDeviceInfo() {
            const isMobile = window.innerWidth <= 768;
            const hasTouch = 'ontouchstart' in window;
            const userAgent = navigator.userAgent;
            
            deviceInfo.innerHTML = `
                屏幕: ${window.innerWidth} x ${window.innerHeight} | 
                ${isMobile ? '移动设备' : '桌面设备'} | 
                触摸: ${hasTouch ? '支持' : '不支持'}
            `;
        }
        
        function updateQuickStatus() {
            const musicController = window.goldenLedgerMusic;
            const musicBtn = document.getElementById('music-toggle');
            const musicElement = document.getElementById('music-controller');
            
            let status = [];
            
            if (musicController) {
                status.push('✅ 控制器');
            } else {
                status.push('❌ 控制器');
            }
            
            if (musicBtn) {
                status.push('✅ 按钮');
            } else {
                status.push('❌ 按钮');
            }
            
            if (musicElement) {
                status.push('✅ 元素');
            } else {
                status.push('❌ 元素');
            }
            
            quickStatus.innerHTML = status.join(' | ');
        }
        
        function updateDetailedStatus() {
            const musicController = window.goldenLedgerMusic;
            const musicBtn = document.getElementById('music-toggle');
            const musicElement = document.getElementById('music-controller');
            
            let details = [];
            
            // 音乐控制器状态
            if (musicController) {
                details.push(`🎵 控制器: 存在`);
                details.push(`播放状态: ${musicController.isPlaying ? '播放中' : '暂停'}`);
                details.push(`音量: ${musicController.volume || 'N/A'}`);
            } else {
                details.push(`🎵 控制器: 不存在`);
            }
            
            // 按钮状态
            if (musicBtn) {
                details.push(`🔘 按钮: 存在`);
                details.push(`按钮ID: ${musicBtn.id}`);
                details.push(`按钮类: ${musicBtn.className}`);
                details.push(`按钮样式: cursor=${musicBtn.style.cursor}`);
                
                // 检查事件监听器
                const hasClick = musicBtn.onclick !== null;
                details.push(`点击事件: ${hasClick ? '有' : '无'}`);
            } else {
                details.push(`🔘 按钮: 不存在`);
            }
            
            // 元素状态
            if (musicElement) {
                details.push(`📦 元素: 存在`);
                details.push(`拖动状态: ${musicElement._isDragging ? '拖动中' : '正常'}`);
                const rect = musicElement.getBoundingClientRect();
                details.push(`位置: (${Math.round(rect.left)}, ${Math.round(rect.top)})`);
            } else {
                details.push(`📦 元素: 不存在`);
            }
            
            // 触摸事件统计
            details.push(`触摸事件: ${touchEvents.length} 次`);
            details.push(`点击事件: ${clickEvents.length} 次`);
            
            detailedStatus.innerHTML = details.map(d => `<div>${d}</div>`).join('');
        }
        
        function testMusicButtonClick() {
            addLog('开始测试音乐按钮点击', 'info');
            
            const musicBtn = document.getElementById('music-toggle');
            if (musicBtn) {
                addLog('找到音乐按钮，模拟点击', 'success');
                
                // 记录点击前状态
                const beforeState = window.goldenLedgerMusic ? window.goldenLedgerMusic.isPlaying : 'unknown';
                addLog(`点击前播放状态: ${beforeState}`, 'info');
                
                // 模拟点击
                musicBtn.click();
                
                // 检查点击后状态
                setTimeout(() => {
                    const afterState = window.goldenLedgerMusic ? window.goldenLedgerMusic.isPlaying : 'unknown';
                    addLog(`点击后播放状态: ${afterState}`, afterState !== beforeState ? 'success' : 'warning');
                }, 100);
                
            } else {
                addLog('音乐按钮未找到', 'error');
            }
        }
        
        function testDirectToggle() {
            addLog('开始测试直接调用 toggleMusic', 'info');
            
            if (window.goldenLedgerMusic && typeof window.goldenLedgerMusic.toggleMusic === 'function') {
                const beforeState = window.goldenLedgerMusic.isPlaying;
                addLog(`调用前播放状态: ${beforeState}`, 'info');
                
                try {
                    window.goldenLedgerMusic.toggleMusic();
                    addLog('toggleMusic 调用成功', 'success');
                    
                    setTimeout(() => {
                        const afterState = window.goldenLedgerMusic.isPlaying;
                        addLog(`调用后播放状态: ${afterState}`, afterState !== beforeState ? 'success' : 'warning');
                    }, 100);
                    
                } catch (error) {
                    addLog(`toggleMusic 调用失败: ${error.message}`, 'error');
                }
            } else {
                addLog('toggleMusic 方法不存在', 'error');
            }
        }
        
        function simulateTouch() {
            addLog('开始模拟触摸事件', 'info');
            
            const musicBtn = document.getElementById('music-toggle');
            if (musicBtn) {
                const rect = musicBtn.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // 创建触摸事件
                const touchStart = new TouchEvent('touchstart', {
                    touches: [{
                        clientX: centerX,
                        clientY: centerY,
                        target: musicBtn
                    }],
                    bubbles: true,
                    cancelable: true
                });
                
                const touchEnd = new TouchEvent('touchend', {
                    bubbles: true,
                    cancelable: true
                });
                
                addLog(`模拟触摸位置: (${Math.round(centerX)}, ${Math.round(centerY)})`, 'info');
                
                musicBtn.dispatchEvent(touchStart);
                setTimeout(() => {
                    musicBtn.dispatchEvent(touchEnd);
                    musicBtn.click();
                    addLog('触摸事件序列完成', 'success');
                }, 50);
                
            } else {
                addLog('音乐按钮未找到，无法模拟触摸', 'error');
            }
        }
        
        function checkAllStates() {
            addLog('检查所有状态', 'info');
            updateQuickStatus();
            updateDetailedStatus();
            addLog('状态检查完成', 'success');
        }
        
        function clearLogs() {
            logs = [];
            touchEvents = [];
            clickEvents = [];
            renderLogs();
            addLog('日志已清除', 'info');
        }
        
        // 监听所有相关事件
        document.addEventListener('touchstart', (e) => {
            touchEvents.push({
                type: 'touchstart',
                time: Date.now(),
                target: e.target.id || e.target.tagName,
                x: e.touches[0].clientX,
                y: e.touches[0].clientY
            });
            addLog(`触摸开始: ${e.target.id || e.target.tagName} (${Math.round(e.touches[0].clientX)}, ${Math.round(e.touches[0].clientY)})`, 'info');
        });
        
        document.addEventListener('touchend', (e) => {
            addLog(`触摸结束: ${e.target.id || e.target.tagName}`, 'info');
        });
        
        document.addEventListener('click', (e) => {
            clickEvents.push({
                type: 'click',
                time: Date.now(),
                target: e.target.id || e.target.tagName
            });
            addLog(`点击事件: ${e.target.id || e.target.tagName}`, 'info');
        });
        
        // 页面加载完成
        window.addEventListener('load', function() {
            updateDeviceInfo();
            addLog('页面加载完成', 'success');
            
            setTimeout(() => {
                updateQuickStatus();
                updateDetailedStatus();
                addLog('初始状态检查完成', 'success');
                
                // 监听音乐按钮
                const musicBtn = document.getElementById('music-toggle');
                if (musicBtn) {
                    musicBtn.addEventListener('click', () => {
                        addLog('音乐按钮点击事件触发', 'success');
                    });
                }
                
            }, 1000);
        });
        
        // 定期更新状态
        setInterval(() => {
            updateQuickStatus();
            updateDetailedStatus();
        }, 2000);
        
        // 监听控制台日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.join(' ');
            if (message.includes('🎵')) {
                addLog(`控制台: ${message}`, 'info');
            }
        };
        
        addLog('调试页面初始化完成', 'success');
    </script>
</body>
</html>

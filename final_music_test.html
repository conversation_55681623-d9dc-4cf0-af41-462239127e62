<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终音乐测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="background_control.js"></script>
    <script src="music_control.js"></script>
    <style>
        body {
            font-family: 'Inter', 'Noto Sans JP', sans-serif;
        }
        
        /* 强制显示音乐控制器 */
        #music-controller {
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 99999 !important;
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50px !important;
            padding: 10px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
            border: 2px solid #6c5ce7 !important;
        }
        
        .debug-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 12px;
            max-width: 400px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 99998;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-600 via-blue-600 to-teal-500">
    <!-- 背景动画容器 -->
    <div id="background-container" class="fixed inset-0 z-0"></div>
    
    <!-- 主要内容 -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div class="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 max-w-2xl w-full">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">🎵 最终音乐控制器测试</h1>
                <p class="text-gray-600">检查右上角是否显示音乐控制按钮</p>
            </div>

            <!-- 状态显示 -->
            <div class="bg-gradient-to-r from-purple-100 to-blue-100 rounded-2xl p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">📊 实时状态</h2>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="bg-white rounded-lg p-3">
                        <div class="text-gray-500">控制器状态</div>
                        <div id="controller-status" class="font-semibold">检查中...</div>
                    </div>
                    <div class="bg-white rounded-lg p-3">
                        <div class="text-gray-500">音乐状态</div>
                        <div id="music-status" class="font-semibold">检查中...</div>
                    </div>
                    <div class="bg-white rounded-lg p-3">
                        <div class="text-gray-500">音量</div>
                        <div id="volume-status" class="font-semibold">检查中...</div>
                    </div>
                    <div class="bg-white rounded-lg p-3">
                        <div class="text-gray-500">DOM元素</div>
                        <div id="dom-status" class="font-semibold">检查中...</div>
                    </div>
                </div>
            </div>

            <!-- 手动控制 -->
            <div class="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-2xl p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">🎛️ 手动控制</h2>
                <div class="grid grid-cols-2 gap-3">
                    <button id="manual-play" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg transition-all">
                        ▶️ 播放
                    </button>
                    <button id="manual-pause" class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-all">
                        ⏸️ 暂停
                    </button>
                    <button id="force-create" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-all">
                        🔧 强制创建控制器
                    </button>
                    <button id="check-elements" class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg transition-all">
                        🔍 检查元素
                    </button>
                </div>
            </div>

            <!-- 返回链接 -->
            <div class="text-center">
                <a href="index.html" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all">
                    ← 返回首页
                </a>
            </div>
        </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel">
        <div style="font-weight: bold; margin-bottom: 10px;">🔧 调试日志</div>
        <div id="debug-log">等待初始化...</div>
    </div>

    <script>
        // 调试日志
        const debugLog = document.getElementById('debug-log');
        function addDebugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        // 状态更新函数
        function updateStatus() {
            // 控制器状态
            const hasController = !!window.musicController;
            document.getElementById('controller-status').textContent = hasController ? '✅ 已创建' : '❌ 未创建';
            
            // 音乐状态
            if (hasController) {
                const status = window.musicController.getStatus();
                document.getElementById('music-status').textContent = status.isPlaying ? '🎵 播放中' : '⏸️ 已暂停';
                document.getElementById('volume-status').textContent = Math.round(status.volume * 100) + '%';
            } else {
                document.getElementById('music-status').textContent = '❌ 无法获取';
                document.getElementById('volume-status').textContent = '❌ 无法获取';
            }
            
            // DOM元素状态
            const panel = document.getElementById('music-controller');
            const toggle = document.getElementById('music-toggle');
            document.getElementById('dom-status').textContent = 
                panel && toggle ? '✅ 完整' : (panel ? '⚠️ 部分' : '❌ 缺失');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugLog('页面DOM加载完成');
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
            
            // 检查音乐控制器创建
            let checkCount = 0;
            const checkInterval = setInterval(() => {
                checkCount++;
                if (window.musicController) {
                    addDebugLog('✅ 音乐控制器已创建');
                    clearInterval(checkInterval);
                    
                    // 检查控制面板
                    setTimeout(() => {
                        const panel = document.getElementById('music-controller');
                        if (panel) {
                            addDebugLog('✅ 音乐控制面板已显示');
                            panel.style.border = '3px solid #00ff00'; // 绿色边框标识
                        } else {
                            addDebugLog('❌ 音乐控制面板未显示');
                        }
                    }, 500);
                } else if (checkCount > 50) {
                    addDebugLog('❌ 5秒内未检测到音乐控制器');
                    clearInterval(checkInterval);
                }
            }, 100);

            // 手动控制按钮
            document.getElementById('manual-play').addEventListener('click', () => {
                if (window.musicController) {
                    window.musicController.playMusic();
                    addDebugLog('手动播放音乐');
                } else {
                    addDebugLog('❌ 音乐控制器不存在');
                }
            });

            document.getElementById('manual-pause').addEventListener('click', () => {
                if (window.musicController) {
                    window.musicController.pauseMusic();
                    addDebugLog('手动暂停音乐');
                } else {
                    addDebugLog('❌ 音乐控制器不存在');
                }
            });

            document.getElementById('force-create').addEventListener('click', () => {
                try {
                    if (!window.musicController) {
                        window.musicController = new MusicController();
                        addDebugLog('✅ 强制创建音乐控制器成功');
                    } else {
                        addDebugLog('⚠️ 音乐控制器已存在');
                        // 强制重新创建控制面板
                        window.musicController.createMusicControl();
                        addDebugLog('🔧 重新创建控制面板');
                    }
                } catch (error) {
                    addDebugLog('❌ 强制创建失败: ' + error.message);
                }
            });

            document.getElementById('check-elements').addEventListener('click', () => {
                const controller = document.getElementById('music-controller');
                const toggle = document.getElementById('music-toggle');
                const volume = document.getElementById('music-volume');
                
                addDebugLog(`控制器面板: ${controller ? '✅' : '❌'}`);
                addDebugLog(`播放按钮: ${toggle ? '✅' : '❌'}`);
                addDebugLog(`音量控制: ${volume ? '✅' : '❌'}`);
                
                if (controller) {
                    const rect = controller.getBoundingClientRect();
                    addDebugLog(`位置: top=${Math.round(rect.top)}, right=${Math.round(window.innerWidth - rect.right)}`);
                }
            });

            addDebugLog('调试系统初始化完成');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的音乐按钮测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 10001;
            max-width: 350px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 20px;
            margin-top: 100px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
    </style>
</head>
<body>
    <div class="test-info" id="testInfo">
        <h3>🎵 音乐按钮测试状态</h3>
        <div id="statusList">
            <div><span class="status-indicator status-warning"></span>初始化中...</div>
        </div>
        <div style="margin-top: 10px; font-size: 12px;">
            <strong>测试说明:</strong><br>
            • 拖动音乐按钮到任意位置<br>
            • 点击音乐按钮播放/暂停<br>
            • 拖动眼睛按钮测试对比<br>
            • 检查控制台日志
        </div>
    </div>

    <div class="test-container">
        <h1 class="text-3xl font-bold text-center mb-8">🎵 音乐按钮拖动修复测试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-blue-50 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">🔧 修复内容</h2>
                <ul class="space-y-2 text-sm">
                    <li>✅ 移除旧的拖动系统冲突</li>
                    <li>✅ 修复 300ms 延迟问题</li>
                    <li>✅ 优化点击与拖动检测</li>
                    <li>✅ 添加拖动状态样式</li>
                    <li>✅ 支持任意方向拖动</li>
                </ul>
            </div>
            
            <div class="bg-green-50 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">🧪 测试项目</h2>
                <ul class="space-y-2 text-sm">
                    <li>🎯 横向拖动</li>
                    <li>🎯 纵向拖动</li>
                    <li>🎯 对角线拖动</li>
                    <li>🎯 边界限制</li>
                    <li>🎯 位置保存</li>
                    <li>🎯 点击功能</li>
                </ul>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <button onclick="resetPositions()" class="bg-blue-500 text-white px-6 py-2 rounded-lg mr-4">
                重置所有位置
            </button>
            <button onclick="showPositions()" class="bg-green-500 text-white px-6 py-2 rounded-lg">
                显示当前位置
            </button>
        </div>
        
        <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h3 class="font-semibold mb-2">📱 移动端测试</h3>
            <p class="text-sm">在手机上访问此页面，测试触摸拖动功能是否正常工作。</p>
        </div>
    </div>

    <script>
        const statusList = document.getElementById('statusList');
        const testInfo = document.getElementById('testInfo');
        
        let testResults = [];
        
        function updateStatus(test, status, message = '') {
            const existing = testResults.find(t => t.test === test);
            if (existing) {
                existing.status = status;
                existing.message = message;
            } else {
                testResults.push({ test, status, message });
            }
            
            renderStatus();
        }
        
        function renderStatus() {
            const statusHtml = testResults.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'warning' ? 'status-warning' : 'status-error';
                return `<div><span class="status-indicator ${statusClass}"></span>${result.test} ${result.message}</div>`;
            }).join('');
            
            statusList.innerHTML = statusHtml;
        }
        
        function resetPositions() {
            localStorage.removeItem('golden_ledger_music_settings');
            localStorage.removeItem('golden_ledger_background_settings');
            location.reload();
        }
        
        function showPositions() {
            const musicSettings = JSON.parse(localStorage.getItem('golden_ledger_music_settings') || '{}');
            const bgSettings = JSON.parse(localStorage.getItem('golden_ledger_background_settings') || '{}');
            
            alert(`当前位置:\n音乐按钮: ${JSON.stringify(musicSettings.position || '默认')}\n眼睛按钮: ${JSON.stringify(bgSettings.position || '默认')}`);
        }
        
        // 监听页面加载
        window.addEventListener('load', () => {
            updateStatus('页面加载', 'success');
            
            // 检查音乐控制器
            setTimeout(() => {
                const musicController = document.getElementById('music-controller');
                const musicBtn = document.getElementById('music-toggle');
                
                if (musicController && musicBtn) {
                    updateStatus('音乐按钮', 'success', '- 已找到');
                    
                    // 检查拖动功能
                    let dragDetected = false;
                    musicBtn.addEventListener('mousedown', () => {
                        setTimeout(() => {
                            if (!dragDetected) {
                                updateStatus('拖动检测', 'warning', '- 等待拖动测试');
                            }
                        }, 1000);
                    });
                    
                    // 监听拖动
                    const observer = new MutationObserver(() => {
                        if (musicController.style.left || musicController.style.top) {
                            if (!dragDetected) {
                                dragDetected = true;
                                updateStatus('拖动检测', 'success', '- 拖动功能正常');
                            }
                        }
                    });
                    
                    observer.observe(musicController, { 
                        attributes: true, 
                        attributeFilter: ['style'] 
                    });
                    
                } else {
                    updateStatus('音乐按钮', 'error', '- 未找到');
                }
                
                // 检查背景控制器
                const bgController = document.querySelector('.background-control');
                if (bgController) {
                    updateStatus('眼睛按钮', 'success', '- 已找到');
                } else {
                    updateStatus('眼睛按钮', 'error', '- 未找到');
                }
                
            }, 1000);
        });
        
        // 监听控制台日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.join(' ');
            if (message.includes('🎵 开始拖动')) {
                updateStatus('音乐拖动', 'success', '- 开始拖动');
            } else if (message.includes('👁️ 开始拖动')) {
                updateStatus('眼睛拖动', 'success', '- 开始拖动');
            }
        };
        
        console.log('🧪 音乐按钮测试页面初始化完成');
    </script>
</body>
</html>

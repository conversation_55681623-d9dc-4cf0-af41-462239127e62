<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置Session</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-red-600">🔄 重置Session</h1>
        
        <!-- 操作按钮 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 快速修复</h2>
            <div class="space-y-4">
                <button onclick="resetSessions()" class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    重置会话表
                </button>
                <button onclick="clearLocalStorage()" class="w-full bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    清除本地存储
                </button>
                <button onclick="testAPI()" class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    测试API连接
                </button>
                <button onclick="goToLogin()" class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    重新登录
                </button>
            </div>
        </div>

        <!-- 结果显示 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 操作结果</h2>
            <div id="results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始操作...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        function addResult(title, success, message) {
            const container = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
            `;
            
            container.appendChild(resultDiv);
        }

        async function resetSessions() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/fix-sessions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'reset_sessions'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('重置会话表', true, '会话表重置成功');
                } else {
                    addResult('重置会话表', false, result.error || '重置失败');
                }
            } catch (error) {
                addResult('重置会话表', false, error.message);
            }
        }

        function clearLocalStorage() {
            try {
                localStorage.removeItem('goldenledger_session_token');
                localStorage.removeItem('goldenledger_user');
                addResult('清除本地存储', true, '本地存储已清除');
            } catch (error) {
                addResult('清除本地存储', false, error.message);
            }
        }

        async function testAPI() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);
                const result = await response.json();
                
                if (response.ok) {
                    addResult('API连接测试', true, `API正常，版本: ${result.version}`);
                } else {
                    addResult('API连接测试', false, 'API连接失败');
                }
            } catch (error) {
                addResult('API连接测试', false, error.message);
            }
        }

        function goToLogin() {
            window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');
            
            if (token && user) {
                addResult('当前状态', false, '检测到无效的session，需要重置');
            } else {
                addResult('当前状态', true, '未检测到session，可以直接登录');
            }
        });
    </script>
</body>
</html>

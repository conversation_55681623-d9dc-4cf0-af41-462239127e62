<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ログイン - GoldenLedger | Smart AI-Powered Finance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>

    <!-- 只加载 API 配置，登录页面不需要认证管理器 -->
    <script src="api_config.js"></script>

    <!-- Google OAuth 2.0 SDK -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <!-- Google认证配置 -->
    <script src="google_auth_config.js"></script>

    <!-- 用户管理系统 -->
    <script src="user_manager.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="image/favicon.ico">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .login-card { 
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-field {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }
        .input-field:focus {
            border-color: #6c5ce7;
            box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(108, 92, 231, 0.3);
        }
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="floating-element absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full"></div>
        <div class="floating-element absolute top-40 right-32 w-24 h-24 bg-white/5 rounded-full" style="animation-delay: -2s;"></div>
        <div class="floating-element absolute bottom-32 left-1/4 w-40 h-40 bg-white/5 rounded-full" style="animation-delay: -4s;"></div>
    </div>

    <!-- Login Container -->
    <div class="w-full max-w-md relative z-10">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-2xl mb-4">
                <span class="text-4xl">🚀</span>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">GoldenLedger</h1>
            <p class="text-white/80 text-lg">Smart AI-Powered Finance System</p>
            <div class="flex justify-center items-center mt-4 space-x-4">
                <div class="flex items-center space-x-2">
                    <span class="pulse-dot w-2 h-2 bg-green-400 rounded-full"></span>
                    <span class="text-white/70 text-sm">AI Engine Active</span>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="pulse-dot w-2 h-2 bg-blue-400 rounded-full"></span>
                    <span class="text-white/70 text-sm">Cloud Ready</span>
                </div>
            </div>
        </div>

        <!-- Login Form -->
        <div class="login-card rounded-2xl p-8 shadow-2xl">
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        👤 ユーザー名
                    </label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        required
                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                        placeholder="ユーザー名を入力"
                        autocomplete="username"
                    >
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        🔒 パスワード
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                        placeholder="パスワードを入力"
                        autocomplete="current-password"
                    >
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                        <span class="ml-2 text-sm text-gray-600">ログイン状態を保持</span>
                    </label>
                    <a href="#" class="text-sm text-purple-600 hover:text-purple-800 transition-colors">
                        パスワードを忘れた方
                    </a>
                </div>

                <button 
                    type="submit" 
                    class="btn-primary w-full py-3 px-4 rounded-xl text-white font-semibold text-lg"
                >
                    🚀 ログイン
                </button>
            </form>

            <!-- Divider -->
            <div class="my-6 flex items-center">
                <div class="flex-1 border-t border-gray-200"></div>
                <span class="px-4 text-sm text-gray-500">または</span>
                <div class="flex-1 border-t border-gray-200"></div>
            </div>

            <!-- Social Login -->
            <div class="space-y-3">
                <button id="google-login-btn" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors" onclick="handleGoogleLogin()">
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Googleでログイン
                </button>
                
                <button class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
                    <svg class="w-5 h-5 mr-3" fill="#1877F2" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    Facebookでログイン
                </button>
            </div>



            <!-- Sign Up Link -->
            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    アカウントをお持ちでない方は
                    <a href="register.html" class="text-purple-600 hover:text-purple-800 font-semibold transition-colors">
                        新規登録
                    </a>
                </p>
            </div>
        </div>

        <!-- Features Preview -->
        <div class="mt-8 text-center">
            <div class="grid grid-cols-3 gap-4 text-white/80">
                <div class="text-center">
                    <div class="text-2xl mb-1">🤖</div>
                    <div class="text-xs">AI記帳</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl mb-1">📊</div>
                    <div class="text-xs">リアルタイム分析</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl mb-1">🔒</div>
                    <div class="text-xs">企業級セキュリティ</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 禁用页面保护 - 登录页面不需要认证
        window.protectPage = function() {
            console.log('🔐 页面保护已禁用 - 登录页面');
            return Promise.resolve(true);
        };

        // 禁用页面初始化器
        window.pageInitializer = {
            initialize: () => Promise.resolve(true)
        };

        // 专业级登录处理
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loginForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    alert('ユーザー名とパスワードを入力してください。');
                    return;
                }

                // Show loading state
                const submitBtn = e.target.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '🔄 ログイン中...';
                submitBtn.disabled = true;

                try {
                    // 等待 API 配置加载
                    let retries = 0;
                    while (!window.GoldenLedgerAPI && retries < 30) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        retries++;
                    }

                    if (!window.GoldenLedgerAPI) {
                        throw new Error('API 配置加载失败');
                    }

                    // 直接调用登录 API
                    const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/login'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 内测期间用户限制检查
                        const allowedUsers = ['<EMAIL>', 'admin'];
                        const userEmail = result.data.user.email || result.data.user.username;

                        if (!allowedUsers.includes(userEmail)) {
                            // 不允许的用户，显示内测提示
                            alert('現在ベータテスト中です。このアカウントではログインできません。');
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            return;
                        }

                        // 保存认证信息
                        localStorage.setItem('goldenledger_session_token', result.data.session_token);
                        localStorage.setItem('goldenledger_user', JSON.stringify(result.data.user));

                        // 获取返回 URL
                        const urlParams = new URLSearchParams(window.location.search);
                        const returnUrl = urlParams.get('return') || '/master_dashboard.html';

                        console.log('🔐 登录成功，重定向到:', returnUrl);

                        // 重定向到目标页面
                        window.location.href = decodeURIComponent(returnUrl);
                    } else {
                        alert(result.error || 'ログインに失敗しました。ユーザー名とパスワードを確認してください。');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                } catch (error) {
                    console.error('🔐 Login error:', error);
                    alert('ログインエラー: ' + error.message);
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        });

        console.log('🔐 专业级登录页面已加载');

        // 简化的Google登录处理
        function handleGoogleLogin() {
            console.log('🔐 Google登录按钮被点击');

            if (window.googleAuthManager) {
                window.googleAuthManager.signIn();
            } else {
                console.error('❌ Google认证管理器未找到');
                alert('Google登录服务未就绪，请刷新页面重试');
            }
        }

        // 页面加载完成后初始化Google登录
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.googleAuthManager) {
                    window.googleAuthManager.initialize().catch(error => {
                        console.error('❌ Google登录初始化失败:', error);
                    });
                }
            }, 1000);
        });
    </script>
</body>
</html>

/**
 * PayPal 配置文件
 * 支持 Sandbox 和 Production 环境
 */

// PayPal 环境配置
const PAYPAL_CONFIG = {
    // Sandbox 环境配置（测试用）
    sandbox: {
        clientId: 'AYiPC-8q5XOKJKZugWVgMNFJebq_RU8XXZ8kDB9_h2w_FQzQz8z_9z8z_9z8z_9z8z',  // 请替换为您的 Sandbox Client ID
        environment: 'sandbox',
        currency: 'JPY',
        locale: 'ja_JP'
    },
    
    // 生产环境配置（上线时使用）
    production: {
        clientId: 'YOUR_PRODUCTION_CLIENT_ID',  // 请替换为您的生产环境 Client ID
        environment: 'production',
        currency: 'JPY',
        locale: 'ja_JP'
    }
};

// 当前使用的环境（开发时使用 sandbox）
const CURRENT_ENV = 'sandbox';

// 套餐配置
const SUBSCRIPTION_PLANS = {
    free: {
        id: 'free',
        name: '無料プラン',
        price: 0,
        currency: 'JPY',
        interval: 'month',
        features: [
            'AI記帳: 10回/月',
            'データ保存: 30日',
            '基本レポート',
            'メールサポート'
        ],
        limits: {
            ai_requests_monthly: 10,
            data_retention_days: 30,
            export_enabled: false,
            advanced_ai: false,
            multi_company: false
        }
    },
    
    basic: {
        id: 'basic',
        name: 'ベーシックプラン',
        price: 980,
        currency: 'JPY',
        interval: 'month',
        paypal_plan_id: 'P-BASIC-PLAN-ID',  // PayPal 订阅计划 ID
        features: [
            'AI記帳: 100回/月',
            'データ保存: 1年',
            'PDF エクスポート',
            '基本AI分析',
            'メールサポート'
        ],
        limits: {
            ai_requests_monthly: 100,
            data_retention_days: 365,
            export_enabled: true,
            advanced_ai: false,
            multi_company: false
        }
    },
    
    pro: {
        id: 'pro',
        name: 'プロプラン',
        price: 2980,
        currency: 'JPY',
        interval: 'month',
        paypal_plan_id: 'P-PRO-PLAN-ID',  // PayPal 订阅计划 ID
        features: [
            'AI記帳: 無制限',
            'データ保存: 無制限',
            '高度AI分析',
            '複数会社管理',
            'OCR発票認識',
            '優先サポート'
        ],
        limits: {
            ai_requests_monthly: null, // 无限制
            data_retention_days: null, // 无限制
            export_enabled: true,
            advanced_ai: true,
            multi_company: true
        }
    },
    
    enterprise: {
        id: 'enterprise',
        name: 'エンタープライズプラン',
        price: 9800,
        currency: 'JPY',
        interval: 'month',
        paypal_plan_id: 'P-ENTERPRISE-PLAN-ID',  // PayPal 订阅计划 ID
        features: [
            'すべてのPro機能',
            'マルチユーザー',
            'API アクセス',
            'カスタム統合',
            '専属サポート'
        ],
        limits: {
            ai_requests_monthly: null,
            data_retention_days: null,
            export_enabled: true,
            advanced_ai: true,
            multi_company: true,
            api_access: true,
            multi_user: true
        }
    }
};

// PayPal SDK 配置
const getPayPalConfig = () => {
    const config = PAYPAL_CONFIG[CURRENT_ENV];
    return {
        'client-id': config.clientId,
        'currency': config.currency,
        'locale': config.locale,
        'components': 'buttons,funding-eligibility',
        'enable-funding': 'venmo,paylater',
        'disable-funding': 'card'
    };
};

// API 端点配置
const PAYMENT_API = {
    createOrder: '/api/payment/create-order',
    captureOrder: '/api/payment/capture-order',
    createSubscription: '/api/payment/create-subscription',
    cancelSubscription: '/api/payment/cancel-subscription',
    getSubscriptionStatus: '/api/payment/subscription-status',
    webhook: '/api/payment/webhook'
};

// 导出配置
window.PayPalConfig = {
    config: PAYPAL_CONFIG[CURRENT_ENV],
    plans: SUBSCRIPTION_PLANS,
    api: PAYMENT_API,
    getPayPalConfig,
    currentEnv: CURRENT_ENV
};

// 动态加载 PayPal SDK
function loadPayPalSDK() {
    return new Promise((resolve, reject) => {
        // 检查是否已经加载
        if (window.paypal) {
            resolve(window.paypal);
            return;
        }
        
        const config = getPayPalConfig();
        const params = new URLSearchParams(config);
        const script = document.createElement('script');
        
        script.src = `https://www.paypal.com/sdk/js?${params.toString()}`;
        script.async = true;
        
        script.onload = () => {
            if (window.paypal) {
                console.log('✅ PayPal SDK loaded successfully');
                resolve(window.paypal);
            } else {
                reject(new Error('PayPal SDK failed to load'));
            }
        };
        
        script.onerror = () => {
            reject(new Error('Failed to load PayPal SDK script'));
        };
        
        document.head.appendChild(script);
    });
}

// 初始化 PayPal
window.initPayPal = loadPayPalSDK;

console.log('💳 PayPal Config loaded:', {
    environment: CURRENT_ENV,
    currency: PAYPAL_CONFIG[CURRENT_ENV].currency,
    plansCount: Object.keys(SUBSCRIPTION_PLANS).length
});

#!/usr/bin/env python3
"""
手动初始化数据库
"""
import sys
import os
sys.path.append('backend')

from database import DatabaseManager

def init_database():
    """初始化数据库"""
    
    print("🔧 手动初始化数据库")
    print("=" * 30)
    
    try:
        # 创建数据库管理器实例
        db = DatabaseManager()
        
        print("✅ 数据库初始化成功")
        
        # 测试插入一条记录
        test_entry = {
            'id': 'TEST001',
            'company_id': 'default',
            'entry_date': '2025-07-11',
            'entry_time': '13:30:00',
            'entry_datetime': '2025-07-11T13:30:00',
            'description': '测试记录',
            'debit_account': '现金',
            'credit_account': '销售收入',
            'amount': 1000.0,
            'reference_number': 'REF001',
            'ai_generated': True,
            'ai_confidence': 0.9,
            'attachment_path': 'test/path.png'
        }
        
        success = db.save_journal_entry(test_entry)
        if success:
            print("✅ 测试记录插入成功")
        else:
            print("❌ 测试记录插入失败")
        
        # 查询记录
        entries = db.get_journal_entries('default')
        print(f"📊 数据库中的记录数: {len(entries)}")
        
        if entries:
            latest = entries[-1]
            print(f"最新记录:")
            print(f"  ID: {latest.get('id')}")
            print(f"  描述: {latest.get('description')}")
            print(f"  附件: {latest.get('attachment_path')}")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    init_database()

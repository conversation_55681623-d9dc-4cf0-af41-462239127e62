<!DOCTYPE html>
<html lang="zh-CN" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">仕訳帳 - goldenledger会計AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_control_new.js"></script>

    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="image/favicon.ico">
    <style>
        /* 多语言字体支持 - 优雅的字体组合 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&family=Noto+Sans+JP:wght@300;400;500;600;700&display=swap');

        /* 根据语言设置最佳字体 */
        body {
            font-family: 'Inter', 'Noto Sans SC', 'Noto Sans JP', 'Hiragino Sans', 'Hiragino Kaku Gothic ProN', 'Yu Gothic', 'Meiryo', 'Microsoft YaHei', 'PingFang SC', 'SimHei', sans-serif;
            font-feature-settings: "liga" 1, "kern" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 中文字体优化 */
        [lang="zh-CN"] {
            font-family: 'Inter', 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;
        }

        /* 日文字体优化 */
        [lang="ja"] {
            font-family: 'Inter', 'Noto Sans JP', 'Hiragino Sans', 'Hiragino Kaku Gothic ProN', 'Yu Gothic', 'Meiryo', sans-serif;
        }

        /* 英文字体优化 */
        [lang="en"] {
            font-family: 'Inter', 'SF Pro Display', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', sans-serif;
        }
        
        .journal-entry {
            transition: all 0.2s ease;
        }
        
        .journal-entry:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .ai-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .loading {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .sortable-header {
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
            position: relative;
        }

        .sortable-header:hover {
            background-color: rgba(59, 130, 246, 0.1);
            color: #2563eb;
        }

        .sort-icon {
            display: inline-block;
            margin-left: 4px;
            transition: transform 0.2s ease;
            opacity: 0.5;
        }

        .sortable-header.active .sort-icon {
            opacity: 1;
            color: #2563eb;
        }

        .sortable-header.desc .sort-icon {
            transform: rotate(180deg);
        }

        .sort-indicator {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #6b7280;
        }

        /* 表格换行样式 */
        .table-cell-wrap {
            word-wrap: break-word;
            word-break: break-word;
            hyphens: auto;
            line-height: 1.4;
            min-height: 2.5rem;
        }

        .description-cell {
            max-width: 200px;
            min-width: 150px;
        }

        .account-cell {
            max-width: 150px;
            min-width: 120px;
        }

        /* 确保表格行高度自适应 */
        .journal-entry td {
            vertical-align: top;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        /* 语言切换器样式 */
        .language-switcher {
            position: relative;
            display: inline-block;
        }

        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 150px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        }

        .language-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .language-option:hover {
            background-color: #f3f4f6;
        }

        .language-option.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 500;
        }

        .language-flag {
            width: 20px;
            height: 15px;
            border-radius: 2px;
            display: inline-block;
        }

        /* 国旗样式 */
        .flag-cn {
            background: linear-gradient(to bottom, #de2910 0%, #de2910 100%);
            position: relative;
        }

        .flag-jp {
            background: white;
            border: 1px solid #ddd;
            position: relative;
        }

        .flag-jp::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 页面内容 -->
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">仕訳帳</h1>
                <p class="text-gray-600 mt-1">会計記録の管理</p>
            </div>

            <!-- 记录列表 -->
            <div class="p-6">
                <div id="entries-container" class="space-y-4">
                    <div class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p class="text-gray-600 mt-2">データを読み込み中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allEntries = [];

        // 页面加载时获取记录
        document.addEventListener('DOMContentLoaded', function() {
            loadJournalEntries();
        });

        // 加载记录列表
        async function loadJournalEntries() {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    document.getElementById('entries-container').innerHTML =
                        '<div class="text-red-500 text-center py-8">请先登录</div>';
                    return;
                }

                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/journal-entries?company_id=default', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('获取数据失败');
                }

                const result = await response.json();
                const entries = result.success ? result.data : [];
                allEntries = entries;
                displayEntries(entries);
            } catch (error) {
                console.error('加载记录失败:', error);
                document.getElementById('entries-container').innerHTML =
                    '<div class="text-red-500 text-center py-8">加载记录失败: ' + error.message + '</div>';
            }
        }

        // 显示记录列表
        function displayEntries(entries) {
            const container = document.getElementById('entries-container');

            if (entries.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-8">暂无记录</div>';
                return;
            }

            let html = `
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse">
                        <thead>
                            <tr class="bg-gray-50 border-b">
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">日期</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">描述</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">借方科目</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">贷方科目</th>
                                <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">金额</th>
                                <th class="px-4 py-3 text-center text-sm font-medium text-gray-700">附件</th>
                                <th class="px-4 py-3 text-center text-sm font-medium text-gray-700">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
            `;

            entries.forEach(entry => {
                const hasAttachment = entry.attachment_path ? '📎' : '';
                html += `
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-3 text-sm text-gray-900">${entry.entry_date || ''}</td>
                        <td class="px-4 py-3 text-sm text-gray-900">${entry.description || ''}</td>
                        <td class="px-4 py-3 text-sm text-gray-900">${entry.debit_account || ''}</td>
                        <td class="px-4 py-3 text-sm text-gray-900">${entry.credit_account || ''}</td>
                        <td class="px-4 py-3 text-sm text-gray-900 text-right">¥${(entry.amount || 0).toLocaleString()}</td>
                        <td class="px-4 py-3 text-center text-sm">${hasAttachment}</td>
                        <td class="px-4 py-3 text-center">
                            <div class="flex justify-center space-x-2">
                                <button onclick="editEntry('${entry.id}')"
                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                    编辑
                                </button>
                                <button onclick="deleteEntry('${entry.id}')"
                                        class="text-red-600 hover:text-red-800 text-sm">
                                    删除
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }

        // 编辑记录
        async function editEntry(entryId) {
            try {
                const entry = allEntries.find(e => e.id === entryId);
                if (!entry) {
                    alert('记录不存在');
                    return;
                }

                // 创建编辑模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">编辑记录</h2>
                        </div>
                        <div class="p-6">
                            <form class="space-y-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                                        <input type="date" id="edit-date" value="${entry.entry_date || ''}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">时间</label>
                                        <input type="time" id="edit-time" value="${entry.entry_time || ''}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                    <input type="text" id="edit-description" value="${entry.description || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">借方科目</label>
                                        <select id="edit-debit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">选择借方科目</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">贷方科目</label>
                                        <select id="edit-credit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">选择贷方科目</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">金额</label>
                                        <input type="number" id="edit-amount" value="${entry.amount || ''}" step="0.01"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">借方税率(%)</label>
                                        <input type="number" id="edit-debit-tax-rate" value="${entry.debit_tax_rate || 0}" step="0.01"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">贷方税率(%)</label>
                                        <input type="number" id="edit-credit-tax-rate" value="${entry.credit_tax_rate || 0}" step="0.01"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">参考号码</label>
                                    <input type="text" id="edit-reference" value="${entry.reference_number || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>

                                <!-- 附件管理部分 -->
                                <div class="border-t pt-4">
                                    <h3 class="text-lg font-medium text-gray-900 mb-3">附件管理</h3>

                                    <!-- 当前附件显示 -->
                                    <div id="current-attachments" class="mb-4">
                                        <div class="text-sm text-gray-500 italic">加载中...</div>
                                    </div>

                                    <!-- 上传新附件 -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="mb-3">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">上传新附件</label>
                                            <input type="file" id="edit-attachment-file"
                                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <p class="text-xs text-gray-500 mt-1">
                                                支持格式: 图片、PDF、Word、Excel
                                            </p>
                                        </div>

                                        <button type="button" onclick="uploadEditAttachment('${entryId}')"
                                                class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400">
                                            上传附件
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
                            <button onclick="closeEditModal()"
                                    class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                                取消
                            </button>
                            <button onclick="saveEditedEntry('${entryId}')" id="save-edit-btn"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                                保存修改
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // 加载科目选项
                await loadAccountOptions(entry);

                // 加载当前附件
                loadCurrentAttachments(entryId);

            } catch (error) {
                console.error('编辑记录失败:', error);
                alert('编辑记录失败: ' + error.message);
            }
        }

        // 关闭编辑模态框
        function closeEditModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black');
            if (modal) {
                modal.remove();
            }
        }

        // 加载科目选项
        async function loadAccountOptions(entry) {
            try {
                // 获取科目列表 - 使用预定义的科目列表，按类别分组
                const accounts = {
                    '資産': ['現金', '普通預金', '当座預金', '売掛金', '商品', '建物', '土地', '車両運搬具', '工具器具備品'],
                    '負債': ['買掛金', '未払金', '借入金', '預り金'],
                    '純資産': ['資本金', '利益剰余金'],
                    '収益': ['売上', '受取利息', '雑収入'],
                    '費用': ['仕入', '給料', '地代家賃', '水道光熱費', '通信費', '消耗品費', '雑費', '支払利息', '雑損失', '法人税等']
                };

                // 获取下拉框元素
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (!debitSelect || !creditSelect) {
                    console.error('科目選択ボックスが見つかりません');
                    return;
                }

                // 清空现有选项
                debitSelect.innerHTML = '<option value="">借方科目を選択</option>';
                creditSelect.innerHTML = '<option value="">貸方科目を選択</option>';

                // 添加所有科目选项
                Object.keys(accounts).forEach(category => {
                    // 为每个类别创建选项组
                    const debitOptgroup = document.createElement('optgroup');
                    debitOptgroup.label = category;
                    const creditOptgroup = document.createElement('optgroup');
                    creditOptgroup.label = category;

                    // 确保 accounts[category] 是数组
                    if (Array.isArray(accounts[category])) {
                        accounts[category].forEach(account => {
                            // 借方科目选项
                            const debitOption = document.createElement('option');
                            debitOption.value = account;
                            debitOption.textContent = account;
                            if (entry.debit_account === account) {
                                debitOption.selected = true;
                            }
                            debitOptgroup.appendChild(debitOption);

                            // 贷方科目选项
                            const creditOption = document.createElement('option');
                            creditOption.value = account;
                            creditOption.textContent = account;
                            if (entry.credit_account === account) {
                                creditOption.selected = true;
                            }
                            creditOptgroup.appendChild(creditOption);
                        });
                    }

                    debitSelect.appendChild(debitOptgroup);
                    creditSelect.appendChild(creditOptgroup);
                });

            } catch (error) {
                console.error('加载科目选项失败:', error);
                // 如果加载失败，回退到文本输入
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (debitSelect) {
                    debitSelect.outerHTML = `<input type="text" id="edit-debit" value="${entry.debit_account || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">`;
                }
                if (creditSelect) {
                    creditSelect.outerHTML = `<input type="text" id="edit-credit" value="${entry.credit_account || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">`;
                }
            }
        }

        // 关闭编辑模态框
        function closeEditModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black');
            if (modal) {
                modal.remove();
            }
        }

        // 保存编辑的记录
        async function saveEditedEntry(entryId) {
            const saveBtn = document.getElementById('save-edit-btn');
            const originalText = saveBtn ? saveBtn.textContent : '保存修改';

            try {
                // 禁用保存按钮，显示加载状态
                if (saveBtn) {
                    saveBtn.disabled = true;
                    saveBtn.textContent = '保存中...';
                }

                // 获取表单数据
                const formData = {
                    id: entryId,
                    entry_date: document.getElementById('edit-date').value,
                    entry_time: document.getElementById('edit-time').value,
                    description: document.getElementById('edit-description').value,
                    debit_account: document.getElementById('edit-debit').value,
                    credit_account: document.getElementById('edit-credit').value,
                    debit_tax_rate: parseFloat(document.getElementById('edit-debit-tax-rate').value) || 0,
                    credit_tax_rate: parseFloat(document.getElementById('edit-credit-tax-rate').value) || 0,
                    amount: parseFloat(document.getElementById('edit-amount').value),
                    reference_number: document.getElementById('edit-reference').value
                };

                console.log('准备保存的数据:', formData);

                // 验证必填字段
                if (!formData.description || !formData.debit_account || !formData.credit_account || !formData.amount) {
                    alert('请填写所有必填字段');
                    return;
                }

                // 发送更新请求
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                console.log('发送PUT请求到:', `https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${entryId}`);
                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${entryId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(formData)
                });

                console.log('服务器响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('服务器错误响应:', errorText);
                    throw new Error(`更新记录失败: ${response.status} ${errorText}`);
                }

                const result = await response.json();
                console.log('服务器响应结果:', result);

                if (!result.success) {
                    throw new Error(result.error || '更新记录失败');
                }

                // 关闭模态框
                closeEditModal();

                // 重新加载数据
                await loadJournalEntries();

                alert('记录更新成功！');

            } catch (error) {
                console.error('保存编辑失败:', error);
                alert('保存编辑失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.textContent = originalText;
                }
            }
        }

        // 删除记录
        async function deleteEntry(entryId) {
            try {
                // 确认删除
                if (!confirm('确定要删除这条记录吗？此操作无法撤销。')) {
                    return;
                }

                // 发送删除请求
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${entryId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('删除记录失败');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '删除记录失败');
                }

                // 重新加载数据
                await loadJournalEntries();

                alert('记录删除成功！');

            } catch (error) {
                console.error('删除记录失败:', error);
                alert('删除记录失败: ' + error.message);
            }
        }

        // 加载当前附件
        async function loadCurrentAttachments(entryId) {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    console.warn('認証トークンが見つかりません');
                    const attachmentsContainer = document.getElementById('current-attachments');
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-gray-500 italic">
                            暂无附件
                        </div>
                    `;
                    return;
                }

                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/attachments/${entryId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                const attachmentsContainer = document.getElementById('current-attachments');

                if (response.ok) {
                    // 检查响应内容类型
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        console.warn('添付ファイルAPIが非JSON応答を返しました。認証の問題の可能性があります');
                        attachmentsContainer.innerHTML = `
                            <div class="text-sm text-gray-500 italic">
                                暂无附件
                            </div>
                        `;
                        return;
                    }
                    
                    const attachments = await response.json();

                    if (attachments && attachments.length > 0) {
                        attachmentsContainer.innerHTML = `
                            <div class="bg-gray-50 rounded-lg p-3">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">
                                    当前附件
                                </h4>
                                <div class="space-y-2">
                                    ${attachments.map(attachment => `
                                        <div class="flex items-center justify-between bg-white p-2 rounded border">
                                            <div class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700">${attachment.filename || '附件文件'}</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <button onclick="simplePreview('${entryId}')"
                                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                                    预览
                                                </button>
                                                <button onclick="deleteAttachment('${entryId}', '${attachment.filename}')"
                                                        class="text-red-600 hover:text-red-800 text-sm">
                                                    删除
                                                </button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        attachmentsContainer.innerHTML = `
                            <div class="text-sm text-gray-500 italic">
                                <span data-i18n="attachments.no_attachments">暂无附件</span>
                            </div>
                        `;
                    }
                } else {
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-gray-500 italic">
                            <span data-i18n="attachments.no_attachments">暂无附件</span>
                        </div>
                    `;
                }

                // 不需要多语言翻译，直接使用中文文本

            } catch (error) {
                console.error('添付ファイルの読み込みに失敗:', error);
                const attachmentsContainer = document.getElementById('current-attachments');
                if (attachmentsContainer) {
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-red-500">
                            添付ファイルの読み込みに失敗: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // 上传编辑附件
        async function uploadEditAttachment(entryId) {
            try {
                const fileInput = document.getElementById('edit-attachment-file');
                const file = fileInput.files[0];

                if (!file) {
                    alert('请选择要上传的文件');
                    return;
                }

                // 检查文件大小 (10MB限制)
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    return;
                }

                const formData = new FormData();
                formData.append('file', file);
                formData.append('entry_id', entryId);

                // 显示上传进度
                const uploadButton = document.querySelector('button[onclick*="uploadEditAttachment"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = 'アップロード中...';

                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    throw new Error('登録してください');
                }

                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/attachments/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('上传失败');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '上传失败');
                }

                // 清空文件输入
                fileInput.value = '';

                // 重新加载附件列表
                await loadCurrentAttachments(entryId);

                alert('附件上传成功！');

            } catch (error) {
                console.error('上传附件失败:', error);
                alert('上传附件失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                const uploadButton = document.querySelector('button[onclick*="uploadEditAttachment"]');
                if (uploadButton) {
                    uploadButton.disabled = false;
                    uploadButton.textContent = '上传附件';
                }
            }
        }

        // 删除重复的预览附件函数

        // 删除附件
        async function deleteAttachment(entryId, filename) {
            try {
                if (!confirm(`确定要删除附件 "${filename}" 吗？此操作无法撤销。`)) {
                    return;
                }

                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    throw new Error('登録してください');
                }

                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/attachments/${entryId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ filename: filename })
                });

                if (!response.ok) {
                    throw new Error('删除附件失败');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '删除附件失败');
                }

                // 重新加载附件列表
                await loadCurrentAttachments(entryId);

                alert('附件删除成功！');

            } catch (error) {
                console.error('删除附件失败:', error);
                alert('删除附件失败: ' + error.message);
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">仕訳帳 - GoldenLedger AI会計システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_control_new.js"></script>

    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="image/favicon.ico">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 多语言字体支持 - 优雅的字体组合 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&family=Noto+Sans+JP:wght@300;400;500;600;700&display=swap');

        /* 根据语言设置最佳字体 */
        body {
            font-family: 'Inter', 'Noto Sans SC', 'Noto Sans JP', 'Hiragino Sans', 'Hiragino Kaku Gothic ProN', 'Yu Gothic', 'Meiryo', 'Microsoft YaHei', 'PingFang SC', 'SimHei', sans-serif;
            font-feature-settings: "liga" 1, "kern" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 中文字体优化 */
        [lang="zh-CN"] {
            font-family: 'Inter', 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;
        }

        /* 日文字体优化 */
        [lang="ja"] {
            font-family: 'Inter', 'Noto Sans JP', 'Hiragino Sans', 'Hiragino Kaku Gothic ProN', 'Yu Gothic', 'Meiryo', sans-serif;
        }

        /* 英文字体优化 */
        [lang="en"] {
            font-family: 'Inter', 'SF Pro Display', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', sans-serif;
        }

        /* 现代化卡片设计 */
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 表格现代化样式 */
        .modern-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 悬停效果 */
        .table-row:hover {
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        /* 语言切换按钮 */
        .lang-btn {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .lang-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .lang-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-success { background: #10b981; }
        .status-pending { background: #f59e0b; }
        .status-error { background: #ef4444; }

        /* 移动端响应式设计 */
        @media (max-width: 768px) {
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 0;
            }

            .container {
                padding: 1rem;
                max-width: 100%;
            }

            /* 语言切换器移动端优化 */
            .fixed.top-4.right-4 {
                top: 0.5rem;
                right: 0.5rem;
                flex-direction: column;
                gap: 0.25rem;
            }

            .lang-btn {
                font-size: 0.75rem;
                padding: 0.5rem 0.75rem;
                min-width: 60px;
                text-align: center;
            }

            /* 标题区域移动端优化 */
            .glass-card {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }

            .glass-card h1 {
                font-size: 1.875rem;
                margin-bottom: 0.5rem;
            }

            .glass-card p {
                font-size: 1rem;
            }

            /* 操作栏移动端优化 */
            .glass-card .flex.flex-col.sm\\:flex-row {
                flex-direction: column;
                gap: 1rem;
            }

            /* 隐藏桌面版表格，显示移动版卡片 */
            .modern-table {
                display: none;
            }

            .mobile-entries {
                display: block;
            }

            /* 移动端记录卡片样式 */
            .mobile-entry-card {
                background: white;
                border-radius: 12px;
                padding: 1rem;
                margin-bottom: 0.75rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-left: 4px solid #667eea;
            }

            .mobile-entry-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 0.75rem;
            }

            .mobile-entry-date {
                font-size: 0.75rem;
                color: #6b7280;
                font-weight: 500;
            }

            .mobile-entry-amount {
                font-size: 1.125rem;
                font-weight: bold;
                color: #1f2937;
            }

            .mobile-entry-description {
                font-size: 0.875rem;
                color: #374151;
                margin-bottom: 0.75rem;
                line-height: 1.4;
            }

            .mobile-entry-accounts {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-bottom: 0.75rem;
            }

            .mobile-account-tag {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
                border-radius: 6px;
                font-weight: 500;
            }

            .mobile-debit {
                background: #fef2f2;
                color: #dc2626;
            }

            .mobile-credit {
                background: #eff6ff;
                color: #2563eb;
            }

            .mobile-entry-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 0.75rem;
                border-top: 1px solid #f3f4f6;
            }

            .mobile-attachment-indicator {
                display: flex;
                align-items: center;
                font-size: 0.75rem;
                color: #6b7280;
            }

            .mobile-actions {
                display: flex;
                gap: 0.5rem;
            }

            .mobile-action-btn {
                padding: 0.5rem;
                border-radius: 6px;
                font-size: 0.75rem;
                transition: all 0.2s ease;
            }

            .mobile-edit-btn {
                background: #eff6ff;
                color: #2563eb;
            }

            .mobile-delete-btn {
                background: #fef2f2;
                color: #dc2626;
            }

            .mobile-action-btn:hover {
                transform: scale(1.05);
            }

            /* 搜索框移动端优化 */
            #search-input {
                width: 100%;
                font-size: 1rem;
            }

            /* 按钮移动端优化 */
            .flex.items-center.space-x-4 button {
                font-size: 0.875rem;
                padding: 0.75rem 1rem;
            }

            /* 加载和错误状态移动端优化 */
            .text-center.py-16 {
                padding: 2rem 1rem;
            }

            .w-24.h-24 {
                width: 4rem;
                height: 4rem;
            }
        }

        /* 桌面端隐藏移动版 */
        @media (min-width: 769px) {
            .mobile-entries {
                display: none;
            }
        }

        /* 编辑模态框样式 */
        .edit-modal {
            backdrop-filter: blur(8px);
            animation: fadeIn 0.3s ease-out;
        }

        .edit-modal-content {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            animation: slideUp 0.3s ease-out;
            max-height: 90vh;
            overflow-y: auto;
        }

        .edit-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px 16px 0 0;
            position: relative;
        }

        .edit-modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .edit-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .edit-form-section {
            background: #f8faff;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }

        .edit-form-input {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .edit-form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .edit-form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* 编辑模态框移动端优化 */
        @media (max-width: 768px) {
            .edit-modal-content {
                margin: 1rem;
                max-width: calc(100vw - 2rem);
                max-height: calc(100vh - 2rem);
                border-radius: 12px;
            }

            .edit-modal-header {
                border-radius: 12px 12px 0 0;
                padding: 1rem 1.5rem;
            }

            .edit-modal-header h2 {
                font-size: 1.25rem;
                margin-right: 2.5rem;
            }

            .edit-form-section {
                padding: 1rem;
                margin-bottom: 0.75rem;
            }

            .grid.grid-cols-2 {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .grid.grid-cols-3 {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .edit-form-input {
                font-size: 1rem;
                padding: 0.75rem;
            }

            .edit-modal-footer {
                padding: 1rem 1.5rem;
                flex-direction: column;
                gap: 0.75rem;
            }

            .edit-modal-footer button {
                width: 100%;
                padding: 0.75rem 1rem;
                font-size: 1rem;
            }
        }

        /* 删除确认模态框样式 */
        .delete-modal {
            backdrop-filter: blur(8px);
            animation: fadeIn 0.3s ease-out;
        }

        .delete-modal-content {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            animation: slideUp 0.3s ease-out;
            max-width: 400px;
        }

        .delete-modal-header {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border-radius: 16px 16px 0 0;
            padding: 1.5rem;
            text-align: center;
        }

        .delete-modal-icon {
            width: 4rem;
            height: 4rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }

        .delete-modal-body {
            padding: 2rem;
            text-align: center;
        }

        .delete-modal-footer {
            padding: 1.5rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        /* 多选功能样式 */
        .multi-select-toolbar {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .multi-select-toolbar.active {
            display: flex;
        }

        .multi-select-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .multi-select-actions {
            display: flex;
            gap: 0.5rem;
        }

        .checkbox-column {
            width: 40px;
            display: none;
        }

        .multi-select-mode .checkbox-column {
            display: block;
        }

        .entry-checkbox {
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 4px;
            border: 2px solid #d1d5db;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .entry-checkbox:checked {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #3b82f6;
        }

        .selected-row {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-left: 4px solid #3b82f6;
        }

        /* 排序下拉框样式 */
        .sort-dropdown {
            position: relative;
            display: inline-block;
        }

        .sort-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background: white;
            min-width: 200px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            z-index: 1000;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .sort-dropdown.active .sort-dropdown-content {
            display: block;
            animation: slideDown 0.2s ease-out;
        }

        .sort-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sort-option:hover {
            background: #f3f4f6;
        }

        .sort-option.active {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1d4ed8;
            font-weight: 600;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .journal-entry {
            transition: all 0.2s ease;
        }
        
        .journal-entry:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .ai-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .loading {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .sortable-header {
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
            position: relative;
        }

        .sortable-header:hover {
            background-color: rgba(59, 130, 246, 0.1);
            color: #2563eb;
        }

        .sort-icon {
            display: inline-block;
            margin-left: 4px;
            transition: transform 0.2s ease;
            opacity: 0.5;
        }

        .sortable-header.active .sort-icon {
            opacity: 1;
            color: #2563eb;
        }

        .sortable-header.desc .sort-icon {
            transform: rotate(180deg);
        }

        .sort-indicator {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #6b7280;
        }

        /* 表格换行样式 */
        .table-cell-wrap {
            word-wrap: break-word;
            word-break: break-word;
            hyphens: auto;
            line-height: 1.4;
            min-height: 2.5rem;
        }

        .description-cell {
            max-width: 200px;
            min-width: 150px;
        }

        .account-cell {
            max-width: 150px;
            min-width: 120px;
        }

        /* 确保表格行高度自适应 */
        .journal-entry td {
            vertical-align: top;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        /* 语言切换器样式 */
        .language-switcher {
            position: relative;
            display: inline-block;
        }

        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 150px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        }

        .language-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .language-option:hover {
            background-color: #f3f4f6;
        }

        .language-option.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 500;
        }

        .language-flag {
            width: 20px;
            height: 15px;
            border-radius: 2px;
            display: inline-block;
        }

        /* 国旗样式 */
        .flag-cn {
            background: linear-gradient(to bottom, #de2910 0%, #de2910 100%);
            position: relative;
        }

        .flag-jp {
            background: white;
            border: 1px solid #ddd;
            position: relative;
        }

        .flag-jp::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 语言切换器 -->
    <div class="fixed top-4 right-4 z-50 flex space-x-2">
        <button onclick="switchLanguage('ja')" class="lang-btn px-3 py-2 rounded-lg bg-white/80 text-sm font-medium active" id="lang-ja">
            🇯🇵 日本語
        </button>
        <button onclick="switchLanguage('zh')" class="lang-btn px-3 py-2 rounded-lg bg-white/80 text-sm font-medium" id="lang-zh">
            🇨🇳 中文
        </button>
        <button onclick="switchLanguage('en')" class="lang-btn px-3 py-2 rounded-lg bg-white/80 text-sm font-medium" id="lang-en">
            🇺🇸 English
        </button>
    </div>

    <!-- 主容器 -->
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- 页面标题 -->
        <div class="glass-card rounded-2xl p-8 mb-8 fade-in-up">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold text-gray-900 mb-2" data-i18n="page-title">仕訳帳</h1>
                    <p class="text-gray-600 text-lg" data-i18n="page-subtitle">会計記録の管理</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <div class="text-sm text-gray-500" data-i18n="total-entries">総記録数</div>
                        <div class="text-2xl font-bold text-gray-900" id="total-count">-</div>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                        <i class="fas fa-book text-white text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="glass-card rounded-2xl p-6 mb-8 fade-in-up">
            <div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                <div class="flex items-center space-x-4">
                    <button onclick="refreshEntries()" class="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-sync-alt"></i>
                        <span data-i18n="refresh">${translations[currentLanguage]['refresh']}</span>
                    </button>
                    <button onclick="window.location.href='/interactive_demo'" class="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-plus"></i>
                        <span data-i18n="add-entry">${translations[currentLanguage]['add-entry']}</span>
                    </button>
                    <button onclick="toggleMultiSelect()" id="multi-select-btn" class="flex items-center space-x-2 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-check-square"></i>
                        <span data-i18n="multi-select">${translations[currentLanguage]['multi-select']}</span>
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">
                        <span data-i18n="total-entries">${translations[currentLanguage]['total-entries']}</span>: <span id="total-count" class="font-semibold text-blue-600">0</span>
                    </div>

                    <!-- 排序下拉框 -->
                    <div class="sort-dropdown">
                        <button onclick="toggleSortDropdown()" class="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-sort"></i>
                            <span data-i18n="sort-by">${translations[currentLanguage]['sort-by']}</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="sort-dropdown-content">
                            <div class="sort-option active" onclick="sortEntries('date-desc')">
                                <i class="fas fa-calendar-alt"></i>
                                <span data-i18n="sort-date-desc">${translations[currentLanguage]['sort-date-desc']}</span>
                            </div>
                            <div class="sort-option" onclick="sortEntries('date-asc')">
                                <i class="fas fa-calendar-alt"></i>
                                <span data-i18n="sort-date-asc">${translations[currentLanguage]['sort-date-asc']}</span>
                            </div>
                            <div class="sort-option" onclick="sortEntries('amount-desc')">
                                <i class="fas fa-yen-sign"></i>
                                <span data-i18n="sort-amount-desc">${translations[currentLanguage]['sort-amount-desc']}</span>
                            </div>
                            <div class="sort-option" onclick="sortEntries('amount-asc')">
                                <i class="fas fa-yen-sign"></i>
                                <span data-i18n="sort-amount-asc">${translations[currentLanguage]['sort-amount-asc']}</span>
                            </div>
                            <div class="sort-option" onclick="sortEntries('description')">
                                <i class="fas fa-font"></i>
                                <span data-i18n="sort-description">${translations[currentLanguage]['sort-description']}</span>
                            </div>
                        </div>
                    </div>

                    <div class="relative">
                        <input type="text" id="search-input" placeholder="${translations[currentLanguage]['search-placeholder']}" class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 多选工具栏 -->
        <div id="multi-select-toolbar" class="multi-select-toolbar">
            <div class="multi-select-info">
                <span id="selected-count" class="font-semibold text-gray-700">已选择: 0项</span>
            </div>
            <div class="multi-select-actions">
                <button onclick="selectAllEntries()" class="px-3 py-1.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                    <i class="fas fa-check-double mr-1"></i>
                    <span data-i18n="select-all">${translations[currentLanguage]['select-all']}</span>
                </button>
                <button onclick="deselectAllEntries()" class="px-3 py-1.5 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm">
                    <i class="fas fa-times mr-1"></i>
                    <span data-i18n="deselect-all">${translations[currentLanguage]['deselect-all']}</span>
                </button>
                <button onclick="deleteSelectedEntries()" id="delete-selected-btn" class="px-3 py-1.5 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm" disabled>
                    <i class="fas fa-trash mr-1"></i>
                    <span data-i18n="delete-selected">${translations[currentLanguage]['delete-selected']}</span>
                </button>
                <button onclick="toggleMultiSelect()" class="px-3 py-1.5 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors text-sm">
                    <i class="fas fa-times mr-1"></i>
                    ${translations[currentLanguage]['cancel']}
                </button>
            </div>
        </div>

        <!-- 记录表格 -->
        <div class="modern-table fade-in-up">
            <div class="table-header p-4">
                <div class="grid gap-4 font-semibold" id="table-header-grid" style="grid-template-columns: 2fr 3fr 2fr 2fr 1fr 1fr 1fr;">
                    <div class="checkbox-column text-center">
                        <input type="checkbox" id="select-all-checkbox" class="entry-checkbox" onchange="toggleSelectAll()">
                    </div>
                    <div data-i18n="date-time">${translations[currentLanguage]['date-time']}</div>
                    <div data-i18n="description">${translations[currentLanguage]['description']}</div>
                    <div data-i18n="debit-account">${translations[currentLanguage]['debit-account']}</div>
                    <div data-i18n="credit-account">${translations[currentLanguage]['credit-account']}</div>
                    <div class="text-right" data-i18n="amount">${translations[currentLanguage]['amount']}</div>
                    <div class="text-center" data-i18n="attachment">${translations[currentLanguage]['attachment']}</div>
                    <div class="text-center" data-i18n="actions">${translations[currentLanguage]['actions']}</div>
                </div>
            </div>
            <div id="entries-container" class="divide-y divide-gray-100">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600 text-lg" data-i18n="loading">データを読み込み中...</p>
                </div>
            </div>
        </div>

        <!-- 移动端记录列表 -->
        <div class="mobile-entries fade-in-up">
            <div id="mobile-entries-container">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600 text-lg" data-i18n="loading">データを読み込み中...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allEntries = [];
        let currentLanguage = 'ja'; // 默认日语

        // 多语言翻译
        const translations = {
            ja: {
                'page-title': '仕訳帳',
                'page-subtitle': '会計記録の管理',
                'total-entries': '総記録数',
                'refresh': '更新',
                'add-entry': '新規記録',
                'search-placeholder': '検索...',
                'date-time': '日時',
                'description': '摘要',
                'debit-account': '借方科目',
                'credit-account': '貸方科目',
                'amount': '金額',
                'attachment': '添付',
                'actions': '操作',
                'loading': 'データを読み込み中...',
                'edit': '編集',
                'delete': '削除',
                'no-data': 'データがありません',
                'login-required': '未ログイン、ログインページに移動中...',
                'load-error': 'データの読み込みに失敗しました',
                // 编辑模态框翻译
                'edit-record': '記録を編集',
                'close': '閉じる',
                'date': '日付',
                'time': '時間',
                'description-label': '摘要',
                'debit-account-label': '借方科目',
                'credit-account-label': '貸方科目',
                'amount-label': '金額',
                'debit-tax-rate': '借方税率(%)',
                'credit-tax-rate': '貸方税率(%)',
                'reference-number': '参照番号',
                'attachment-management': '添付ファイル管理',
                'current-attachments': '現在の添付ファイル',
                'upload-new-attachment': '新しい添付ファイルをアップロード',
                'supported-formats': 'サポート形式: 画像、PDF、Word、Excel',
                'upload-attachment': '添付ファイルをアップロード',
                'preview': 'プレビュー',
                'cancel': 'キャンセル',
                'save-changes': '変更を保存',
                'select-debit-account': '借方科目を選択',
                'select-credit-account': '貸方科目を選択',
                'record-not-found': '記録が見つかりません',
                // 删除和排序功能翻译
                'delete-confirm': '削除の確認',
                'delete-confirm-message': 'この記録を削除してもよろしいですか？この操作は元に戻せません。',
                'delete-success': '記録が正常に削除されました',
                'delete-error': '記録の削除に失敗しました',
                'sort-by': '並び替え',
                'sort-date-asc': '日付（古い順）',
                'sort-date-desc': '日付（新しい順）',
                'sort-amount-asc': '金額（少ない順）',
                'sort-amount-desc': '金額（多い順）',
                'sort-description': '摘要（A-Z）',
                'multi-select': '複数選択',
                'select-all': 'すべて選択',
                'deselect-all': 'すべて解除',
                'delete-selected': '選択項目を削除',
                'selected-count': '選択済み: {count}件',
                'bulk-delete-confirm': '選択した{count}件の記録を削除してもよろしいですか？',
                'bulk-delete-success': '{count}件の記録が正常に削除されました'
            },
            zh: {
                'page-title': '会计分录',
                'page-subtitle': '会计记录管理',
                'total-entries': '总记录数',
                'refresh': '刷新',
                'add-entry': '新增记录',
                'search-placeholder': '搜索...',
                'date-time': '日期时间',
                'description': '摘要',
                'debit-account': '借方科目',
                'credit-account': '贷方科目',
                'amount': '金额',
                'attachment': '附件',
                'actions': '操作',
                'loading': '数据加载中...',
                'edit': '编辑',
                'delete': '删除',
                'no-data': '暂无数据',
                'login-required': '未登录，正在跳转到登录页...',
                'load-error': '数据加载失败',
                // 编辑模态框翻译
                'edit-record': '编辑记录',
                'close': '关闭',
                'date': '日期',
                'time': '时间',
                'description-label': '描述',
                'debit-account-label': '借方科目',
                'credit-account-label': '贷方科目',
                'amount-label': '金额',
                'debit-tax-rate': '借方税率(%)',
                'credit-tax-rate': '贷方税率(%)',
                'reference-number': '参考号码',
                'attachment-management': '附件管理',
                'current-attachments': '当前附件',
                'upload-new-attachment': '上传新附件',
                'supported-formats': '支持格式: 图片、PDF、Word、Excel',
                'upload-attachment': '上传附件',
                'preview': '预览',
                'cancel': '取消',
                'save-changes': '保存修改',
                'select-debit-account': '选择借方科目',
                'select-credit-account': '选择贷方科目',
                'record-not-found': '记录不存在',
                // 删除和排序功能翻译
                'delete-confirm': '删除确认',
                'delete-confirm-message': '确定要删除这条记录吗？此操作无法撤销。',
                'delete-success': '记录删除成功',
                'delete-error': '记录删除失败',
                'sort-by': '排序方式',
                'sort-date-asc': '日期（从早到晚）',
                'sort-date-desc': '日期（从晚到早）',
                'sort-amount-asc': '金额（从小到大）',
                'sort-amount-desc': '金额（从大到小）',
                'sort-description': '描述（A-Z）',
                'multi-select': '多选模式',
                'select-all': '全选',
                'deselect-all': '取消全选',
                'delete-selected': '删除选中项',
                'selected-count': '已选择: {count}项',
                'bulk-delete-confirm': '确定要删除选中的{count}条记录吗？',
                'bulk-delete-success': '成功删除{count}条记录'
            },
            en: {
                'page-title': 'Journal Entries',
                'page-subtitle': 'Accounting Records Management',
                'total-entries': 'Total Entries',
                'refresh': 'Refresh',
                'add-entry': 'Add Entry',
                'search-placeholder': 'Search...',
                'date-time': 'Date & Time',
                'description': 'Description',
                'debit-account': 'Debit Account',
                'credit-account': 'Credit Account',
                'amount': 'Amount',
                'attachment': 'Attachment',
                'actions': 'Actions',
                'loading': 'Loading data...',
                'edit': 'Edit',
                'delete': 'Delete',
                'no-data': 'No data available',
                'login-required': 'Not logged in, redirecting to login page...',
                'load-error': 'Failed to load data',
                // 编辑模态框翻译
                'edit-record': 'Edit Record',
                'close': 'Close',
                'date': 'Date',
                'time': 'Time',
                'description-label': 'Description',
                'debit-account-label': 'Debit Account',
                'credit-account-label': 'Credit Account',
                'amount-label': 'Amount',
                'debit-tax-rate': 'Debit Tax Rate (%)',
                'credit-tax-rate': 'Credit Tax Rate (%)',
                'reference-number': 'Reference Number',
                'attachment-management': 'Attachment Management',
                'current-attachments': 'Current Attachments',
                'upload-new-attachment': 'Upload New Attachment',
                'supported-formats': 'Supported formats: Images, PDF, Word, Excel',
                'upload-attachment': 'Upload Attachment',
                'preview': 'Preview',
                'cancel': 'Cancel',
                'save-changes': 'Save Changes',
                'select-debit-account': 'Select Debit Account',
                'select-credit-account': 'Select Credit Account',
                'record-not-found': 'Record not found',
                // 删除和排序功能翻译
                'delete-confirm': 'Delete Confirmation',
                'delete-confirm-message': 'Are you sure you want to delete this record? This action cannot be undone.',
                'delete-success': 'Record deleted successfully',
                'delete-error': 'Failed to delete record',
                'sort-by': 'Sort By',
                'sort-date-asc': 'Date (Oldest First)',
                'sort-date-desc': 'Date (Newest First)',
                'sort-amount-asc': 'Amount (Low to High)',
                'sort-amount-desc': 'Amount (High to Low)',
                'sort-description': 'Description (A-Z)',
                'multi-select': 'Multi-Select',
                'select-all': 'Select All',
                'deselect-all': 'Deselect All',
                'delete-selected': 'Delete Selected',
                'selected-count': 'Selected: {count} items',
                'bulk-delete-confirm': 'Are you sure you want to delete {count} selected records?',
                'bulk-delete-success': 'Successfully deleted {count} records'
            }
        };

        // 切换语言
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('preferred_language', lang);

            // 更新HTML lang属性
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : lang;

            // 更新所有翻译文本
            updateTranslations();

            // 更新语言按钮状态
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`lang-${lang}`).classList.add('active');

            // 重新渲染记录列表以应用新语言
            if (allEntries.length > 0) {
                renderEntries(allEntries);
            }
        }

        // 更新翻译文本
        function updateTranslations() {
            const elements = document.querySelectorAll('[data-i18n]');
            elements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (translations[currentLanguage] && translations[currentLanguage][key]) {
                    element.textContent = translations[currentLanguage][key];
                }
            });

            // 更新placeholder
            const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
            placeholderElements.forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (translations[currentLanguage] && translations[currentLanguage][key]) {
                    element.placeholder = translations[currentLanguage][key];
                }
            });
        }

        // 格式化日期时间
        function formatDateTime(dateStr, timeStr) {
            if (!dateStr) return '-';

            const date = new Date(dateStr + (timeStr ? `T${timeStr}` : ''));

            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            };

            // 根据语言设置不同的日期格式
            if (currentLanguage === 'ja') {
                return date.toLocaleDateString('ja-JP', options).replace(/\//g, '/') +
                       ' ' + date.toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit', hour12: false });
            } else if (currentLanguage === 'zh') {
                return date.toLocaleDateString('zh-CN', options) +
                       ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
            } else {
                return date.toLocaleDateString('en-US', options) +
                       ' ' + date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
            }
        }

        // 初始化语言设置
        function initializeLanguage() {
            const savedLang = localStorage.getItem('preferred_language') || 'ja';
            switchLanguage(savedLang);
        }

        // 检查认证状态
        async function checkAuthStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (!token) {
                document.getElementById('entries-container').innerHTML =
                    '<div class="text-red-500 text-center py-8">未登录，正在跳转到登录页...</div>';
                setTimeout(() => {
                    window.location.href = '/login.html';
                }, 2000);
                return false;
            }

            // 测试token有效性
            try {
                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/journal-entries?company_id=default&limit=1', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401) {
                    localStorage.removeItem('goldenledger_session_token');
                    document.getElementById('entries-container').innerHTML =
                        '<div class="text-red-500 text-center py-8">登录已过期，正在跳转到登录页...</div>';
                    setTimeout(() => {
                        window.location.href = '/login.html';
                    }, 2000);
                    return false;
                }

                return true;
            } catch (error) {
                console.error('认证检查失败:', error);
                return true; // 网络错误时继续尝试加载
            }
        }

        // 页面加载时获取记录 - 已移动到页面底部的统一初始化函数中

        // 加载记录列表
        async function loadJournalEntries() {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    document.getElementById('entries-container').innerHTML =
                        '<div class="text-red-500 text-center py-8">请先登录</div>';
                    return;
                }

                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/journal-entries?company_id=default', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status === 401) {
                    // 认证失败，清除token并跳转到登录页
                    localStorage.removeItem('goldenledger_session_token');
                    const loginErrorHtml = `
                        <div class="text-center py-16">
                            <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-exclamation-triangle text-red-500 text-3xl"></i>
                            </div>
                            <p class="text-red-600 text-lg" data-i18n="login-required">${translations[currentLanguage]['login-required']}</p>
                        </div>
                    `;
                    document.getElementById('entries-container').innerHTML = loginErrorHtml;
                    document.getElementById('mobile-entries-container').innerHTML = loginErrorHtml;
                    setTimeout(() => {
                        window.location.href = '/login.html';
                    }, 2000);
                    return;
                } else if (!response.ok) {
                    throw new Error(translations[currentLanguage]['load-error']);
                }

                const result = await response.json();
                const entries = result.success ? result.data : [];
                allEntries = entries;
                displayEntries(entries);
            } catch (error) {
                console.error('加载记录失败:', error);
                const errorHtml = `
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-exclamation-circle text-red-500 text-3xl"></i>
                        </div>
                        <p class="text-red-600 text-lg">${translations[currentLanguage]['load-error']}: ${error.message}</p>
                        <button onclick="refreshEntries()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            ${translations[currentLanguage]['refresh']}
                        </button>
                    </div>
                `;
                document.getElementById('entries-container').innerHTML = errorHtml;
                document.getElementById('mobile-entries-container').innerHTML = errorHtml;
            }
        }

        // 显示记录列表
        function displayEntries(entries) {
            const container = document.getElementById('entries-container');
            const mobileContainer = document.getElementById('mobile-entries-container');

            // 更新总记录数
            document.getElementById('total-count').textContent = entries.length;

            if (entries.length === 0) {
                const emptyHtml = `
                    <div class="text-center py-16">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-inbox text-gray-400 text-3xl"></i>
                        </div>
                        <p class="text-gray-500 text-lg" data-i18n="no-data">${translations[currentLanguage]['no-data']}</p>
                    </div>
                `;
                container.innerHTML = emptyHtml;
                mobileContainer.innerHTML = emptyHtml;
                return;
            }

            // 渲染桌面版
            renderDesktopEntries(entries, container);

            // 渲染移动版
            renderMobileEntries(entries, mobileContainer);
        }

        // 渲染桌面版记录
        function renderDesktopEntries(entries, container) {

            let html = '';
            entries.forEach((entry, index) => {
                // 检查多个可能的附件字段
                const hasAttachment = entry.attachment_path || entry.attachment_file_name || entry.attachment_id;
                const attachmentIcon = hasAttachment ?
                    '<i class="fas fa-paperclip text-green-500"></i>' :
                    '<i class="fas fa-minus text-gray-300"></i>';

                // 格式化金额
                const formattedAmount = new Intl.NumberFormat(
                    currentLanguage === 'ja' ? 'ja-JP' :
                    currentLanguage === 'zh' ? 'zh-CN' : 'en-US',
                    { style: 'currency', currency: 'JPY' }
                ).format(entry.amount || 0);

                html += `
                    <div class="table-row p-4 grid gap-4 items-center hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-200" data-entry-id="${entry.id}" style="animation-delay: ${index * 0.1}s; grid-template-columns: 2fr 3fr 2fr 2fr 1fr 1fr 1fr;">
                        <div class="checkbox-column text-center">
                            <input type="checkbox" class="entry-checkbox" data-entry-id="${entry.id}" onchange="toggleEntrySelection('${entry.id}', this.checked)">
                        </div>
                        <div>
                            <div class="flex items-center space-x-2">
                                <div class="status-indicator status-success"></div>
                                <div>
                                    <div class="font-medium text-gray-900 text-sm">
                                        ${formatDateTime(entry.entry_date, entry.entry_time)}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        ${entry.reference_number || ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900 text-sm leading-relaxed">
                                ${entry.description || ''}
                            </div>
                        </div>
                        <div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                ${entry.debit_account || ''}
                            </span>
                        </div>
                        <div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                ${entry.credit_account || ''}
                            </span>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-900">
                                ${formattedAmount}
                            </div>
                        </div>
                        <div class="text-center">
                            ${attachmentIcon}
                        </div>
                        <div class="text-center">
                            <div class="flex items-center justify-center space-x-2">
                                <button onclick="editEntry('${entry.id}')"
                                        class="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                                        title="${translations[currentLanguage]['edit']}">
                                    <i class="fas fa-edit text-sm"></i>
                                </button>
                                <button onclick="deleteEntry('${entry.id}')"
                                        class="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                                        title="${translations[currentLanguage]['delete']}">
                                    <i class="fas fa-trash text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 渲染移动版记录
        function renderMobileEntries(entries, container) {
            let html = '';
            entries.forEach((entry, index) => {
                // 检查多个可能的附件字段
                const hasAttachment = entry.attachment_path || entry.attachment_file_name || entry.attachment_id;
                const attachmentIcon = hasAttachment ?
                    '<i class="fas fa-paperclip text-green-500 mr-1"></i>' :
                    '<i class="fas fa-minus text-gray-300 mr-1"></i>';

                // 格式化金额
                const formattedAmount = new Intl.NumberFormat(
                    currentLanguage === 'ja' ? 'ja-JP' :
                    currentLanguage === 'zh' ? 'zh-CN' : 'en-US',
                    { style: 'currency', currency: 'JPY' }
                ).format(entry.amount || 0);

                html += `
                    <div class="mobile-entry-card" data-entry-id="${entry.id}" style="animation-delay: ${index * 0.1}s">
                        <div class="mobile-entry-header">
                            <div class="flex items-start space-x-3">
                                <div class="checkbox-column">
                                    <input type="checkbox" class="entry-checkbox" data-entry-id="${entry.id}" onchange="toggleEntrySelection('${entry.id}', this.checked)">
                                </div>
                                <div>
                                    <div class="mobile-entry-date">
                                        ${formatDateTime(entry.entry_date, entry.entry_time)}
                                    </div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        ${entry.reference_number || ''}
                                    </div>
                                </div>
                            </div>
                            <div class="mobile-entry-amount">
                                ${formattedAmount}
                            </div>
                        </div>

                        <div class="mobile-entry-description">
                            ${entry.description || ''}
                        </div>

                        <div class="mobile-entry-accounts">
                            <span class="mobile-account-tag mobile-debit">
                                借: ${entry.debit_account || ''}
                            </span>
                            <span class="mobile-account-tag mobile-credit">
                                贷: ${entry.credit_account || ''}
                            </span>
                        </div>

                        <div class="mobile-entry-footer">
                            <div class="mobile-attachment-indicator">
                                ${attachmentIcon}
                                <span>${hasAttachment ? translations[currentLanguage]['attachment'] : ''}</span>
                            </div>
                            <div class="mobile-actions">
                                <button onclick="editEntry('${entry.id}')"
                                        class="mobile-action-btn mobile-edit-btn"
                                        title="${translations[currentLanguage]['edit']}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteEntry('${entry.id}')"
                                        class="mobile-action-btn mobile-delete-btn"
                                        title="${translations[currentLanguage]['delete']}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 刷新记录
        function refreshEntries() {
            loadJournalEntries();
        }

        // 编辑记录
        async function editEntry(entryId) {
            try {
                const entry = allEntries.find(e => e.id === entryId);
                if (!entry) {
                    alert(translations[currentLanguage]['record-not-found']);
                    return;
                }

                // 创建编辑模态框
                const modal = document.createElement('div');
                modal.className = 'edit-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="edit-modal-content max-w-4xl w-full mx-4">
                        <div class="edit-modal-header p-6">
                            <h2 class="text-2xl font-bold">${translations[currentLanguage]['edit-record']}</h2>
                            <button class="edit-modal-close" onclick="closeEditModal()" title="${translations[currentLanguage]['close']}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="p-6 space-y-6">
                            <!-- 基本信息 -->
                            <div class="edit-form-section">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                                    ${translations[currentLanguage]['page-subtitle']}
                                </h3>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['date']}</label>
                                        <input type="date" id="edit-date" value="${entry.entry_date || ''}"
                                               class="edit-form-input w-full px-4 py-3">
                                    </div>
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['time']}</label>
                                        <input type="time" id="edit-time" value="${entry.entry_time || ''}"
                                               class="edit-form-input w-full px-4 py-3">
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <label class="edit-form-label">${translations[currentLanguage]['description-label']}</label>
                                    <input type="text" id="edit-description" value="${entry.description || ''}"
                                           class="edit-form-input w-full px-4 py-3">
                                </div>
                            </div>

                            <!-- 会计科目 -->
                            <div class="edit-form-section">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-balance-scale mr-2 text-green-500"></i>
                                    ${translations[currentLanguage]['debit-account']} & ${translations[currentLanguage]['credit-account']}
                                </h3>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['debit-account-label']}</label>
                                        <select id="edit-debit" class="edit-form-input w-full px-4 py-3">
                                            <option value="">${translations[currentLanguage]['select-debit-account']}</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['credit-account-label']}</label>
                                        <select id="edit-credit" class="edit-form-input w-full px-4 py-3">
                                            <option value="">${translations[currentLanguage]['select-credit-account']}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 金额和税率 -->
                            <div class="edit-form-section">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-yen-sign mr-2 text-yellow-500"></i>
                                    ${translations[currentLanguage]['amount-label']}
                                </h3>
                                <div class="grid grid-cols-3 gap-4">
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['amount-label']}</label>
                                        <input type="number" id="edit-amount" value="${entry.amount || ''}" step="0.01"
                                               class="edit-form-input w-full px-4 py-3">
                                    </div>
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['debit-tax-rate']}</label>
                                        <input type="number" id="edit-debit-tax-rate" value="${entry.debit_tax_rate || 0}" step="0.01"
                                               class="edit-form-input w-full px-4 py-3">
                                    </div>
                                    <div>
                                        <label class="edit-form-label">${translations[currentLanguage]['credit-tax-rate']}</label>
                                        <input type="number" id="edit-credit-tax-rate" value="${entry.credit_tax_rate || 0}" step="0.01"
                                               class="edit-form-input w-full px-4 py-3">
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <label class="edit-form-label">${translations[currentLanguage]['reference-number']}</label>
                                    <input type="text" id="edit-reference" value="${entry.reference_number || ''}"
                                           class="edit-form-input w-full px-4 py-3">
                                </div>
                            </div>

                            <!-- 附件管理 -->
                            <div class="edit-form-section">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-paperclip mr-2 text-purple-500"></i>
                                    ${translations[currentLanguage]['attachment-management']}
                                </h3>

                                <!-- 当前附件显示 -->
                                <div id="current-attachments" class="mb-6">
                                    <div class="flex items-center justify-center py-8 text-gray-500">
                                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-2"></div>
                                        ${translations[currentLanguage]['loading']}
                                    </div>
                                </div>

                                <!-- 上传新附件 -->
                                <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border-2 border-dashed border-green-300">
                                    <div class="text-center mb-4">
                                        <i class="fas fa-cloud-upload-alt text-3xl text-green-500 mb-2"></i>
                                        <h4 class="font-semibold text-gray-800">${translations[currentLanguage]['upload-new-attachment']}</h4>
                                    </div>

                                    <div class="mb-4">
                                        <input type="file" id="edit-attachment-file"
                                               accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
                                               class="edit-form-input w-full px-4 py-3">
                                        <p class="text-sm text-gray-600 mt-2 text-center">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            ${translations[currentLanguage]['supported-formats']}
                                        </p>
                                    </div>

                                    <button type="button" onclick="uploadEditAttachment('${entryId}')"
                                            class="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg hover:from-green-600 hover:to-blue-600 disabled:from-gray-400 disabled:to-gray-400 transition-all duration-200 font-semibold">
                                        <i class="fas fa-upload mr-2"></i>
                                        ${translations[currentLanguage]['upload-attachment']}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="edit-modal-footer flex justify-end space-x-4 p-6 bg-gradient-to-r from-gray-50 to-gray-100 border-t">
                            <button onclick="closeEditModal()"
                                    class="px-6 py-3 text-gray-700 bg-white border-2 border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-semibold">
                                <i class="fas fa-times mr-2"></i>
                                ${translations[currentLanguage]['cancel']}
                            </button>
                            <button onclick="saveEditedEntry('${entryId}')" id="save-edit-btn"
                                    class="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 font-semibold shadow-lg">
                                <i class="fas fa-save mr-2"></i>
                                ${translations[currentLanguage]['save-changes']}
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // 添加键盘事件监听
                const handleKeyPress = (e) => {
                    if (e.key === 'Escape') {
                        closeEditModal();
                    }
                };
                document.addEventListener('keydown', handleKeyPress);

                // 存储事件监听器以便清理
                modal.keydownHandler = handleKeyPress;

                // 加载科目选项
                await loadAccountOptions(entry);

                // 加载当前附件
                loadCurrentAttachments(entryId);

            } catch (error) {
                console.error('编辑记录失败:', error);
                alert('编辑记录失败: ' + error.message);
            }
        }

        // 关闭编辑模态框
        function closeEditModal() {
            const modal = document.querySelector('.edit-modal');
            if (modal) {
                // 清理键盘事件监听器
                if (modal.keydownHandler) {
                    document.removeEventListener('keydown', modal.keydownHandler);
                }

                // 添加关闭动画
                modal.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // 加载科目选项
        async function loadAccountOptions(entry) {
            try {
                // 获取科目列表 - 使用预定义的科目列表，按类别分组
                const accounts = {
                    '資産': ['現金', '普通預金', '当座預金', '売掛金', '商品', '建物', '土地', '車両運搬具', '工具器具備品'],
                    '負債': ['買掛金', '未払金', '借入金', '預り金'],
                    '純資産': ['資本金', '利益剰余金'],
                    '収益': ['売上', '受取利息', '雑収入'],
                    '費用': ['仕入', '給料', '地代家賃', '水道光熱費', '通信費', '消耗品費', '雑費', '支払利息', '雑損失', '法人税等']
                };

                // 获取下拉框元素
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (!debitSelect || !creditSelect) {
                    console.error('科目選択ボックスが見つかりません');
                    return;
                }

                // 清空现有选项
                debitSelect.innerHTML = '<option value="">借方科目を選択</option>';
                creditSelect.innerHTML = '<option value="">貸方科目を選択</option>';

                // 添加所有科目选项
                Object.keys(accounts).forEach(category => {
                    // 为每个类别创建选项组
                    const debitOptgroup = document.createElement('optgroup');
                    debitOptgroup.label = category;
                    const creditOptgroup = document.createElement('optgroup');
                    creditOptgroup.label = category;

                    // 确保 accounts[category] 是数组
                    if (Array.isArray(accounts[category])) {
                        accounts[category].forEach(account => {
                            // 借方科目选项
                            const debitOption = document.createElement('option');
                            debitOption.value = account;
                            debitOption.textContent = account;
                            if (entry.debit_account === account) {
                                debitOption.selected = true;
                            }
                            debitOptgroup.appendChild(debitOption);

                            // 贷方科目选项
                            const creditOption = document.createElement('option');
                            creditOption.value = account;
                            creditOption.textContent = account;
                            if (entry.credit_account === account) {
                                creditOption.selected = true;
                            }
                            creditOptgroup.appendChild(creditOption);
                        });
                    }

                    debitSelect.appendChild(debitOptgroup);
                    creditSelect.appendChild(creditOptgroup);
                });

            } catch (error) {
                console.error('加载科目选项失败:', error);
                // 如果加载失败，回退到文本输入
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (debitSelect) {
                    debitSelect.outerHTML = `<input type="text" id="edit-debit" value="${entry.debit_account || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">`;
                }
                if (creditSelect) {
                    creditSelect.outerHTML = `<input type="text" id="edit-credit" value="${entry.credit_account || ''}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">`;
                }
            }
        }

        // 关闭编辑模态框
        function closeEditModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black');
            if (modal) {
                modal.remove();
            }
        }

        // 保存编辑的记录
        async function saveEditedEntry(entryId) {
            const saveBtn = document.getElementById('save-edit-btn');
            const originalText = saveBtn ? saveBtn.textContent : '保存修改';

            try {
                // 禁用保存按钮，显示加载状态
                if (saveBtn) {
                    saveBtn.disabled = true;
                    saveBtn.textContent = '保存中...';
                }

                // 获取表单数据
                const formData = {
                    id: entryId,
                    entry_date: document.getElementById('edit-date').value,
                    entry_time: document.getElementById('edit-time').value,
                    description: document.getElementById('edit-description').value,
                    debit_account: document.getElementById('edit-debit').value,
                    credit_account: document.getElementById('edit-credit').value,
                    debit_tax_rate: parseFloat(document.getElementById('edit-debit-tax-rate').value) || 0,
                    credit_tax_rate: parseFloat(document.getElementById('edit-credit-tax-rate').value) || 0,
                    amount: parseFloat(document.getElementById('edit-amount').value),
                    reference_number: document.getElementById('edit-reference').value
                };

                console.log('准备保存的数据:', formData);

                // 验证必填字段
                if (!formData.description || !formData.debit_account || !formData.credit_account || !formData.amount) {
                    alert('请填写所有必填字段');
                    return;
                }

                // 发送更新请求
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                console.log('发送PUT请求到:', `https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${entryId}`);
                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${entryId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(formData)
                });

                console.log('服务器响应状态:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('服务器错误响应:', errorText);
                    throw new Error(`更新记录失败: ${response.status} ${errorText}`);
                }

                const result = await response.json();
                console.log('服务器响应结果:', result);

                if (!result.success) {
                    throw new Error(result.error || '更新记录失败');
                }

                // 关闭模态框
                closeEditModal();

                // 重新加载数据
                await loadJournalEntries();

                alert('记录更新成功！');

            } catch (error) {
                console.error('保存编辑失败:', error);
                alert('保存编辑失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.textContent = originalText;
                }
            }
        }

        // 删除记录
        async function deleteEntry(entryId) {
            showDeleteConfirmModal([entryId], false);
        }

        // 显示删除确认模态框
        function showDeleteConfirmModal(entryIds, isBulk = false) {
            const count = entryIds.length;
            const message = isBulk ?
                translations[currentLanguage]['bulk-delete-confirm'].replace('{count}', count) :
                translations[currentLanguage]['delete-confirm-message'];

            const modal = document.createElement('div');
            modal.className = 'delete-modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="delete-modal-content w-full mx-4">
                    <div class="delete-modal-header">
                        <div class="delete-modal-icon">
                            <i class="fas fa-exclamation-triangle text-3xl"></i>
                        </div>
                        <h2 class="text-xl font-bold">${translations[currentLanguage]['delete-confirm']}</h2>
                    </div>
                    <div class="delete-modal-body">
                        <p class="text-gray-700 text-lg leading-relaxed">${message}</p>
                        ${isBulk ? `<div class="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
                            <p class="text-red-700 text-sm font-medium">
                                <i class="fas fa-info-circle mr-2"></i>
                                ${translations[currentLanguage]['bulk-delete-confirm'].replace('{count}', count)}
                            </p>
                        </div>` : ''}
                    </div>
                    <div class="delete-modal-footer">
                        <button onclick="closeDeleteModal()" class="px-6 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium">
                            <i class="fas fa-times mr-2"></i>
                            ${translations[currentLanguage]['cancel']}
                        </button>
                        <button onclick="confirmDelete(['${entryIds.join("','")}'], ${isBulk})" class="px-6 py-2.5 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium">
                            <i class="fas fa-trash mr-2"></i>
                            ${translations[currentLanguage]['delete']}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 添加键盘事件监听
            const handleKeyPress = (e) => {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                }
            };
            document.addEventListener('keydown', handleKeyPress);
            modal.keydownHandler = handleKeyPress;
        }

        // 关闭删除确认模态框
        function closeDeleteModal() {
            const modal = document.querySelector('.delete-modal');
            if (modal) {
                if (modal.keydownHandler) {
                    document.removeEventListener('keydown', modal.keydownHandler);
                }
                modal.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // 确认删除
        async function confirmDelete(entryIds, isBulk = false) {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    alert(translations[currentLanguage]['login-required']);
                    return;
                }

                // 显示加载状态
                const deleteBtn = document.querySelector('.delete-modal button[onclick*="confirmDelete"]');
                if (deleteBtn) {
                    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>删除中...';
                    deleteBtn.disabled = true;
                }

                // 删除记录
                for (const entryId of entryIds) {
                    const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${entryId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(translations[currentLanguage]['delete-error']);
                    }

                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || translations[currentLanguage]['delete-error']);
                    }
                }

                // 关闭模态框
                closeDeleteModal();

                // 重新加载数据
                await loadJournalEntries();

                // 显示成功消息
                const successMessage = isBulk ?
                    translations[currentLanguage]['bulk-delete-success'].replace('{count}', entryIds.length) :
                    translations[currentLanguage]['delete-success'];

                showSuccessToast(successMessage);

                // 如果是批量删除，退出多选模式
                if (isBulk) {
                    toggleMultiSelect();
                }

            } catch (error) {
                console.error('删除记录失败:', error);
                closeDeleteModal();
                showErrorToast(translations[currentLanguage]['delete-error'] + ': ' + error.message);
            }
        }

        // 获取认证token
        function getAuthToken() {
            return localStorage.getItem('authToken') ||
                   sessionStorage.getItem('authToken') ||
                   localStorage.getItem('goldenledger_session_token');
        }

        // 加载当前附件
        async function loadCurrentAttachments(entryId) {
            try {
                const token = getAuthToken();
                if (!token) {
                    console.warn('認証トークンが見つかりません');
                    const attachmentsContainer = document.getElementById('current-attachments');
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-red-500">
                            认证token不存在，请重新登录
                            <br>
                            <a href="index.html" class="text-blue-600 hover:text-blue-800 underline">点击登录</a>
                        </div>
                    `;
                    return;
                }

                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/attachments/${entryId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                const attachmentsContainer = document.getElementById('current-attachments');

                if (response.status === 401) {
                    // 认证失败，清除token并提示重新登录
                    localStorage.removeItem('goldenledger_session_token');
                    localStorage.removeItem('authToken');
                    sessionStorage.removeItem('authToken');
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-red-500">
                            登录已过期，请重新登录
                            <br>
                            <a href="index.html" class="text-blue-600 hover:text-blue-800 underline">点击重新登录</a>
                        </div>
                    `;
                    return;
                } else if (response.ok) {
                    // 检查响应内容类型
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        console.warn('添付ファイルAPIが非JSON応答を返しました。認証の問題の可能性があります');
                        attachmentsContainer.innerHTML = `
                            <div class="text-sm text-gray-500 italic">
                                暂无附件
                            </div>
                        `;
                        return;
                    }
                    
                    const result = await response.json();
                    const attachments = result.success ? result.data : [];

                    if (attachments && attachments.length > 0) {
                        attachmentsContainer.innerHTML = `
                            <div class="bg-gray-50 rounded-lg p-3">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">
                                    当前附件
                                </h4>
                                <div class="space-y-2">
                                    ${attachments.map(attachment => `
                                        <div class="flex items-center justify-between bg-white p-2 rounded border">
                                            <div class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700">${attachment.file_name || '附件文件'}</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <button onclick="previewAttachment('${entryId}')"
                                                        class="text-blue-600 hover:text-blue-800 text-sm">
                                                    预览
                                                </button>
                                                <button onclick="deleteAttachment('${entryId}', '${attachment.file_name}')"
                                                        class="text-red-600 hover:text-red-800 text-sm">
                                                    删除
                                                </button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        attachmentsContainer.innerHTML = `
                            <div class="text-sm text-gray-500 italic">
                                暂无附件
                            </div>
                        `;
                    }
                } else {
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-gray-500 italic">
                            暂无附件
                        </div>
                    `;
                }

                // 不需要多语言翻译，直接使用中文文本

            } catch (error) {
                console.error('添付ファイルの読み込みに失敗:', error);
                const attachmentsContainer = document.getElementById('current-attachments');
                if (attachmentsContainer) {
                    attachmentsContainer.innerHTML = `
                        <div class="text-sm text-red-500">
                            添付ファイルの読み込みに失敗: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // 上传编辑附件
        async function uploadEditAttachment(entryId) {
            try {
                const fileInput = document.getElementById('edit-attachment-file');
                const file = fileInput.files[0];

                if (!file) {
                    alert('请选择要上传的文件');
                    return;
                }

                // 检查文件大小 (10MB限制)
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    return;
                }

                const formData = new FormData();
                formData.append('file', file);
                formData.append('entryId', entryId);
                formData.append('companyId', 'default');

                // 显示上传进度
                const uploadButton = document.querySelector('button[onclick*="uploadEditAttachment"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = 'アップロード中...';

                const token = getAuthToken();
                if (!token) {
                    throw new Error('请先登录');
                }

                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/attachments/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('上传失败');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '上传失败');
                }

                // 清空文件输入
                fileInput.value = '';

                // 重新加载附件列表
                await loadCurrentAttachments(entryId);

                alert('附件上传成功！');

            } catch (error) {
                console.error('上传附件失败:', error);
                alert('上传附件失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                const uploadButton = document.querySelector('button[onclick*="uploadEditAttachment"]');
                if (uploadButton) {
                    uploadButton.disabled = false;
                    uploadButton.textContent = '上传附件';
                }
            }
        }

        // 预览附件
        async function previewAttachment(entryId) {
            try {
                const token = getAuthToken();
                if (!token) {
                    alert('请先登录');
                    return;
                }

                // 使用新的预览API端点
                const previewUrl = `https://goldenledger-api.souyousann.workers.dev/api/attachments/${entryId}/preview`;

                // 创建一个带认证头的请求来获取文件
                const response = await fetch(previewUrl, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    // 获取文件内容和类型
                    const blob = await response.blob();
                    const contentType = response.headers.get('Content-Type');

                    // 创建临时URL
                    const blobUrl = URL.createObjectURL(blob);

                    // 在新窗口中打开
                    const newWindow = window.open(blobUrl, '_blank');

                    // 清理临时URL（延迟清理，确保窗口已加载）
                    setTimeout(() => {
                        URL.revokeObjectURL(blobUrl);
                    }, 1000);

                } else {
                    const error = await response.json();
                    alert('预览失败: ' + (error.error || '未知错误'));
                }

            } catch (error) {
                console.error('预览附件失败:', error);
                alert('预览附件失败: ' + error.message);
            }
        }

        // 多选功能
        let isMultiSelectMode = false;
        let selectedEntries = new Set();
        let currentSortBy = 'date-desc';

        // 切换多选模式
        function toggleMultiSelect() {
            isMultiSelectMode = !isMultiSelectMode;
            const toolbar = document.getElementById('multi-select-toolbar');
            const tableContainer = document.querySelector('.modern-table');
            const mobileContainer = document.querySelector('.mobile-entries');
            const btn = document.getElementById('multi-select-btn');

            if (isMultiSelectMode) {
                toolbar.classList.add('active');
                tableContainer.classList.add('multi-select-mode');
                if (mobileContainer) mobileContainer.classList.add('multi-select-mode');
                btn.innerHTML = '<i class="fas fa-times mr-2"></i>' + translations[currentLanguage]['cancel'];
                btn.className = btn.className.replace('bg-purple-500 hover:bg-purple-600', 'bg-gray-500 hover:bg-gray-600');
            } else {
                toolbar.classList.remove('active');
                tableContainer.classList.remove('multi-select-mode');
                if (mobileContainer) mobileContainer.classList.remove('multi-select-mode');
                btn.innerHTML = '<i class="fas fa-check-square mr-2"></i>' + translations[currentLanguage]['multi-select'];
                btn.className = btn.className.replace('bg-gray-500 hover:bg-gray-600', 'bg-purple-500 hover:bg-purple-600');
                selectedEntries.clear();
                updateSelectedCount();

                // 取消所有选中状态
                document.querySelectorAll('.entry-checkbox').forEach(cb => cb.checked = false);
                document.querySelectorAll('.selected-row').forEach(row => row.classList.remove('selected-row'));
            }
        }

        // 切换排序下拉框
        function toggleSortDropdown() {
            const dropdown = document.querySelector('.sort-dropdown');
            dropdown.classList.toggle('active');

            // 点击外部关闭下拉框
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdown.contains(e.target)) {
                    dropdown.classList.remove('active');
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }

        // 排序记录
        function sortEntries(sortBy) {
            currentSortBy = sortBy;

            // 更新排序选项的激活状态
            document.querySelectorAll('.sort-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="sortEntries('${sortBy}')"]`).classList.add('active');

            // 关闭下拉框
            document.querySelector('.sort-dropdown').classList.remove('active');

            // 排序数据
            const sortedEntries = [...allEntries].sort((a, b) => {
                switch (sortBy) {
                    case 'date-asc':
                        return new Date(a.entry_date + ' ' + (a.entry_time || '00:00')) - new Date(b.entry_date + ' ' + (b.entry_time || '00:00'));
                    case 'date-desc':
                        return new Date(b.entry_date + ' ' + (b.entry_time || '00:00')) - new Date(a.entry_date + ' ' + (a.entry_time || '00:00'));
                    case 'amount-asc':
                        return (a.amount || 0) - (b.amount || 0);
                    case 'amount-desc':
                        return (b.amount || 0) - (a.amount || 0);
                    case 'description':
                        return (a.description || '').localeCompare(b.description || '');
                    default:
                        return 0;
                }
            });

            displayEntries(sortedEntries);
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const entryCheckboxes = document.querySelectorAll('.entry-checkbox:not(#select-all-checkbox)');

            entryCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                toggleEntrySelection(checkbox.dataset.entryId, selectAllCheckbox.checked);
            });
        }

        // 选择所有记录
        function selectAllEntries() {
            allEntries.forEach(entry => selectedEntries.add(entry.id));
            document.querySelectorAll('.entry-checkbox:not(#select-all-checkbox)').forEach(cb => cb.checked = true);
            document.querySelectorAll('.table-row, .mobile-entry-card').forEach(row => row.classList.add('selected-row'));
            document.getElementById('select-all-checkbox').checked = true;
            updateSelectedCount();
        }

        // 取消选择所有记录
        function deselectAllEntries() {
            selectedEntries.clear();
            document.querySelectorAll('.entry-checkbox').forEach(cb => cb.checked = false);
            document.querySelectorAll('.selected-row').forEach(row => row.classList.remove('selected-row'));
            updateSelectedCount();
        }

        // 切换记录选择状态
        function toggleEntrySelection(entryId, isSelected) {
            if (isSelected) {
                selectedEntries.add(entryId);
            } else {
                selectedEntries.delete(entryId);
            }

            // 更新行样式
            const row = document.querySelector(`[data-entry-id="${entryId}"]`);
            if (row) {
                if (isSelected) {
                    row.classList.add('selected-row');
                } else {
                    row.classList.remove('selected-row');
                }
            }

            updateSelectedCount();
        }

        // 更新选中计数
        function updateSelectedCount() {
            const count = selectedEntries.size;
            const countElement = document.getElementById('selected-count');
            const deleteBtn = document.getElementById('delete-selected-btn');

            countElement.textContent = translations[currentLanguage]['selected-count'].replace('{count}', count);
            deleteBtn.disabled = count === 0;

            // 更新全选复选框状态
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const totalEntries = allEntries.length;

            if (count === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (count === totalEntries) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 删除选中的记录
        function deleteSelectedEntries() {
            if (selectedEntries.size === 0) return;
            showDeleteConfirmModal([...selectedEntries], true);
        }

        // 显示成功提示
        function showSuccessToast(message) {
            showToast(message, 'success');
        }

        // 显示错误提示
        function showErrorToast(message) {
            showToast(message, 'error');
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transform translate-x-full transition-transform duration-300`;

            if (type === 'success') {
                toast.className += ' bg-green-500';
                toast.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            } else if (type === 'error') {
                toast.className += ' bg-red-500';
                toast.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
            } else {
                toast.className += ' bg-blue-500';
                toast.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
            }

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(full)';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }

        // 删除附件
        async function deleteAttachment(entryId, filename) {
            try {
                if (!confirm(`确定要删除附件 "${filename}" 吗？此操作无法撤销。`)) {
                    return;
                }

                const token = getAuthToken();
                if (!token) {
                    throw new Error('请先登录');
                }

                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/attachments/${entryId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ filename: filename })
                });

                if (!response.ok) {
                    throw new Error('删除附件失败');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '删除附件失败');
                }

                // 重新加载附件列表
                await loadCurrentAttachments(entryId);

                alert('附件删除成功！');

            } catch (error) {
                console.error('删除附件失败:', error);
                alert('删除附件失败: ' + error.message);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 初始化语言设置
            initializeLanguage();

            // 检查认证状态并加载记录数据
            const isAuthenticated = await checkAuthStatus();
            if (isAuthenticated) {
                loadJournalEntries();
            }

            // 添加搜索功能
            const searchInput = document.getElementById('search-input');
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                if (searchTerm === '') {
                    displayEntries(allEntries);
                } else {
                    const filteredEntries = allEntries.filter(entry =>
                        (entry.description || '').toLowerCase().includes(searchTerm) ||
                        (entry.debit_account || '').toLowerCase().includes(searchTerm) ||
                        (entry.credit_account || '').toLowerCase().includes(searchTerm) ||
                        (entry.reference_number || '').toLowerCase().includes(searchTerm)
                    );
                    displayEntries(filteredEntries);
                }
            });
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
测试附件管理功能
"""
import requests
import json
import os
from pathlib import Path

def test_attachment_management():
    """测试附件管理功能"""
    
    print("🔗 测试附件管理功能")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 获取记录列表
        print("\n📋 步骤1: 获取记录列表")
        response = requests.get(f"{base_url}/journal-entries/default")
        entries = response.json()
        print(f"✅ 获取到 {len(entries)} 条记录")
        
        if len(entries) == 0:
            print("❌ 没有记录可以测试")
            return False
        
        # 选择第一条记录进行测试
        test_entry = entries[0]
        entry_id = test_entry['id']
        print(f"📝 选择记录: {entry_id}")
        print(f"   描述: {test_entry.get('description', 'N/A')}")
        
        # 2. 测试获取附件列表
        print(f"\n📎 步骤2: 测试获取附件列表")
        attachments_response = requests.get(f"{base_url}/attachments/{entry_id}")
        
        if attachments_response.status_code == 200:
            attachments = attachments_response.json()
            print(f"✅ 获取附件列表成功")
            print(f"   附件数量: {len(attachments)}")
            
            if len(attachments) > 0:
                for i, attachment in enumerate(attachments):
                    print(f"   附件{i+1}: {attachment.get('filename', 'N/A')}")
                    print(f"     大小: {attachment.get('size', 0)} bytes")
                    print(f"     创建时间: {attachment.get('created_at', 'N/A')}")
            else:
                print("   暂无附件")
        else:
            print(f"❌ 获取附件列表失败: {attachments_response.status_code}")
        
        # 3. 测试预览附件（如果有附件的话）
        if attachments_response.status_code == 200:
            attachments = attachments_response.json()
            if len(attachments) > 0:
                print(f"\n🖼️ 步骤3: 测试预览附件")
                preview_response = requests.get(f"{base_url}/attachments/{entry_id}?preview=true")
                
                if preview_response.status_code == 200:
                    print("✅ 附件预览API正常")
                    print(f"   Content-Type: {preview_response.headers.get('content-type', 'N/A')}")
                    print(f"   Content-Length: {preview_response.headers.get('content-length', 'N/A')}")
                else:
                    print(f"❌ 附件预览失败: {preview_response.status_code}")
            else:
                print(f"\n🖼️ 步骤3: 跳过预览测试（无附件）")
        
        # 4. 测试创建测试附件（模拟上传）
        print(f"\n📤 步骤4: 测试附件上传功能")
        
        # 创建一个简单的测试文件
        test_file_path = Path("test_attachment.txt")
        test_content = f"这是一个测试附件文件\n记录ID: {entry_id}\n创建时间: {datetime.now()}"
        
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        try:
            # 模拟文件上传
            with open(test_file_path, 'rb') as f:
                files = {'file': ('test_attachment.txt', f, 'text/plain')}
                data = {'entry_id': entry_id}
                
                upload_response = requests.post(f"{base_url}/upload-attachment", files=files, data=data)
                
                if upload_response.status_code == 200:
                    upload_result = upload_response.json()
                    print(f"✅ 附件上传成功: {upload_result.get('message')}")
                    print(f"   文件路径: {upload_result.get('file_path')}")
                    
                    # 重新获取附件列表验证
                    verify_response = requests.get(f"{base_url}/attachments/{entry_id}")
                    if verify_response.status_code == 200:
                        new_attachments = verify_response.json()
                        print(f"✅ 上传后附件数量: {len(new_attachments)}")
                    
                else:
                    print(f"❌ 附件上传失败: {upload_response.status_code}")
                    print(f"   错误信息: {upload_response.text}")
        
        finally:
            # 清理测试文件
            if test_file_path.exists():
                test_file_path.unlink()
        
        # 5. 测试删除附件功能
        print(f"\n🗑️ 步骤5: 测试删除附件功能")
        
        # 获取当前附件列表
        current_attachments_response = requests.get(f"{base_url}/attachments/{entry_id}")
        if current_attachments_response.status_code == 200:
            current_attachments = current_attachments_response.json()
            
            if len(current_attachments) > 0:
                # 删除第一个附件
                attachment_to_delete = current_attachments[0]
                filename = attachment_to_delete.get('filename')
                
                delete_data = {'filename': filename}
                delete_response = requests.delete(
                    f"{base_url}/attachments/{entry_id}",
                    headers={'Content-Type': 'application/json'},
                    data=json.dumps(delete_data)
                )
                
                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    print(f"✅ 附件删除成功: {delete_result.get('message')}")
                    print(f"   删除的文件: {delete_result.get('deleted_file')}")
                    
                    # 验证删除结果
                    final_attachments_response = requests.get(f"{base_url}/attachments/{entry_id}")
                    if final_attachments_response.status_code == 200:
                        final_attachments = final_attachments_response.json()
                        print(f"✅ 删除后附件数量: {len(final_attachments)}")
                else:
                    print(f"❌ 附件删除失败: {delete_response.status_code}")
                    print(f"   错误信息: {delete_response.text}")
            else:
                print("   没有附件可以删除")
        
        print("\n🎉 附件管理功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    from datetime import datetime
    success = test_attachment_management()
    if success:
        print("\n🎯 附件管理功能测试通过！")
    else:
        print("\n❌ 测试失败，请检查错误信息")

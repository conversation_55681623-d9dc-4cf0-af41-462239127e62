/**
 * 重置AI使用记录的脚本
 * 用于解决403错误 - 清理超出限制的使用记录
 */

// 这个脚本需要在Cloudflare Workers环境中运行
// 可以通过wrangler dev或直接在Workers中添加临时端点来执行

export async function resetAIUsage(env, userId = null, companyId = null) {
    try {
        console.log('开始重置AI使用记录...');
        
        // 如果指定了用户ID，只重置该用户的记录
        if (userId) {
            const result = await env.DB.prepare(`
                DELETE FROM ai_usage_logs 
                WHERE user_id = ?
                ${companyId ? 'AND company_id = ?' : ''}
            `).bind(userId, ...(companyId ? [companyId] : [])).run();
            
            console.log(`用户 ${userId} 的AI使用记录已重置，删除了 ${result.changes} 条记录`);
            return {
                success: true,
                message: `用户 ${userId} 的AI使用记录已重置`,
                deleted_records: result.changes
            };
        }
        
        // 重置所有用户的当月记录
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
        const result = await env.DB.prepare(`
            DELETE FROM ai_usage_logs 
            WHERE strftime('%Y-%m', created_at) = ?
        `).bind(currentMonth).run();
        
        console.log(`当月(${currentMonth})所有AI使用记录已重置，删除了 ${result.changes} 条记录`);
        return {
            success: true,
            message: `当月所有AI使用记录已重置`,
            deleted_records: result.changes,
            month: currentMonth
        };
        
    } catch (error) {
        console.error('重置AI使用记录失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 获取用户的AI使用统计
export async function getAIUsageStats(env, userId) {
    try {
        const currentMonth = new Date().toISOString().slice(0, 7);
        
        // 获取当月使用次数
        const monthlyUsage = await env.DB.prepare(`
            SELECT COUNT(*) as count
            FROM ai_usage_logs
            WHERE user_id = ? 
            AND strftime('%Y-%m', created_at) = ?
        `).bind(userId, currentMonth).first();
        
        // 获取总使用次数
        const totalUsage = await env.DB.prepare(`
            SELECT COUNT(*) as count
            FROM ai_usage_logs
            WHERE user_id = ?
        `).bind(userId).first();
        
        // 获取最近的使用记录
        const recentUsage = await env.DB.prepare(`
            SELECT *
            FROM ai_usage_logs
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        `).bind(userId).all();
        
        return {
            success: true,
            data: {
                monthly_usage: monthlyUsage?.count || 0,
                total_usage: totalUsage?.count || 0,
                current_month: currentMonth,
                recent_usage: recentUsage.results || []
            }
        };
        
    } catch (error) {
        console.error('获取AI使用统计失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 临时的Workers端点处理函数
export async function handleResetAIUsage(request, env) {
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    if (request.method === 'OPTIONS') {
        return new Response(null, { headers: corsHeaders });
    }

    try {
        // 验证用户认证
        const authHeader = request.headers.get('Authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return new Response(JSON.stringify({
                success: false,
                error: 'ログインが必要です'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                }
            });
        }

        const sessionToken = authHeader.substring(7);
        const { AuthManager } = await import('./auth/manager.js');
        const authManager = new AuthManager(env.DB);
        const session = await authManager.verifySession(sessionToken);
        
        if (!session) {
            return new Response(JSON.stringify({
                success: false,
                error: 'セッションが無効です'
            }), {
                status: 401,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                }
            });
        }

        const url = new URL(request.url);
        const action = url.searchParams.get('action') || 'reset';
        const userId = url.searchParams.get('user_id') || session.user_id;
        const companyId = url.searchParams.get('company_id');

        let result;
        if (action === 'stats') {
            result = await getAIUsageStats(env, userId);
        } else if (action === 'reset') {
            result = await resetAIUsage(env, userId, companyId);
        } else {
            return new Response(JSON.stringify({
                success: false,
                error: '无效的操作'
            }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                }
            });
        }

        return new Response(JSON.stringify(result), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });

    } catch (error) {
        console.error('处理重置请求失败:', error);
        return new Response(JSON.stringify({
            success: false,
            error: 'サーバーエラーが発生しました'
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json',
                ...corsHeaders
            }
        });
    }
}

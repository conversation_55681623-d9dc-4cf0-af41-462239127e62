#!/usr/bin/env python3
"""
GoldenLedger 系统健康检查脚本
定期检查系统各组件的运行状态
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any

# 配置日志
import os
os.makedirs('monitoring', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('health_check.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class GoldenLedgerMonitor:
    """GoldenLedger系统监控器"""
    
    def __init__(self):
        self.endpoints = {
            'frontend': 'https://ledger.goldenorangetech.com/',
            'api_health': 'https://goldenledger-api.souyousann.workers.dev/api/health',
            'api_companies': 'https://goldenledger-api.souyousann.workers.dev/api/companies',
        }
        
        self.results = []
        
    def check_endpoint(self, name: str, url: str, timeout: int = 10) -> Dict[str, Any]:
        """检查单个端点的健康状态"""
        try:
            start_time = time.time()
            response = requests.get(url, timeout=timeout)
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            result = {
                'name': name,
                'url': url,
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'status_code': response.status_code,
                'response_time_ms': round(response_time, 2),
                'timestamp': datetime.now().isoformat(),
                'error': None
            }
            
            # 检查响应内容
            if name == 'api_health':
                try:
                    data = response.json()
                    if data.get('status') == 'healthy':
                        result['api_status'] = 'healthy'
                    else:
                        result['status'] = 'unhealthy'
                        result['error'] = 'API返回非健康状态'
                except:
                    result['status'] = 'unhealthy'
                    result['error'] = 'API响应格式错误'
            
            elif name == 'api_companies':
                try:
                    data = response.json()
                    if data.get('success') and isinstance(data.get('data'), list):
                        result['data_count'] = len(data['data'])
                    else:
                        result['status'] = 'unhealthy'
                        result['error'] = '公司数据API异常'
                except:
                    result['status'] = 'unhealthy'
                    result['error'] = '公司数据API响应格式错误'
            
            return result
            
        except requests.exceptions.Timeout:
            return {
                'name': name,
                'url': url,
                'status': 'unhealthy',
                'status_code': None,
                'response_time_ms': timeout * 1000,
                'timestamp': datetime.now().isoformat(),
                'error': '请求超时'
            }
        except requests.exceptions.ConnectionError:
            return {
                'name': name,
                'url': url,
                'status': 'unhealthy',
                'status_code': None,
                'response_time_ms': None,
                'timestamp': datetime.now().isoformat(),
                'error': '连接失败'
            }
        except Exception as e:
            return {
                'name': name,
                'url': url,
                'status': 'unhealthy',
                'status_code': None,
                'response_time_ms': None,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def run_health_check(self) -> Dict[str, Any]:
        """运行完整的健康检查"""
        logger.info("开始系统健康检查...")
        
        check_results = []
        overall_status = 'healthy'
        
        for name, url in self.endpoints.items():
            logger.info(f"检查 {name}: {url}")
            result = self.check_endpoint(name, url)
            check_results.append(result)
            
            if result['status'] != 'healthy':
                overall_status = 'unhealthy'
                logger.warning(f"{name} 状态异常: {result['error']}")
            else:
                logger.info(f"{name} 状态正常 (响应时间: {result['response_time_ms']}ms)")
        
        # 计算平均响应时间
        response_times = [r['response_time_ms'] for r in check_results if r['response_time_ms']]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        summary = {
            'overall_status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'total_endpoints': len(self.endpoints),
            'healthy_endpoints': len([r for r in check_results if r['status'] == 'healthy']),
            'unhealthy_endpoints': len([r for r in check_results if r['status'] != 'healthy']),
            'average_response_time_ms': round(avg_response_time, 2),
            'details': check_results
        }
        
        return summary
    
    def save_results(self, results: Dict[str, Any]):
        """保存检查结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"health_check_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"检查结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def send_alert(self, results: Dict[str, Any]):
        """发送告警（如果有问题）"""
        if results['overall_status'] != 'healthy':
            unhealthy_services = [
                detail['name'] for detail in results['details'] 
                if detail['status'] != 'healthy'
            ]
            
            alert_message = f"""
🚨 GoldenLedger系统告警

时间: {results['timestamp']}
状态: {results['overall_status']}
异常服务: {', '.join(unhealthy_services)}
健康服务: {results['healthy_endpoints']}/{results['total_endpoints']}

详细信息:
{json.dumps(results['details'], indent=2, ensure_ascii=False)}
"""
            
            logger.error(alert_message)
            # 这里可以添加邮件、Slack、微信等通知方式
            
        else:
            logger.info(f"✅ 所有服务运行正常 (平均响应时间: {results['average_response_time_ms']}ms)")

def main():
    """主函数"""
    monitor = GoldenLedgerMonitor()
    
    try:
        # 运行健康检查
        results = monitor.run_health_check()
        
        # 保存结果
        monitor.save_results(results)
        
        # 发送告警（如果需要）
        monitor.send_alert(results)
        
        # 输出摘要
        print(f"\n📊 健康检查摘要:")
        print(f"总体状态: {results['overall_status']}")
        print(f"健康服务: {results['healthy_endpoints']}/{results['total_endpoints']}")
        print(f"平均响应时间: {results['average_response_time_ms']}ms")
        
        return 0 if results['overall_status'] == 'healthy' else 1
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

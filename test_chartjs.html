<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart.js测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
    </style>
</head>
<body>
    <h1>Chart.js加载测试</h1>
    
    <div class="card">
        <h2>Chart.js状态检查</h2>
        <div id="chart-status">检查中...</div>
    </div>
    
    <div class="card">
        <h2>AI统计图表测试</h2>
        <canvas id="ai-stats-chart" width="400" height="200"></canvas>
        <div id="chart-result" style="margin-top: 10px;">等待初始化...</div>
    </div>
    
    <div class="card">
        <h2>控制</h2>
        <button onclick="testChart()" style="padding: 10px 20px; margin: 5px;">重新测试图表</button>
        <button onclick="clearLog()" style="padding: 10px 20px; margin: 5px;">清空日志</button>
    </div>
    
    <div class="card">
        <h2>详细日志</h2>
        <div id="log" class="log">等待测试...</div>
    </div>

    <script>
        let aiStatsChart = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logDiv.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function checkChartJS() {
            log('🔍 检查Chart.js加载状态...');
            
            const statusDiv = document.getElementById('chart-status');
            
            if (typeof Chart === 'undefined') {
                statusDiv.innerHTML = '<span class="error">❌ Chart.js未加载</span>';
                log('❌ Chart.js未定义', 'error');
                return false;
            } else {
                statusDiv.innerHTML = '<span class="success">✅ Chart.js已加载</span>';
                log('✅ Chart.js已正确加载', 'success');
                log(`📊 Chart.js版本: ${Chart.version || 'unknown'}`, 'info');
                return true;
            }
        }
        
        function testChart() {
            log('🎯 开始测试AI统计图表...');
            
            const resultDiv = document.getElementById('chart-result');
            
            try {
                // 检查Chart.js
                if (!checkChartJS()) {
                    throw new Error('Chart.js未加载');
                }
                
                // 检查canvas元素
                const canvas = document.getElementById('ai-stats-chart');
                if (!canvas) {
                    throw new Error('Canvas元素未找到');
                }
                log('✅ Canvas元素找到', 'success');
                
                // 销毁现有图表
                if (aiStatsChart) {
                    aiStatsChart.destroy();
                    log('🗑️ 销毁现有图表', 'info');
                }
                
                // 创建新图表
                log('🎨 创建AI统计图表...', 'info');
                
                const ctx = canvas.getContext('2d');
                aiStatsChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['成功', '失敗', '処理中'],
                        datasets: [{
                            label: 'AI処理結果',
                            data: [145, 8, 3],
                            backgroundColor: [
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(251, 191, 36, 0.8)'
                            ],
                            borderColor: [
                                'rgba(16, 185, 129, 1)',
                                'rgba(239, 68, 68, 1)',
                                'rgba(251, 191, 36, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'AI処理統計'
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 10
                                }
                            }
                        }
                    }
                });
                
                resultDiv.innerHTML = '<span class="success">✅ 图表创建成功</span>';
                log('🎉 AI统计图表创建成功！', 'success');
                
                return true;
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 图表创建失败: ${error.message}</span>`;
                log(`❌ 图表创建失败: ${error.message}`, 'error');
                console.error('Chart creation error:', error);
                return false;
            }
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成', 'success');
            
            // 延迟一点时间确保Chart.js完全加载
            setTimeout(() => {
                checkChartJS();
                testChart();
            }, 100);
        });
        
        // 监听Chart.js加载错误
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('chart.js')) {
                log(`❌ Chart.js加载错误: ${e.message}`, 'error');
            }
        });
    </script>
</body>
</html>

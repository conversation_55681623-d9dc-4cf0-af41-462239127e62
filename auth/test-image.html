<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #001122;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 10px;
        }
        
        .image-test {
            width: 300px;
            height: 300px;
            border: 2px solid #00ffff;
            margin: 10px 0;
        }
        
        .bg-test {
            background: url('../image/gl1.png');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            background-color: rgba(0, 100, 200, 0.3);
        }
        
        .img-test {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 图片显示测试</h1>
        
        <div class="test-section">
            <h2>1. CSS背景图片测试</h2>
            <p>路径: ../image/gl1.png</p>
            <div class="image-test bg-test"></div>
        </div>
        
        <div class="test-section">
            <h2>2. HTML img标签测试</h2>
            <p>路径: ../image/gl1.png</p>
            <div class="image-test">
                <img src="../image/gl1.png" alt="测试图片" class="img-test" 
                     onload="document.getElementById('img-status').textContent = '✅ 图片加载成功'" 
                     onerror="document.getElementById('img-status').textContent = '❌ 图片加载失败'">
            </div>
            <p id="img-status">⏳ 加载中...</p>
        </div>
        
        <div class="test-section">
            <h2>3. 绝对路径测试</h2>
            <p>路径: /image/gl1.png (仅在服务器环境有效)</p>
            <div class="image-test">
                <img src="/image/gl1.png" alt="测试图片" class="img-test" 
                     onload="document.getElementById('abs-status').textContent = '✅ 绝对路径图片加载成功'" 
                     onerror="document.getElementById('abs-status').textContent = '❌ 绝对路径图片加载失败'">
            </div>
            <p id="abs-status">⏳ 加载中...</p>
        </div>
        
        <div class="test-section">
            <h2>4. 文件信息</h2>
            <p>当前页面路径: <span id="current-path"></span></p>
            <p>图片相对路径: ../image/gl1.png</p>
            <p>图片绝对路径: /image/gl1.png</p>
        </div>
    </div>
    
    <script>
        document.getElementById('current-path').textContent = window.location.href;
        
        // 检查图片文件是否存在
        function checkImageExists(url, callback) {
            const img = new Image();
            img.onload = () => callback(true);
            img.onerror = () => callback(false);
            img.src = url;
        }
        
        checkImageExists('../image/gl1.png', (exists) => {
            console.log('相对路径图片存在:', exists);
        });
        
        checkImageExists('/image/gl1.png', (exists) => {
            console.log('绝对路径图片存在:', exists);
        });
    </script>
</body>
</html>

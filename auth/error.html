<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录失败 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .error-animation {
            animation: errorShake 0.5s ease-in-out;
        }
        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .fade-in {
            animation: fadeIn 1s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-2xl p-8 shadow-2xl text-center fade-in">
            <!-- 错误图标 -->
            <div class="error-animation w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-exclamation-triangle text-3xl text-red-600"></i>
            </div>

            <!-- 错误消息 -->
            <h1 class="text-2xl font-bold text-gray-800 mb-4">登录失败</h1>
            <div id="errorMessage" class="text-gray-600 mb-8 bg-red-50 border border-red-200 rounded-lg p-4">
                <p class="text-red-700">登录过程中发生错误，请重试。</p>
            </div>

            <!-- 常见问题 -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h3 class="font-semibold text-gray-800 mb-3">可能的原因：</h3>
                <ul class="text-sm text-gray-600 space-y-2">
                    <li class="flex items-start">
                        <i class="fas fa-circle text-xs text-gray-400 mt-2 mr-2"></i>
                        <span>网络连接问题</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-circle text-xs text-gray-400 mt-2 mr-2"></i>
                        <span>Google账户权限设置</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-circle text-xs text-gray-400 mt-2 mr-2"></i>
                        <span>浏览器阻止了弹出窗口</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-circle text-xs text-gray-400 mt-2 mr-2"></i>
                        <span>服务器临时不可用</span>
                    </li>
                </ul>
            </div>

            <!-- 操作按钮 -->
            <div class="space-y-3">
                <button 
                    onclick="retryLogin()"
                    class="w-full bg-blue-600 text-white font-semibold py-3 px-6 rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300">
                    <i class="fas fa-redo mr-2"></i>
                    重试登录
                </button>
                
                <button 
                    onclick="goToLogin()"
                    class="w-full bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300 transition-all duration-300">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    返回登录页
                </button>

                <button 
                    onclick="contactSupport()"
                    class="w-full bg-green-600 text-white font-semibold py-3 px-6 rounded-xl hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 transition-all duration-300">
                    <i class="fas fa-life-ring mr-2"></i>
                    联系技术支持
                </button>
            </div>

            <!-- 返回首页链接 -->
            <div class="mt-6">
                <a href="../index.html" class="text-gray-500 hover:text-gray-700 text-sm">
                    <i class="fas fa-home mr-1"></i>
                    返回首页
                </a>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时处理错误信息
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');

            if (error) {
                displayError(decodeURIComponent(error));
            }
        });

        // 显示错误信息
        function displayError(errorMessage) {
            const errorDiv = document.getElementById('errorMessage');
            
            let displayMessage = '登录过程中发生错误，请重试。';
            let suggestions = [];

            // 根据错误类型提供具体建议
            if (errorMessage.includes('access_denied')) {
                displayMessage = '您拒绝了授权请求。';
                suggestions = [
                    '请确保同意授权GoldenLedger访问您的基本信息',
                    '如果误操作，请重新尝试登录'
                ];
            } else if (errorMessage.includes('invalid_request')) {
                displayMessage = '登录请求无效。';
                suggestions = [
                    '请清除浏览器缓存后重试',
                    '确保使用最新版本的浏览器'
                ];
            } else if (errorMessage.includes('server_error')) {
                displayMessage = '服务器暂时不可用。';
                suggestions = [
                    '请稍后重试',
                    '如果问题持续存在，请联系技术支持'
                ];
            } else if (errorMessage.includes('network')) {
                displayMessage = '网络连接失败。';
                suggestions = [
                    '请检查网络连接',
                    '尝试刷新页面重新登录'
                ];
            }

            errorDiv.innerHTML = `
                <p class="text-red-700 font-medium mb-2">${displayMessage}</p>
                ${suggestions.length > 0 ? `
                    <div class="text-sm text-red-600">
                        <p class="font-medium mb-1">建议：</p>
                        <ul class="list-disc list-inside space-y-1">
                            ${suggestions.map(s => `<li>${s}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                <div class="mt-3 text-xs text-gray-500 bg-gray-100 rounded p-2">
                    <strong>错误详情：</strong> ${errorMessage}
                </div>
            `;
        }

        // 重试登录
        function retryLogin() {
            // 清除可能的错误状态
            localStorage.removeItem('session_token');
            
            // 重定向到Google登录
            window.location.href = 'login.html';
        }

        // 返回登录页
        function goToLogin() {
            window.location.href = 'login.html';
        }

        // 联系技术支持
        function contactSupport() {
            const errorDetails = new URLSearchParams(window.location.search).get('error') || '未知错误';
            const subject = encodeURIComponent('GoldenLedger登录问题');
            const body = encodeURIComponent(`
您好，

我在使用GoldenLedger登录时遇到了问题：

错误信息：${errorDetails}
浏览器：${navigator.userAgent}
时间：${new Date().toLocaleString()}

请协助解决，谢谢！
            `);

            // 这里可以替换为实际的支持邮箱
            const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
            
            // 尝试打开邮件客户端
            window.location.href = mailtoLink;
            
            // 同时显示备用联系方式
            setTimeout(() => {
                alert(`
如果邮件客户端未打开，您也可以通过以下方式联系我们：

📧 邮箱：<EMAIL>
💬 在线客服：https://ledger.goldenorangetech.com/support
📱 微信：GoldenLedger客服

请在联系时提供错误信息以便我们快速解决问题。
                `);
            }, 1000);
        }
    </script>
</body>
</html>

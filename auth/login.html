<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ログイン - GoldenLedger AI記帳システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Noto Sans JP', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            overflow-x: hidden;
        }

        .tech-bg {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(0, 20, 40, 0.98) 100%);
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
            min-height: 100vh;
        }

        .tech-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: techPulse 4s ease-in-out infinite alternate;
        }

        @keyframes techPulse {
            0% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }

        .cyber-glass {
            backdrop-filter: blur(20px);
            background: rgba(0, 20, 40, 0.15);
            border: 1px solid rgba(120, 219, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .cyber-glass::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(120, 219, 255, 0.8), transparent);
            animation: scanLine 3s linear infinite;
        }

        @keyframes scanLine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .neon-text {
            color: #78dbff;
            text-shadow:
                0 0 5px rgba(120, 219, 255, 0.5),
                0 0 10px rgba(120, 219, 255, 0.3),
                0 0 15px rgba(120, 219, 255, 0.2);
        }

        .neon-button {
            background: linear-gradient(45deg, rgba(120, 219, 255, 0.1), rgba(120, 119, 198, 0.1));
            border: 1px solid rgba(120, 219, 255, 0.5);
            color: #78dbff;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .neon-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(120, 219, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .neon-button:hover::before {
            left: 100%;
        }

        .neon-button:hover {
            transform: translateY(-2px);
            box-shadow:
                0 10px 25px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(120, 219, 255, 0.4);
            border-color: rgba(120, 219, 255, 0.8);
        }

        .google-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .google-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(66, 133, 244, 0.1), transparent);
            transition: left 0.5s;
        }

        .google-btn:hover::before {
            left: 100%;
        }

        .google-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .cyber-input {
            background: rgba(0, 20, 40, 0.3);
            border: 1px solid rgba(120, 219, 255, 0.3);
            color: #78dbff;
            transition: all 0.3s ease;
        }

        .cyber-input:focus {
            border-color: rgba(120, 219, 255, 0.8);
            box-shadow: 0 0 20px rgba(120, 219, 255, 0.3);
            background: rgba(0, 20, 40, 0.5);
        }

        .cyber-input::placeholder {
            color: rgba(120, 219, 255, 0.5);
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(120, 219, 255, 0.6);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="tech-bg">
    <!-- 浮动粒子效果 -->
    <div class="floating-particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 15%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 25%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 35%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 5%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 2.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 3.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 4.5s;"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="min-h-screen flex items-center justify-center px-4 py-12 relative z-10">
        <div class="w-full max-w-md relative z-20">
        <!-- ログインカード -->
        <div class="cyber-glass rounded-3xl p-10 shadow-2xl">
            <!-- ロゴとタイトル -->
            <div class="text-center mb-10">
                <div class="w-24 h-24 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl relative">
                    <i class="fas fa-coins text-4xl text-white"></i>
                    <div class="absolute inset-0 rounded-full border-2 border-cyan-400 animate-ping opacity-20"></div>
                </div>
                <h1 class="text-4xl font-bold neon-text mb-3">GoldenLedger</h1>
                <p class="text-cyan-200 text-lg font-light">AI記帳システム</p>
                <div class="mt-4 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></div>
            </div>

            <!-- ログインオプション -->
            <div class="space-y-6">
                <!-- Googleログインボタン -->
                <button
                    id="googleLoginBtn"
                    onclick="loginWithGoogle()"
                    class="google-btn w-full text-gray-700 font-semibold py-5 px-8 rounded-2xl flex items-center justify-center space-x-4 focus:outline-none focus:ring-4 focus:ring-cyan-400/30">
                    <svg class="w-7 h-7" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span class="text-lg">Googleアカウントでログイン</span>
                </button>

                <!-- 分割線 -->
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-cyan-400/30"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-4 bg-transparent text-cyan-200 font-medium">または</span>
                    </div>
                </div>

                <!-- 従来のログインフォーム -->
                <form id="loginForm" class="space-y-6">
                    <div>
                        <label class="block text-cyan-200 text-sm font-medium mb-3">
                            <i class="fas fa-user mr-2"></i>ユーザー名
                        </label>
                        <input
                            type="text"
                            id="username"
                            class="cyber-input w-full px-5 py-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-300"
                            placeholder="ユーザー名を入力してください"
                            required>
                    </div>
                    <div>
                        <label class="block text-cyan-200 text-sm font-medium mb-3">
                            <i class="fas fa-lock mr-2"></i>パスワード
                        </label>
                        <input
                            type="password"
                            id="password"
                            class="cyber-input w-full px-5 py-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent transition-all duration-300"
                            placeholder="パスワードを入力してください"
                            required>
                    </div>
                    <button
                        type="submit"
                        id="loginSubmitBtn"
                        class="neon-button w-full font-semibold py-4 px-8 rounded-xl focus:outline-none focus:ring-4 focus:ring-cyan-400/30 transition-all duration-300 text-lg">
                        <i class="fas fa-sign-in-alt mr-2"></i>ログイン
                    </button>
                </form>
            </div>

            <!-- 底部リンク -->
            <div class="mt-10 text-center">
                <p class="text-cyan-300/70 text-sm">
                    アカウントをお持ちでない方は
                    <a href="#" class="text-cyan-400 hover:text-cyan-300 font-medium transition-colors duration-300">新規登録</a>
                </p>
                <div class="mt-6">
                    <a href="../index.html" class="text-cyan-300/70 hover:text-cyan-300 text-sm transition-colors duration-300">
                        <i class="fas fa-arrow-left mr-2"></i>
                        ホームページに戻る
                    </a>
                </div>

                <!-- セキュリティ情報 -->
                <div class="mt-8 pt-6 border-t border-cyan-400/20">
                    <div class="flex items-center justify-center space-x-4 text-xs text-cyan-300/50">
                        <span><i class="fas fa-shield-alt mr-1"></i>SSL暗号化</span>
                        <span><i class="fas fa-lock mr-1"></i>セキュア認証</span>
                        <span><i class="fas fa-cloud mr-1"></i>クラウド同期</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- ローディング表示 -->
        <div id="loadingModal" class="hidden fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="cyber-glass rounded-3xl p-10 text-center">
                <div class="loading-spinner w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full mx-auto mb-6"></div>
                <p class="text-cyan-200 text-lg font-medium">ログイン処理中...</p>
                <div class="mt-4 h-1 bg-cyan-900/30 rounded-full overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full animate-pulse"></div>
                </div>
            </div>
        </div>

        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        // Googleログイン
        async function loginWithGoogle() {
            try {
                showLoading();

                const response = await fetch(`${API_BASE_URL}/api/auth/google/login`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    // Google認証ページにリダイレクト
                    window.location.href = result.auth_url;
                } else {
                    throw new Error(result.error || 'Googleログインの初期化に失敗しました');
                }
            } catch (error) {
                hideLoading();
                console.error('Googleログインエラー:', error);
                showErrorMessage('Googleログインに失敗しました: ' + error.message);
            }
        }

        // 従来のログイン
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showErrorMessage('ユーザー名とパスワードを入力してください');
                return;
            }

            try {
                showLoading();

                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 内测期间用户限制检查
                    const allowedUsers = ['<EMAIL>', 'admin'];
                    const userEmail = result.user?.email || result.user?.username || username;

                    if (!allowedUsers.includes(userEmail)) {
                        // 不允许的用户，显示内测提示
                        showErrorMessage('現在ベータテスト中です。このアカウントではログインできません。');
                        return;
                    }

                    // セッショントークンを保存
                    localStorage.setItem('session_token', result.session_token);

                    // ホームページにリダイレクト
                    window.location.href = '../index.html';
                } else {
                    throw new Error(result.error || 'ログインに失敗しました');
                }
            } catch (error) {
                hideLoading();
                console.error('ログインエラー:', error);
                showErrorMessage('ログインに失敗しました: ' + error.message);
            }
        });

        // エラーメッセージ表示
        function showErrorMessage(message) {
            // 既存のエラーメッセージを削除
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }

            // 新しいエラーメッセージを作成
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message fixed top-4 right-4 bg-red-500/90 backdrop-blur-sm text-white px-6 py-4 rounded-xl shadow-2xl z-50 border border-red-400/50';
            errorDiv.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                    <span class="font-medium">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white/80 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(errorDiv);

            // 5秒後に自動削除
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }

        // ローディング状態表示
        function showLoading() {
            document.getElementById('loadingModal').classList.remove('hidden');
            document.getElementById('googleLoginBtn').disabled = true;
            document.getElementById('loginSubmitBtn').disabled = true;
        }

        // ローディング状態非表示
        function hideLoading() {
            document.getElementById('loadingModal').classList.add('hidden');
            document.getElementById('googleLoginBtn').disabled = false;
            document.getElementById('loginSubmitBtn').disabled = false;
        }

        // ページ読み込み時の初期化
        document.addEventListener('DOMContentLoaded', function() {
            // パーティクルアニメーションの初期化
            initParticles();

            // キーボードショートカット
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    document.getElementById('loginForm').dispatchEvent(new Event('submit'));
                }
            });
        });

        // パーティクル効果の初期化
        function initParticles() {
            const particles = document.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                // ランダムな位置とアニメーション遅延を設定
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (4 + Math.random() * 4) + 's';
            });
        }

        // 检查是否已经登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('session_token');
            if (token) {
                // 验证令牌是否有效
                fetch(`${API_BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.authenticated) {
                        // 已登录，重定向到主页
                        window.location.href = '../index.html';
                    }
                })
                .catch(error => {
                    console.log('令牌验证失败:', error);
                    localStorage.removeItem('session_token');
                });
            }
        });

        // 图片相关函数已移除
    </script>
</body>
</html>

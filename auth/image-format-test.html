<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片格式测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #001122;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #00ffff;
            border-radius: 10px;
            background: rgba(0, 20, 40, 0.5);
        }
        
        .image-container {
            width: 300px;
            height: 300px;
            border: 2px solid #ff0000;
            margin: 10px 0;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .test-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success { background: #004400; color: #00ff00; }
        .error { background: #440000; color: #ff0000; }
        .info { background: #004444; color: #00ffff; }
        .warning { background: #444400; color: #ffff00; }
    </style>
</head>
<body>
    <h1>🔧 图片格式问题诊断</h1>
    
    <div class="status warning">
        <strong>⚠️ 发现问题:</strong><br>
        文件名: gl1.png<br>
        实际格式: JPEG image data<br>
        <strong>这可能导致浏览器无法正确显示图片！</strong>
    </div>
    
    <div class="test-section">
        <h2>测试1: 原始文件 (gl1.png - 实际是JPEG)</h2>
        <div class="image-container">
            <img src="../image/gl1.png" alt="原始文件" class="test-img" 
                 onload="showStatus('test1-status', '✅ 原始文件加载成功', 'success')" 
                 onerror="showStatus('test1-status', '❌ 原始文件加载失败 - 格式问题', 'error')">
        </div>
        <div id="test1-status" class="status info">⏳ 加载中...</div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 强制JPEG格式解析</h2>
        <div class="image-container">
            <img src="../image/gl1.png" alt="强制JPEG" class="test-img" 
                 style="image-rendering: auto;"
                 onload="showStatus('test2-status', '✅ 强制JPEG解析成功', 'success')" 
                 onerror="showStatus('test2-status', '❌ 强制JPEG解析失败', 'error')">
        </div>
        <div id="test2-status" class="status info">⏳ 加载中...</div>
    </div>
    
    <div class="test-section">
        <h2>解决方案建议</h2>
        <div class="status info">
            <strong>🔧 修复方法:</strong><br>
            1. 将文件重命名为 gl1.jpg<br>
            2. 或者转换为真正的PNG格式<br>
            3. 更新HTML中的文件引用
        </div>
        
        <div class="status warning">
            <strong>📋 技术说明:</strong><br>
            - 文件扩展名: .png<br>
            - 实际格式: JPEG (2048x2048)<br>
            - 浏览器可能因格式不匹配而拒绝显示<br>
            - 需要确保文件名与实际格式一致
        </div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 备用图片 (如果有)</h2>
        <div class="image-container">
            <img src="../image/favicon.png" alt="备用图片" class="test-img" 
                 onload="showStatus('test3-status', '✅ 备用图片加载成功', 'success')" 
                 onerror="showStatus('test3-status', '❌ 备用图片不存在', 'error')">
        </div>
        <div id="test3-status" class="status info">⏳ 加载中...</div>
    </div>
    
    <script>
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            console.log(message);
        }
        
        // 输出详细的调试信息
        console.log('=== 图片格式诊断 ===');
        console.log('文件路径: ../image/gl1.png');
        console.log('实际格式: JPEG image data');
        console.log('文件大小: 585873 bytes');
        console.log('图片尺寸: 2048x2048');
        console.log('问题: 文件扩展名与实际格式不匹配');
        
        // 检测浏览器支持
        console.log('浏览器信息:', navigator.userAgent);
        console.log('支持的图片格式测试...');
        
        // 测试不同的加载方式
        setTimeout(() => {
            const img = new Image();
            img.onload = function() {
                console.log('✅ JavaScript Image对象加载成功');
                console.log('图片实际尺寸:', this.naturalWidth, 'x', this.naturalHeight);
            };
            img.onerror = function() {
                console.log('❌ JavaScript Image对象加载失败');
            };
            img.src = '../image/gl1.png';
        }, 1000);
    </script>
</body>
</html>

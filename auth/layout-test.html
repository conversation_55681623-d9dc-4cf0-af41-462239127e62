<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #001122;
            color: white;
        }

        .split-layout {
            display: grid;
            grid-template-columns: 1fr 1fr !important;
            min-height: 100vh;
        }

        .left-panel {
            background: linear-gradient(135deg, rgba(0, 20, 40, 0.95) 0%, rgba(0, 0, 0, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            border-right: 2px solid #00ffff;
        }

        .right-panel {
            background: linear-gradient(225deg, rgba(0, 20, 40, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .content-box {
            padding: 2rem;
            border: 2px solid #00ffff;
            border-radius: 10px;
            text-align: center;
            background: rgba(0, 20, 40, 0.5);
        }

        .image-placeholder {
            width: 300px;
            height: 300px;
            background: url('../image/gl1.jpg');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            border: 2px solid #00ffff;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00ffff;
            font-size: 24px;
            background-color: rgba(0, 100, 200, 0.3);
        }

        .debug-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <div>窗口宽度: <span id="window-width"></span>px</div>
        <div>窗口高度: <span id="window-height"></span>px</div>
        <div>布局: <span id="layout-type"></span></div>
    </div>

    <div class="split-layout">
        <div class="left-panel">
            <div class="content-box">
                <h1 style="color: #00ffff; margin-bottom: 20px;">左侧面板</h1>
                <p>这里应该是登录表单</p>
                <div style="margin-top: 20px;">
                    <input type="text" placeholder="用户名" style="padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #00ffff; background: rgba(0, 20, 40, 0.5); color: white;">
                    <br>
                    <input type="password" placeholder="密码" style="padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #00ffff; background: rgba(0, 20, 40, 0.5); color: white;">
                    <br>
                    <button style="padding: 10px 20px; margin: 10px; border-radius: 5px; border: none; background: #00ffff; color: #001122;">登录</button>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="content-box">
                <h1 style="color: #00ffff; margin-bottom: 20px;">右侧面板</h1>
                <p>这里应该是图片和动画</p>
                <div class="image-placeholder" style="margin: 20px auto;">
                    图片区域
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateDebugInfo() {
            document.getElementById('window-width').textContent = window.innerWidth;
            document.getElementById('window-height').textContent = window.innerHeight;
            
            if (window.innerWidth > 1024) {
                document.getElementById('layout-type').textContent = '大屏幕 - 左右分栏';
            } else if (window.innerWidth > 768) {
                document.getElementById('layout-type').textContent = '中等屏幕 - 左右分栏';
            } else {
                document.getElementById('layout-type').textContent = '小屏幕 - 左右分栏';
            }
        }

        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);

        console.log('=== 布局测试 ===');
        console.log('窗口尺寸:', window.innerWidth, 'x', window.innerHeight);
        console.log('CSS Grid布局: grid-template-columns: 1fr 1fr');
        console.log('强制左右分栏，不使用垂直布局');
    </script>
</body>
</html>

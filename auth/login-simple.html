<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化登录测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #001122;
            color: white;
            overflow-x: hidden;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 100vh;
        }

        .left-side {
            background: linear-gradient(135deg, rgba(0, 20, 40, 0.95) 0%, rgba(0, 0, 0, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .right-side {
            background: linear-gradient(225deg, rgba(0, 20, 40, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .login-form {
            background: rgba(0, 20, 40, 0.8);
            padding: 2rem;
            border-radius: 20px;
            border: 1px solid #00ffff;
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .image-container {
            width: 80%;
            height: 80%;
            min-height: 400px;
            position: relative;
            border: 2px solid #00ffff;
            border-radius: 20px;
            background: rgba(0, 100, 200, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 18px;
        }

        .fallback-text {
            color: #00ffff;
            font-size: 24px;
            text-align: center;
        }

        .input-field {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid #00ffff;
            border-radius: 8px;
            background: rgba(0, 20, 40, 0.5);
            color: white;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            margin: 15px 0;
            border: none;
            border-radius: 8px;
            background: #00ffff;
            color: #001122;
            font-weight: bold;
            cursor: pointer;
        }

        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <div>窗口: <span id="size"></span></div>
        <div>图片: <span id="img-status">检测中...</span></div>
    </div>

    <div class="container">
        <!-- 左侧登录表单 -->
        <div class="left-side">
            <div class="login-form">
                <h1 style="color: #00ffff; margin-bottom: 20px;">GoldenLedger</h1>
                <p style="margin-bottom: 20px;">AI記帳システム</p>
                
                <input type="text" class="input-field" placeholder="ユーザー名">
                <input type="password" class="input-field" placeholder="パスワード">
                <button class="login-btn">ログイン</button>
                
                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #00ffff;">
                    <button class="login-btn" style="background: white; color: #001122;">
                        🔍 Googleでログイン
                    </button>
                </div>
            </div>
        </div>

        <!-- 右侧图片区域 -->
        <div class="right-side">
            <div class="image-container">
                <!-- 测试多种图片路径 -->
                <img src="../image/gl1.jpg" alt="GoldenLedger" class="main-image"
                     onload="updateStatus('✅ JPG格式加载成功')" 
                     onerror="tryPNG(this)">
                
                <div class="fallback-text" style="position: absolute;">
                    📊 AI記帳システム
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('img-status').textContent = message;
            console.log(message);
        }

        function tryPNG(img) {
            console.log('JPG加载失败，尝试PNG格式...');
            img.src = '../image/gl1.png';
            img.onerror = function() {
                updateStatus('❌ 图片加载失败');
                this.style.display = 'none';
            };
            img.onload = function() {
                updateStatus('✅ PNG格式加载成功');
            };
        }

        function updateSize() {
            document.getElementById('size').textContent = 
                window.innerWidth + 'x' + window.innerHeight;
        }

        updateSize();
        window.addEventListener('resize', updateSize);

        console.log('=== 简化登录页面测试 ===');
        console.log('布局: CSS Grid 1fr 1fr');
        console.log('图片路径1: ../image/gl1.jpg');
        console.log('图片路径2: ../image/gl1.png (备用)');
        console.log('无Tailwind CSS干扰');
    </script>
</body>
</html>

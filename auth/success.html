<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ログイン成功 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- User Status Component -->
    <link rel="stylesheet" href="../components/UserStatusComponent.css?v=3.9.7">
    <script src="../components/UserStatusComponent.js?v=3.9.7"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .fade-in {
            animation: fadeIn 1s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-2xl p-8 shadow-2xl text-center fade-in">
            <!-- 成功アイコン -->
            <div class="success-animation w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-check text-3xl text-green-600"></i>
            </div>

            <!-- 成功メッセージ -->
            <h1 class="text-2xl font-bold text-gray-800 mb-4">ログイン成功！</h1>
            <p class="text-gray-600 mb-8">
                GoldenLedger AI智能記帳システムへようこそ
            </p>

            <!-- ユーザー情報 -->
            <div id="userInfo" class="bg-gray-50 rounded-lg p-4 mb-6 hidden">
                <div class="flex items-center justify-center space-x-3">
                    <img id="userAvatar" class="w-12 h-12 rounded-full" alt="ユーザーアバター">
                    <div class="text-left">
                        <div id="userName" class="font-semibold text-gray-800"></div>
                        <div id="userEmail" class="text-sm text-gray-600"></div>
                    </div>
                </div>
            </div>

            <!-- 操作ボタン -->
            <div class="space-y-3">
                <button 
                    onclick="goToMainPage()"
                    class="w-full bg-blue-600 text-white font-semibold py-3 px-6 rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300">
                    <i class="fas fa-home mr-2"></i>
                    メインページへ
                </button>
                
                <button 
                    onclick="goToAIPage()"
                    class="w-full bg-green-600 text-white font-semibold py-3 px-6 rounded-xl hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 transition-all duration-300">
                    <i class="fas fa-robot mr-2"></i>
                    AI記帳を開始
                </button>
            </div>

            <!-- 自動リダイレクト案内 -->
            <div class="mt-6 text-sm text-gray-500">
                <span id="countdown">5</span> 秒後にメインページへ自動移動します
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        let countdownTimer;

        // ページ読み込み時の処理
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');

            if (token) {
                // セッショントークンを保存 - 正しいキー名を使用
                localStorage.setItem('goldenledger_session_token', token);
                
                // ユーザー情報を取得
                fetchUserInfo(token);
                
                // カウントダウンを開始
                startCountdown();
                
                // URLからtokenパラメータをクリア
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            } else {
                // tokenがない場合、ローカルストレージをチェック
                const storedToken = localStorage.getItem('goldenledger_session_token');
                if (storedToken) {
                    fetchUserInfo(storedToken);
                    startCountdown();
                } else {
                    // 有効なtokenがない場合、ログインページにリダイレクト
                    window.location.href = 'login.html';
                }
            }
        });

        // ユーザー情報を取得
        async function fetchUserInfo(token) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();
                
                if (result.success && result.authenticated) {
                    // ユーザー情報をlocalStorageに保存
                    localStorage.setItem('goldenledger_user', JSON.stringify(result.user));
                    displayUserInfo(result.user);
                } else {
                    console.error('ユーザー認証に失敗しました:', result.error);
                    localStorage.removeItem('goldenledger_session_token');
                    window.location.href = 'login.html';
                }
            } catch (error) {
                console.error('ユーザー情報の取得に失敗しました:', error);
                localStorage.removeItem('goldenledger_session_token');
                window.location.href = 'login.html';
            }
        }

        // ユーザー情報を表示
        function displayUserInfo(user) {
            const userInfoDiv = document.getElementById('userInfo');
            const userAvatar = document.getElementById('userAvatar');
            const userName = document.getElementById('userName');
            const userEmail = document.getElementById('userEmail');

            if (user.avatar_url) {
                userAvatar.src = user.avatar_url;
                userAvatar.style.display = 'block';
            } else {
                userAvatar.style.display = 'none';
            }

            userName.textContent = user.name || user.username || 'ユーザー';
            userEmail.textContent = user.email || '';

            userInfoDiv.classList.remove('hidden');
        }

        // カウントダウンを開始
        function startCountdown() {
            let count = 5;
            const countdownElement = document.getElementById('countdown');
            
            countdownTimer = setInterval(() => {
                count--;
                countdownElement.textContent = count;
                
                if (count <= 0) {
                    clearInterval(countdownTimer);
                    goToMainPage();
                }
            }, 1000);
        }

        // メインページにリダイレクト
        function goToMainPage() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
            window.location.href = '../index.html';
        }

        // AI記帳ページにリダイレクト
        function goToAIPage() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
            window.location.href = '../frontend/ai-enhanced.html';
        }

        // ページアンロード時にタイマーをクリア
        window.addEventListener('beforeunload', function() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
        });

        // ユーザーステータスコンポーネントを初期化（認証成功ページ用）
        if (typeof UserStatusComponent !== 'undefined' && UserStatusComponent.isLoggedIn()) {
            window.userStatus = new UserStatusComponent({
                position: 'top-right', // 認証成功ページでは右上に配置
                showRole: true,
                showLoginTime: true,
                autoHide: false // 認証成功時は常に表示
            });
            console.log('👤 ユーザーステータスコンポーネント初期化完了 (認証成功ページ)');
        }
    </script>
</body>
</html>

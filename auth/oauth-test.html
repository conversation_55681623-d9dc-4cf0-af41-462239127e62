<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth配置测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .code-block {
            background: #1a1a1a;
            color: #f8f8f2;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-2xl p-8 shadow-2xl">
            <!-- 标题 -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fab fa-google text-blue-600 mr-2"></i>
                    Google OAuth配置测试
                </h1>
                <p class="text-gray-600">解决redirect_uri_mismatch错误</p>
            </div>

            <!-- 错误说明 -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-red-800 mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    当前错误
                </h3>
                <p class="text-red-700 text-sm">
                    <strong>错误 400: redirect_uri_mismatch</strong><br>
                    Google Cloud Console中配置的重定向URI与实际请求不匹配
                </p>
            </div>

            <!-- 配置信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-800 mb-3">当前配置信息</h3>
                    <div class="space-y-2 text-sm">
                        <div>
                            <strong>客户端ID:</strong><br>
                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">441923165006-houru024frrn51m217b473guu0so8oob.apps.googleusercontent.com</code>
                        </div>
                        <div>
                            <strong>重定向URI:</strong><br>
                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">https://goldenledger-api.souyousann.workers.dev/api/auth/google/callback</code>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 rounded-lg p-4">
                    <h3 class="font-semibold text-green-800 mb-3">测试状态</h3>
                    <div id="testStatus" class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-spinner fa-spin text-blue-500 mr-2"></i>
                            <span>正在测试API端点...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置步骤 -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 class="font-semibold text-gray-800 mb-4">
                    <i class="fas fa-cog mr-2"></i>
                    Google Cloud Console配置步骤
                </h3>
                
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">1</div>
                        <div>
                            <p class="font-medium">访问Google Cloud Console</p>
                            <p class="text-sm text-gray-600">
                                访问 <a href="https://console.cloud.google.com/" target="_blank" class="text-blue-600 hover:underline">https://console.cloud.google.com/</a>
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">2</div>
                        <div>
                            <p class="font-medium">导航到凭据页面</p>
                            <p class="text-sm text-gray-600">API和服务 → 凭据</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">3</div>
                        <div>
                            <p class="font-medium">编辑OAuth 2.0客户端</p>
                            <p class="text-sm text-gray-600">找到客户端ID并点击编辑</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">4</div>
                        <div>
                            <p class="font-medium">添加重定向URI</p>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600 mb-2">在"已获授权的重定向URI"中添加：</p>
                                <div class="code-block p-3 rounded text-sm">
                                    https://goldenledger-api.souyousann.workers.dev/api/auth/google/callback
                                </div>
                                <button onclick="copyToClipboard('https://goldenledger-api.souyousann.workers.dev/api/auth/google/callback')" 
                                        class="mt-2 text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">
                                    <i class="fas fa-copy mr-1"></i>复制URI
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">5</div>
                        <div>
                            <p class="font-medium">添加JavaScript来源</p>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600 mb-2">在"已获授权的JavaScript来源"中添加：</p>
                                <div class="code-block p-3 rounded text-sm space-y-1">
                                    <div>https://ledger.goldenorangetech.com</div>
                                    <div>https://goldenledger-api.souyousann.workers.dev</div>
                                </div>
                                <button onclick="copyOrigins()" 
                                        class="mt-2 text-xs bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600">
                                    <i class="fas fa-copy mr-1"></i>复制来源
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">6</div>
                        <div>
                            <p class="font-medium">保存配置</p>
                            <p class="text-sm text-gray-600">点击"保存"按钮，等待配置生效（通常需要几分钟）</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试按钮 -->
            <div class="text-center space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="runDatabaseMigration()"
                            class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 transition-all">
                        <i class="fas fa-database mr-2"></i>
                        修复数据库Schema
                    </button>

                    <button onclick="testOAuthEndpoint()"
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all">
                        <i class="fas fa-flask mr-2"></i>
                        测试OAuth端点
                    </button>
                </div>

                <div class="text-sm text-gray-600">
                    <p class="mb-2">
                        <strong>步骤1:</strong> 先点击"修复数据库Schema"确保数据库正确配置<br>
                        <strong>步骤2:</strong> 在Google Cloud Console中配置重定向URI<br>
                        <strong>步骤3:</strong> 点击"测试OAuth端点"验证配置<br>
                        <strong>步骤4:</strong> <a href="login.html" class="text-blue-600 hover:underline">返回登录页面</a>测试完整流程
                    </p>
                </div>
            </div>

            <!-- 结果显示 -->
            <div id="testResult" class="hidden mt-6 p-4 rounded-lg"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        // 页面加载时自动测试
        window.addEventListener('load', function() {
            testOAuthEndpoint();
        });

        // 运行数据库迁移
        async function runDatabaseMigration() {
            const statusDiv = document.getElementById('testStatus');
            const resultDiv = document.getElementById('testResult');

            statusDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-spinner fa-spin text-green-500 mr-2"></i>
                    <span>正在修复数据库Schema...</span>
                </div>
            `;

            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/migrate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    statusDiv.innerHTML = `
                        <div class="flex items-center text-green-600">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span>数据库Schema修复成功</span>
                        </div>
                        <div class="flex items-center text-blue-600 mt-1">
                            <i class="fas fa-database mr-2"></i>
                            <span>Google OAuth字段已添加</span>
                        </div>
                    `;

                    resultDiv.className = 'mt-6 p-4 rounded-lg bg-green-50 border border-green-200';
                    resultDiv.innerHTML = `
                        <h4 class="font-semibold text-green-800 mb-2">✅ 数据库修复成功</h4>
                        <div class="text-green-700 text-sm space-y-2">
                            <p><strong>Google OAuth迁移:</strong> ${result.migrations.google_oauth.message}</p>
                            <p><strong>AI增强功能迁移:</strong> ${result.migrations.ai_enhancements.message}</p>
                        </div>
                        <div class="mt-4">
                            <button onclick="testOAuthEndpoint()"
                                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                                <i class="fas fa-flask mr-2"></i>现在测试OAuth端点
                            </button>
                        </div>
                    `;
                } else {
                    throw new Error(result.error || 'Database migration failed');
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="flex items-center text-red-600">
                        <i class="fas fa-times-circle mr-2"></i>
                        <span>数据库修复失败</span>
                    </div>
                `;

                resultDiv.className = 'mt-6 p-4 rounded-lg bg-red-50 border border-red-200';
                resultDiv.innerHTML = `
                    <h4 class="font-semibold text-red-800 mb-2">❌ 数据库修复失败</h4>
                    <p class="text-red-700 text-sm">${error.message}</p>
                `;
            }

            resultDiv.classList.remove('hidden');
        }

        // 测试OAuth端点
        async function testOAuthEndpoint() {
            const statusDiv = document.getElementById('testStatus');
            const resultDiv = document.getElementById('testResult');
            
            statusDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-spinner fa-spin text-blue-500 mr-2"></i>
                    <span>正在测试API端点...</span>
                </div>
            `;

            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/google/login`);
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.innerHTML = `
                        <div class="flex items-center text-green-600">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span>API端点正常</span>
                        </div>
                        <div class="flex items-center text-blue-600 mt-1">
                            <i class="fas fa-link mr-2"></i>
                            <span>授权URL已生成</span>
                        </div>
                    `;
                    
                    resultDiv.className = 'mt-6 p-4 rounded-lg bg-green-50 border border-green-200';
                    resultDiv.innerHTML = `
                        <h4 class="font-semibold text-green-800 mb-2">✅ 测试成功</h4>
                        <p class="text-green-700 text-sm mb-3">API端点正常工作，现在可以测试Google登录了。</p>
                        <a href="${result.auth_url}" target="_blank" 
                           class="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                            <i class="fab fa-google mr-2"></i>测试Google授权
                        </a>
                    `;
                } else {
                    throw new Error(result.error || 'Unknown error');
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="flex items-center text-red-600">
                        <i class="fas fa-times-circle mr-2"></i>
                        <span>API端点错误</span>
                    </div>
                `;
                
                resultDiv.className = 'mt-6 p-4 rounded-lg bg-red-50 border border-red-200';
                resultDiv.innerHTML = `
                    <h4 class="font-semibold text-red-800 mb-2">❌ 测试失败</h4>
                    <p class="text-red-700 text-sm">${error.message}</p>
                `;
            }
            
            resultDiv.classList.remove('hidden');
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('已复制到剪贴板');
            });
        }

        // 复制JavaScript来源
        function copyOrigins() {
            const origins = `https://ledger.goldenorangetech.com
https://goldenledger-api.souyousann.workers.dev`;
            navigator.clipboard.writeText(origins).then(function() {
                showToast('JavaScript来源已复制到剪贴板');
            });
        }

        // 显示提示
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google認証処理中 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-600 to-blue-600 min-h-screen flex items-center justify-center">
    <div class="bg-white rounded-2xl p-8 shadow-2xl text-center max-w-md w-full mx-4">
        <div class="mb-6">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <div class="spinner w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full"></div>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">認証処理中</h1>
            <p class="text-gray-600">Googleアカウントの認証を処理しています...</p>
        </div>
        
        <div id="status" class="text-sm text-gray-500">
            認証情報を確認中...
        </div>
    </div>

    <script>
        // Google OAuth回调处理
        function handleGoogleCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const error = urlParams.get('error');
            const state = urlParams.get('state');

            const statusElement = document.getElementById('status');

            if (error) {
                console.error('Google OAuth错误:', error);
                statusElement.textContent = '認証エラーが発生しました';
                
                // 通知父窗口认证失败
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'GOOGLE_AUTH_ERROR',
                        error: error
                    }, window.location.origin);
                    window.close();
                } else {
                    // 如果不是弹窗，跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/login.html?error=google_auth_failed';
                    }, 2000);
                }
                return;
            }

            if (code) {
                console.log('收到Google授权码:', code);
                statusElement.textContent = 'ユーザー情報を取得中...';
                
                // 这里应该将授权码发送到后端交换访问令牌
                // 由于我们是前端应用，这里模拟处理
                setTimeout(() => {
                    // 模拟用户数据（实际应该从Google API获取）
                    const mockUserData = {
                        id: 'google_' + Date.now(),
                        name: 'Google ユーザー',
                        email: '<EMAIL>',
                        picture: 'https://ui-avatars.com/api/?name=Google+User&background=4285f4&color=fff',
                        provider: 'google',
                        loginTime: new Date().toISOString()
                    };

                    statusElement.textContent = '認証完了！リダイレクト中...';

                    // 通知父窗口认证成功
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'GOOGLE_AUTH_SUCCESS',
                            user: mockUserData
                        }, window.location.origin);
                        window.close();
                    } else {
                        // 如果不是弹窗，保存用户信息并跳转
                        if (window.userManager) {
                            window.userManager.loginUser(mockUserData);
                        } else {
                            localStorage.setItem('goldenledger_user', JSON.stringify(mockUserData));
                            localStorage.setItem('goldenledger_token', 'google_token_' + Date.now());
                        }
                        
                        setTimeout(() => {
                            window.location.href = '/master_dashboard.html';
                        }, 1000);
                    }
                }, 1500);
            } else {
                statusElement.textContent = '認証情報が見つかりません';
                console.error('未收到授权码或错误信息');
                
                setTimeout(() => {
                    window.location.href = '/login.html?error=no_auth_code';
                }, 2000);
            }
        }

        // 页面加载时处理回调
        window.addEventListener('load', handleGoogleCallback);
        
        console.log('Google OAuth回调页面已加载');
    </script>
</body>
</html>

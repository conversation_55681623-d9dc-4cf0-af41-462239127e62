<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化图片测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #001122;
            color: white;
            font-family: Arial, sans-serif;
            height: 100vh;
            display: grid;
            grid-template-columns: 1fr 1fr;
        }
        
        .left-panel {
            background: linear-gradient(135deg, rgba(0, 20, 40, 0.95) 0%, rgba(0, 0, 0, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .right-panel {
            background: linear-gradient(225deg, rgba(0, 20, 40, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .tech-image {
            width: 80%;
            height: 80%;
            min-height: 400px;
            position: relative;
            border: 2px solid #00ffff;
            border-radius: 20px;
            background-color: rgba(0, 100, 200, 0.2);
        }
        
        .image-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('../image/gl1.png');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 18px;
            z-index: 1;
        }
        
        .image-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 18px;
            z-index: 2;
        }
        
        .image-text {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            z-index: 10;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 10px;
        }
        
        .login-card {
            background: rgba(0, 20, 40, 0.8);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(120, 219, 255, 0.3);
            text-align: center;
        }
        
        .status {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status" id="status">
        图片加载状态: <span id="load-status">检测中...</span>
    </div>
    
    <div class="left-panel">
        <div class="login-card">
            <h1 style="color: #00ffff; margin-bottom: 20px;">GoldenLedger</h1>
            <p>AI记帐システム</p>
            <button style="background: #00ffff; color: #001122; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 20px;">
                Googleアカウントでログイン
            </button>
        </div>
    </div>
    
    <div class="right-panel">
        <div class="tech-image">
            <!-- CSS背景图片 -->
            <div class="image-bg"></div>
            
            <!-- HTML img标签 -->
            <img src="../image/gl1.png" alt="GoldenLedger AI" class="image-img"
                 onload="updateStatus('✅ 图片加载成功')" 
                 onerror="updateStatus('❌ 图片加载失败')">
            
            <!-- 文字叠加 -->
            <div class="image-text">
                <h2 style="color: #00ffff; margin: 0 0 10px 0;">未来の記帳</h2>
                <p style="margin: 0; color: #aaffff;">AI技術で革新する記帳システム</p>
            </div>
        </div>
    </div>
    
    <script>
        function updateStatus(message) {
            document.getElementById('load-status').textContent = message;
            console.log(message);
        }
        
        // 检查图片路径
        console.log('当前页面:', window.location.href);
        console.log('图片路径:', '../image/gl1.png');
        
        // 延迟检查
        setTimeout(() => {
            const img = document.querySelector('.image-img');
            if (img.complete && img.naturalHeight !== 0) {
                updateStatus('✅ 图片已加载');
            } else {
                updateStatus('⏳ 图片加载中...');
            }
        }, 1000);
    </script>
</body>
</html>

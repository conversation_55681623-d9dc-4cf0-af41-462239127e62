<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片调试测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #001122;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #00ffff;
            border-radius: 10px;
            background: rgba(0, 20, 40, 0.5);
        }
        
        .image-container {
            width: 300px;
            height: 300px;
            border: 2px solid #ff0000;
            margin: 10px 0;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .test-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .bg-image {
            width: 100%;
            height: 100%;
            background: url('../image/gl1.png');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            background-color: rgba(0, 100, 200, 0.3);
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success { background: #004400; color: #00ff00; }
        .error { background: #440000; color: #ff0000; }
        .info { background: #004444; color: #00ffff; }
    </style>
</head>
<body>
    <h1>🔧 图片显示调试测试</h1>
    
    <div class="status info">
        <strong>当前页面路径:</strong> <span id="current-path"></span><br>
        <strong>图片相对路径:</strong> ../image/gl1.png<br>
        <strong>图片绝对路径:</strong> /image/gl1.png
    </div>
    
    <div class="test-section">
        <h2>测试1: HTML img标签 (相对路径)</h2>
        <div class="image-container">
            <img src="../image/gl1.png" alt="测试图片" class="test-img" 
                 onload="showStatus('img1-status', '✅ 相对路径图片加载成功', 'success')" 
                 onerror="showStatus('img1-status', '❌ 相对路径图片加载失败', 'error')">
        </div>
        <div id="img1-status" class="status info">⏳ 加载中...</div>
    </div>
    
    <div class="test-section">
        <h2>测试2: HTML img标签 (绝对路径)</h2>
        <div class="image-container">
            <img src="/image/gl1.png" alt="测试图片" class="test-img" 
                 onload="showStatus('img2-status', '✅ 绝对路径图片加载成功', 'success')" 
                 onerror="showStatus('img2-status', '❌ 绝对路径图片加载失败', 'error')">
        </div>
        <div id="img2-status" class="status info">⏳ 加载中...</div>
    </div>
    
    <div class="test-section">
        <h2>测试3: CSS背景图片 (相对路径)</h2>
        <div class="image-container">
            <div class="bg-image"></div>
        </div>
        <div class="status info">CSS背景图片 - 如果看到图片则成功</div>
    </div>
    
    <div class="test-section">
        <h2>测试4: 文件存在性检查</h2>
        <div id="file-check" class="status info">检查中...</div>
    </div>
    
    <div class="test-section">
        <h2>测试5: 当前登录页面图片容器</h2>
        <div style="width: 400px; height: 400px; position: relative; border: 2px solid #00ffff; background: rgba(0, 20, 40, 0.3);">
            <!-- 完全复制登录页面的图片显示方式 -->
            <img src="../image/gl1.png" alt="GoldenLedger AI"
                 style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: contain; z-index: 2;"
                 onload="showStatus('login-status', '✅ 登录页面样式图片加载成功', 'success')" 
                 onerror="showStatus('login-status', '❌ 登录页面样式图片加载失败', 'error')">
            
            <!-- 占位符 -->
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 0; color: #00ffff; font-size: 48px;">
                📊
            </div>
        </div>
        <div id="login-status" class="status info">⏳ 加载中...</div>
    </div>
    
    <script>
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            console.log(message);
        }
        
        // 显示当前路径
        document.getElementById('current-path').textContent = window.location.href;
        
        // 检查文件是否存在
        function checkFileExists() {
            const img = new Image();
            img.onload = function() {
                showStatus('file-check', '✅ 图片文件存在且可访问', 'success');
                console.log('图片尺寸:', this.width, 'x', this.height);
            };
            img.onerror = function() {
                showStatus('file-check', '❌ 图片文件不存在或无法访问', 'error');
            };
            img.src = '../image/gl1.png';
        }
        
        // 延迟检查
        setTimeout(checkFileExists, 1000);
        
        // 输出调试信息
        console.log('=== 图片调试信息 ===');
        console.log('当前页面:', window.location.href);
        console.log('相对路径:', '../image/gl1.png');
        console.log('绝对路径:', '/image/gl1.png');
        console.log('协议:', window.location.protocol);
    </script>
</body>
</html>

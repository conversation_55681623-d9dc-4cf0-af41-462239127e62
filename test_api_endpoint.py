#!/usr/bin/env python3
"""
测试API端点
"""
import requests
import json
import time

def test_ai_endpoint():
    """测试AI端点"""
    url = "http://localhost:8000/ai-bookkeeping/natural-language"
    
    test_cases = [
        {
            "text": "购买办公用品1000円",
            "language": "ja",
            "description": "日语购买记录"
        },
        {
            "text": "支付房租50000円",
            "language": "ja", 
            "description": "日语房租支付"
        },
        {
            "text": "收到客户付款30000円",
            "language": "ja",
            "description": "日语收款记录"
        }
    ]
    
    print("🧪 测试AI端点功能")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['description']}")
        print(f"输入: {test_case['text']}")
        
        try:
            start_time = time.time()
            
            response = requests.post(
                url,
                json={
                    "text": test_case["text"],
                    "language": test_case["language"]
                },
                timeout=30
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功 (耗时: {processing_time:.2f}s)")
                
                if result.get("success"):
                    journal_entry = result.get("journal_entry", {})
                    print(f"   借方: {journal_entry.get('debit_account', 'N/A')}")
                    print(f"   贷方: {journal_entry.get('credit_account', 'N/A')}")
                    print(f"   金额: {journal_entry.get('amount', 'N/A')}")
                    print(f"   置信度: {result.get('confidence', 0):.2f}")
                else:
                    print(f"   ❌ 处理失败: {result.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求错误: {str(e)}")
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    test_ai_endpoint()

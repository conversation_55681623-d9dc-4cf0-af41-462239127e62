<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑附件管理测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">编辑附件管理功能测试</h1>
        
        <!-- 记录选择 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">选择记录进行编辑测试</h2>
            <div id="entries-list" class="space-y-2">
                <div class="text-center text-gray-500">加载中...</div>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-medium mb-4">测试结果</h2>
            <div id="test-results" class="space-y-2">
                <div class="text-gray-500">等待测试...</div>
            </div>
        </div>
    </div>

    <script>
        // 多语言配置（简化版）
        const i18n = {
            'zh-CN': {
                'actions.edit_title': '编辑仕訳记录',
                'actions.save': '保存',
                'actions.cancel': '取消',
                'attachments.title': '附件管理',
                'attachments.current': '当前附件',
                'attachments.no_attachments': '暂无附件',
                'attachments.upload_new': '上传新附件',
                'attachments.supported_formats': '支持格式: 图片、PDF、Word、Excel',
                'attachments.upload_button': '上传附件',
                'attachments.preview': '预览',
                'attachments.delete': '删除'
            }
        };
        
        const currentLanguage = 'zh-CN';
        
        function t(key) {
            return i18n[currentLanguage]?.[key] || key;
        }

        // 页面加载时获取记录
        document.addEventListener('DOMContentLoaded', function() {
            loadEntries();
        });

        // 加载记录列表
        async function loadEntries() {
            try {
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                displayEntries(entries);
            } catch (error) {
                console.error('加载记录失败:', error);
                document.getElementById('entries-list').innerHTML = 
                    '<div class="text-red-500">加载记录失败</div>';
            }
        }

        // 显示记录列表
        function displayEntries(entries) {
            const container = document.getElementById('entries-list');
            
            if (entries.length === 0) {
                container.innerHTML = '<div class="text-gray-500">暂无记录</div>';
                return;
            }

            const html = entries.map(entry => `
                <div class="flex items-center justify-between p-3 border rounded hover:bg-gray-50">
                    <div>
                        <div class="font-medium">${entry.id}</div>
                        <div class="text-sm text-gray-600">${entry.description || 'N/A'}</div>
                        <div class="text-sm text-gray-500">金额: ¥${entry.amount || 0}</div>
                    </div>
                    <button onclick="testEditWithAttachment('${entry.id}')" 
                            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        测试编辑
                    </button>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 测试编辑功能（包含附件管理）
        async function testEditWithAttachment(entryId) {
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '<div class="text-blue-600">开始测试编辑功能...</div>';
            
            try {
                // 1. 获取记录详情
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                const entry = entries.find(e => e.id === entryId);
                
                if (!entry) {
                    throw new Error('记录不存在');
                }
                
                addTestResult('✅ 获取记录详情成功', 'text-green-600');
                
                // 2. 测试获取附件列表
                const attachmentsResponse = await fetch(`/attachments/${entryId}`);
                const attachments = await attachmentsResponse.json();
                
                addTestResult(`✅ 获取附件列表成功 (${attachments.length} 个附件)`, 'text-green-600');
                
                // 3. 创建编辑模态框（模拟）
                addTestResult('✅ 创建编辑模态框', 'text-green-600');
                
                // 4. 测试上传附件
                await testUploadAttachment(entryId);
                
                // 5. 重新获取附件列表验证上传
                const newAttachmentsResponse = await fetch(`/attachments/${entryId}`);
                const newAttachments = await newAttachmentsResponse.json();
                
                addTestResult(`✅ 上传后附件数量: ${newAttachments.length}`, 'text-green-600');
                
                // 6. 测试预览附件（如果有的话）
                if (newAttachments.length > 0) {
                    const previewResponse = await fetch(`/attachments/${entryId}?preview=true`);
                    if (previewResponse.ok) {
                        addTestResult('✅ 附件预览功能正常', 'text-green-600');
                    } else {
                        addTestResult('❌ 附件预览失败', 'text-red-600');
                    }
                }
                
                // 7. 测试删除附件（如果有的话）
                if (newAttachments.length > 0) {
                    await testDeleteAttachment(entryId, newAttachments[0].filename);
                }
                
                addTestResult('🎉 编辑附件管理功能测试完成！', 'text-green-600 font-bold');
                
            } catch (error) {
                addTestResult(`❌ 测试失败: ${error.message}`, 'text-red-600');
                console.error('测试失败:', error);
            }
        }
        
        // 测试上传附件
        async function testUploadAttachment(entryId) {
            try {
                // 创建测试文件
                const testContent = `测试附件文件\n记录ID: ${entryId}\n时间: ${new Date().toISOString()}`;
                const blob = new Blob([testContent], { type: 'text/plain' });
                
                const formData = new FormData();
                formData.append('file', blob, 'test_attachment.txt');
                formData.append('entry_id', entryId);
                
                const response = await fetch('/upload-attachment', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addTestResult(`✅ 附件上传成功: ${result.filename}`, 'text-green-600');
                } else {
                    throw new Error('上传失败');
                }
                
            } catch (error) {
                addTestResult(`❌ 附件上传失败: ${error.message}`, 'text-red-600');
                throw error;
            }
        }
        
        // 测试删除附件
        async function testDeleteAttachment(entryId, filename) {
            try {
                const response = await fetch(`/attachments/${entryId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ filename: filename })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addTestResult(`✅ 附件删除成功: ${result.deleted_file}`, 'text-green-600');
                } else {
                    throw new Error('删除失败');
                }
                
            } catch (error) {
                addTestResult(`❌ 附件删除失败: ${error.message}`, 'text-red-600');
                throw error;
            }
        }
        
        // 添加测试结果
        function addTestResult(message, className = 'text-gray-700') {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = className;
            resultDiv.textContent = message;
            resultsContainer.appendChild(resultDiv);
        }
    </script>
</body>
</html>

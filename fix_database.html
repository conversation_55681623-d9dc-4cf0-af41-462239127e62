<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复数据库</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-red-600">🔧 修复数据库结构</h1>
        
        <!-- 问题说明 -->
        <div class="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>问题:</strong> journal_entries表缺少user_id字段，导致多用户查询失败 (500错误)
                    </p>
                </div>
            </div>
        </div>

        <!-- 修复步骤 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 修复步骤</h2>
            <div class="space-y-4">
                <button onclick="step1()" class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    步骤1: 运行数据库迁移
                </button>
                <button onclick="step2()" class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    步骤2: 重置会话表
                </button>
                <button onclick="step3()" class="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    步骤3: 测试API连接
                </button>
                <button onclick="step4()" class="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    步骤4: 清除本地存储
                </button>
            </div>
        </div>

        <!-- 一键修复 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">⚡ 一键修复</h2>
            <button onclick="fixAll()" class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                执行完整修复流程
            </button>
        </div>

        <!-- 结果显示 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 修复结果</h2>
            <div id="results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始修复...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        function addResult(title, success, message, data = null) {
            const container = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
                ${data ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto max-h-32 overflow-y-auto">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 步骤1: 运行数据库迁移
        async function step1() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/force-migrate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    addResult('强制数据库迁移', true, '数据库结构修复成功', result);
                } else {
                    addResult('强制数据库迁移', false, result.error || '数据库迁移失败', result);
                }
            } catch (error) {
                addResult('强制数据库迁移', false, error.message);
            }
        }

        // 步骤2: 重置会话表
        async function step2() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/fix-sessions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'reset_sessions'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('重置会话表', true, '会话表重置成功', result);
                } else {
                    addResult('重置会话表', false, result.error || '会话表重置失败', result);
                }
            } catch (error) {
                addResult('重置会话表', false, error.message);
            }
        }

        // 步骤3: 测试API连接
        async function step3() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`);
                const result = await response.json();
                
                if (response.ok) {
                    addResult('API连接测试', true, `API正常，版本: ${result.version}`, result);
                } else {
                    addResult('API连接测试', false, 'API连接失败', result);
                }
            } catch (error) {
                addResult('API连接测试', false, error.message);
            }
        }

        // 步骤4: 清除本地存储
        function step4() {
            try {
                localStorage.removeItem('goldenledger_session_token');
                localStorage.removeItem('goldenledger_user');
                addResult('清除本地存储', true, '本地存储已清除');
            } catch (error) {
                addResult('清除本地存储', false, error.message);
            }
        }

        // 一键修复所有问题
        async function fixAll() {
            addResult('开始修复', true, '开始执行完整修复流程...');
            
            // 按顺序执行所有步骤
            await step1();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            
            await step2();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step3();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            step4();
            
            addResult('修复完成', true, '所有修复步骤已完成，请重新登录测试');
            
            // 提示用户下一步操作
            setTimeout(() => {
                if (confirm('修复完成！是否立即跳转到登录页面？')) {
                    window.location.href = '/login_simple.html?return=' + encodeURIComponent('/journal_entries');
                }
            }, 2000);
        }

        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            addResult('系统检查', false, '检测到数据库结构问题，需要执行修复');
            addResult('问题详情', false, 'journal_entries表缺少user_id字段，导致多用户查询失败');
        });
    </script>
</body>
</html>

// Cloudflare Pages Functions Middleware
// Handle environment variable injection for production

export async function onRequest(context) {
  const { request, env, next } = context;
  const url = new URL(request.url);

  // 管理员页面访问控制 - 完全禁用服务端重定向
  // 所有认证检查由客户端处理，避免服务端客户端冲突
  if (false && url.pathname.includes('admin_usage_management')) {
    // 此代码块已禁用
  }

  // Handle env-config.js requests dynamically
  if (url.pathname === '/env-config.js') {
    console.log('🔑 Serving dynamic env-config.js');

    const apiKey = env.GEMINI_API_KEY || 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER';

    const envConfigContent = `
// Environment Configuration for Cloudflare Pages
// This file handles secure API key injection from environment variables only
// Version: 3.6.7 - Dynamic Injection

(function() {
    if (typeof window !== 'undefined') {
        // Force clear any cached API keys
        console.log('🔄 Clearing cached API configuration...');

        // Check if we're in local development
        const isLocalDevelopment = window.location.hostname === 'localhost' ||
                                  window.location.hostname === '127.0.0.1' ||
                                  window.location.hostname === '';

        if (isLocalDevelopment) {
            // Local development: Check for manually set key in localStorage
            const localKey = localStorage.getItem('gemini_api_key_dev');
            if (localKey && localKey !== 'YOUR_API_KEY_HERE') {
                window.GEMINI_API_KEY = localKey;
                console.log('🔑 Local development API key loaded from localStorage');
                console.log('🔑 API Key prefix:', localKey.substring(0, 10) + '...');
            } else {
                // No API key available for local development
                window.GEMINI_API_KEY = null;
                console.warn('⚠️ No API key found for local development. Please set it manually.');
                console.log('💡 Use: localStorage.setItem("gemini_api_key_dev", "YOUR_API_KEY")');
            }
        } else {
            // Production: Injected by Cloudflare Pages Functions
            window.GEMINI_API_KEY = '${apiKey}';
            console.log('🔑 Production API key loaded via Functions - Version 3.6.7');
            ${apiKey !== 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER' ?
              `console.log('🔑 API Key prefix:', window.GEMINI_API_KEY.substring(0, 10) + '...');` :
              `console.warn('⚠️ API key placeholder not replaced - check environment variables');`
            }
        }

        // Verify API key is set correctly
        if (window.GEMINI_API_KEY && window.GEMINI_API_KEY !== 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER') {
            console.log('✅ API Key successfully configured');
        } else {
            console.error('❌ API Key configuration failed');
        }
    }
})();
`;

    return new Response(envConfigContent, {
      headers: {
        'Content-Type': 'application/javascript',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }

  // Get the response from the next middleware/page
  const response = await next();

  // Log environment variable status for debugging
  if (env.GEMINI_API_KEY) {
    console.log('🔑 GEMINI_API_KEY available in Functions environment');
  } else {
    console.warn('⚠️ GEMINI_API_KEY not found in Functions environment');
  }

  return response;
}

// Cloudflare Pages Function: Environment Status API
// Provides API key status for debugging

export async function onRequest(context) {
  const { env } = context;
  
  // Check environment variables
  const hasApiKey = !!env.GEMINI_API_KEY;
  const apiKeyPrefix = hasApiKey ? env.GEMINI_API_KEY.substring(0, 10) + '...' : 'Not set';
  
  const status = {
    timestamp: new Date().toISOString(),
    environment: 'production',
    cloudflare_pages: true,
    api_key_configured: hasApiKey,
    api_key_prefix: hasApiKey ? apiKeyPrefix : null,
    api_key_length: hasApiKey ? env.GEMINI_API_KEY.length : 0,
    environment_variables: {
      CF_PAGES: !!process.env.CF_PAGES,
      GEMINI_API_KEY: hasApiKey
    }
  };
  
  return new Response(JSON.stringify(status, null, 2), {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}

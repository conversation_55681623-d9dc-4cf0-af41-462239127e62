/**
 * 服务器端使用量跟踪API
 * 基于Cloudflare D1数据库存储
 * 防止客户端绕过限制，记录详细的使用数据
 */

export async function onRequestPost(context) {
    const { request, env } = context;
    
    try {
        const { action, userIP, userAgent, sessionId, messageType, agentType } = await request.json();

        // 获取用户真实IP
        const clientIP = request.headers.get('CF-Connecting-IP') ||
                        request.headers.get('X-Forwarded-For') ||
                        userIP || 'unknown';

        // 创建用户哈希标识
        const userIPHash = hashString(clientIP);
        const userAgentHash = userAgent ? hashString(userAgent) : null;
        const today = getTodayKey();

        switch (action) {
            case 'check':
                return await checkUsageLimit(env, userIPHash, userAgentHash, today);

            case 'increment':
                return await incrementUsage(env, userIPHash, userAgentHash, today, sessionId, agentType);

            case 'reset':
                return await resetUsage(env, userIPHash, today);

            case 'session_start':
                return await startChatSession(env, userIPHash, userAgentHash, sessionId);

            case 'session_end':
                return await endChatSession(env, sessionId);

            case 'log_message':
                return await logMessage(env, sessionId, userIPHash, messageType, agentType);

            default:
                return new Response(JSON.stringify({ error: 'Invalid action' }), {
                    status: 400,
                    headers: { 'Content-Type': 'application/json' }
                });
        }
        
    } catch (error) {
        console.error('Usage tracker error:', error);
        return new Response(JSON.stringify({ 
            error: 'Internal server error',
            details: error.message 
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 检查使用量限制
async function checkUsageLimit(env, userIPHash, userAgentHash, today) {
    try {
        // 查询今日使用量
        const usageQuery = await env.DB.prepare(`
            SELECT chat_count, plan_type, first_usage_time, last_usage_time
            FROM usage_tracking
            WHERE user_ip_hash = ? AND usage_date = ?
        `).bind(userIPHash, today).first();

        const usage = usageQuery || {
            chat_count: 0,
            plan_type: 'FREE',
            first_usage_time: null,
            last_usage_time: null
        };

        // 获取用户计划限制
        const userPlan = await getUserPlan(env, userIPHash);
        const dailyLimit = userPlan.daily_limit;

        const result = {
            allowed: usage.chat_count < dailyLimit,
            count: usage.chat_count,
            limit: dailyLimit,
            planName: userPlan.plan_type,
            resetTime: getNextResetTime(),
            userKey: userIPHash.substring(0, 8) // 只返回前8位用于显示
        };

        console.log(`📊 Usage check for ${userIPHash.substring(0, 8)}: ${usage.chat_count}/${dailyLimit}`);

        return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('Check usage error:', error);
        return new Response(JSON.stringify({ error: 'Failed to check usage' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 增加使用量
async function incrementUsage(env, userIPHash, userAgentHash, today, sessionId, agentType = 'bookkeeping') {
    try {
        // 获取用户计划限制
        const userPlan = await getUserPlan(env, userIPHash);

        // 检查当前使用量
        const currentUsage = await env.DB.prepare(`
            SELECT chat_count FROM usage_tracking
            WHERE user_ip_hash = ? AND usage_date = ?
        `).bind(userIPHash, today).first();

        const currentCount = currentUsage ? currentUsage.chat_count : 0;

        // 检查是否超过限制
        if (currentCount >= userPlan.daily_limit) {
            return new Response(JSON.stringify({
                error: 'Daily limit exceeded',
                count: currentCount,
                limit: userPlan.daily_limit
            }), {
                status: 429,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        const now = new Date().toISOString();

        // 使用UPSERT更新或插入使用量记录
        await env.DB.prepare(`
            INSERT INTO usage_tracking (
                user_ip_hash, user_agent_hash, usage_date, chat_count,
                plan_type, first_usage_time, last_usage_time, updated_at
            ) VALUES (?, ?, ?, 1, ?, ?, ?, ?)
            ON CONFLICT(user_ip_hash, usage_date) DO UPDATE SET
                chat_count = chat_count + 1,
                last_usage_time = ?,
                updated_at = ?
        `).bind(
            userIPHash, userAgentHash, today, userPlan.plan_type,
            now, now, now, now, now
        ).run();

        // 记录消息到聊天记录
        if (sessionId) {
            await logMessage(env, sessionId, userIPHash, 'user', agentType);
        }

        const newCount = currentCount + 1;
        console.log(`📈 Usage incremented for ${userIPHash.substring(0, 8)}: ${newCount}/${userPlan.daily_limit}`);

        return new Response(JSON.stringify({
            success: true,
            count: newCount,
            limit: userPlan.daily_limit,
            remaining: userPlan.daily_limit - newCount
        }), {
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('Increment usage error:', error);
        return new Response(JSON.stringify({ error: 'Failed to increment usage' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 重置使用量（管理员功能）
async function resetUsage(env, storageKey, clientIP) {
    try {
        await env.USAGE_KV.delete(storageKey);
        
        console.log(`🔄 Usage reset for ${hashIP(clientIP)}`);
        
        return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' }
        });
        
    } catch (error) {
        console.error('Reset usage error:', error);
        return new Response(JSON.stringify({ error: 'Failed to reset usage' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 获取用户计划
async function getUserPlan(env, userIPHash) {
    try {
        // 查询用户计划
        const userPlan = await env.DB.prepare(`
            SELECT plan_type, daily_limit, monthly_limit, is_active
            FROM user_plans
            WHERE user_ip_hash = ? AND is_active = 1
        `).bind(userIPHash).first();

        if (userPlan) {
            return userPlan;
        }

        // 如果没有找到用户计划，创建默认的免费计划
        await env.DB.prepare(`
            INSERT OR IGNORE INTO user_plans (user_ip_hash, plan_type, daily_limit)
            VALUES (?, 'FREE', 20)
        `).bind(userIPHash).run();

        return {
            plan_type: 'FREE',
            daily_limit: 20,
            monthly_limit: null,
            is_active: 1
        };

    } catch (error) {
        console.error('Failed to get user plan:', error);
        // 返回默认免费计划
        return {
            plan_type: 'FREE',
            daily_limit: 20,
            monthly_limit: null,
            is_active: 1
        };
    }
}

// 开始聊天会话
async function startChatSession(env, userIPHash, userAgentHash, sessionId) {
    try {
        const userPlan = await getUserPlan(env, userIPHash);
        const now = new Date().toISOString();

        await env.DB.prepare(`
            INSERT OR REPLACE INTO chat_sessions (
                session_id, user_ip_hash, user_agent_hash, plan_type,
                started_at, last_activity, status
            ) VALUES (?, ?, ?, ?, ?, ?, 'active')
        `).bind(sessionId, userIPHash, userAgentHash, userPlan.plan_type, now, now).run();

        console.log(`🚀 Chat session started: ${sessionId}`);

        return new Response(JSON.stringify({ success: true, sessionId }), {
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('Start session error:', error);
        return new Response(JSON.stringify({ error: 'Failed to start session' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 结束聊天会话
async function endChatSession(env, sessionId) {
    try {
        const now = new Date().toISOString();

        await env.DB.prepare(`
            UPDATE chat_sessions
            SET ended_at = ?, status = 'ended', last_activity = ?
            WHERE session_id = ?
        `).bind(now, now, sessionId).run();

        console.log(`🏁 Chat session ended: ${sessionId}`);

        return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('End session error:', error);
        return new Response(JSON.stringify({ error: 'Failed to end session' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 记录聊天消息
async function logMessage(env, sessionId, userIPHash, messageType, agentType = 'bookkeeping') {
    try {
        const now = new Date().toISOString();

        // 记录消息
        await env.DB.prepare(`
            INSERT INTO chat_messages (
                session_id, user_ip_hash, message_type, agent_type, created_at
            ) VALUES (?, ?, ?, ?, ?)
        `).bind(sessionId, userIPHash, messageType, agentType, now).run();

        // 更新会话的消息计数和最后活动时间
        await env.DB.prepare(`
            UPDATE chat_sessions
            SET message_count = message_count + 1, last_activity = ?
            WHERE session_id = ?
        `).bind(now, sessionId).run();

        return new Response(JSON.stringify({ success: true }), {
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('Log message error:', error);
        return new Response(JSON.stringify({ error: 'Failed to log message' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 创建用户唯一标识
function createUserKey(ip, userAgent) {
    const combined = `${ip}_${userAgent || 'unknown'}`;
    return hashString(combined);
}

// 哈希字符串
function hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
}

// 哈希IP（用于日志显示）
function hashIP(ip) {
    return hashString(ip).substring(0, 8);
}

// 获取今天的键
function getTodayKey() {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
}

// 获取下次重置时间
function getNextResetTime() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow.getTime();
}

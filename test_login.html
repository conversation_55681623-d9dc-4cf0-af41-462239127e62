<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>テストログイン - GoldenLedger AI記帳システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Noto Sans JP', sans-serif;
        }
        
        .tech-bg {
            background: 
                linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 20, 40, 0.8) 100%),
                url('image/gl1.png');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }
        
        .tech-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: techPulse 4s ease-in-out infinite alternate;
        }
        
        @keyframes techPulse {
            0% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }
        
        .cyber-glass {
            backdrop-filter: blur(20px);
            background: rgba(0, 20, 40, 0.15);
            border: 1px solid rgba(120, 219, 255, 0.3);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .cyber-glass::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(120, 219, 255, 0.8), transparent);
            animation: scanLine 3s linear infinite;
        }
        
        @keyframes scanLine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .neon-text {
            color: #78dbff;
            text-shadow: 
                0 0 5px rgba(120, 219, 255, 0.5),
                0 0 10px rgba(120, 219, 255, 0.3),
                0 0 15px rgba(120, 219, 255, 0.2);
        }
        
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(120, 219, 255, 0.6);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="tech-bg min-h-screen flex items-center justify-center p-4 relative">
    <!-- 浮動粒子効果 -->
    <div class="floating-particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 2.5s;"></div>
    </div>
    
    <div class="w-full max-w-md relative z-10">
        <!-- テストカード -->
        <div class="cyber-glass rounded-3xl p-10 shadow-2xl">
            <!-- ロゴとタイトル -->
            <div class="text-center mb-10">
                <div class="w-24 h-24 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl relative">
                    <i class="fas fa-coins text-4xl text-white"></i>
                    <div class="absolute inset-0 rounded-full border-2 border-cyan-400 animate-ping opacity-20"></div>
                </div>
                <h1 class="text-4xl font-bold neon-text mb-3">GoldenLedger</h1>
                <p class="text-cyan-200 text-lg font-light">AI記帳システム</p>
                <div class="mt-4 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></div>
            </div>

            <!-- テスト情報 -->
            <div class="space-y-6">
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-cyan-300 mb-4">
                        <i class="fas fa-flask mr-2"></i>テストページ
                    </h2>
                    <p class="text-cyan-200 mb-6">
                        新しいデザインが正常に表示されているかテストしています
                    </p>
                </div>

                <!-- 機能テスト -->
                <div class="space-y-4">
                    <div class="bg-cyan-900/20 border border-cyan-400/30 rounded-xl p-4">
                        <h3 class="text-cyan-300 font-semibold mb-2">
                            <i class="fas fa-check-circle mr-2"></i>日本語表示
                        </h3>
                        <p class="text-cyan-200 text-sm">すべてのテキストが日本語で表示されています</p>
                    </div>

                    <div class="bg-cyan-900/20 border border-cyan-400/30 rounded-xl p-4">
                        <h3 class="text-cyan-300 font-semibold mb-2">
                            <i class="fas fa-check-circle mr-2"></i>背景画像
                        </h3>
                        <p class="text-cyan-200 text-sm">gl1.png が背景として使用されています</p>
                    </div>

                    <div class="bg-cyan-900/20 border border-cyan-400/30 rounded-xl p-4">
                        <h3 class="text-cyan-300 font-semibold mb-2">
                            <i class="fas fa-check-circle mr-2"></i>高科技効果
                        </h3>
                        <p class="text-cyan-200 text-sm">粒子アニメーション、スキャンライン、ネオン効果</p>
                    </div>
                </div>

                <!-- 戻るボタン -->
                <div class="mt-8 text-center">
                    <a href="auth/login.html" class="inline-flex items-center bg-cyan-600/20 hover:bg-cyan-600/30 text-cyan-300 px-6 py-3 rounded-xl border border-cyan-400/50 transition-all duration-300">
                        <i class="fas fa-arrow-left mr-2"></i>
                        実際のログインページへ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 現在時刻を表示
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const timeString = now.toLocaleString('ja-JP');
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'fixed bottom-4 right-4 text-cyan-300/70 text-sm';
            timeDiv.innerHTML = `<i class="fas fa-clock mr-1"></i>更新時刻: ${timeString}`;
            document.body.appendChild(timeDiv);
        });
    </script>
</body>
</html>

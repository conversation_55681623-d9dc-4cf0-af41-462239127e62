#!/usr/bin/env python3
"""
GoldenLedger — Smart AI-Powered Finance System - 系统状态检查脚本
检查所有功能模块和API端点的可用性
"""
import requests
import json
import sys
from datetime import datetime

# 系统配置
BASE_URL = "http://localhost:8000"
TIMEOUT = 10

# 颜色定义
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_header():
    """打印系统检查头部信息"""
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("=" * 70)
    print("  GoldenLedger — Smart AI-Powered Finance System - 系统状态检查")
    print("  System Health Check & Validation")
    print("=" * 70)
    print(f"{Colors.END}")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"基础URL: {BASE_URL}")
    print()

def check_endpoint(url, description, method="GET", data=None):
    """检查单个端点"""
    try:
        if method == "GET":
            response = requests.get(url, timeout=TIMEOUT)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=TIMEOUT)
        
        if response.status_code == 200:
            print(f"{Colors.GREEN}✓{Colors.END} {description}")
            return True
        else:
            print(f"{Colors.RED}✗{Colors.END} {description} (状态码: {response.status_code})")
            return False
    except requests.exceptions.RequestException as e:
        print(f"{Colors.RED}✗{Colors.END} {description} (错误: {str(e)})")
        return False

def check_html_pages():
    """检查HTML页面"""
    print(f"{Colors.BLUE}{Colors.BOLD}📄 HTML页面检查{Colors.END}")
    print("-" * 50)
    
    pages = [
        ("master_dashboard.html", "主控制台"),
        ("ai_demo.html", "AI智能演示"),
        ("multilingual_interface.html", "多语言界面"),
        ("batch_import.html", "批量导入工具"),
        ("advanced_analytics.html", "高级数据分析"),
        ("financial_reports.html", "财务报表系统"),
        ("ai_audit_system.html", "AI智能审计"),
        ("system_monitor.html", "系统监控中心"),
        ("realtime_monitoring.html", "实时通信系统"),
        ("user_management.html", "用户管理系统"),
        ("backup_management.html", "备份管理系统"),
        ("performance_monitoring.html", "性能监控系统"),
        ("system_overview.html", "系统概览页面"),
        ("api_documentation.html", "API文档系统"),
        ("journal_entries.html", "记账记录查看"),
        ("ai_test_simple.html", "AI功能测试")
    ]
    
    success_count = 0
    for page, description in pages:
        url = f"{BASE_URL}/{page}"
        if check_endpoint(url, f"{description} ({page})"):
            success_count += 1
    
    print(f"\n页面检查结果: {success_count}/{len(pages)} 成功")
    print()
    return success_count, len(pages)

def check_api_endpoints():
    """检查API端点"""
    print(f"{Colors.PURPLE}{Colors.BOLD}🔌 API端点检查{Colors.END}")
    print("-" * 50)
    
    endpoints = [
        ("/", "根路径"),
        ("/health", "健康检查"),
        ("/docs", "Swagger文档"),
        ("/api/system/health", "系统健康检查"),
        ("/api/system/performance", "系统性能指标"),
        ("/api/system/services", "系统服务状态"),
        ("/ws/stats", "WebSocket统计")
    ]
    
    success_count = 0
    for endpoint, description in endpoints:
        url = f"{BASE_URL}{endpoint}"
        if check_endpoint(url, f"{description} ({endpoint})"):
            success_count += 1
    
    print(f"\nAPI检查结果: {success_count}/{len(endpoints)} 成功")
    print()
    return success_count, len(endpoints)

def check_system_health():
    """检查系统健康状态"""
    print(f"{Colors.GREEN}{Colors.BOLD}🏥 系统健康状态{Colors.END}")
    print("-" * 50)
    
    try:
        # 检查系统健康
        response = requests.get(f"{BASE_URL}/api/system/health", timeout=TIMEOUT)
        if response.status_code == 200:
            health_data = response.json()
            print(f"{Colors.GREEN}✓{Colors.END} 系统状态: {health_data.get('status', 'unknown')}")
            
            # 显示系统信息
            system_info = health_data.get('system', {})
            if system_info:
                cpu_info = system_info.get('cpu', {})
                memory_info = system_info.get('memory', {})
                disk_info = system_info.get('disk', {})
                
                print(f"  CPU使用率: {cpu_info.get('usage_percent', 0):.1f}%")
                print(f"  内存使用率: {memory_info.get('percent', 0):.1f}%")
                print(f"  磁盘使用率: {disk_info.get('percent', 0):.1f}%")
        else:
            print(f"{Colors.RED}✗{Colors.END} 无法获取系统健康状态")
            
        # 检查性能指标
        response = requests.get(f"{BASE_URL}/api/system/performance", timeout=TIMEOUT)
        if response.status_code == 200:
            perf_data = response.json()
            metrics = perf_data.get('metrics', {})
            print(f"{Colors.GREEN}✓{Colors.END} 性能指标可用")
            print(f"  平均响应时间: {metrics.get('avg_response_time', 0)}ms")
            print(f"  活跃用户数: {metrics.get('active_users', 0)}")
            print(f"  缓存命中率: {metrics.get('cache_hit_rate', 0)}%")
        else:
            print(f"{Colors.RED}✗{Colors.END} 无法获取性能指标")
            
        # 检查服务状态
        response = requests.get(f"{BASE_URL}/api/system/services", timeout=TIMEOUT)
        if response.status_code == 200:
            services_data = response.json()
            services = services_data.get('services', {})
            print(f"{Colors.GREEN}✓{Colors.END} 服务状态可用")
            
            for service_name, service_info in services.items():
                status = service_info.get('status', 'unknown')
                if status in ['running', 'active', 'connected', 'available']:
                    print(f"  {service_name}: {Colors.GREEN}{status}{Colors.END}")
                else:
                    print(f"  {service_name}: {Colors.YELLOW}{status}{Colors.END}")
        else:
            print(f"{Colors.RED}✗{Colors.END} 无法获取服务状态")
            
    except Exception as e:
        print(f"{Colors.RED}✗{Colors.END} 系统健康检查失败: {str(e)}")
    
    print()

def check_websocket():
    """检查WebSocket功能"""
    print(f"{Colors.CYAN}{Colors.BOLD}📡 WebSocket功能检查{Colors.END}")
    print("-" * 50)
    
    try:
        # 检查WebSocket统计
        response = requests.get(f"{BASE_URL}/ws/stats", timeout=TIMEOUT)
        if response.status_code == 200:
            ws_data = response.json()
            print(f"{Colors.GREEN}✓{Colors.END} WebSocket统计可用")
            print(f"  活跃连接: {ws_data.get('active_connections', 0)}")
            print(f"  总连接数: {ws_data.get('total_connections', 0)}")
            print(f"  消息发送: {ws_data.get('messages_sent', 0)}")
        else:
            print(f"{Colors.RED}✗{Colors.END} WebSocket统计不可用")
    except Exception as e:
        print(f"{Colors.RED}✗{Colors.END} WebSocket检查失败: {str(e)}")
    
    print()

def print_summary(html_success, html_total, api_success, api_total):
    """打印检查摘要"""
    print(f"{Colors.BOLD}📊 检查摘要{Colors.END}")
    print("=" * 50)
    
    total_success = html_success + api_success
    total_items = html_total + api_total
    success_rate = (total_success / total_items * 100) if total_items > 0 else 0
    
    print(f"HTML页面: {html_success}/{html_total} ({html_success/html_total*100:.1f}%)")
    print(f"API端点: {api_success}/{api_total} ({api_success/api_total*100:.1f}%)")
    print(f"总体成功率: {total_success}/{total_items} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        status_color = Colors.GREEN
        status_text = "优秀"
    elif success_rate >= 70:
        status_color = Colors.YELLOW
        status_text = "良好"
    else:
        status_color = Colors.RED
        status_text = "需要关注"
    
    print(f"系统状态: {status_color}{status_text}{Colors.END}")
    print()
    
    # 访问链接
    print(f"{Colors.BOLD}🔗 快速访问链接{Colors.END}")
    print("-" * 30)
    print(f"主控制台: {BASE_URL}/master_dashboard.html")
    print(f"系统概览: {BASE_URL}/system_overview.html")
    print(f"API文档: {BASE_URL}/docs")
    print(f"性能监控: {BASE_URL}/performance_monitoring.html")
    print()

def main():
    """主函数"""
    print_header()
    
    # 检查HTML页面
    html_success, html_total = check_html_pages()
    
    # 检查API端点
    api_success, api_total = check_api_endpoints()
    
    # 检查系统健康状态
    check_system_health()
    
    # 检查WebSocket功能
    check_websocket()
    
    # 打印摘要
    print_summary(html_success, html_total, api_success, api_total)
    
    # 返回退出码
    total_success = html_success + api_success
    total_items = html_total + api_total
    success_rate = (total_success / total_items * 100) if total_items > 0 else 0
    
    if success_rate >= 90:
        sys.exit(0)  # 成功
    elif success_rate >= 70:
        sys.exit(1)  # 警告
    else:
        sys.exit(2)  # 错误

if __name__ == "__main__":
    main()

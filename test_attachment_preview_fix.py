#!/usr/bin/env python3
"""
测试附件预览功能修复
"""
import requests
import json

def test_attachment_preview_fix():
    """测试附件预览功能修复"""
    
    print("🔧 测试附件预览功能修复")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 获取有附件的记录
        print("\n📋 步骤1: 获取有附件的记录")
        entries_response = requests.get(f"{base_url}/journal-entries/default")
        
        if entries_response.status_code == 200:
            entries = entries_response.json()
            print(f"✅ 获取记录成功，总数: {len(entries)}")
            
            # 查找有附件的记录
            entries_with_attachments = [e for e in entries if e.get('attachment_path')]
            print(f"  有附件的记录数: {len(entries_with_attachments)}")
            
            if not entries_with_attachments:
                print("⚠️ 没有找到有附件的记录，无法测试预览功能")
                return False
            
            # 使用第一个有附件的记录进行测试
            test_entry = entries_with_attachments[0]
            entry_id = test_entry['id']
            attachment_path = test_entry['attachment_path']
            
            print(f"  测试记录ID: {entry_id}")
            print(f"  附件路径: {attachment_path}")
            
        else:
            print(f"❌ 获取记录失败: {entries_response.status_code}")
            return False
        
        # 2. 测试旧的附件API（列表模式）
        print(f"\n📄 步骤2: 测试附件列表API")
        list_response = requests.get(f"{base_url}/attachments/{entry_id}")
        
        if list_response.status_code == 200:
            attachments = list_response.json()
            print("✅ 附件列表API正常")
            print(f"  返回类型: {type(attachments)}")
            if isinstance(attachments, list) and attachments:
                print(f"  附件数量: {len(attachments)}")
                print(f"  第一个附件: {attachments[0].get('filename', 'N/A')}")
            else:
                print("  返回空列表或非列表格式")
        else:
            print(f"❌ 附件列表API失败: {list_response.status_code}")
        
        # 3. 测试新的JSON预览API
        print(f"\n👁️ 步骤3: 测试JSON预览API")
        json_preview_response = requests.get(f"{base_url}/attachments/{entry_id}?json_preview=true")
        
        if json_preview_response.status_code == 200:
            try:
                preview_data = json_preview_response.json()
                print("✅ JSON预览API正常")
                print(f"  成功标志: {preview_data.get('success', False)}")
                print(f"  文件名: {preview_data.get('filename', 'N/A')}")
                print(f"  内容类型: {preview_data.get('content_type', 'N/A')}")
                print(f"  文件大小: {preview_data.get('size', 0)} bytes")
                
                # 检查base64内容
                file_content = preview_data.get('file_content', '')
                if file_content:
                    print(f"  Base64内容长度: {len(file_content)} 字符")
                    print(f"  Base64前缀: {file_content[:50]}...")
                else:
                    print("  ❌ 没有Base64内容")
                
                return True
                
            except json.JSONDecodeError:
                print("❌ JSON预览API返回非JSON格式")
                print(f"响应内容: {json_preview_response.text[:200]}...")
                return False
        else:
            print(f"❌ JSON预览API失败: {json_preview_response.status_code}")
            print(f"响应: {json_preview_response.text}")
            return False
        
        # 4. 测试文件预览API
        print(f"\n🖼️ 步骤4: 测试文件预览API")
        file_preview_response = requests.get(f"{base_url}/attachments/{entry_id}?preview=true")
        
        if file_preview_response.status_code == 200:
            print("✅ 文件预览API正常")
            print(f"  Content-Type: {file_preview_response.headers.get('content-type', 'N/A')}")
            print(f"  Content-Length: {file_preview_response.headers.get('content-length', 'N/A')}")
            print(f"  Content-Disposition: {file_preview_response.headers.get('content-disposition', 'N/A')}")
        else:
            print(f"❌ 文件预览API失败: {file_preview_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_frontend_preview_function():
    """测试前端预览功能"""
    
    print(f"\n🌐 测试前端预览功能")
    print("=" * 30)
    
    try:
        base_url = "http://localhost:8000"
        
        # 获取记账页面
        response = requests.get(f"{base_url}/journal_entries.html")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查前端预览相关内容
            checks = [
                ("预览函数存在", "function previewAttachment" in content),
                ("JSON预览参数", "json_preview=true" in content),
                ("预览按钮", "プレビュー" in content or "预览" in content),
                ("模态框关闭", "closeAttachmentModal" in content),
                ("Base64显示", "data:${result.content_type};base64" in content),
                ("错误处理", "catch (error)" in content)
            ]
            
            print("前端功能检查:")
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}")
            
            all_checks_passed = all(result for _, result in checks)
            
            if all_checks_passed:
                print("✅ 前端预览功能完整")
            else:
                print("⚠️ 前端预览功能可能有问题")
            
            return all_checks_passed
            
        else:
            print(f"❌ 记账页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端功能测试失败: {e}")
        return False

def test_api_compatibility():
    """测试API兼容性"""
    
    print(f"\n🔄 测试API兼容性")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    
    try:
        # 获取一个有附件的记录ID
        entries_response = requests.get(f"{base_url}/journal-entries/default")
        if entries_response.status_code != 200:
            print("❌ 无法获取记录列表")
            return False
        
        entries = entries_response.json()
        entries_with_attachments = [e for e in entries if e.get('attachment_path')]
        
        if not entries_with_attachments:
            print("⚠️ 没有附件记录可供测试")
            return True
        
        entry_id = entries_with_attachments[0]['id']
        
        # 测试不同的API调用方式
        api_tests = [
            ("默认调用", f"/attachments/{entry_id}"),
            ("列表模式", f"/attachments/{entry_id}?preview=false"),
            ("文件预览", f"/attachments/{entry_id}?preview=true"),
            ("JSON预览", f"/attachments/{entry_id}?json_preview=true"),
            ("混合参数", f"/attachments/{entry_id}?preview=true&json_preview=true")
        ]
        
        print("API兼容性测试:")
        for test_name, endpoint in api_tests:
            try:
                response = requests.get(f"{base_url}{endpoint}")
                status = "✅" if response.status_code == 200 else "❌"
                print(f"  {status} {test_name}: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        print(f"    返回类型: JSON")
                    elif 'image/' in content_type:
                        print(f"    返回类型: 图片 ({content_type})")
                    else:
                        print(f"    返回类型: {content_type}")
                
            except Exception as e:
                print(f"  ❌ {test_name}: 请求失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ API兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始测试附件预览功能修复")
    
    # 测试后端API
    api_success = test_attachment_preview_fix()
    
    # 测试前端功能
    frontend_success = test_frontend_preview_function()
    
    # 测试API兼容性
    compatibility_success = test_api_compatibility()
    
    print("\n" + "=" * 80)
    print("📊 附件预览修复测试结果:")
    
    if api_success and frontend_success and compatibility_success:
        print("🎉 附件预览功能修复完全成功！")
        print("\n✅ 修复内容:")
        print("  - 后端添加了json_preview参数")
        print("  - 返回包含base64内容的JSON格式")
        print("  - 前端使用新的API参数")
        print("  - 删除了重复的预览函数")
        print("  - 保持了API向后兼容性")
        
        print(f"\n🌐 现在可以正常使用:")
        print("  记账页面: http://localhost:8000/journal_entries.html")
        print("  - 点击编辑按钮")
        print("  - 在附件部分点击'プレビュー'按钮")
        print("  - 应该能正常显示附件预览模态框")
        
    else:
        print("❌ 部分功能测试失败")
        if not api_success:
            print("  - 后端API有问题")
        if not frontend_success:
            print("  - 前端功能有问题")
        if not compatibility_success:
            print("  - API兼容性有问题")

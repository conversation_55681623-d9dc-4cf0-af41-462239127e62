#!/usr/bin/env python3
"""
测试OCR API
"""
import requests
import json

def test_ocr_api():
    """测试发票OCR API"""
    
    url = "http://localhost:8000/ai-bookkeeping/invoice-ocr"
    
    # 准备文件
    try:
        with open('test_invoice.png', 'rb') as f:
            files = {
                'invoice': ('test_invoice.png', f, 'image/png')
            }
            data = {
                'company_id': 'default'
            }
            
            print("🧪 测试发票OCR API...")
            print(f"📤 上传文件: test_invoice.png")
            
            response = requests.post(url, files=files, data=data, timeout=60)
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📥 OCR结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                if result.get('success'):
                    print("✅ OCR识别成功!")
                    print(f"   金额: ¥{result.get('amount', 'N/A')}")
                    print(f"   日期: {result.get('date', 'N/A')}")
                    print(f"   商户: {result.get('vendor', 'N/A')}")
                    print(f"   描述: {result.get('description', 'N/A')}")
                    print(f"   置信度: {result.get('confidence', 'N/A')}")
                else:
                    print(f"❌ OCR识别失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except FileNotFoundError:
        print("❌ 测试发票图片不存在，请先运行 create_test_invoice.py")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_ocr_api()

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新規登録 - GoldenLedger | Smart AI-Powered Finance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 背景和音乐控制已禁用以避免重定向问题 -->
    <!-- <script src="background_control_new.js"></script> -->
    <!-- <script src="music_control_new.js"></script> -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .register-card { 
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-field {
            transition: all 0.3s ease;
            border: 2px solid #e1e5e9;
        }
        .input-field:focus {
            border-color: #00d4aa;
            box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
        }
        .password-strength {
            height: 4px;
            border-radius: 2px;
            transition: all 0.3s ease;
        }
        .strength-weak { background-color: #ef4444; width: 33%; }
        .strength-medium { background-color: #f59e0b; width: 66%; }
        .strength-strong { background-color: #10b981; width: 100%; }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <!-- 背景和音乐控制已禁用以避免重定向问题 -->
    <!-- <div id="background-controls"></div> -->
    <!-- <div id="music-controls"></div> -->

    <div class="w-full max-w-md">
        <!-- Logo和标题 -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full shadow-lg mb-4">
                <span class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-purple-600">GL</span>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">GoldenLedger</h1>
            <p class="text-white/80">Smart AI-Powered Finance System</p>
        </div>

        <!-- 注册表单 -->
        <div class="register-card rounded-2xl shadow-2xl p-8">
            <h2 class="text-2xl font-bold text-gray-800 text-center mb-6">新規登録</h2>
            
            <form id="registerForm" class="space-y-6">
                <!-- 姓名输入 -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        お名前
                    </label>
                    <input 
                        type="text" 
                        id="name" 
                        name="name"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="山田太郎"
                        required
                    >
                </div>

                <!-- 邮箱输入 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        メールアドレス
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="<EMAIL>"
                        required
                    >
                </div>

                <!-- 密码输入 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        パスワード
                    </label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="••••••••"
                        required
                    >
                    <!-- 密码强度指示器 -->
                    <div class="mt-2">
                        <div class="password-strength bg-gray-200" id="passwordStrength"></div>
                        <p class="text-xs text-gray-500 mt-1" id="passwordText">
                            8文字以上、大文字・小文字・数字を含む
                        </p>
                    </div>
                </div>

                <!-- 确认密码 -->
                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                        パスワード確認
                    </label>
                    <input 
                        type="password" 
                        id="confirmPassword" 
                        name="confirmPassword"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="••••••••"
                        required
                    >
                </div>

                <!-- 公司名（可选） -->
                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                        会社名（任意）
                    </label>
                    <input 
                        type="text" 
                        id="company" 
                        name="company"
                        class="input-field w-full px-4 py-3 rounded-lg focus:outline-none"
                        placeholder="株式会社サンプル"
                    >
                </div>

                <!-- 同意条款 -->
                <div class="flex items-start">
                    <input type="checkbox" id="terms" class="mt-1 rounded border-gray-300 text-green-500 focus:ring-green-500" required>
                    <label for="terms" class="ml-2 text-sm text-gray-600">
                        <a href="#" class="text-green-600 hover:text-green-700">利用規約</a>
                        および
                        <a href="#" class="text-green-600 hover:text-green-700">プライバシーポリシー</a>
                        に同意します
                    </label>
                </div>

                <!-- 注册按钮 -->
                <button 
                    type="submit" 
                    id="registerBtn"
                    class="btn-primary w-full py-3 px-4 rounded-lg text-white font-semibold focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                    アカウント作成
                </button>


            </form>

            <!-- 分割线 -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <p class="text-center text-sm text-gray-600">
                    すでにアカウントをお持ちの方は
                    <a href="login.html" class="text-green-600 hover:text-green-700 font-semibold">
                        ログイン
                    </a>
                </p>
            </div>

            <!-- 返回首页 -->
            <div class="mt-4 text-center">
                <a href="index.html" class="text-sm text-gray-500 hover:text-gray-700">
                    ← ホームに戻る
                </a>
            </div>
        </div>
    </div>

    <script>
        console.log('New register page loaded - no auth redirects');

        // 密码强度检查
        document.getElementById('password').addEventListener('input', function(e) {
            const password = e.target.value;
            const strengthBar = document.getElementById('passwordStrength');
            const strengthText = document.getElementById('passwordText');
            
            let strength = 0;
            let text = '';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            strengthBar.className = 'password-strength bg-gray-200';
            
            if (strength <= 2) {
                strengthBar.classList.add('strength-weak');
                text = '弱い - より複雑なパスワードを使用してください';
            } else if (strength <= 3) {
                strengthBar.classList.add('strength-medium');
                text = '普通 - もう少し複雑にできます';
            } else {
                strengthBar.classList.add('strength-strong');
                text = '強い - 安全なパスワードです';
            }
            
            strengthText.textContent = text;
        });

        // 表单提交处理
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const company = document.getElementById('company').value;
            const terms = document.getElementById('terms').checked;
            const submitBtn = document.getElementById('registerBtn');
            
            // 验证密码匹配
            if (password !== confirmPassword) {
                alert('❌ パスワードが一致しません');
                return;
            }
            
            // 验证条款同意
            if (!terms) {
                alert('❌ 利用規約に同意してください');
                return;
            }
            
            // 显示加载状态
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '🔄 アカウント作成中...';
            submitBtn.disabled = true;
            
            // 模拟注册处理
            setTimeout(() => {
                // 创建用户数据
                const userData = {
                    name: name,
                    email: email,
                    company: company,
                    loginTime: new Date().toISOString(),
                    sessionId: 'session_' + Date.now()
                };
                
                // 保存到localStorage
                localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
                
                // 显示成功消息
                alert('🎉 アカウントが正常に作成されました！\nダッシュボードに移動します。');
                
                // 跳转到仪表板
                window.location.href = 'master_dashboard.html';
            }, 2000);
        });


    </script>
</body>
</html>

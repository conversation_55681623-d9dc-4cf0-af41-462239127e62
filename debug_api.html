<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-red-600">🔧 API调试工具</h1>
        
        <!-- 请求配置 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">📝 请求配置</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">请求URL</label>
                    <input type="text" id="api-url" value="https://goldenledger-api.souyousann.workers.dev/api/journal-entries"
                           class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">请求方法</label>
                    <select id="api-method" class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                        <option value="OPTIONS">OPTIONS</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-4">
                <label class="block text-sm font-medium mb-2">请求体 (JSON)</label>
                <textarea id="api-body" rows="4" 
                          class="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
                          placeholder='{"key": "value"}'></textarea>
            </div>
            
            <div class="mt-4 flex space-x-4">
                <button onclick="sendRequest()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    发送请求
                </button>
                <button onclick="testAuth()" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                    测试认证
                </button>
                <button onclick="clearResults()" class="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600">
                    清空结果
                </button>
            </div>
        </div>

        <!-- 快速测试按钮 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">⚡ 快速测试</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="quickTest('https://goldenledger-api.souyousann.workers.dev/api/health', 'GET')" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    健康检查
                </button>
                <button onclick="quickTest('https://goldenledger-api.souyousann.workers.dev/api/journal-entries', 'GET')" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    获取分录
                </button>
                <button onclick="quickTest('https://goldenledger-api.souyousann.workers.dev/api/auth/verify', 'GET')" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    验证认证
                </button>
                <button onclick="quickTest('https://goldenledger-api.souyousann.workers.dev/api/journal-entries', 'OPTIONS')" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    CORS检查
                </button>
            </div>
        </div>

        <!-- 响应结果 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 响应结果</h2>
            <div id="response-container" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <script>
        // 添加响应结果
        function addResponse(url, method, status, headers, body, error = null) {
            const container = document.getElementById('response-container');
            const timestamp = new Date().toLocaleTimeString();
            
            const responseDiv = document.createElement('div');
            responseDiv.className = `p-4 border rounded ${status >= 200 && status < 300 ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            const statusColor = status >= 200 && status < 300 ? 'text-green-800' : 'text-red-800';
            
            responseDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${statusColor}">
                        ${method} ${url}
                    </h4>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm ${statusColor}">状态: ${status}</span>
                        <span class="text-sm text-gray-500">${timestamp}</span>
                    </div>
                </div>
                
                ${error ? `<div class="mb-2"><strong class="text-red-600">错误:</strong> ${error}</div>` : ''}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium mb-2">响应头:</h5>
                        <pre class="text-xs p-2 bg-gray-100 rounded overflow-x-auto">${JSON.stringify(headers, null, 2)}</pre>
                    </div>
                    <div>
                        <h5 class="font-medium mb-2">响应体:</h5>
                        <pre class="text-xs p-2 bg-gray-100 rounded overflow-x-auto max-h-32 overflow-y-auto">${typeof body === 'string' ? body : JSON.stringify(body, null, 2)}</pre>
                    </div>
                </div>
            `;
            
            container.appendChild(responseDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('goldenledger_session_token');
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            
            return headers;
        }

        // 发送请求
        async function sendRequest() {
            const url = document.getElementById('api-url').value;
            const method = document.getElementById('api-method').value;
            const bodyText = document.getElementById('api-body').value;
            
            try {
                const options = {
                    method: method,
                    headers: getAuthHeaders()
                };
                
                if (bodyText && (method === 'POST' || method === 'PUT')) {
                    options.body = bodyText;
                }
                
                console.log('发送请求:', url, options);
                
                const response = await fetch(url, options);
                
                // 获取响应头
                const responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });
                
                // 获取响应体
                let responseBody;
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    try {
                        responseBody = await response.json();
                    } catch (e) {
                        responseBody = await response.text();
                    }
                } else {
                    responseBody = await response.text();
                }
                
                addResponse(url, method, response.status, responseHeaders, responseBody);
                
            } catch (error) {
                console.error('请求失败:', error);
                addResponse(url, method, 0, {}, null, error.message);
            }
        }

        // 快速测试
        async function quickTest(url, method) {
            document.getElementById('api-url').value = url;
            document.getElementById('api-method').value = method;
            document.getElementById('api-body').value = '';
            
            await sendRequest();
        }

        // 测试认证
        function testAuth() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');
            
            const authInfo = {
                hasToken: !!token,
                tokenLength: token ? token.length : 0,
                hasUser: !!user,
                userInfo: user ? JSON.parse(user) : null
            };
            
            addResponse('localStorage', 'CHECK', 200, {}, authInfo);
        }

        // 清空结果
        function clearResults() {
            document.getElementById('response-container').innerHTML = '<p class="text-gray-500">结果已清空，可以开始新的测试...</p>';
        }

        // 页面加载时自动测试认证状态
        window.addEventListener('load', function() {
            console.log('API调试工具已加载');
            testAuth();
        });
    </script>
</body>
</html>

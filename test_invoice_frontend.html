<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票上传测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-xl font-bold mb-4">发票上传测试</h1>
        
        <!-- 文件上传 -->
        <div class="mb-4">
            <label for="invoice-upload" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 cursor-pointer flex items-center space-x-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <span>上传发票</span>
            </label>
            <input id="invoice-upload" type="file" accept="image/*,.pdf" class="hidden">
        </div>
        
        <!-- 结果显示 -->
        <div id="result-container" class="hidden">
            <div class="bg-gray-100 p-4 rounded mb-4">
                <h3 class="font-medium mb-2">OCR识别结果:</h3>
                <div id="ocr-result"></div>
                <button id="confirm-btn" 
                        onclick="confirmInvoiceEntry(this)" 
                        class="mt-3 bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                        data-result=''>
                    确认记账
                </button>
            </div>
        </div>
        
        <!-- 状态显示 -->
        <div id="status" class="text-sm text-gray-600"></div>
    </div>

    <script>
        // 文件上传处理
        document.getElementById('invoice-upload').addEventListener('change', async function(e) {
            const file = e.target.files[0];
            if (file) {
                await handleInvoiceUpload(file);
            }
        });

        // 处理发票上传
        async function handleInvoiceUpload(file) {
            const status = document.getElementById('status');
            const resultContainer = document.getElementById('result-container');
            
            status.textContent = '正在上传和识别...';
            resultContainer.classList.add('hidden');
            
            try {
                const formData = new FormData();
                formData.append('invoice', file);
                formData.append('company_id', 'default');
                
                const response = await fetch('/ai-bookkeeping/invoice-ocr', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    status.textContent = 'OCR识别成功！';
                    
                    // 显示结果
                    const ocrResult = document.getElementById('ocr-result');
                    ocrResult.innerHTML = `
                        <div><strong>金额:</strong> ¥${result.amount || 'N/A'}</div>
                        <div><strong>日期:</strong> ${result.date || 'N/A'}</div>
                        <div><strong>商户:</strong> ${result.vendor || 'N/A'}</div>
                        <div><strong>描述:</strong> ${result.description || 'N/A'}</div>
                        <div><strong>置信度:</strong> ${result.confidence || 'N/A'}</div>
                    `;
                    
                    // 设置按钮数据
                    const confirmBtn = document.getElementById('confirm-btn');
                    confirmBtn.setAttribute('data-result', JSON.stringify(result).replace(/'/g, "&apos;"));
                    
                    resultContainer.classList.remove('hidden');
                } else {
                    status.textContent = `识别失败: ${result.error}`;
                }
                
            } catch (error) {
                status.textContent = `上传失败: ${error.message}`;
            }
        }

        // 确认发票记账
        async function confirmInvoiceEntry(buttonElement) {
            try {
                const resultJson = buttonElement.getAttribute('data-result').replace(/&apos;/g, "'");
                const result = JSON.parse(resultJson);
                
                buttonElement.disabled = true;
                buttonElement.textContent = '处理中...';
                
                // 构造自然语言描述
                const naturalText = `${result.date || '今日'}${result.vendor || ''}で${result.description || '商品'}を${result.amount || '0'}円で購入`;
                
                console.log('自然语言描述:', naturalText);
                console.log('附件数据:', result);
                
                // 这里可以调用AI记账API
                document.getElementById('status').textContent = `记账成功！描述: ${naturalText}`;
                buttonElement.textContent = '已记账';
                buttonElement.className = 'mt-3 bg-green-500 text-white px-3 py-1 rounded text-sm cursor-not-allowed';
                
            } catch (error) {
                console.error('确认记账失败:', error);
                buttonElement.disabled = false;
                buttonElement.textContent = '重试';
                buttonElement.className = 'mt-3 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600';
                document.getElementById('status').textContent = `记账失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>

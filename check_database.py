#!/usr/bin/env python3
"""
检查数据库中的附件信息
"""
import sqlite3
import json

def check_database():
    """检查数据库中的附件信息"""
    
    print("🔍 检查数据库中的附件信息")
    print("=" * 40)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('backend/goldenledger_accounting.db')
        cursor = conn.cursor()

        # 首先检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 数据库中的表: {[table[0] for table in tables]}")

        # 检查journal_entries表结构
        try:
            cursor.execute("PRAGMA table_info(journal_entries);")
            columns = cursor.fetchall()
            print(f"📊 journal_entries表结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
        except:
            print("❌ journal_entries表不存在")
            return

        # 查询最近的记录
        cursor.execute('''
            SELECT id, description, attachment_path, entry_date, created_at
            FROM journal_entries
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        records = cursor.fetchall()
        
        print(f"📊 最近10条记录:")
        for record in records:
            entry_id, description, attachment_path, entry_date, created_at = record
            print(f"  ID: {entry_id}")
            print(f"  描述: {description}")
            print(f"  附件路径: {attachment_path or 'None'}")
            print(f"  日期: {entry_date}")
            print(f"  创建时间: {created_at}")
            print("-" * 30)
        
        # 查询有附件的记录
        cursor.execute('''
            SELECT id, description, attachment_path
            FROM journal_entries 
            WHERE attachment_path IS NOT NULL AND attachment_path != ''
            ORDER BY created_at DESC
        ''')
        
        attachment_records = cursor.fetchall()
        
        print(f"\n📎 有附件的记录数: {len(attachment_records)}")
        for record in attachment_records:
            entry_id, description, attachment_path = record
            print(f"  ID: {entry_id}")
            print(f"  描述: {description}")
            print(f"  附件: {attachment_path}")
            print("-" * 20)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    check_database()

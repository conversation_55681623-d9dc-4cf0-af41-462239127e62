# Cloudflare Workers + Pages 完整部署配置
# GoldenLedger - Smart AI-Powered Finance System

# Workers API 配置
name = "goldenledger-api"
main = "workers/src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Pages 前端配置
[env.production]
name = "goldenledger-frontend"

[env.staging]
name = "goldenledger-staging"

# Build configuration for Pages
[build]
command = "echo 'Static site - no build required'"
cwd = "."
watch_dir = "."

# 环境变量
[vars]
ENVIRONMENT = "production"
API_BASE_URL = "https://goldenledger-api.souyousann.workers.dev"
GOOGLE_CLIENT_ID = "441923165006-houru024frrn51m217b473guu0so8oob.apps.googleusercontent.com"
PAYPAL_ENV = "sandbox"
GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

# KV 存储绑定（用于缓存和会话）
[[kv_namespaces]]
binding = "CACHE"
id = "6fae3ae8759f481194c4bc9a66dcbdfe"
preview_id = "28ed67ae63e24920b81ef15d0424f76c"

# KV 存储绑定（用于使用量跟踪）- 暂时禁用
# [[kv_namespaces]]
# binding = "USAGE_KV"
# id = "usage-tracker-kv-namespace-id"
# preview_id = "usage-tracker-preview-kv-namespace-id"

# R2 存储绑定（用于文件上传）
[[r2_buckets]]
binding = "FILES"
bucket_name = "goldenledger-files"

# Workers 路由配置 - 暂时使用默认 workers.dev 域名
# [[routes]]
# pattern = "api.goldenledger.pages.dev/*"
# zone_name = "goldenledger.pages.dev"

# Custom domains (configure in Cloudflare dashboard)
# routes = [
#   { pattern = "golden-ledger.com", zone_name = "golden-ledger.com" },
#   { pattern = "www.golden-ledger.com", zone_name = "golden-ledger.com" }
# ]

# Security settings
[security]
# Enable security headers
headers = true

# Rate limiting (if using Cloudflare Workers)
[rate_limiting]
enabled = true
requests_per_minute = 100

# Analytics
[analytics]
enabled = true

# Cache settings
[cache]
# Cache static assets for 1 year
"*.css" = "max-age=31536000"
"*.js" = "max-age=31536000"
"*.png" = "max-age=31536000"
"*.jpg" = "max-age=31536000"
"*.jpeg" = "max-age=31536000"
"*.gif" = "max-age=31536000"
"*.svg" = "max-age=31536000"
"*.ico" = "max-age=31536000"
"*.woff" = "max-age=31536000"
"*.woff2" = "max-age=31536000"

# Cache HTML files for 1 hour
"*.html" = "max-age=3600"

# Don't cache API responses
"/api/*" = "no-cache"

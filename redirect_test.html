<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重定向测试 - GoldenLedger</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .test-link {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 GoldenLedger 重定向测试</h1>
        
        <div class="status">
            <h3>📊 当前状态</h3>
            <p><strong>页面:</strong> <span id="current-page"></span></p>
            <p><strong>认证状态:</strong> <span id="auth-status"></span></p>
            <p><strong>存储键:</strong> <span id="storage-keys"></span></p>
        </div>

        <h3>🔗 测试链接</h3>
        <div>
            <a href="/" class="test-link">首页 (/)</a>
            <a href="/index.html" class="test-link">首页 (index.html)</a>
            <a href="/login" class="test-link">登录 (/login)</a>
            <a href="/login.html" class="test-link">登录 (login.html)</a>
            <a href="/register" class="test-link">注册 (/register)</a>
            <a href="/register.html" class="test-link">注册 (register.html)</a>
            <a href="/dashboard" class="test-link">仪表板 (/dashboard)</a>
            <a href="/master_dashboard.html" class="test-link">仪表板 (master_dashboard.html)</a>
        </div>

        <h3>🧹 清理操作</h3>
        <div>
            <button onclick="clearStorage()" class="test-link" style="border: none; cursor: pointer;">清除所有存储</button>
            <button onclick="clearOldStorage()" class="test-link" style="border: none; cursor: pointer;">清除旧存储键</button>
            <button onclick="testAuth()" class="test-link" style="border: none; cursor: pointer;">测试认证</button>
        </div>

        <div id="debug-info" class="status" style="margin-top: 20px;">
            <h3>🐛 调试信息</h3>
            <pre id="debug-content"></pre>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        // 更新页面信息
        function updatePageInfo() {
            document.getElementById('current-page').textContent = window.location.pathname;
            
            // 检查认证状态
            const authStatus = window.goldenLedgerAuth ? 
                (window.goldenLedgerAuth.isAuthenticated() ? '已登录' : '未登录') : 
                '认证系统未加载';
            document.getElementById('auth-status').textContent = authStatus;
            
            // 检查存储键
            const oldKey = localStorage.getItem('fire_accounting_user');
            const newKey = localStorage.getItem('golden_ledger_user');
            const storageInfo = [];
            if (oldKey) storageInfo.push('旧键存在');
            if (newKey) storageInfo.push('新键存在');
            if (!oldKey && !newKey) storageInfo.push('无存储');
            document.getElementById('storage-keys').textContent = storageInfo.join(', ');
            
            // 调试信息
            const debugInfo = {
                url: window.location.href,
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash,
                userAgent: navigator.userAgent.substring(0, 50) + '...',
                localStorage: {
                    oldKey: oldKey ? 'exists' : 'null',
                    newKey: newKey ? 'exists' : 'null'
                },
                authSystem: {
                    loaded: !!window.goldenLedgerAuth,
                    authenticated: window.goldenLedgerAuth ? window.goldenLedgerAuth.isAuthenticated() : false
                }
            };
            document.getElementById('debug-content').textContent = JSON.stringify(debugInfo, null, 2);
        }

        // 清理函数
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            alert('所有存储已清除');
            updatePageInfo();
        }

        function clearOldStorage() {
            localStorage.removeItem('fire_accounting_user');
            localStorage.removeItem('golden_ledger_user');
            sessionStorage.removeItem('golden_ledger_redirect');
            alert('所有认证存储已清除');
            updatePageInfo();
        }

        function testAuth() {
            if (window.goldenLedgerAuth) {
                const user = window.goldenLedgerAuth.getCurrentUser();
                alert('认证测试:\n' + JSON.stringify(user, null, 2));
            } else {
                alert('认证系统未加载');
            }
        }

        // 页面加载时更新信息
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updatePageInfo, 100);
        });

        // 定期更新信息
        setInterval(updatePageInfo, 2000);
    </script>
</body>
</html>

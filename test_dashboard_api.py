#!/usr/bin/env python3
"""
测试仪表盘API
"""
import requests
import json

def test_dashboard_api():
    """测试仪表盘API"""
    
    print("📊 测试仪表盘API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 测试仪表盘汇总API
        print("\n📈 步骤1: 测试仪表盘汇总API")
        response = requests.get(f"{base_url}/dashboard/summary/default")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"📊 响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查数据结构
            if 'financial' in data:
                financial = data['financial']
                print(f"\n💰 财务数据:")
                print(f"  本月收入: ¥{financial.get('monthly_revenue', 0):,}")
                print(f"  本月支出: ¥{financial.get('monthly_expenses', 0):,}")
                print(f"  本月利润: ¥{financial.get('monthly_profit', 0):,}")
                print(f"  总记录数: {financial.get('total_entries', 0):,}")
            
            if 'ai_stats' in data:
                ai_stats = data['ai_stats']
                print(f"\n🤖 AI统计:")
                print(f"  总处理数: {ai_stats.get('total_processed', 0):,}")
                print(f"  成功率: {ai_stats.get('success_rate', 0)*100:.1f}%")
                print(f"  平均置信度: {ai_stats.get('avg_confidence', 0)*100:.1f}%")
                print(f"  平均响应时间: {ai_stats.get('avg_response_time', 0):.1f}ms")
            
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dashboard_page():
    """测试仪表盘页面"""
    
    print("\n🌐 测试仪表盘页面")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 测试页面访问
        response = requests.get(f"{base_url}/advanced_dashboard.html")
        
        if response.status_code == 200:
            print("✅ 页面访问成功")
            print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"   Content-Length: {response.headers.get('content-length', 'N/A')}")
            return True
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 页面测试失败: {e}")
        return False

def test_journal_entries_data():
    """测试仕訳记录数据"""
    
    print("\n📋 测试仕訳记录数据")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 获取仕訳记录
        response = requests.get(f"{base_url}/journal-entries/default")
        
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ 获取到 {len(entries)} 条仕訳记录")
            
            if len(entries) > 0:
                print(f"📝 最近的记录:")
                for i, entry in enumerate(entries[:3]):
                    print(f"  {i+1}. {entry.get('id')} - {entry.get('description', 'N/A')} - ¥{entry.get('amount', 0)}")
            else:
                print("⚠️ 没有仕訳记录，这可能是仪表盘数据为空的原因")
            
            return len(entries) > 0
        else:
            print(f"❌ 获取仕訳记录失败: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始测试仪表盘功能")
    
    # 测试API
    api_success = test_dashboard_api()
    
    # 测试页面
    page_success = test_dashboard_page()
    
    # 测试数据
    data_success = test_journal_entries_data()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  API功能: {'✅ 正常' if api_success else '❌ 异常'}")
    print(f"  页面访问: {'✅ 正常' if page_success else '❌ 异常'}")
    print(f"  数据存在: {'✅ 有数据' if data_success else '❌ 无数据'}")
    
    if api_success and page_success:
        if data_success:
            print("\n🎉 仪表盘功能完全正常！")
            print("🌐 访问地址: http://localhost:8000/advanced_dashboard.html")
        else:
            print("\n⚠️ 仪表盘功能正常，但缺少数据")
            print("💡 建议添加一些仕訳记录来查看数据")
    else:
        print("\n❌ 仪表盘功能存在问题，请检查错误信息")

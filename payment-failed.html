<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支払い失敗 - GoldenLedger AI記帳システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- PayPal 配置 -->
    <script src="paypal_config.js"></script>
    
    <!-- API 配置 -->
    <script src="api_config.js"></script>
    
    <style>
        * {
            font-family: 'Noto Sans JP', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            min-height: 100vh;
        }

        .tech-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: techPulse 4s ease-in-out infinite alternate;
        }

        @keyframes techPulse {
            0% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }

        .cyber-glass {
            backdrop-filter: blur(20px);
            background: rgba(0, 20, 40, 0.15);
            border: 1px solid rgba(120, 219, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .error-animation {
            animation: errorShake 0.5s ease-in-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(120, 219, 255, 0.6);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="tech-bg">
    <!-- 浮动粒子效果 -->
    <div class="floating-particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 15%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 25%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 35%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 5%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 2.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 3.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 4.5s;"></div>
    </div>

    <!-- 主要内容 -->
    <div class="min-h-screen flex items-center justify-center px-4 relative z-10">
        <div class="cyber-glass rounded-3xl p-12 max-w-2xl w-full text-center">
            <!-- 错误图标 -->
            <div class="error-animation mb-8">
                <div class="w-24 h-24 bg-gradient-to-r from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-exclamation-triangle text-4xl text-white"></i>
                </div>
            </div>

            <!-- 错误消息 -->
            <h1 class="text-4xl font-bold text-red-400 mb-4">
                支払いに失敗しました
            </h1>
            
            <p class="text-xl text-cyan-200 mb-8">
                申し訳ございません。支払い処理中にエラーが発生しました。
            </p>

            <!-- 错误详情 -->
            <div class="cyber-glass rounded-2xl p-6 mb-8 text-left">
                <h3 class="text-lg font-semibold text-red-400 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>エラー詳細
                </h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-cyan-300">エラーコード:</span>
                        <span class="text-white font-mono text-sm" id="errorCode">PAYMENT_FAILED</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-cyan-300">発生時刻:</span>
                        <span class="text-white" id="errorTime">読み込み中...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-cyan-300">プラン:</span>
                        <span class="text-white font-semibold" id="planName">読み込み中...</span>
                    </div>
                </div>
                
                <div class="mt-4 p-4 bg-red-900/20 rounded-lg">
                    <p class="text-red-300 text-sm" id="errorMessage">
                        支払い処理中に予期しないエラーが発生しました。
                    </p>
                </div>
            </div>

            <!-- 解決方法 -->
            <div class="cyber-glass rounded-2xl p-6 mb-8 text-left">
                <h3 class="text-lg font-semibold text-cyan-400 mb-4">
                    <i class="fas fa-lightbulb mr-2"></i>解決方法
                </h3>
                
                <ul class="space-y-3 text-cyan-200">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                        <span>PayPalアカウントの残高や支払い方法を確認してください</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                        <span>インターネット接続が安定していることを確認してください</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                        <span>ブラウザのキャッシュをクリアして再試行してください</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-green-400 mr-3 mt-1"></i>
                        <span>問題が続く場合は、サポートチームにお問い合わせください</span>
                    </li>
                </ul>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="retryPayment()" 
                        class="bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-8 rounded-xl font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300">
                    <i class="fas fa-redo mr-2"></i>再試行
                </button>
                
                <a href="payment.html" 
                   class="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white py-3 px-8 rounded-xl font-semibold hover:from-cyan-600 hover:to-cyan-700 transition-all duration-300 text-center">
                    <i class="fas fa-arrow-left mr-2"></i>プラン選択に戻る
                </a>
                
                <a href="dashboard.html" 
                   class="bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-8 rounded-xl font-semibold hover:from-gray-600 hover:to-gray-700 transition-all duration-300 text-center">
                    <i class="fas fa-home mr-2"></i>ダッシュボード
                </a>
            </div>

            <!-- 支持信息 -->
            <div class="mt-8 pt-6 border-t border-cyan-400/20">
                <p class="text-cyan-300/70 text-sm mb-4">
                    問題が解決しない場合は、以下の方法でサポートチームにお問い合わせください：
                </p>
                
                <div class="flex justify-center text-sm">
                    <a href="mailto:<EMAIL>"
                       class="text-cyan-400 hover:text-cyan-300 transition-colors">
                        <i class="fas fa-envelope mr-1"></i><EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFailedPage();
        });

        // 初始化失败页面
        function initFailedPage() {
            try {
                // 从 URL 参数获取错误信息
                const urlParams = new URLSearchParams(window.location.search);
                const errorCode = urlParams.get('error') || 'PAYMENT_FAILED';
                const planId = urlParams.get('plan');
                const errorMsg = urlParams.get('message');

                // 更新错误信息
                document.getElementById('errorCode').textContent = errorCode;
                document.getElementById('errorTime').textContent = new Date().toLocaleString('ja-JP');

                if (planId) {
                    const plan = window.PayPalConfig?.plans?.[planId];
                    if (plan) {
                        document.getElementById('planName').textContent = plan.name;
                    }
                }

                if (errorMsg) {
                    document.getElementById('errorMessage').textContent = decodeURIComponent(errorMsg);
                }

                // 记录错误日志
                logPaymentError(errorCode, planId, errorMsg);

            } catch (error) {
                console.error('Failed to initialize failed page:', error);
            }
        }

        // 重试支付
        function retryPayment() {
            const urlParams = new URLSearchParams(window.location.search);
            const planId = urlParams.get('plan');
            
            if (planId) {
                window.location.href = `payment.html?retry=true&plan=${planId}`;
            } else {
                window.location.href = 'payment.html';
            }
        }



        // 记录支付错误日志
        async function logPaymentError(errorCode, planId, errorMessage) {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) return;

                await fetch(window.GoldenLedgerAPI.url('/api/payment/log-error'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        error_code: errorCode,
                        plan_id: planId,
                        error_message: errorMessage,
                        user_agent: navigator.userAgent,
                        timestamp: new Date().toISOString()
                    })
                });
            } catch (error) {
                console.error('Failed to log payment error:', error);
            }
        }
    </script>
</body>
</html>

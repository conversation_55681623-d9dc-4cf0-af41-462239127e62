/**
 * Message Handler - 消息处理系统
 * 处理消息发送、接收、存储和显示
 */

class MessageHandler {
    constructor() {
        this.messages = [];
        this.messageQueue = [];
        this.isProcessing = false;
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        
        this.initializeStorage();
        console.log('📨 Message Handler initialized');
    }

    initializeStorage() {
        // Load messages from localStorage
        try {
            const stored = localStorage.getItem('sakura_chat_messages');
            if (stored) {
                this.messages = JSON.parse(stored);
                this.restoreMessages();
            }
        } catch (error) {
            console.error('Failed to load stored messages:', error);
            this.messages = [];
        }
    }

    restoreMessages() {
        // Restore messages to chat interface
        const messagesContainer = document.getElementById('chat-messages');
        if (!messagesContainer) return;

        // Clear existing messages except welcome message
        const welcomeMessage = messagesContainer.querySelector('.message-container');
        messagesContainer.innerHTML = '';
        if (welcomeMessage) {
            messagesContainer.appendChild(welcomeMessage);
        }

        // Restore stored messages
        this.messages.forEach(message => {
            this.displayMessage(message, false); // Don't save again
        });
    }

    async sendMessage(content, type = 'user', metadata = {}) {
        const message = {
            id: this.generateMessageId(),
            content: content,
            type: type,
            timestamp: new Date().toISOString(),
            status: 'sending',
            metadata: metadata
        };

        // Add to queue and display immediately for user messages
        if (type === 'user') {
            this.addMessage(message);
            this.displayMessage(message);
        }

        // Process message
        try {
            await this.processMessage(message);
        } catch (error) {
            console.error('Failed to process message:', error);
            this.updateMessageStatus(message.id, 'failed');
            
            // Show retry option
            this.showRetryOption(message);
        }

        return message;
    }

    async processMessage(message) {
        if (message.type === 'user') {
            // Show typing indicator
            this.showTypingIndicator();

            try {
                // Get AI response
                const aiResponse = await this.getAIResponse(message.content);
                
                // Hide typing indicator
                this.hideTypingIndicator();

                // Create AI message
                const aiMessage = {
                    id: this.generateMessageId(),
                    content: aiResponse,
                    type: 'ai',
                    timestamp: new Date().toISOString(),
                    status: 'delivered',
                    metadata: {
                        responseTime: Date.now() - new Date(message.timestamp).getTime()
                    }
                };

                // Add and display AI response
                this.addMessage(aiMessage);
                this.displayMessage(aiMessage);

                // Update user message status
                this.updateMessageStatus(message.id, 'delivered');

            } catch (error) {
                this.hideTypingIndicator();
                throw error;
            }
        }
    }

    async getAIResponse(userMessage) {
        if (window.SakuraAI) {
            return await window.SakuraAI.sendMessage(userMessage);
        } else {
            throw new Error('AI service not available');
        }
    }

    addMessage(message) {
        this.messages.push(message);
        this.saveMessages();
        
        // Limit message history
        if (this.messages.length > 100) {
            this.messages = this.messages.slice(-50);
            this.saveMessages();
        }
    }

    displayMessage(message, shouldSave = true) {
        const messagesContainer = document.getElementById('chat-messages');
        if (!messagesContainer) return;

        const messageElement = this.createMessageElement(message);
        messagesContainer.appendChild(messageElement);

        // Scroll to bottom
        this.scrollToBottom();

        // Add animation
        messageElement.classList.add('fade-in');

        // Save if needed
        if (shouldSave) {
            this.saveMessages();
        }

        // Update notification if minimized
        if (window.sakuraChatbox?.isMinimized && message.type === 'ai') {
            this.showNotification();
        }
    }

    createMessageElement(message) {
        const container = document.createElement('div');
        container.className = `message-container ${message.type}-message`;
        container.dataset.messageId = message.id;

        // Create avatar
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = this.getAvatarHTML(message.type);

        // Create message bubble
        const bubble = document.createElement('div');
        bubble.className = `message-bubble ${message.type}-bubble`;
        
        // Format message content
        bubble.innerHTML = this.formatMessageContent(message.content);

        // Add timestamp
        const timestamp = document.createElement('div');
        timestamp.className = 'message-timestamp';
        timestamp.textContent = this.formatTimestamp(message.timestamp);
        bubble.appendChild(timestamp);

        // Add status indicator for user messages
        if (message.type === 'user') {
            const status = document.createElement('div');
            status.className = `message-status status-${message.status}`;
            status.innerHTML = this.getStatusIcon(message.status);
            bubble.appendChild(status);
        }

        container.appendChild(avatar);
        container.appendChild(bubble);

        return container;
    }

    getAvatarHTML(type) {
        if (type === 'ai') {
            return `
                <div class="sakura-avatar-small">
                    <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                    </svg>
                </div>
            `;
        } else {
            return `
                <div class="user-avatar-small">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
            `;
        }
    }

    formatMessageContent(content) {
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/🌸/g, '<span class="sakura-emoji">🌸</span>');
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) { // Less than 1 minute
            return 'たった今';
        } else if (diff < 3600000) { // Less than 1 hour
            return `${Math.floor(diff / 60000)}分前`;
        } else if (date.toDateString() === now.toDateString()) { // Same day
            return date.toLocaleTimeString('ja-JP', { hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('ja-JP', { month: 'short', day: 'numeric' });
        }
    }

    getStatusIcon(status) {
        const icons = {
            'sending': '⏳',
            'delivered': '✓',
            'failed': '❌'
        };
        return icons[status] || '';
    }

    updateMessageStatus(messageId, status) {
        // Update in memory
        const message = this.messages.find(m => m.id === messageId);
        if (message) {
            message.status = status;
            this.saveMessages();
        }

        // Update in DOM
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
            const statusElement = messageElement.querySelector('.message-status');
            if (statusElement) {
                statusElement.className = `message-status status-${status}`;
                statusElement.innerHTML = this.getStatusIcon(status);
            }
        }
    }

    showRetryOption(message) {
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (messageElement) {
            const retryBtn = document.createElement('button');
            retryBtn.className = 'retry-btn';
            retryBtn.textContent = '再送信';
            retryBtn.onclick = () => this.retryMessage(message);
            
            const bubble = messageElement.querySelector('.message-bubble');
            bubble.appendChild(retryBtn);
        }
    }

    async retryMessage(message) {
        // Remove retry button
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        const retryBtn = messageElement?.querySelector('.retry-btn');
        if (retryBtn) {
            retryBtn.remove();
        }

        // Update status and retry
        this.updateMessageStatus(message.id, 'sending');
        
        try {
            await this.processMessage(message);
        } catch (error) {
            console.error('Retry failed:', error);
            this.updateMessageStatus(message.id, 'failed');
            this.showRetryOption(message);
        }
    }

    showTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = 'block';
            this.scrollToBottom();
        }
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    showNotification() {
        const badge = document.getElementById('notification-badge');
        if (badge) {
            badge.style.display = 'flex';
            badge.textContent = '1';
        }
    }

    scrollToBottom() {
        const container = document.getElementById('chat-messages');
        if (container) {
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
            }, 100);
        }
    }

    saveMessages() {
        try {
            localStorage.setItem('sakura_chat_messages', JSON.stringify(this.messages));
        } catch (error) {
            console.error('Failed to save messages:', error);
        }
    }

    generateMessageId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    clearMessages() {
        this.messages = [];
        this.saveMessages();
        
        // Clear DOM
        const container = document.getElementById('chat-messages');
        if (container) {
            // Keep welcome message
            const welcomeMessage = container.querySelector('.message-container');
            container.innerHTML = '';
            if (welcomeMessage) {
                container.appendChild(welcomeMessage);
            }
        }
    }

    exportMessages() {
        return JSON.stringify(this.messages, null, 2);
    }

    getMessageCount() {
        return this.messages.length;
    }
}

// Initialize Message Handler
document.addEventListener('DOMContentLoaded', () => {
    window.messageHandler = new MessageHandler();
    console.log('📨 Message Handler ready');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MessageHandler;
}

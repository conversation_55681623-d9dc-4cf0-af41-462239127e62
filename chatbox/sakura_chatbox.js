/**
 * Sakura Chatbox - 核心交互功能
 * 基于jiajibo项目的优秀设计实现
 */

class SakuraChatbox {
    constructor() {
        this.isMinimized = false;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.messageCount = 0;
        
        this.initializeElements();
        this.bindEvents();
        this.initializePosition();
        
        console.log('🌸 Sakura Chatbox initialized');
    }

    initializeElements() {
        // Main elements
        this.chatbox = document.getElementById('sakura-chatbox');
        this.floatingBtn = document.getElementById('floating-chat-btn');
        this.messagesContainer = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendBtn = document.getElementById('send-btn');
        this.typingIndicator = document.getElementById('typing-indicator');
        this.notificationBadge = document.getElementById('notification-badge');

        // Control buttons
        this.minimizeBtn = document.getElementById('minimize-btn');
        this.closeBtn = document.getElementById('close-btn');

        // Quick action buttons
        this.quickActions = document.getElementById('quick-actions');
        this.quickBtns = document.querySelectorAll('.quick-btn');

        // Header for dragging
        this.header = document.querySelector('.chatbox-header');
    }

    bindEvents() {
        // Window controls
        this.minimizeBtn?.addEventListener('click', () => this.minimize());
        this.closeBtn?.addEventListener('click', () => this.close());
        this.floatingBtn?.addEventListener('click', () => this.restore());

        // Message input
        this.messageInput?.addEventListener('input', () => this.handleInputChange());
        this.messageInput?.addEventListener('keypress', (e) => this.handleKeyPress(e));
        this.sendBtn?.addEventListener('click', () => this.sendMessage());

        // Quick actions
        this.quickBtns?.forEach(btn => {
            btn.addEventListener('click', () => this.handleQuickAction(btn.dataset.action));
        });

        // Drag functionality
        this.header?.addEventListener('mousedown', (e) => this.startDrag(e));
        document.addEventListener('mousemove', (e) => this.drag(e));
        document.addEventListener('mouseup', () => this.endDrag());

        // Touch events for mobile
        this.header?.addEventListener('touchstart', (e) => this.startDrag(e.touches[0]));
        document.addEventListener('touchmove', (e) => this.drag(e.touches[0]));
        document.addEventListener('touchend', () => this.endDrag());

        // Prevent text selection during drag
        this.header?.addEventListener('selectstart', (e) => e.preventDefault());
    }

    initializePosition() {
        // Set initial position for desktop
        if (window.innerWidth > 768) {
            this.chatbox.style.right = '20px';
            this.chatbox.style.bottom = '20px';
        }
    }

    // Window Controls
    minimize() {
        this.isMinimized = true;
        this.chatbox.classList.add('minimized');
        this.floatingBtn.style.display = 'flex';
        
        // Add fade animation
        this.floatingBtn.classList.add('fade-in');
        
        console.log('🌸 Chatbox minimized');
    }

    restore() {
        this.isMinimized = false;
        this.chatbox.classList.remove('minimized');
        this.floatingBtn.style.display = 'none';
        this.notificationBadge.style.display = 'none';
        
        // Focus input when restored
        setTimeout(() => {
            this.messageInput?.focus();
        }, 300);
        
        console.log('🌸 Chatbox restored');
    }

    close() {
        this.chatbox.style.display = 'none';
        this.floatingBtn.style.display = 'flex';
        this.floatingBtn.classList.add('fade-in');
        
        console.log('🌸 Chatbox closed');
    }

    // Message Handling
    handleInputChange() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendBtn.disabled = !hasText;
        
        // Update send button style
        if (hasText) {
            this.sendBtn.style.opacity = '1';
            this.sendBtn.style.transform = 'scale(1)';
        } else {
            this.sendBtn.style.opacity = '0.5';
            this.sendBtn.style.transform = 'scale(0.95)';
        }
    }

    handleKeyPress(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;

        // Clear input
        this.messageInput.value = '';
        this.handleInputChange();

        // Add user message to chat
        this.addMessage(message, 'user');

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send to AI (will be implemented in sakura_ai.js)
            if (window.SakuraAI) {
                const response = await window.SakuraAI.sendMessage(message);
                this.hideTypingIndicator();
                this.addMessage(response, 'ai');
            } else {
                // Fallback response
                setTimeout(() => {
                    this.hideTypingIndicator();
                    this.addMessage('申し訳ございません。現在AIサービスに接続できません。しばらくしてからもう一度お試しください。', 'ai');
                }, 1500);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.hideTypingIndicator();
            this.addMessage('エラーが発生しました。もう一度お試しください。', 'ai');
        }
    }

    addMessage(content, type = 'ai', timestamp = new Date()) {
        const messageContainer = document.createElement('div');
        messageContainer.className = `message-container ${type}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';

        if (type === 'ai') {
            avatar.innerHTML = `
                <div class="sakura-avatar-small">
                    <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                    </svg>
                </div>
            `;
        } else {
            avatar.innerHTML = `
                <div class="user-avatar-small">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
            `;
        }

        const bubble = document.createElement('div');
        bubble.className = `message-bubble ${type}-bubble`;
        bubble.innerHTML = this.formatMessage(content);

        messageContainer.appendChild(avatar);
        messageContainer.appendChild(bubble);

        this.messagesContainer.appendChild(messageContainer);
        this.scrollToBottom();

        // Update message count for notifications
        this.messageCount++;
        if (this.isMinimized && type === 'ai') {
            this.showNotification();
        }
    }

    formatMessage(content) {
        // Basic message formatting
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    showTypingIndicator() {
        this.typingIndicator.style.display = 'block';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.typingIndicator.style.display = 'none';
    }

    showNotification() {
        this.notificationBadge.style.display = 'flex';
        this.notificationBadge.textContent = '1';
    }

    scrollToBottom() {
        setTimeout(() => {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }, 100);
    }

    // Quick Actions
    handleQuickAction(action) {
        const actionMessages = {
            'monthly-expenses': '今月の支出を教えて',
            'bookkeeping-tips': '仕訳のコツは？',
            'budget-review': '予算を見直したい',
            'seasonal-advice': '季節のアドバイス'
        };

        const message = actionMessages[action];
        if (message) {
            this.messageInput.value = message;
            this.sendMessage();
        }
    }

    // Drag Functionality
    startDrag(e) {
        if (window.innerWidth <= 768) return; // Disable drag on mobile

        this.isDragging = true;
        this.chatbox.classList.add('dragging');

        const rect = this.chatbox.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;

        document.body.style.userSelect = 'none';
    }

    drag(e) {
        if (!this.isDragging) return;

        e.preventDefault();
        
        const x = e.clientX - this.dragOffset.x;
        const y = e.clientY - this.dragOffset.y;

        // Constrain to viewport
        const maxX = window.innerWidth - this.chatbox.offsetWidth;
        const maxY = window.innerHeight - this.chatbox.offsetHeight;

        const constrainedX = Math.max(0, Math.min(x, maxX));
        const constrainedY = Math.max(0, Math.min(y, maxY));

        this.chatbox.style.left = constrainedX + 'px';
        this.chatbox.style.top = constrainedY + 'px';
        this.chatbox.style.right = 'auto';
        this.chatbox.style.bottom = 'auto';
    }

    endDrag() {
        if (!this.isDragging) return;

        this.isDragging = false;
        this.chatbox.classList.remove('dragging');
        document.body.style.userSelect = '';
    }

    // Responsive handling
    handleResize() {
        if (window.innerWidth <= 768) {
            // Mobile mode
            this.chatbox.style.left = '';
            this.chatbox.style.top = '';
            this.chatbox.style.right = '';
            this.chatbox.style.bottom = '';
        } else if (!this.isDragging) {
            // Desktop mode - reset to default position if not dragged
            if (!this.chatbox.style.left) {
                this.chatbox.style.right = '20px';
                this.chatbox.style.bottom = '20px';
            }
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.sakuraChatbox = new SakuraChatbox();
    
    // Handle window resize
    window.addEventListener('resize', () => {
        window.sakuraChatbox.handleResize();
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SakuraChatbox;
}

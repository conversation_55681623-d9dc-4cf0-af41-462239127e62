/**
 * Sakura AI - Gemini API集成
 * 提供智能会计咨询和建议功能
 */

class SakuraAI {
    constructor() {
        this.apiBaseUrl = 'https://goldenledger-api.souyousann.workers.dev';
        this.conversationHistory = [];
        this.maxHistoryLength = 10;
        this.isProcessing = false;
        
        this.initializePersonality();
        console.log('🌸 Sakura AI initialized');
    }

    initializePersonality() {
        // さくらちゃんの性格設定
        this.systemPrompt = `
あなたは「さくらちゃん」という名前の親しみやすいAI会計アシスタントです。

性格と特徴:
- 温かく親しみやすい口調で話します
- 日本の会計法規と税務に詳しいです
- 家計管理や個人事業主の帳簿管理が得意です
- 季節感を大切にし、時々桜🌸の絵文字を使います
- 複雑な会計用語も分かりやすく説明します
- ユーザーの立場に立って親身にアドバイスします

専門分野:
- 日本の会計基準と税務処理
- 個人事業主・小規模企業の帳簿管理
- 算管理
- 確定申告の準備と手続き
- 経費処理と節税対策
- 財務分析と経営改善提案

回答スタイル:
- 敬語を使いつつ親しみやすい口調
- 具体例を交えた分かりやすい説明
- 必要に応じて段階的な手順を提示
- 関連する法規や期限も併せて案内
- 不明な点は遠慮なく質問するよう促す

常に親切で丁寧に、そして専門的で正確な情報を提供してください。
`;
    }

    async sendMessage(userMessage) {
        if (this.isProcessing) {
            return 'すみません、前の質問を処理中です。少々お待ちください🌸';
        }

        this.isProcessing = true;

        try {
            // Add user message to history
            this.addToHistory('user', userMessage);

            // Prepare conversation context
            const context = this.buildContext();

            // Send to API
            const response = await this.callGeminiAPI(context);

            // Add AI response to history
            this.addToHistory('assistant', response);

            return response;

        } catch (error) {
            console.error('Sakura AI Error:', error);
            return this.getErrorResponse(error);
        } finally {
            this.isProcessing = false;
        }
    }

    async callGeminiAPI(context) {
        const response = await fetch(`${this.apiBaseUrl}/api/chat/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getAuthToken()}`
            },
            body: JSON.stringify({
                messages: context,
                model: 'gemini-2.0-flash-exp',
                temperature: 0.7,
                max_tokens: 1000
            })
        });

        if (!response.ok) {
            throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || 'API request failed');
        }

        return data.response || 'すみません、回答を生成できませんでした。';
    }

    buildContext() {
        const context = [
            {
                role: 'system',
                content: this.systemPrompt
            }
        ];

        // Add conversation history
        this.conversationHistory.forEach(msg => {
            context.push({
                role: msg.role,
                content: msg.content
            });
        });

        return context;
    }

    addToHistory(role, content) {
        this.conversationHistory.push({
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        });

        // Limit history length
        if (this.conversationHistory.length > this.maxHistoryLength * 2) {
            this.conversationHistory = this.conversationHistory.slice(-this.maxHistoryLength * 2);
        }
    }

    getAuthToken() {
        // Get auth token from localStorage or session
        return localStorage.getItem('goldenledger_session_token') || '';
    }

    getErrorResponse(error) {
        const errorResponses = {
            'network': 'ネットワークエラーが発生しました。インターネット接続をご確認ください🌸',
            'auth': 'ログインが必要です。ログインしてからもう一度お試しください🌸',
            'rate_limit': 'しばらく時間をおいてからもう一度お試しください🌸',
            'server': 'サーバーエラーが発生しました。しばらくしてからもう一度お試しください🌸'
        };

        if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            return errorResponses.auth;
        } else if (error.message.includes('429') || error.message.includes('rate limit')) {
            return errorResponses.rate_limit;
        } else if (error.message.includes('500') || error.message.includes('server')) {
            return errorResponses.server;
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            return errorResponses.network;
        }

        return '申し訳ございません。エラーが発生しました。もう一度お試しください🌸';
    }

    // Quick action handlers
    async handleQuickAction(action) {
        const quickActionPrompts = {
            'monthly-expenses': `
今月の支出について教えてください。
以下の情報を含めて回答してください：
- 支出の分析方法
- 記帳の確認ポイント
- 節約のアドバイス
- 来月の予算計画のヒント
`,
            'bookkeeping-tips': `
効果的な仕訳のコツを教えてください。
以下の点を含めて説明してください：
- 基本的な仕訳のルール
- よくある間違いと対策
- 効率的な記帳方法
- おすすめの帳簿管理ツール
`,
            'budget-review': `
予算の見直しについてアドバイスをください。
以下の観点から説明してください：
- 予算見直しのタイミング
- 収支バランスの確認方法
- 無駄な支出の見つけ方
- 貯蓄目標の設定方法
`,
            'seasonal-advice': `
現在の季節に応じた財務管理のアドバイスをください。
以下の内容を含めてください：
- 季節特有の支出項目
- 年末調整や確定申告の準備
- 季節のイベント費用の管理
- 来年度の計画立案のヒント
`
        };

        const prompt = quickActionPrompts[action];
        if (prompt) {
            return await this.sendMessage(prompt);
        }

        return 'すみません、その機能は現在準備中です🌸';
    }

    // Utility methods
    clearHistory() {
        this.conversationHistory = [];
        console.log('🌸 Conversation history cleared');
    }

    getHistoryLength() {
        return this.conversationHistory.length;
    }

    exportHistory() {
        return JSON.stringify(this.conversationHistory, null, 2);
    }

    importHistory(historyJson) {
        try {
            const history = JSON.parse(historyJson);
            if (Array.isArray(history)) {
                this.conversationHistory = history;
                return true;
            }
        } catch (error) {
            console.error('Failed to import history:', error);
        }
        return false;
    }

    // Stream response handling (for future implementation)
    async sendMessageStream(userMessage, onChunk) {
        // This would implement streaming responses
        // For now, we'll use the regular sendMessage method
        const response = await this.sendMessage(userMessage);
        
        // Simulate streaming by sending chunks
        const words = response.split(' ');
        for (let i = 0; i < words.length; i++) {
            const chunk = words.slice(0, i + 1).join(' ');
            onChunk(chunk);
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }

    // Context-aware responses
    async getContextualResponse(userMessage, context = {}) {
        // Add context information to the message
        let enhancedMessage = userMessage;
        
        if (context.currentPage) {
            enhancedMessage += `\n\n[現在のページ: ${context.currentPage}]`;
        }
        
        if (context.userFinancialData) {
            enhancedMessage += `\n\n[ユーザーの財務データを参考にしてください]`;
        }

        return await this.sendMessage(enhancedMessage);
    }
}

// Initialize Sakura AI
document.addEventListener('DOMContentLoaded', () => {
    window.SakuraAI = new SakuraAI();
    console.log('🌸 Sakura AI ready for conversations');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SakuraAI;
}

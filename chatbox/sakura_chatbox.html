<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>さくらちゃん - AI会計アシスタント</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="sakura_chatbox.css">
</head>
<body>
    <!-- Chatbox Container -->
    <div id="sakura-chatbox" class="chatbox-container">
        <!-- Header -->
        <div class="chatbox-header">
            <div class="header-content">
                <div class="avatar-section">
                    <div class="sakura-avatar">
                        <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                    <div class="user-info">
                        <h3 class="user-name">さくらちゃん</h3>
                        <span class="online-status">オンライン</span>
                    </div>
                </div>
                <div class="header-controls">
                    <button id="minimize-btn" class="control-btn" title="最小化">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </button>
                    <button id="close-btn" class="control-btn" title="閉じる">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages Area -->
        <div class="chat-messages" id="chat-messages">
            <!-- Welcome Message -->
            <div class="message-container ai-message">
                <div class="message-avatar">
                    <div class="sakura-avatar-small">
                        <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                </div>
                <div class="message-bubble ai-bubble">
                    <p>こんにちは！さくらちゃんです🌸</p>
                    <p>家計管理のお手伝いをさせていただきます。何でもお気軽にご相談ください！</p>
                </div>
            </div>
        </div>

        <!-- Quick Action Buttons -->
        <div class="quick-actions" id="quick-actions">
            <button class="quick-btn" data-action="monthly-expenses">
                今月の支出を教えて
            </button>
            <button class="quick-btn" data-action="bookkeeping-tips">
                仕訳のコツは？
            </button>
            <button class="quick-btn" data-action="budget-review">
                予算を見直したい
            </button>
            <button class="quick-btn" data-action="seasonal-advice">
                季節のアドバイス
            </button>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <div class="input-container">
                <input 
                    type="text" 
                    id="message-input" 
                    class="message-input" 
                    placeholder="メッセージを入力..."
                    maxlength="500"
                >
                <button id="send-btn" class="send-btn" disabled>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Typing Indicator -->
        <div class="typing-indicator" id="typing-indicator" style="display: none;">
            <div class="message-container ai-message">
                <div class="message-avatar">
                    <div class="sakura-avatar-small">
                        <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                </div>
                <div class="message-bubble ai-bubble typing-bubble">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Chat Button (when minimized) -->
    <div id="floating-chat-btn" class="floating-chat-btn" style="display: none;">
        <div class="sakura-avatar">
            <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
            </svg>
        </div>
        <div class="notification-badge" id="notification-badge" style="display: none;">1</div>
    </div>

    <!-- Scripts -->
    <script src="sakura_chatbox.js"></script>
    <script src="sakura_ai.js"></script>
    <script src="message_handler.js"></script>
    <script src="auth_integration.js"></script>
    <script src="database_integration.js"></script>
</body>
</html>

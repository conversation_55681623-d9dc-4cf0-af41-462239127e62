/**
 * Auth Integration - 用户认证集成
 * 与GoldenLedger现有认证系统集成
 */

class AuthIntegration {
    constructor() {
        this.apiBaseUrl = 'https://goldenledger-api.souyousann.workers.dev';
        this.currentUser = null;
        this.sessionToken = null;
        this.isAuthenticated = false;
        
        this.initializeAuth();
        console.log('🔐 Auth Integration initialized');
    }

    async initializeAuth() {
        // Check for existing session
        await this.checkExistingSession();
        
        // Set up auth event listeners
        this.setupAuthListeners();
        
        // Update UI based on auth status
        this.updateAuthUI();
    }

    async checkExistingSession() {
        try {
            // Get token from localStorage
            this.sessionToken = localStorage.getItem('goldenledger_session_token');
            
            if (this.sessionToken) {
                // Validate token with server
                const response = await fetch(`${this.apiBaseUrl}/api/auth/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.sessionToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.user) {
                        this.currentUser = data.user;
                        this.isAuthenticated = true;
                        console.log('🔐 User authenticated:', this.currentUser.username);
                        return true;
                    }
                }
            }
            
            // Clear invalid session
            this.clearSession();
            return false;
            
        } catch (error) {
            console.error('Auth validation error:', error);
            this.clearSession();
            return false;
        }
    }

    setupAuthListeners() {
        // Listen for storage changes (multi-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'goldenledger_session_token') {
                if (e.newValue) {
                    this.sessionToken = e.newValue;
                    this.checkExistingSession();
                } else {
                    this.clearSession();
                }
            }
        });

        // Listen for custom auth events
        document.addEventListener('goldenledger:login', (e) => {
            this.handleLogin(e.detail);
        });

        document.addEventListener('goldenledger:logout', () => {
            this.handleLogout();
        });
    }

    async handleLogin(loginData) {
        try {
            if (loginData.token && loginData.user) {
                this.sessionToken = loginData.token;
                this.currentUser = loginData.user;
                this.isAuthenticated = true;
                
                // Store token
                localStorage.setItem('goldenledger_session_token', this.sessionToken);
                localStorage.setItem('goldenledger_user', JSON.stringify(this.currentUser));
                
                // Update UI
                this.updateAuthUI();
                
                // Initialize user-specific features
                await this.initializeUserFeatures();
                
                console.log('🔐 Login successful:', this.currentUser.username);
            }
        } catch (error) {
            console.error('Login handling error:', error);
        }
    }

    handleLogout() {
        this.clearSession();
        this.updateAuthUI();
        console.log('🔐 User logged out');
    }

    clearSession() {
        this.sessionToken = null;
        this.currentUser = null;
        this.isAuthenticated = false;
        
        // Clear storage
        localStorage.removeItem('goldenledger_session_token');
        localStorage.removeItem('goldenledger_user');
        
        // Clear chat history for security
        if (window.messageHandler) {
            window.messageHandler.clearMessages();
        }
    }

    updateAuthUI() {
        const chatbox = document.getElementById('sakura-chatbox');
        if (!chatbox) return;

        if (this.isAuthenticated) {
            // Show authenticated state
            chatbox.classList.remove('unauthenticated');
            chatbox.classList.add('authenticated');
            
            // Update user info in header if needed
            this.updateUserInfo();
            
            // Enable all features
            this.enableChatFeatures();
            
        } else {
            // Show unauthenticated state
            chatbox.classList.remove('authenticated');
            chatbox.classList.add('unauthenticated');
            
            // Show login prompt
            this.showLoginPrompt();
            
            // Disable some features
            this.disableChatFeatures();
        }
    }

    updateUserInfo() {
        if (!this.currentUser) return;

        // Update online status to show user name
        const onlineStatus = document.querySelector('.online-status');
        if (onlineStatus) {
            onlineStatus.textContent = `${this.currentUser.username || this.currentUser.name}さん`;
        }
    }

    showLoginPrompt() {
        // Add login message to chat
        const messagesContainer = document.getElementById('chat-messages');
        if (!messagesContainer) return;

        // Check if login prompt already exists
        if (messagesContainer.querySelector('.login-prompt')) return;

        const loginPrompt = document.createElement('div');
        loginPrompt.className = 'message-container ai-message login-prompt';
        loginPrompt.innerHTML = `
            <div class="message-avatar">
                <div class="sakura-avatar-small">
                    <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                    </svg>
                </div>
            </div>
            <div class="message-bubble ai-bubble">
                <p>より詳しいサポートを受けるには、ログインが必要です🌸</p>
                <button class="login-btn" onclick="authIntegration.redirectToLogin()">
                    ログインする
                </button>
            </div>
        `;

        messagesContainer.appendChild(loginPrompt);
        this.scrollToBottom();
    }

    enableChatFeatures() {
        // Enable input
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        
        if (messageInput) {
            messageInput.disabled = false;
            messageInput.placeholder = 'メッセージを入力...';
        }
        
        if (sendBtn) {
            sendBtn.disabled = false;
        }

        // Enable quick actions
        const quickBtns = document.querySelectorAll('.quick-btn');
        quickBtns.forEach(btn => {
            btn.disabled = false;
        });
    }

    disableChatFeatures() {
        // Disable input for non-authenticated users
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        
        if (messageInput) {
            messageInput.disabled = true;
            messageInput.placeholder = 'ログインしてチャットを開始...';
        }
        
        if (sendBtn) {
            sendBtn.disabled = true;
        }

        // Disable some quick actions
        const quickBtns = document.querySelectorAll('.quick-btn');
        quickBtns.forEach(btn => {
            if (btn.dataset.action !== 'bookkeeping-tips') {
                btn.disabled = true;
            }
        });
    }

    redirectToLogin() {
        // Redirect to login page with return URL
        const returnUrl = encodeURIComponent(window.location.href);
        window.location.href = `/login_simple.html?return=${returnUrl}`;
    }

    async initializeUserFeatures() {
        if (!this.isAuthenticated) return;

        try {
            // Load user-specific chat history
            await this.loadUserChatHistory();
            
            // Initialize user preferences
            await this.loadUserPreferences();
            
            // Set up user-specific AI context
            this.setupUserContext();
            
        } catch (error) {
            console.error('Failed to initialize user features:', error);
        }
    }

    async loadUserChatHistory() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/chat/history`, {
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.messages) {
                    // Load messages into message handler
                    if (window.messageHandler) {
                        window.messageHandler.messages = data.messages;
                        window.messageHandler.restoreMessages();
                    }
                }
            }
        } catch (error) {
            console.error('Failed to load chat history:', error);
        }
    }

    async loadUserPreferences() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/user/preferences`, {
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.preferences) {
                    this.applyUserPreferences(data.preferences);
                }
            }
        } catch (error) {
            console.error('Failed to load user preferences:', error);
        }
    }

    applyUserPreferences(preferences) {
        // Apply theme
        if (preferences.theme) {
            document.body.setAttribute('data-theme', preferences.theme);
        }

        // Apply language
        if (preferences.language) {
            document.documentElement.lang = preferences.language;
        }

        // Apply other preferences
        if (preferences.notifications !== undefined) {
            this.notificationsEnabled = preferences.notifications;
        }
    }

    setupUserContext() {
        // Provide user context to AI
        if (window.SakuraAI && this.currentUser) {
            const userContext = {
                userId: this.currentUser.id,
                username: this.currentUser.username,
                userType: this.currentUser.user_type || 'individual',
                preferences: this.currentUser.preferences || {}
            };

            // Add user context to AI system prompt
            window.SakuraAI.userContext = userContext;
        }
    }

    // Utility methods
    getAuthToken() {
        return this.sessionToken;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    async refreshToken() {
        if (!this.sessionToken) return false;

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.token) {
                    this.sessionToken = data.token;
                    localStorage.setItem('goldenledger_session_token', this.sessionToken);
                    return true;
                }
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
        }

        return false;
    }

    scrollToBottom() {
        const container = document.getElementById('chat-messages');
        if (container) {
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
            }, 100);
        }
    }
}

// Initialize Auth Integration
document.addEventListener('DOMContentLoaded', () => {
    window.authIntegration = new AuthIntegration();
    console.log('🔐 Auth Integration ready');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthIntegration;
}

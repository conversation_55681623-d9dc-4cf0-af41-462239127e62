/* Sakura Chatbox Styles */
:root {
    --sakura-pink: #FF69B4;
    --sakura-light-pink: #FFB6C1;
    --sakura-green: #98FB98;
    --sakura-gold: #FFD700;
    --sakura-white: #FFFFFF;
    --sakura-gray: #F8F9FA;
    --sakura-text: #333333;
    --sakura-shadow: rgba(255, 105, 180, 0.2);
    --sakura-border-radius: 16px;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: var(--sakura-gray);
}

/* Chatbox Container */
.chatbox-container {
    position: fixed;
    bottom: 50px;
    right: 20px;
    width: 420px;
    height: 650px;
    background: linear-gradient(135deg, var(--sakura-pink) 0%, var(--sakura-light-pink) 50%, var(--sakura-green) 100%);
    border-radius: var(--sakura-border-radius);
    box-shadow: 0 20px 40px var(--sakura-shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.chatbox-container.minimized {
    transform: scale(0);
    opacity: 0;
    pointer-events: none;
}

/* Header */
.chatbox-header {
    background: linear-gradient(135deg, var(--sakura-pink), var(--sakura-light-pink));
    padding: 14px 18px;
    border-radius: var(--sakura-border-radius) var(--sakura-border-radius) 0 0;
    cursor: move;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.sakura-avatar {
    width: 40px;
    height: 40px;
    background: var(--sakura-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sakura-icon {
    width: 24px;
    height: 24px;
    color: var(--sakura-pink);
}

.user-info {
    color: var(--sakura-white);
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
}

.online-status {
    font-size: 12px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 6px;
}

.online-status::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #00FF00;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: var(--sakura-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.control-btn svg {
    width: 16px;
    height: 16px;
}

/* Chat Messages Area */
.chat-messages {
    flex: 1;
    padding: 16px 18px;
    overflow-y: auto;
    background: var(--sakura-white);
    display: flex;
    flex-direction: column;
    gap: 14px;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--sakura-light-pink);
    border-radius: 3px;
}

.message-container {
    display: flex;
    gap: 12px;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-container.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    flex-shrink: 0;
}

.sakura-avatar-small {
    width: 32px;
    height: 32px;
    background: var(--sakura-white);
    border: 2px solid var(--sakura-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sakura-icon-small {
    width: 16px;
    height: 16px;
    color: var(--sakura-pink);
}

.message-bubble {
    max-width: 320px;
    padding: 14px 18px;
    border-radius: 18px;
    font-size: 16px;
    line-height: 1.5;
    word-wrap: break-word;
}

.ai-bubble {
    background: var(--sakura-gray);
    color: var(--sakura-text);
    border-bottom-left-radius: 6px;
}

.user-bubble {
    background: var(--sakura-pink);
    color: var(--sakura-white);
    border-bottom-right-radius: 6px;
}

/* Typing Indicator */
.typing-bubble {
    padding: 16px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: var(--sakura-pink);
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Quick Actions */
.quick-actions {
    padding: 14px 18px;
    background: var(--sakura-white);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quick-btn {
    background: linear-gradient(135deg, var(--sakura-gold), #FFA500);
    color: var(--sakura-white);
    border: none;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.quick-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
}

.quick-btn:active {
    transform: translateY(0);
}

/* Input Area */
.input-area {
    padding: 14px 18px;
    background: var(--sakura-white);
    border-top: 1px solid rgba(255, 105, 180, 0.1);
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--sakura-gray);
    border-radius: 24px;
    font-size: 16px;
    outline: none;
    transition: all 0.2s ease;
    background: var(--sakura-gray);
}

.message-input:focus {
    border-color: var(--sakura-pink);
    background: var(--sakura-white);
}

.send-btn {
    width: 44px;
    height: 44px;
    background: var(--sakura-pink);
    border: none;
    border-radius: 50%;
    color: var(--sakura-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px var(--sakura-shadow);
}

.send-btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 6px 16px var(--sakura-shadow);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-btn svg {
    width: 20px;
    height: 20px;
}

/* Floating Chat Button */
.floating-chat-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--sakura-pink), var(--sakura-light-pink));
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px var(--sakura-shadow);
    transition: all 0.3s ease;
    z-index: 999;
}

.floating-chat-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 32px var(--sakura-shadow);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 20px;
    height: 20px;
    background: #FF4444;
    color: var(--sakura-white);
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .chatbox-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        border-radius: 0;
    }
    
    .chatbox-header {
        border-radius: 0;
    }
    
    .floating-chat-btn {
        bottom: 80px;
    }
}

/* Drag and Drop */
.chatbox-container.dragging {
    transition: none;
    z-index: 1001;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

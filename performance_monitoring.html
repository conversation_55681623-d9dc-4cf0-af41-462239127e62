<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 系统性能监控</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .metric-card { transition: all 0.3s ease; }
        .metric-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .status-healthy { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-critical { color: #ef4444; }
        .pulse { animation: pulse 2s infinite; }
        .progress-ring { transform: rotate(-90deg); }
        .progress-ring-circle { transition: stroke-dasharray 0.35s; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">📊 系统性能监控</h1>
                    <p class="text-lg opacity-90">实时系统资源监控和性能分析</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div id="system-status" class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        <span class="status-healthy">● 系统正常</span>
                    </div>
                    <button id="refresh-all-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        🔄 刷新全部
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- System Overview -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- CPU Usage -->
            <div class="metric-card bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">🖥️ CPU使用率</h3>
                    <div class="relative w-16 h-16">
                        <svg class="progress-ring w-16 h-16">
                            <circle class="progress-ring-circle stroke-gray-200" stroke-width="4" fill="transparent" r="28" cx="32" cy="32"/>
                            <circle id="cpu-progress" class="progress-ring-circle stroke-blue-500" stroke-width="4" fill="transparent" r="28" cx="32" cy="32" stroke-dasharray="0 176"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span id="cpu-percentage" class="text-sm font-bold">0%</span>
                        </div>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>当前:</span>
                        <span id="cpu-current" class="font-medium">0%</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>平均:</span>
                        <span id="cpu-average" class="font-medium">0%</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>峰值:</span>
                        <span id="cpu-peak" class="font-medium">0%</span>
                    </div>
                </div>
            </div>

            <!-- Memory Usage -->
            <div class="metric-card bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">🧠 内存使用</h3>
                    <div class="relative w-16 h-16">
                        <svg class="progress-ring w-16 h-16">
                            <circle class="progress-ring-circle stroke-gray-200" stroke-width="4" fill="transparent" r="28" cx="32" cy="32"/>
                            <circle id="memory-progress" class="progress-ring-circle stroke-green-500" stroke-width="4" fill="transparent" r="28" cx="32" cy="32" stroke-dasharray="0 176"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span id="memory-percentage" class="text-sm font-bold">0%</span>
                        </div>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>已用:</span>
                        <span id="memory-used" class="font-medium">0 MB</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>总计:</span>
                        <span id="memory-total" class="font-medium">0 MB</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>可用:</span>
                        <span id="memory-available" class="font-medium">0 MB</span>
                    </div>
                </div>
            </div>

            <!-- Disk Usage -->
            <div class="metric-card bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">💽 磁盘使用</h3>
                    <div class="relative w-16 h-16">
                        <svg class="progress-ring w-16 h-16">
                            <circle class="progress-ring-circle stroke-gray-200" stroke-width="4" fill="transparent" r="28" cx="32" cy="32"/>
                            <circle id="disk-progress" class="progress-ring-circle stroke-purple-500" stroke-width="4" fill="transparent" r="28" cx="32" cy="32" stroke-dasharray="0 176"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span id="disk-percentage" class="text-sm font-bold">0%</span>
                        </div>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>已用:</span>
                        <span id="disk-used" class="font-medium">0 GB</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>总计:</span>
                        <span id="disk-total" class="font-medium">0 GB</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>可用:</span>
                        <span id="disk-free" class="font-medium">0 GB</span>
                    </div>
                </div>
            </div>

            <!-- Network I/O -->
            <div class="metric-card bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">🌐 网络I/O</h3>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-500" id="network-speed">0 KB/s</div>
                        <div class="text-xs text-gray-500">实时速度</div>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>上传:</span>
                        <span id="network-upload" class="font-medium">0 KB/s</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>下载:</span>
                        <span id="network-download" class="font-medium">0 KB/s</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>连接数:</span>
                        <span id="network-connections" class="font-medium">0</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Charts -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- System Resources Chart -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">📈 系统资源趋势</h3>
                    <div class="flex space-x-2">
                        <button id="chart-1h-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">1小时</button>
                        <button id="chart-24h-btn" class="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400">24小时</button>
                        <button id="chart-7d-btn" class="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400">7天</button>
                    </div>
                </div>
                <canvas id="resources-chart" width="400" height="300"></canvas>
            </div>

            <!-- API Performance Chart -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">⚡ API性能监控</h3>
                    <div class="text-sm text-gray-600">
                        平均响应时间: <span id="avg-response-time" class="font-medium">0ms</span>
                    </div>
                </div>
                <canvas id="api-performance-chart" width="400" height="300"></canvas>
            </div>
        </section>

        <!-- Service Status -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Backend Services -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🔧 后端服务</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm">FastAPI服务</span>
                        <span class="status-healthy text-sm">● 运行中</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">数据库连接</span>
                        <span class="status-healthy text-sm">● 正常</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">AI智能体</span>
                        <span class="status-healthy text-sm">● 活跃</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">WebSocket服务</span>
                        <span class="status-healthy text-sm">● 连接中</span>
                    </div>
                </div>
            </div>

            <!-- External Services -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🌐 外部服务</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm">Google Gemini API</span>
                        <span class="status-healthy text-sm">● 可用</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">备份存储</span>
                        <span class="status-healthy text-sm">● 正常</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">邮件服务</span>
                        <span class="status-warning text-sm">⚠ 警告</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">监控服务</span>
                        <span class="status-healthy text-sm">● 运行中</span>
                    </div>
                </div>
            </div>

            <!-- System Health -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🏥 系统健康</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm">整体状态</span>
                        <span class="status-healthy text-sm font-medium">健康</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">运行时间</span>
                        <span class="text-sm font-medium" id="uptime">0天 0小时</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">错误率</span>
                        <span class="text-sm font-medium" id="error-rate">0.1%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm">负载评分</span>
                        <span class="text-sm font-medium" id="load-score">85/100</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recent Alerts -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">🚨 最近警报</h3>
                <button id="clear-alerts-btn" class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                    清除全部
                </button>
            </div>
            
            <div id="alerts-list" class="space-y-3">
                <!-- 警报列表将在这里动态生成 -->
            </div>
        </section>

        <!-- Performance Metrics -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <h3 class="text-lg font-semibold mb-4">📊 性能指标</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600" id="requests-per-second">0</div>
                    <div class="text-sm text-gray-600">请求/秒</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="avg-response-time-display">0ms</div>
                    <div class="text-sm text-gray-600">平均响应时间</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600" id="active-users">0</div>
                    <div class="text-sm text-gray-600">活跃用户</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600" id="cache-hit-rate">0%</div>
                    <div class="text-sm text-gray-600">缓存命中率</div>
                </div>
            </div>
        </section>
    </main>

    <!-- Notification Area -->
    <div id="notification-area" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- 动态通知将在这里显示 -->
    </div>

    <script>
        // 全局变量
        let resourcesChart, apiPerformanceChart;
        let systemMetrics = {
            cpu: { current: 0, average: 0, peak: 0 },
            memory: { used: 0, total: 8192, available: 8192 },
            disk: { used: 0, total: 500, free: 500 },
            network: { upload: 0, download: 0, connections: 0 }
        };
        let alerts = [];

        // 模拟系统指标数据
        function generateMockMetrics() {
            // CPU使用率 (0-100%)
            systemMetrics.cpu.current = Math.floor(Math.random() * 30) + 10; // 10-40%
            systemMetrics.cpu.average = Math.floor(Math.random() * 25) + 15; // 15-40%
            systemMetrics.cpu.peak = Math.max(systemMetrics.cpu.peak, systemMetrics.cpu.current);

            // 内存使用 (MB)
            systemMetrics.memory.used = Math.floor(Math.random() * 2048) + 1024; // 1-3GB
            systemMetrics.memory.available = systemMetrics.memory.total - systemMetrics.memory.used;

            // 磁盘使用 (GB)
            systemMetrics.disk.used = Math.floor(Math.random() * 100) + 50; // 50-150GB
            systemMetrics.disk.free = systemMetrics.disk.total - systemMetrics.disk.used;

            // 网络I/O (KB/s)
            systemMetrics.network.upload = Math.floor(Math.random() * 500) + 50;
            systemMetrics.network.download = Math.floor(Math.random() * 1000) + 100;
            systemMetrics.network.connections = Math.floor(Math.random() * 50) + 10;
        }

        // 更新进度环
        function updateProgressRing(elementId, percentage, color) {
            const circle = document.getElementById(elementId);
            const radius = 28;
            const circumference = 2 * Math.PI * radius;
            const offset = circumference - (percentage / 100) * circumference;

            circle.style.strokeDasharray = `${circumference} ${circumference}`;
            circle.style.strokeDashoffset = offset;
        }

        // 更新系统指标显示
        function updateSystemMetrics() {
            generateMockMetrics();

            // CPU
            const cpuPercentage = systemMetrics.cpu.current;
            document.getElementById('cpu-percentage').textContent = `${cpuPercentage}%`;
            document.getElementById('cpu-current').textContent = `${cpuPercentage}%`;
            document.getElementById('cpu-average').textContent = `${systemMetrics.cpu.average}%`;
            document.getElementById('cpu-peak').textContent = `${systemMetrics.cpu.peak}%`;
            updateProgressRing('cpu-progress', cpuPercentage);

            // Memory
            const memoryPercentage = Math.floor((systemMetrics.memory.used / systemMetrics.memory.total) * 100);
            document.getElementById('memory-percentage').textContent = `${memoryPercentage}%`;
            document.getElementById('memory-used').textContent = `${systemMetrics.memory.used} MB`;
            document.getElementById('memory-total').textContent = `${systemMetrics.memory.total} MB`;
            document.getElementById('memory-available').textContent = `${systemMetrics.memory.available} MB`;
            updateProgressRing('memory-progress', memoryPercentage);

            // Disk
            const diskPercentage = Math.floor((systemMetrics.disk.used / systemMetrics.disk.total) * 100);
            document.getElementById('disk-percentage').textContent = `${diskPercentage}%`;
            document.getElementById('disk-used').textContent = `${systemMetrics.disk.used} GB`;
            document.getElementById('disk-total').textContent = `${systemMetrics.disk.total} GB`;
            document.getElementById('disk-free').textContent = `${systemMetrics.disk.free} GB`;
            updateProgressRing('disk-progress', diskPercentage);

            // Network
            const totalSpeed = systemMetrics.network.upload + systemMetrics.network.download;
            document.getElementById('network-speed').textContent = `${totalSpeed} KB/s`;
            document.getElementById('network-upload').textContent = `${systemMetrics.network.upload} KB/s`;
            document.getElementById('network-download').textContent = `${systemMetrics.network.download} KB/s`;
            document.getElementById('network-connections').textContent = systemMetrics.network.connections;

            // 更新系统状态
            updateSystemStatus();
        }

        // 更新系统状态
        function updateSystemStatus() {
            const cpuHigh = systemMetrics.cpu.current > 80;
            const memoryHigh = (systemMetrics.memory.used / systemMetrics.memory.total) > 0.9;
            const diskHigh = (systemMetrics.disk.used / systemMetrics.disk.total) > 0.9;

            const statusElement = document.getElementById('system-status');

            if (cpuHigh || memoryHigh || diskHigh) {
                statusElement.innerHTML = '<span class="status-warning">⚠ 系统警告</span>';

                // 生成警报
                if (cpuHigh && !alerts.some(a => a.type === 'cpu')) {
                    addAlert('cpu', 'CPU使用率过高', `当前CPU使用率: ${systemMetrics.cpu.current}%`, 'warning');
                }
                if (memoryHigh && !alerts.some(a => a.type === 'memory')) {
                    addAlert('memory', '内存使用率过高', `当前内存使用率: ${Math.floor((systemMetrics.memory.used / systemMetrics.memory.total) * 100)}%`, 'warning');
                }
                if (diskHigh && !alerts.some(a => a.type === 'disk')) {
                    addAlert('disk', '磁盘空间不足', `当前磁盘使用率: ${Math.floor((systemMetrics.disk.used / systemMetrics.disk.total) * 100)}%`, 'warning');
                }
            } else {
                statusElement.innerHTML = '<span class="status-healthy">● 系统正常</span>';
            }
        }

        // 添加警报
        function addAlert(type, title, message, level) {
            const alert = {
                id: Date.now(),
                type: type,
                title: title,
                message: message,
                level: level,
                timestamp: new Date().toISOString()
            };

            alerts.unshift(alert);
            updateAlertsList();

            // 显示通知
            showNotification(title, message, level);
        }

        // 更新警报列表
        function updateAlertsList() {
            const alertsList = document.getElementById('alerts-list');

            if (alerts.length === 0) {
                alertsList.innerHTML = '<div class="text-center text-gray-500 py-4">暂无警报</div>';
                return;
            }

            alertsList.innerHTML = alerts.slice(0, 10).map(alert => `
                <div class="flex items-center justify-between p-3 border rounded-lg ${alert.level === 'critical' ? 'border-red-200 bg-red-50' : alert.level === 'warning' ? 'border-yellow-200 bg-yellow-50' : 'border-blue-200 bg-blue-50'}">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center ${alert.level === 'critical' ? 'bg-red-100' : alert.level === 'warning' ? 'bg-yellow-100' : 'bg-blue-100'}">
                            <span class="${alert.level === 'critical' ? 'text-red-600' : alert.level === 'warning' ? 'text-yellow-600' : 'text-blue-600'}">${alert.level === 'critical' ? '🚨' : alert.level === 'warning' ? '⚠️' : 'ℹ️'}</span>
                        </div>
                        <div>
                            <div class="font-medium">${alert.title}</div>
                            <div class="text-sm text-gray-600">${alert.message}</div>
                            <div class="text-xs text-gray-500">${formatDateTime(alert.timestamp)}</div>
                        </div>
                    </div>
                    <button class="text-gray-400 hover:text-gray-600" onclick="removeAlert(${alert.id})">
                        ✕
                    </button>
                </div>
            `).join('');
        }

        // 移除警报
        function removeAlert(alertId) {
            alerts = alerts.filter(alert => alert.id !== alertId);
            updateAlertsList();
        }

        // 清除所有警报
        function clearAllAlerts() {
            alerts = [];
            updateAlertsList();
            showNotification('警报清除', '所有警报已清除', 'success');
        }

        // 初始化图表
        function initCharts() {
            // 系统资源趋势图表
            const resourcesCtx = document.getElementById('resources-chart').getContext('2d');
            resourcesChart = new Chart(resourcesCtx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 20}, (_, i) => `${i}分钟前`).reverse(),
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: Array.from({length: 20}, () => Math.floor(Math.random() * 30) + 10),
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '内存使用率 (%)',
                        data: Array.from({length: 20}, () => Math.floor(Math.random() * 40) + 30),
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }, {
                        label: '磁盘使用率 (%)',
                        data: Array.from({length: 20}, () => Math.floor(Math.random() * 20) + 20),
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '使用率 (%)'
                            }
                        }
                    }
                }
            });

            // API性能图表
            const apiCtx = document.getElementById('api-performance-chart').getContext('2d');
            apiPerformanceChart = new Chart(apiCtx, {
                type: 'bar',
                data: {
                    labels: ['/api/journal', '/api/ai/process', '/api/reports', '/api/auth', '/api/backup'],
                    datasets: [{
                        label: '响应时间 (ms)',
                        data: [45, 120, 80, 35, 200],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '响应时间 (ms)'
                            }
                        }
                    }
                }
            });
        }

        // 更新性能指标
        function updatePerformanceMetrics() {
            // 模拟性能数据
            const requestsPerSecond = Math.floor(Math.random() * 50) + 20;
            const avgResponseTime = Math.floor(Math.random() * 100) + 50;
            const activeUsers = Math.floor(Math.random() * 20) + 5;
            const cacheHitRate = Math.floor(Math.random() * 20) + 80;

            document.getElementById('requests-per-second').textContent = requestsPerSecond;
            document.getElementById('avg-response-time').textContent = `${avgResponseTime}ms`;
            document.getElementById('avg-response-time-display').textContent = `${avgResponseTime}ms`;
            document.getElementById('active-users').textContent = activeUsers;
            document.getElementById('cache-hit-rate').textContent = `${cacheHitRate}%`;
        }

        // 更新运行时间
        function updateUptime() {
            const startTime = new Date('2024-01-09T00:00:00');
            const now = new Date();
            const uptime = now - startTime;

            const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
            const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

            document.getElementById('uptime').textContent = `${days}天 ${hours}小时`;
        }

        // 格式化日期时间
        function formatDateTime(isoString) {
            const date = new Date(isoString);
            return date.toLocaleString('ja-JP');
        }

        // 显示通知
        function showNotification(title, message, type = 'info') {
            const notificationArea = document.getElementById('notification-area');

            const notification = document.createElement('div');
            notification.className = `max-w-sm bg-white border-l-4 p-4 shadow-lg rounded-lg`;

            switch(type) {
                case 'success':
                    notification.classList.add('border-green-500');
                    break;
                case 'error':
                case 'critical':
                    notification.classList.add('border-red-500');
                    break;
                case 'warning':
                    notification.classList.add('border-yellow-500');
                    break;
                default:
                    notification.classList.add('border-blue-500');
            }

            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800">${title}</h4>
                        <p class="text-sm text-gray-600">${message}</p>
                    </div>
                    <button class="ml-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                        ✕
                    </button>
                </div>
            `;

            notificationArea.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // 刷新所有数据
        function refreshAllData() {
            updateSystemMetrics();
            updatePerformanceMetrics();
            updateUptime();

            // 更新图表数据
            if (resourcesChart) {
                const newCpuData = Math.floor(Math.random() * 30) + 10;
                const newMemoryData = Math.floor(Math.random() * 40) + 30;
                const newDiskData = Math.floor(Math.random() * 20) + 20;

                resourcesChart.data.datasets[0].data.shift();
                resourcesChart.data.datasets[0].data.push(newCpuData);
                resourcesChart.data.datasets[1].data.shift();
                resourcesChart.data.datasets[1].data.push(newMemoryData);
                resourcesChart.data.datasets[2].data.shift();
                resourcesChart.data.datasets[2].data.push(newDiskData);

                resourcesChart.update('none');
            }

            if (apiPerformanceChart) {
                // 更新API响应时间数据
                apiPerformanceChart.data.datasets[0].data = [
                    Math.floor(Math.random() * 50) + 30,
                    Math.floor(Math.random() * 100) + 80,
                    Math.floor(Math.random() * 60) + 50,
                    Math.floor(Math.random() * 40) + 20,
                    Math.floor(Math.random() * 150) + 100
                ];
                apiPerformanceChart.update('none');
            }

            showNotification('数据刷新', '所有监控数据已更新', 'success');
        }

        // 切换图表时间范围
        function switchChartTimeRange(range) {
            // 更新按钮状态
            document.querySelectorAll('[id^="chart-"]').forEach(btn => {
                btn.className = 'bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400';
            });
            document.getElementById(`chart-${range}-btn`).className = 'bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600';

            // 更新图表数据
            let dataPoints, labels;
            switch(range) {
                case '1h':
                    dataPoints = 20;
                    labels = Array.from({length: dataPoints}, (_, i) => `${i}分钟前`).reverse();
                    break;
                case '24h':
                    dataPoints = 24;
                    labels = Array.from({length: dataPoints}, (_, i) => `${i}小时前`).reverse();
                    break;
                case '7d':
                    dataPoints = 7;
                    labels = Array.from({length: dataPoints}, (_, i) => `${i}天前`).reverse();
                    break;
            }

            if (resourcesChart) {
                resourcesChart.data.labels = labels;
                resourcesChart.data.datasets[0].data = Array.from({length: dataPoints}, () => Math.floor(Math.random() * 30) + 10);
                resourcesChart.data.datasets[1].data = Array.from({length: dataPoints}, () => Math.floor(Math.random() * 40) + 30);
                resourcesChart.data.datasets[2].data = Array.from({length: dataPoints}, () => Math.floor(Math.random() * 20) + 20);
                resourcesChart.update();
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 初始化数据
            updateSystemMetrics();
            updatePerformanceMetrics();
            updateUptime();
            updateAlertsList();

            // 刷新全部按钮
            document.getElementById('refresh-all-btn').addEventListener('click', refreshAllData);

            // 清除警报按钮
            document.getElementById('clear-alerts-btn').addEventListener('click', clearAllAlerts);

            // 图表时间范围按钮
            document.getElementById('chart-1h-btn').addEventListener('click', () => switchChartTimeRange('1h'));
            document.getElementById('chart-24h-btn').addEventListener('click', () => switchChartTimeRange('24h'));
            document.getElementById('chart-7d-btn').addEventListener('click', () => switchChartTimeRange('7d'));

            // 定期更新数据
            setInterval(updateSystemMetrics, 5000); // 每5秒更新系统指标
            setInterval(updatePerformanceMetrics, 10000); // 每10秒更新性能指标
            setInterval(updateUptime, 60000); // 每分钟更新运行时间

            // 模拟一些初始警报
            setTimeout(() => {
                addAlert('info', '系统启动', '监控系统已成功启动', 'info');
            }, 2000);

            setTimeout(() => {
                addAlert('warning', '内存使用警告', '内存使用率超过70%', 'warning');
            }, 10000);
        });
    </script>
</body>
</html>

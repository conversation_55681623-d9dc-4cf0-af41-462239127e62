#!/usr/bin/env python3
"""
测试税率功能更新
"""
import requests
import json

def test_journal_entries_api():
    """测试记账API是否支持税率"""
    
    print("🔍 测试记账API税率支持")
    print("=" * 50)
    
    try:
        # 获取记录列表
        response = requests.get("http://localhost:8000/journal-entries/default")
        
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ 记账API正常工作，返回 {len(entries)} 条记录")
            
            if entries:
                entry = entries[0]
                
                # 检查税率字段
                has_debit_tax = 'debit_tax_rate' in entry
                has_credit_tax = 'credit_tax_rate' in entry
                
                print(f"\n📊 记录字段检查:")
                print(f"  debit_tax_rate: {'✅ 支持' if has_debit_tax else '❌ 不支持'}")
                print(f"  credit_tax_rate: {'✅ 支持' if has_credit_tax else '❌ 不支持'}")
                
                if has_debit_tax and has_credit_tax:
                    print(f"  借方税率: {entry.get('debit_tax_rate', 'N/A')}%")
                    print(f"  贷方税率: {entry.get('credit_tax_rate', 'N/A')}%")
                    return True
                else:
                    print("⚠️ API返回的记录缺少税率字段")
                    return False
            else:
                print("📝 没有记录可供测试")
                return True
        else:
            print(f"❌ 记账API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 记账API测试失败: {e}")
        return False

def test_journal_entries_page():
    """测试记账页面是否显示税率列"""
    
    print(f"\n📄 测试记账页面税率显示")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8000/journal_entries.html")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查页面内容
            checks = [
                ("借方税率表头", "借方税率" in content),
                ("贷方税率表头", "贷方税率" in content),
                ("税率数据显示", "debit_tax_rate" in content and "credit_tax_rate" in content),
                ("编辑税率字段", "edit-debit-tax-rate" in content and "edit-credit-tax-rate" in content),
                ("隐藏类型列", 'style="display: none;"' in content),
                ("多语言支持", "table.debit_tax_rate" in content)
            ]
            
            print("页面功能检查:")
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}")
            
            all_checks_passed = all(result for _, result in checks)
            
            if all_checks_passed:
                print("✅ 记账页面税率功能完整")
            else:
                print("⚠️ 记账页面部分功能可能有问题")
            
            return all_checks_passed
            
        else:
            print(f"❌ 记账页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 记账页面测试失败: {e}")
        return False

def test_database_manager():
    """测试DatabaseManager是否支持税率"""
    
    print(f"\n🗄️ 测试DatabaseManager税率支持")
    print("=" * 30)
    
    try:
        import sys
        from pathlib import Path
        
        # 添加backend路径
        backend_path = Path(__file__).parent / "backend"
        sys.path.insert(0, str(backend_path))
        
        from database import DatabaseManager
        
        db = DatabaseManager("backend/goldenledger_accounting.db")
        
        # 获取记录
        entries = db.get_journal_entries('default', limit=1)
        
        if entries:
            entry = entries[0]
            
            has_debit_tax = 'debit_tax_rate' in entry
            has_credit_tax = 'credit_tax_rate' in entry
            
            print(f"📊 DatabaseManager字段检查:")
            print(f"  debit_tax_rate: {'✅ 支持' if has_debit_tax else '❌ 不支持'}")
            print(f"  credit_tax_rate: {'✅ 支持' if has_credit_tax else '❌ 不支持'}")
            
            if has_debit_tax and has_credit_tax:
                print(f"  示例记录:")
                print(f"    ID: {entry.get('id')}")
                print(f"    借方税率: {entry.get('debit_tax_rate', 0)}%")
                print(f"    贷方税率: {entry.get('credit_tax_rate', 0)}%")
                return True
            else:
                return False
        else:
            print("📝 没有记录可供测试")
            return True
            
    except Exception as e:
        print(f"❌ DatabaseManager测试失败: {e}")
        return False

def test_edit_functionality():
    """测试编辑功能是否支持税率"""
    
    print(f"\n✏️ 测试编辑功能税率支持")
    print("=" * 30)
    
    try:
        # 获取一条记录进行编辑测试
        response = requests.get("http://localhost:8000/journal-entries/default")
        
        if response.status_code == 200:
            entries = response.json()
            
            if entries:
                entry = entries[0]
                entry_id = entry['id']
                
                # 准备更新数据（包含税率）
                update_data = {
                    'id': entry_id,
                    'entry_date': entry.get('entry_date'),
                    'entry_time': entry.get('entry_time'),
                    'description': entry.get('description'),
                    'debit_account': entry.get('debit_account'),
                    'credit_account': entry.get('credit_account'),
                    'amount': entry.get('amount'),
                    'reference_number': entry.get('reference_number'),
                    'debit_tax_rate': 8.0,  # 测试税率
                    'credit_tax_rate': 0.0
                }
                
                # 发送更新请求
                update_response = requests.put(
                    f"http://localhost:8000/journal-entries/{entry_id}",
                    json=update_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if update_response.status_code == 200:
                    print("✅ 编辑API支持税率字段")
                    
                    # 验证更新
                    verify_response = requests.get("http://localhost:8000/journal-entries/default")
                    if verify_response.status_code == 200:
                        updated_entries = verify_response.json()
                        updated_entry = next((e for e in updated_entries if e['id'] == entry_id), None)
                        
                        if updated_entry:
                            debit_tax = updated_entry.get('debit_tax_rate', 0)
                            credit_tax = updated_entry.get('credit_tax_rate', 0)
                            
                            print(f"  更新后借方税率: {debit_tax}%")
                            print(f"  更新后贷方税率: {credit_tax}%")
                            
                            if debit_tax == 8.0 and credit_tax == 0.0:
                                print("✅ 税率更新成功")
                                return True
                            else:
                                print("❌ 税率更新失败")
                                return False
                        else:
                            print("❌ 找不到更新后的记录")
                            return False
                    else:
                        print("❌ 验证更新失败")
                        return False
                else:
                    print(f"❌ 编辑API失败: {update_response.status_code}")
                    print(f"响应: {update_response.text}")
                    return False
            else:
                print("📝 没有记录可供编辑测试")
                return True
        else:
            print(f"❌ 获取记录失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 编辑功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始测试税率功能更新")
    
    # 测试API支持
    api_success = test_journal_entries_api()
    
    # 测试页面显示
    page_success = test_journal_entries_page()
    
    # 测试DatabaseManager
    db_success = test_database_manager()
    
    # 测试编辑功能
    edit_success = test_edit_functionality()
    
    print("\n" + "=" * 80)
    print("📊 税率功能测试结果:")
    
    if api_success and page_success and db_success and edit_success:
        print("🎉 税率功能更新完全成功！")
        print("\n✅ 功能确认:")
        print("  - 数据库支持税率字段")
        print("  - API返回税率数据")
        print("  - 页面显示税率列")
        print("  - 编辑功能支持税率")
        print("  - 隐藏了类型和置信度列")
        
        print(f"\n🌐 现在可以使用:")
        print("  记账页面: http://localhost:8000/journal_entries.html")
        print("  - 表格显示借方税率和贷方税率列")
        print("  - 编辑记录时可以设置税率")
        print("  - 类型和置信度列已隐藏")
        
    else:
        print("❌ 部分功能测试失败")
        if not api_success:
            print("  - API税率支持有问题")
        if not page_success:
            print("  - 页面税率显示有问题")
        if not db_success:
            print("  - DatabaseManager税率支持有问题")
        if not edit_success:
            print("  - 编辑功能税率支持有问题")

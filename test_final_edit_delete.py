#!/usr/bin/env python3
"""
最终测试编辑和删除功能
"""
import requests
import json

def test_final_edit_delete():
    """最终测试编辑和删除功能"""
    
    print("🎯 最终测试编辑和删除功能")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 获取记录列表
        print("\n📋 步骤1: 获取记录列表")
        response = requests.get(f"{base_url}/journal-entries/default")
        entries = response.json()
        print(f"✅ 获取到 {len(entries)} 条记录")
        
        if len(entries) == 0:
            print("❌ 没有记录可以测试")
            return False
        
        # 选择第一条记录进行编辑测试
        test_entry = entries[0]
        entry_id = test_entry['id']
        original_description = test_entry.get('description', '')
        original_amount = test_entry.get('amount', 0)
        
        print(f"📝 选择记录: {entry_id}")
        print(f"   原始描述: {original_description}")
        print(f"   原始金额: ¥{original_amount}")
        
        # 2. 测试编辑功能
        print(f"\n✏️ 步骤2: 测试编辑功能")
        
        edit_data = {
            "entry_date": test_entry.get('entry_date', '2025-07-11'),
            "entry_time": test_entry.get('entry_time', '12:00:00'),
            "description": f"【最终测试】{original_description}",
            "debit_account": test_entry.get('debit_account', '事務用品費'),
            "credit_account": test_entry.get('credit_account', '現金'),
            "amount": float(original_amount) + 0.01,
            "reference_number": f"FINAL_TEST_{test_entry.get('reference_number', '')}"
        }
        
        edit_response = requests.put(f"{base_url}/journal-entries/{entry_id}", json=edit_data)
        
        if edit_response.status_code == 200:
            edit_result = edit_response.json()
            print(f"✅ 编辑成功: {edit_result.get('message')}")
            
            # 验证编辑结果
            verify_response = requests.get(f"{base_url}/journal-entries/default")
            updated_entries = verify_response.json()
            updated_entry = next((e for e in updated_entries if e['id'] == entry_id), None)
            
            if updated_entry:
                print(f"✅ 编辑验证成功:")
                print(f"   新描述: {updated_entry.get('description')}")
                print(f"   新金额: ¥{updated_entry.get('amount')}")
                print(f"   新参考号: {updated_entry.get('reference_number')}")
            else:
                print("❌ 编辑验证失败: 找不到更新的记录")
                return False
        else:
            print(f"❌ 编辑失败: {edit_response.status_code}")
            return False
        
        # 3. 创建测试记录用于删除
        print(f"\n🗑️ 步骤3: 测试删除功能")
        
        test_delete_record = {
            "id": "FINAL_DELETE_TEST",
            "company_id": "default",
            "entry_date": "2025-07-11",
            "entry_time": "23:59:59",
            "description": "【最终删除测试】临时记录",
            "debit_account": "测试科目",
            "credit_account": "现金",
            "amount": 999.99,
            "reference_number": "FINAL_DELETE",
            "ai_generated": False
        }
        
        # 创建测试记录
        create_response = requests.post(f"{base_url}/journal-entries/save", json=test_delete_record)
        if create_response.status_code == 200:
            print("✅ 测试记录创建成功")
            
            # 删除测试记录
            delete_response = requests.delete(f"{base_url}/journal-entries/FINAL_DELETE_TEST")
            
            if delete_response.status_code == 200:
                delete_result = delete_response.json()
                print(f"✅ 删除成功: {delete_result.get('message')}")
                
                # 验证删除结果
                verify_delete_response = requests.get(f"{base_url}/journal-entries/default")
                final_entries = verify_delete_response.json()
                deleted_entry = next((e for e in final_entries if e['id'] == 'FINAL_DELETE_TEST'), None)
                
                if deleted_entry is None:
                    print("✅ 删除验证成功: 记录已被删除")
                else:
                    print("❌ 删除验证失败: 记录仍然存在")
                    return False
            else:
                print(f"❌ 删除失败: {delete_response.status_code}")
                return False
        else:
            print("❌ 测试记录创建失败")
            return False
        
        # 4. 测试前端页面访问
        print(f"\n🌐 步骤4: 测试前端页面访问")
        
        pages_to_test = [
            "/journal_entries.html",
            "/test_edit_modal.html",
            "/test_edit_delete_frontend.html"
        ]
        
        for page in pages_to_test:
            page_response = requests.get(f"{base_url}{page}")
            if page_response.status_code == 200:
                print(f"✅ {page} 访问成功")
            else:
                print(f"❌ {page} 访问失败: {page_response.status_code}")
        
        print("\n🎉 所有测试完成！")
        print("✅ 编辑功能正常")
        print("✅ 删除功能正常")
        print("✅ 前端页面正常")
        print("✅ JavaScript错误已修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_final_edit_delete()
    if success:
        print("\n🎯 编辑和删除功能完全正常！")
    else:
        print("\n❌ 测试失败，请检查错误信息")

/**
 * GoldenLedger API 配置
 * 统一管理前端 API 地址
 */

// 检测环境并设置 API 基础地址
function getAPIBaseURL() {
    // 强制本地开发环境检测
    const hostname = window.location.hostname;
    console.log('🔍 API配置检测 - hostname:', hostname);

    // 检查是否在 Cloudflare Pages 环境或生产域名
    if (hostname.includes('pages.dev') || hostname.includes('goldenledger') || hostname.includes('goldenorangetech.com')) {
        console.log('📍 检测到生产环境，使用Workers API');
        // 生产环境 - 使用 Cloudflare Workers API
        return 'https://goldenledger-api.souyousann.workers.dev';
    }

    // 本地开发环境 - 扩展检测条件
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.') || hostname.startsWith('10.')) {
        console.log('📍 检测到本地环境，使用本地API服务器');
        return 'http://127.0.0.1:8000';
    }

    console.log('📍 使用默认Workers API');
    // 默认使用 Workers API
    return 'https://goldenledger-api.souyousann.workers.dev';
}

// 全局 API 配置
window.GoldenLedgerAPI = {
    baseURL: getAPIBaseURL(),
    
    // API 端点
    endpoints: {
        health: '/api/health',
        companies: '/api/companies',
        journalEntries: '/api/journal-entries',
        aiProcess: '/api/ai/process-text',
        upload: '/api/upload',

        // 支付相关端点
        payment: {
            createSubscription: '/api/payment/create-subscription',
            approveSubscription: '/api/payment/approve-subscription',
            cancelSubscription: '/api/payment/cancel-subscription',
            subscriptionStatus: '/api/payment/subscription-status',
            webhook: '/api/payment/webhook',
            logError: '/api/payment/log-error'
        },

        // 订阅相关端点
        subscription: {
            activateFree: '/api/subscription/activate-free',
            status: '/api/subscription/status',
            details: '/api/subscription/details',
            cancel: '/api/subscription/cancel'
        },

        // 使用量相关端点
        usage: {
            record: '/api/usage/record',
            stats: '/api/usage/stats',
            limits: '/api/usage/limits'
        },

        // 认证相关端点
        auth: {
            login: '/api/auth/login',
            register: '/api/auth/register',
            verify: '/api/auth/verify',
            logout: '/api/auth/logout',
            googleAuth: '/api/auth/google'
        },

        // 兼容旧版本的端点
        attachments: (entryId) => `/attachments/${entryId}`,
        journalEntriesLegacy: (companyId) => `/journal-entries/${companyId}`
    },
    
    // 构建完整 URL
    url: function(endpoint, params = {}) {
        let url = this.baseURL + endpoint;
        
        // 添加查询参数
        const searchParams = new URLSearchParams(params);
        if (searchParams.toString()) {
            url += '?' + searchParams.toString();
        }
        
        return url;
    },
    
    // 发送请求的辅助函数
    async fetch(endpoint, options = {}) {
        const url = typeof endpoint === 'string' ? this.baseURL + endpoint : endpoint;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        console.log(`🌐 API请求: ${finalOptions.method || 'GET'} ${url}`);
        
        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log(`✅ API响应成功:`, data);
            
            return data;
        } catch (error) {
            console.error(`❌ API请求失败:`, error);
            throw error;
        }
    }
};

// 环境信息
console.log(`🔧 GoldenLedger API 配置已加载`);
console.log(`📍 当前环境: ${window.location.hostname}`);
console.log(`🌐 API基础地址: ${window.GoldenLedgerAPI.baseURL}`);

// 兼容性：为旧代码提供全局变量
window.API_BASE_URL = window.GoldenLedgerAPI.baseURL;

// Gemini API 配置
// 检测环境并设置API密钥
function getGeminiApiKey() {
    const hostname = window.location.hostname;

    // 优先级1: 检查是否有通过服务器注入的环境变量
    if (window.CLOUDFLARE_GEMINI_API_KEY) {
        console.log('🔑 使用Cloudflare环境变量中的API密钥');
        return window.CLOUDFLARE_GEMINI_API_KEY;
    }

    // 优先级2: 检查localStorage中是否有用户设置的API密钥
    const storedKey = localStorage.getItem('gemini_api_key');
    if (storedKey && storedKey !== 'YOUR_API_KEY_HERE') {
        console.log('🔑 使用本地存储的API密钥');
        return storedKey;
    }

    // 优先级3: 生产环境默认值
    if (hostname.includes('pages.dev') || hostname.includes('goldenledger') || hostname.includes('goldenorangetech.com')) {
        console.log('🌐 生产环境：等待服务器注入API密钥');
        return 'WAITING_FOR_SERVER_INJECTION';
    }

    // 优先级4: 本地开发环境 - 提示用户设置API密钥
    console.log('🏠 本地开发环境：请设置API密钥');
    return 'LOCAL_DEVELOPMENT_NEEDS_KEY';
}

window.GEMINI_API_KEY = getGeminiApiKey();

console.log('🤖 Gemini API 配置已加载');

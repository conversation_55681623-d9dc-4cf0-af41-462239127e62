/**
 * GoldenLedger - Background Music Control System
 * 背景音乐控制系统 - 支持全站音乐播放和用户偏好记忆
 */

class MusicController {
    constructor() {
        this.audio = null;
        this.isPlaying = false;
        this.volume = 0.3; // 默认音量30%
        this.storageKey = 'golden_ledger_music_settings';
        this.settingsExpiry = 30 * 24 * 60 * 60 * 1000; // 30天
        this.musicUrl = '/music/love.mp3';
        
        this.init();
    }

    /**
     * 初始化音乐控制系统
     */
    init() {
        this.createAudioElement();
        this.loadUserSettings();

        // 确保DOM加载完成后再创建控制界面
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.createMusicControl();
                this.setupEventListeners();
                this.autoPlayIfEnabled();
                console.log('🎵 音乐控制器已在DOM加载完成后初始化');
            });
        } else {
            this.createMusicControl();
            this.setupEventListeners();
            this.autoPlayIfEnabled();
            console.log('🎵 音乐控制器已立即初始化');
        }
    }

    /**
     * 创建音频元素
     */
    createAudioElement() {
        this.audio = new Audio(this.musicUrl);
        this.audio.loop = true;
        this.audio.volume = this.volume;
        this.audio.preload = 'auto';
        
        // 音频事件监听
        this.audio.addEventListener('loadstart', () => {
            console.log('🎵 背景音乐开始加载...');
        });
        
        this.audio.addEventListener('canplaythrough', () => {
            console.log('🎵 背景音乐加载完成');
        });
        
        this.audio.addEventListener('error', (e) => {
            console.error('🎵 背景音乐加载失败:', e);
        });
        
        this.audio.addEventListener('play', () => {
            this.isPlaying = true;
            this.updateControlUI();
        });
        
        this.audio.addEventListener('pause', () => {
            this.isPlaying = false;
            this.updateControlUI();
        });
    }

    /**
     * 加载用户设置
     */
    loadUserSettings() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const settings = JSON.parse(stored);
                const now = new Date().getTime();
                
                // 检查设置是否过期
                if (settings.expiry && now < settings.expiry) {
                    this.isPlaying = settings.enabled || false;
                    this.volume = settings.volume || 0.3;
                    
                    if (this.audio) {
                        this.audio.volume = this.volume;
                    }
                    
                    console.log('🎵 加载用户音乐设置:', settings);
                } else {
                    // 设置已过期，清除
                    localStorage.removeItem(this.storageKey);
                    console.log('🎵 音乐设置已过期，使用默认设置');
                }
            }
        } catch (error) {
            console.error('🎵 加载音乐设置失败:', error);
        }
    }

    /**
     * 保存用户设置
     */
    saveUserSettings() {
        try {
            const settings = {
                enabled: this.isPlaying,
                volume: this.volume,
                expiry: new Date().getTime() + this.settingsExpiry
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(settings));
            console.log('🎵 保存用户音乐设置:', settings);
        } catch (error) {
            console.error('🎵 保存音乐设置失败:', error);
        }
    }

    /**
     * 创建音乐控制界面
     */
    createMusicControl() {
        // 检查是否已存在控制器
        if (document.getElementById('music-controller')) {
            console.log('🎵 音乐控制器已存在，跳过创建');
            return;
        }

        console.log('🎵 开始创建音乐控制器...');

        const controller = document.createElement('div');
        controller.id = 'music-controller';
        controller.innerHTML = `
            <!-- 音乐按钮 -->
            <button id="music-toggle" class="music-btn" title="背景音乐开关">
                🎵
            </button>

            <!-- 音量控制面板（初始隐藏） -->
            <div id="music-volume-panel" class="music-volume-panel" style="display: none;">
                <div class="volume-arrow"></div>
                <div class="volume-content">
                    <input type="range" id="music-volume" min="0" max="100" value="${this.volume * 100}" class="music-volume-slider">
                    <span class="volume-label">${Math.round(this.volume * 100)}%</span>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #music-controller {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 99999;
                background: transparent;
                border-radius: 0;
                padding: 0;
                border: none;
                transition: all 0.3s ease;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                #music-controller {
                    top: 70px;
                    right: 15px;
                }
            }

            #music-controller:hover {
                background: transparent;
            }
            
            .music-control-panel {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .music-btn {
                width: 40px;
                height: 40px;
                border: none;
                border-radius: 50%;
                background: #6c5ce7;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                transition: all 0.3s ease;
                box-shadow: 0 2px 10px rgba(108, 92, 231, 0.3);
            }
            
            .music-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(108, 92, 231, 0.4);
            }
            
            .music-btn.playing {
                animation: musicPulse 2s infinite;
            }
            
            .music-btn.paused {
                background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
            }
            
            @keyframes musicPulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
            
            .music-volume-container {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 25px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                border: 2px solid #6c5ce7;
                margin-left: 8px;
            }
            
            .music-volume-slider {
                width: 80px;
                height: 4px;
                border-radius: 2px;
                background: #e0e0e0;
                outline: none;
                cursor: pointer;
            }
            
            .music-volume-slider::-webkit-slider-thumb {
                appearance: none;
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
                cursor: pointer;
                box-shadow: 0 2px 6px rgba(108, 92, 231, 0.3);
            }
            
            .music-volume-slider::-moz-range-thumb {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
                cursor: pointer;
                border: none;
                box-shadow: 0 2px 6px rgba(108, 92, 231, 0.3);
            }
            
            .volume-label {
                font-size: 12px;
                color: #666;
                min-width: 30px;
                text-align: center;
            }
            
            .music-icon {
                display: inline-block;
                transition: all 0.3s ease;
            }
            
            .music-btn.playing .music-icon {
                animation: musicNote 1s infinite;
            }
            
            @keyframes musicNote {
                0%, 100% { transform: rotate(0deg); }
                25% { transform: rotate(-5deg); }
                75% { transform: rotate(5deg); }
            }
            
            /* 移动端适配 */
            @media (max-width: 768px) {
                #music-controller {
                    top: 10px;
                    right: 10px;
                    padding: 6px;
                }
                
                .music-btn {
                    width: 36px;
                    height: 36px;
                    font-size: 14px;
                }
                
                .music-volume-slider {
                    width: 60px;
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(controller);

        console.log('🎵 音乐控制器已添加到页面');
        this.updateControlUI();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 等待DOM元素创建完成后再绑定事件
        setTimeout(() => {
            // 音乐开关按钮
            const toggleBtn = document.getElementById('music-toggle');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', () => {
                    this.toggleMusic();
                });
                console.log('🎵 音乐开关按钮事件已绑定');
            }

            // 音量控制
            const volumeSlider = document.getElementById('music-volume');
            if (volumeSlider) {
                volumeSlider.addEventListener('input', (e) => {
                    this.setVolume(e.target.value / 100);
                });
                console.log('🎵 音量控制事件已绑定');
            }
        }, 100);

        // 音量控制显示/隐藏逻辑
        setTimeout(() => {
            const controller = document.getElementById('music-controller');
            const volumeContainer = document.querySelector('.music-volume-container');
            const toggleBtn = document.getElementById('music-toggle');

            if (controller && volumeContainer && toggleBtn) {
                let isVolumeVisible = false;

                // 点击音乐按钮切换音量控制显示
                toggleBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    if (!isVolumeVisible) {
                        volumeContainer.style.display = 'flex';
                        isVolumeVisible = true;
                    }
                });

                // 鼠标悬停显示音量控制（桌面端）
                if (!this.isMobileDevice()) {
                    controller.addEventListener('mouseenter', () => {
                        volumeContainer.style.display = 'flex';
                        isVolumeVisible = true;
                    });

                    controller.addEventListener('mouseleave', () => {
                        setTimeout(() => {
                            volumeContainer.style.display = 'none';
                            isVolumeVisible = false;
                        }, 500);
                    });
                }

                // 点击其他位置隐藏音量控制
                document.addEventListener('click', (e) => {
                    if (isVolumeVisible && !controller.contains(e.target)) {
                        volumeContainer.style.display = 'none';
                        isVolumeVisible = false;
                    }
                });

                // 阻止音量控制区域的点击事件冒泡
                volumeContainer.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                console.log('🎵 音量控制交互事件已绑定');
            }
        }, 200);

        // 检测移动设备的辅助方法
        this.isMobileDevice = () => {
            return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        };

        // 页面可见性变化时暂停/恢复音乐
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                if (this.isPlaying && this.audio && !this.audio.paused) {
                    this.audio.pause();
                }
            } else {
                if (this.isPlaying && this.audio && this.audio.paused) {
                    this.playMusic();
                }
            }
        });
    }

    /**
     * 自动播放（如果用户之前开启了音乐）
     */
    autoPlayIfEnabled() {
        if (this.isPlaying) {
            this.playMusic();
        }
    }

    /**
     * 切换音乐播放状态
     */
    toggleMusic() {
        if (this.isPlaying) {
            this.pauseMusic();
        } else {
            this.playMusic();
        }
    }

    /**
     * 播放音乐
     */
    async playMusic() {
        try {
            if (this.audio) {
                await this.audio.play();
                this.isPlaying = true;
                this.saveUserSettings();
                console.log('🎵 背景音乐开始播放');
            }
        } catch (error) {
            console.error('🎵 音乐播放失败:', error);
            // 某些浏览器需要用户交互才能播放音频
            if (error.name === 'NotAllowedError') {
                console.log('🎵 需要用户交互才能播放音乐');
            }
        }
    }

    /**
     * 暂停音乐
     */
    pauseMusic() {
        if (this.audio) {
            this.audio.pause();
            this.isPlaying = false;
            this.saveUserSettings();
            console.log('🎵 背景音乐已暂停');
        }
    }

    /**
     * 设置音量
     */
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        if (this.audio) {
            this.audio.volume = this.volume;
        }
        
        // 更新音量显示
        const volumeLabel = document.querySelector('.volume-label');
        if (volumeLabel) {
            volumeLabel.textContent = `${Math.round(this.volume * 100)}%`;
        }
        
        this.saveUserSettings();
        console.log('🎵 音量设置为:', Math.round(this.volume * 100) + '%');
    }

    /**
     * 更新控制界面
     */
    updateControlUI() {
        const toggleBtn = document.getElementById('music-toggle');
        const volumeSlider = document.getElementById('music-volume');
        
        if (toggleBtn) {
            if (this.isPlaying) {
                toggleBtn.classList.add('playing');
                toggleBtn.classList.remove('paused');
                toggleBtn.title = '暂停背景音乐';
            } else {
                toggleBtn.classList.remove('playing');
                toggleBtn.classList.add('paused');
                toggleBtn.title = '播放背景音乐';
            }
        }
        
        if (volumeSlider) {
            volumeSlider.value = this.volume * 100;
        }
    }

    /**
     * 获取音乐状态
     */
    getStatus() {
        return {
            isPlaying: this.isPlaying,
            volume: this.volume,
            currentTime: this.audio ? this.audio.currentTime : 0,
            duration: this.audio ? this.audio.duration : 0
        };
    }
}

// 确保在DOM加载完成后创建全局音乐控制器实例
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.musicController = new MusicController();
        console.log('🎵 全局音乐控制器已在DOM加载完成后创建');
    });
} else {
    window.musicController = new MusicController();
    console.log('🎵 全局音乐控制器已立即创建');
}

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MusicController;
}

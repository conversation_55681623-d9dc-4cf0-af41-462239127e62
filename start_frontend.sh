#!/bin/bash

echo "🚀 启动GoldenLedger — Smart AI-Powered Finance System前端"
echo "=================================="

# 检查Node.js
echo "📋 检查Node.js环境..."
node --version
npm --version

# 进入前端目录
echo "📁 进入前端目录..."
cd frontend

# 检查package.json
echo "📦 检查package.json..."
if [ ! -f "package.json" ]; then
    echo "❌ package.json不存在"
    exit 1
fi

# 检查node_modules
echo "📚 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "⚠️ 依赖未安装，正在安装..."
    npm install
fi

# 检查index.html
echo "📄 检查index.html..."
if [ ! -f "index.html" ]; then
    echo "❌ index.html不存在"
    exit 1
fi

# 启动开发服务器
echo "🚀 启动开发服务器..."
echo "访问地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务器"
echo "=================================="

npm run dev

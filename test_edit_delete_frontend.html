<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑删除功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">编辑删除功能测试</h1>
        
        <!-- 记录列表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">仕訳记录列表</h2>
            <div id="entries-container">
                <div class="text-center text-gray-500">加载中...</div>
            </div>
        </div>
        
        <!-- 状态显示 -->
        <div id="status" class="text-sm text-gray-600"></div>
    </div>

    <script>
        let allEntries = [];

        // 页面加载时获取记录
        document.addEventListener('DOMContentLoaded', function() {
            loadEntries();
        });

        // 加载记录列表
        async function loadEntries() {
            try {
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                allEntries = entries;
                displayEntries(entries);
            } catch (error) {
                console.error('加载记录失败:', error);
                document.getElementById('entries-container').innerHTML = 
                    '<div class="text-red-500">加载记录失败</div>';
            }
        }

        // 显示记录列表
        function displayEntries(entries) {
            const container = document.getElementById('entries-container');
            
            if (entries.length === 0) {
                container.innerHTML = '<div class="text-gray-500">暂无记录</div>';
                return;
            }

            const html = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">日期</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">描述</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">金额</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${entries.map(entry => `
                                <tr>
                                    <td class="px-4 py-2 text-sm text-gray-900">${entry.id}</td>
                                    <td class="px-4 py-2 text-sm text-gray-900">${entry.entry_date || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-900">${entry.description || 'N/A'}</td>
                                    <td class="px-4 py-2 text-sm text-gray-900">¥${entry.amount || 0}</td>
                                    <td class="px-4 py-2 text-sm text-gray-900">
                                        <div class="flex space-x-2">
                                            <button onclick="editEntry('${entry.id}')" 
                                                    class="text-indigo-600 hover:text-indigo-800 text-sm">
                                                编辑
                                            </button>
                                            <button onclick="deleteEntry('${entry.id}')" 
                                                    class="text-red-600 hover:text-red-800 text-sm">
                                                删除
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 编辑记录
        async function editEntry(entryId) {
            try {
                const entry = allEntries.find(e => e.id === entryId);
                if (!entry) {
                    alert('记录不存在');
                    return;
                }

                // 简单的编辑对话框
                const newDescription = prompt('编辑描述:', entry.description || '');
                if (newDescription === null) return; // 用户取消

                const newAmount = prompt('编辑金额:', entry.amount || '0');
                if (newAmount === null) return; // 用户取消

                // 准备编辑数据
                const editData = {
                    entry_date: entry.entry_date || '2025-07-11',
                    entry_time: entry.entry_time || '12:00:00',
                    description: newDescription,
                    debit_account: entry.debit_account || '事務用品費',
                    credit_account: entry.credit_account || '現金',
                    amount: parseFloat(newAmount) || 0,
                    reference_number: entry.reference_number || ''
                };

                // 发送编辑请求
                const response = await fetch(`/journal-entries/${entryId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(editData)
                });

                if (response.ok) {
                    const result = await response.json();
                    document.getElementById('status').textContent = '编辑成功！';
                    document.getElementById('status').className = 'text-green-600';
                    
                    // 重新加载记录
                    await loadEntries();
                } else {
                    throw new Error('编辑失败');
                }

            } catch (error) {
                console.error('编辑失败:', error);
                document.getElementById('status').textContent = '编辑失败: ' + error.message;
                document.getElementById('status').className = 'text-red-600';
            }
        }

        // 删除记录
        async function deleteEntry(entryId) {
            try {
                if (!confirm('确定要删除这条记录吗？此操作无法撤销。')) {
                    return;
                }

                // 发送删除请求
                const response = await fetch(`/journal-entries/${entryId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    document.getElementById('status').textContent = '删除成功！';
                    document.getElementById('status').className = 'text-green-600';
                    
                    // 重新加载记录
                    await loadEntries();
                } else {
                    throw new Error('删除失败');
                }

            } catch (error) {
                console.error('删除失败:', error);
                document.getElementById('status').textContent = '删除失败: ' + error.message;
                document.getElementById('status').className = 'text-red-600';
            }
        }
    </script>
</body>
</html>

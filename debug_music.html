<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐控制器调试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .debug-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .debug-log {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 12px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">🎵 音乐控制器调试页面</h1>
        
        <!-- 状态检查 -->
        <div class="debug-box">
            <h2 class="text-xl font-semibold mb-4">📊 系统状态检查</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <strong>DOM状态:</strong> <span id="dom-status">检查中...</span>
                </div>
                <div>
                    <strong>音乐控制器:</strong> <span id="controller-status">检查中...</span>
                </div>
                <div>
                    <strong>音乐控制面板:</strong> <span id="panel-status">检查中...</span>
                </div>
                <div>
                    <strong>音频元素:</strong> <span id="audio-status">检查中...</span>
                </div>
            </div>
        </div>

        <!-- 手动测试按钮 -->
        <div class="debug-box">
            <h2 class="text-xl font-semibold mb-4">🎛️ 手动测试</h2>
            <div class="space-x-4">
                <button id="create-controller" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    创建音乐控制器
                </button>
                <button id="test-audio" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    测试音频播放
                </button>
                <button id="check-elements" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    检查DOM元素
                </button>
                <button id="clear-log" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    清除日志
                </button>
            </div>
        </div>

        <!-- 调试日志 -->
        <div class="debug-box">
            <h2 class="text-xl font-semibold mb-4">📝 调试日志</h2>
            <div id="debug-log" class="debug-log">等待日志输出...</div>
        </div>

        <!-- 返回链接 -->
        <div class="text-center mt-8">
            <a href="index.html" class="text-blue-600 hover:text-blue-800 underline">← 返回首页</a>
        </div>
    </div>

    <script>
        // 调试日志功能
        const debugLog = document.getElementById('debug-log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        // 重写console.log来捕获音乐控制器的日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('🎵')) {
                log(args.join(' '));
            }
        };

        // 状态检查函数
        function checkStatus() {
            // DOM状态
            document.getElementById('dom-status').textContent = 
                document.readyState === 'complete' ? '✅ 完成' : '⏳ 加载中';

            // 音乐控制器
            document.getElementById('controller-status').textContent = 
                window.musicController ? '✅ 已创建' : '❌ 未创建';

            // 音乐控制面板
            const panel = document.getElementById('music-controller');
            document.getElementById('panel-status').textContent = 
                panel ? '✅ 已显示' : '❌ 未显示';

            // 音频元素
            const hasAudio = window.musicController && window.musicController.audio;
            document.getElementById('audio-status').textContent = 
                hasAudio ? '✅ 已创建' : '❌ 未创建';
        }

        // 页面加载完成后开始检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面DOM加载完成');
            
            // 定期检查状态
            setInterval(checkStatus, 1000);
            
            // 手动创建控制器按钮
            document.getElementById('create-controller').addEventListener('click', function() {
                log('手动创建音乐控制器...');
                try {
                    if (!window.musicController) {
                        window.musicController = new MusicController();
                        log('✅ 音乐控制器创建成功');
                    } else {
                        log('⚠️ 音乐控制器已存在');
                    }
                } catch (error) {
                    log('❌ 创建音乐控制器失败: ' + error.message);
                }
            });

            // 测试音频播放按钮
            document.getElementById('test-audio').addEventListener('click', function() {
                log('测试音频播放...');
                try {
                    if (window.musicController && window.musicController.audio) {
                        window.musicController.playMusic();
                        log('✅ 音频播放测试完成');
                    } else {
                        log('❌ 音乐控制器或音频元素不存在');
                    }
                } catch (error) {
                    log('❌ 音频播放测试失败: ' + error.message);
                }
            });

            // 检查DOM元素按钮
            document.getElementById('check-elements').addEventListener('click', function() {
                log('检查DOM元素...');
                
                const controller = document.getElementById('music-controller');
                const toggle = document.getElementById('music-toggle');
                const volume = document.getElementById('music-volume');
                
                log(`音乐控制器: ${controller ? '✅ 存在' : '❌ 不存在'}`);
                log(`播放按钮: ${toggle ? '✅ 存在' : '❌ 不存在'}`);
                log(`音量控制: ${volume ? '✅ 存在' : '❌ 不存在'}`);
                
                if (controller) {
                    const rect = controller.getBoundingClientRect();
                    log(`控制器位置: top=${rect.top}, right=${window.innerWidth - rect.right}`);
                    log(`控制器大小: ${rect.width}x${rect.height}`);
                    log(`z-index: ${getComputedStyle(controller).zIndex}`);
                }
            });

            // 清除日志按钮
            document.getElementById('clear-log').addEventListener('click', function() {
                debugLog.textContent = '日志已清除\n';
            });

            log('调试页面初始化完成');
        });

        // 监听音乐控制器创建
        let checkCount = 0;
        const checkInterval = setInterval(function() {
            checkCount++;
            if (window.musicController) {
                log('✅ 检测到音乐控制器已创建');
                clearInterval(checkInterval);
            } else if (checkCount > 50) { // 5秒后停止检查
                log('❌ 5秒内未检测到音乐控制器');
                clearInterval(checkInterval);
            }
        }, 100);
    </script>

    <!-- 在最后加载音乐控制器 -->
    <script src="music_control.js"></script>
</body>
</html>

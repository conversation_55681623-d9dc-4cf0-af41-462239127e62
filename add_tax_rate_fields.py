#!/usr/bin/env python3
"""
添加税率字段到数据库
"""
import sqlite3
import sys
from pathlib import Path

# 添加backend路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def add_tax_rate_fields():
    """添加税率字段到journal_entries表"""
    
    print("🔧 添加税率字段到数据库")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect("backend/goldenledger_accounting.db")
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='journal_entries';")
        if not cursor.fetchone():
            print("❌ journal_entries表不存在")
            return False
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(journal_entries);")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        print("📋 当前表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 检查税率字段是否已存在
        has_debit_tax_rate = 'debit_tax_rate' in existing_columns
        has_credit_tax_rate = 'credit_tax_rate' in existing_columns
        
        print(f"\n🔍 字段检查:")
        print(f"  debit_tax_rate: {'✅ 已存在' if has_debit_tax_rate else '❌ 不存在'}")
        print(f"  credit_tax_rate: {'✅ 已存在' if has_credit_tax_rate else '❌ 不存在'}")
        
        # 添加缺少的字段
        fields_added = 0
        
        if not has_debit_tax_rate:
            try:
                cursor.execute('ALTER TABLE journal_entries ADD COLUMN debit_tax_rate DECIMAL(5,2) DEFAULT 0')
                print("✅ 添加 debit_tax_rate 字段")
                fields_added += 1
            except Exception as e:
                print(f"❌ 添加 debit_tax_rate 字段失败: {e}")
        
        if not has_credit_tax_rate:
            try:
                cursor.execute('ALTER TABLE journal_entries ADD COLUMN credit_tax_rate DECIMAL(5,2) DEFAULT 0')
                print("✅ 添加 credit_tax_rate 字段")
                fields_added += 1
            except Exception as e:
                print(f"❌ 添加 credit_tax_rate 字段失败: {e}")
        
        # 提交更改
        conn.commit()
        
        # 验证字段添加
        cursor.execute("PRAGMA table_info(journal_entries);")
        updated_columns = cursor.fetchall()
        
        print(f"\n📊 更新后的表结构:")
        for col in updated_columns:
            is_new = col[1] in ['debit_tax_rate', 'credit_tax_rate']
            marker = "🆕" if is_new else "  "
            print(f"{marker} {col[1]} ({col[2]})")
        
        # 更新现有记录的默认税率
        if fields_added > 0:
            cursor.execute("SELECT COUNT(*) FROM journal_entries")
            total_records = cursor.fetchone()[0]
            
            if total_records > 0:
                print(f"\n🔄 更新现有记录的默认税率 ({total_records}条记录)")
                cursor.execute('''
                    UPDATE journal_entries 
                    SET debit_tax_rate = 0, credit_tax_rate = 0 
                    WHERE debit_tax_rate IS NULL OR credit_tax_rate IS NULL
                ''')
                updated_records = cursor.rowcount
                print(f"✅ 更新了 {updated_records} 条记录")
                conn.commit()
        
        conn.close()
        
        print(f"\n🎉 税率字段添加完成！")
        print(f"  新增字段: {fields_added}")
        print(f"  总字段数: {len(updated_columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加税率字段失败: {e}")
        return False

def update_database_manager():
    """更新DatabaseManager以支持税率字段"""
    
    print(f"\n🔧 检查DatabaseManager支持")
    print("=" * 30)
    
    try:
        from database import DatabaseManager
        
        # 测试创建记录
        db = DatabaseManager("backend/goldenledger_accounting.db")
        
        # 检查get_journal_entries是否返回税率字段
        entries = db.get_journal_entries('default', limit=1)
        
        if entries:
            entry = entries[0]
            has_debit_tax = 'debit_tax_rate' in entry
            has_credit_tax = 'credit_tax_rate' in entry
            
            print(f"📊 记录字段检查:")
            print(f"  debit_tax_rate: {'✅ 支持' if has_debit_tax else '❌ 不支持'}")
            print(f"  credit_tax_rate: {'✅ 支持' if has_credit_tax else '❌ 不支持'}")
            
            if has_debit_tax and has_credit_tax:
                print("✅ DatabaseManager已支持税率字段")
                return True
            else:
                print("⚠️ DatabaseManager需要更新以支持税率字段")
                return False
        else:
            print("📝 没有记录可供测试")
            return True
            
    except Exception as e:
        print(f"❌ DatabaseManager检查失败: {e}")
        return False

def test_tax_rate_functionality():
    """测试税率功能"""
    
    print(f"\n🧪 测试税率功能")
    print("=" * 30)
    
    try:
        # 连接数据库
        conn = sqlite3.connect("backend/goldenledger_accounting.db")
        cursor = conn.cursor()
        
        # 测试插入带税率的记录
        test_entry = {
            'id': 'TEST_TAX_RATE_001',
            'company_id': 'default',
            'entry_date': '2025-07-11',
            'description': '税率测试记录',
            'debit_account': '事務用品費',
            'credit_account': '現金',
            'amount': 1000,
            'debit_tax_rate': 10.0,
            'credit_tax_rate': 0.0,
            'ai_generated': False
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO journal_entries
            (id, company_id, entry_date, description, debit_account, credit_account,
             amount, debit_tax_rate, credit_tax_rate, ai_generated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_entry['id'],
            test_entry['company_id'],
            test_entry['entry_date'],
            test_entry['description'],
            test_entry['debit_account'],
            test_entry['credit_account'],
            test_entry['amount'],
            test_entry['debit_tax_rate'],
            test_entry['credit_tax_rate'],
            test_entry['ai_generated']
        ))
        
        conn.commit()
        
        # 验证插入
        cursor.execute('''
            SELECT id, description, debit_tax_rate, credit_tax_rate
            FROM journal_entries
            WHERE id = ?
        ''', (test_entry['id'],))
        
        result = cursor.fetchone()
        
        if result:
            print("✅ 税率记录插入成功")
            print(f"  ID: {result[0]}")
            print(f"  描述: {result[1]}")
            print(f"  借方税率: {result[2]}%")
            print(f"  贷方税率: {result[3]}%")
            
            # 清理测试记录
            cursor.execute("DELETE FROM journal_entries WHERE id = ?", (test_entry['id'],))
            conn.commit()
            print("🧹 测试记录已清理")
            
        else:
            print("❌ 税率记录插入失败")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 税率功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始添加税率字段")
    
    # 添加税率字段
    success = add_tax_rate_fields()
    
    if success:
        # 检查DatabaseManager支持
        db_support = update_database_manager()
        
        # 测试税率功能
        test_success = test_tax_rate_functionality()
        
        print("\n" + "=" * 80)
        print("📊 税率字段添加结果:")
        
        if success and test_success:
            print("🎉 税率字段添加完全成功！")
            print("\n✅ 完成内容:")
            print("  - 数据库添加了 debit_tax_rate 字段")
            print("  - 数据库添加了 credit_tax_rate 字段")
            print("  - 现有记录设置了默认税率 (0%)")
            print("  - 税率功能测试通过")
            
            print(f"\n🌐 现在可以使用:")
            print("  记账页面: http://localhost:8000/journal_entries.html")
            print("  - 编辑记录时可以设置借方和贷方税率")
            print("  - 税率显示在表格中")
            
        else:
            print("❌ 部分功能可能有问题")
            if not success:
                print("  - 数据库字段添加失败")
            if not test_success:
                print("  - 税率功能测试失败")
    else:
        print("\n❌ 税率字段添加失败，请检查错误信息")

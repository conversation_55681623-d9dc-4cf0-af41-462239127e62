/**
 * 用户状态组件
 * 在认证页面显示用户信息和登出按钮
 */

class UserStatusComponent {
    constructor() {
        this.authManager = window.authManager;
        this.userManager = window.userManager; // 保持向后兼容
        this.currentUser = null;
        this.statusElement = null;
        this.init();
    }

    // 初始化组件
    async init() {
        // 优先使用authManager获取Google OAuth用户信息
        if (this.authManager) {
            try {
                await this.authManager.initialize();
                this.currentUser = this.authManager.getCurrentUser();

                if (this.currentUser) {
                    this.createStatusComponent();
                    console.log('✅ 用户状态组件已初始化 (Google OAuth):', this.currentUser.name || this.currentUser.username);
                    return;
                }
            } catch (error) {
                console.error('❌ AuthManager初始化失败:', error);
            }
        }

        // 回退到userManager
        if (this.userManager) {
            this.currentUser = this.userManager.getCurrentUser();
            if (this.currentUser) {
                this.createStatusComponent();
                console.log('✅ 用户状态组件已初始化 (UserManager):', this.currentUser.name);
                return;
            }
        }

        console.error('❌ 无法获取用户信息，认证管理器和用户管理器都不可用');
    }

    // 创建用户状态组件
    createStatusComponent() {
        // 检查是否已存在
        if (document.getElementById('user-status-component')) {
            return;
        }

        // 创建组件容器
        const statusContainer = document.createElement('div');
        statusContainer.id = 'user-status-component';
        statusContainer.className = 'fixed top-4 left-4 z-50';

        // 创建组件HTML
        statusContainer.innerHTML = this.getComponentHTML();

        // 添加到页面
        document.body.appendChild(statusContainer);

        // 绑定事件
        this.bindEvents();

        // 添加样式
        this.addStyles();
    }

    // 获取组件HTML
    getComponentHTML() {
        const user = this.currentUser;
        const userName = user.name || user.username || 'ユーザー';

        // 优先使用Google OAuth头像，然后是用户管理器的默认头像
        let userAvatar;
        if (user.avatar_url) {
            userAvatar = user.avatar_url;
        } else if (user.picture) {
            userAvatar = user.picture;
        } else if (this.userManager && this.userManager.getDefaultAvatar) {
            userAvatar = this.userManager.getDefaultAvatar(userName);
        } else {
            // 生成默认头像
            userAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=4285f4&color=fff`;
        }

        return `
            <div class="user-status-card bg-white/90 backdrop-blur-md rounded-xl shadow-lg border border-gray-200 p-4 min-w-[280px]">
                <!-- 用户信息 -->
                <div class="flex items-center space-x-3 mb-3">
                    <img src="${userAvatar}" alt="${userName}" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="text-sm font-semibold text-gray-800">
                            「${userName}」でログイン中
                        </div>
                        <div class="text-xs text-gray-500">
                            ${user.email}
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <!-- 在线状态指示器 -->
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-xs text-green-600">オンライン</span>
                    </div>
                </div>

                <!-- 用户角色和登录时间 -->
                <div class="text-xs text-gray-500 mb-3 space-y-1">
                    <div class="flex justify-between">
                        <span>役割:</span>
                        <span class="font-medium">${this.getRoleDisplayName(user.role)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>ログイン:</span>
                        <span>${this.formatLoginTime(user.last_login_at || user.lastLoginAt)}</span>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button id="user-profile-btn" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs py-2 px-3 rounded-lg transition-colors">
                        プロフィール
                    </button>
                    <button id="logout-btn" class="flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-2 px-3 rounded-lg transition-colors">
                        ログアウト
                    </button>
                </div>

                <!-- 折叠/展开按钮 -->
                <button id="toggle-status-btn" class="absolute -right-2 -top-2 w-6 h-6 bg-purple-500 hover:bg-purple-600 text-white rounded-full text-xs transition-colors">
                    −
                </button>
            </div>

            <!-- 最小化状态 -->
            <div id="minimized-status" class="user-status-minimized bg-white/90 backdrop-blur-md rounded-full shadow-lg border border-gray-200 p-2 hidden">
                <div class="flex items-center space-x-2">
                    <img src="${userAvatar}" alt="${userName}" class="w-8 h-8 rounded-full object-cover">
                    <span class="text-sm font-medium text-gray-800 hidden sm:block">${userName}</span>
                    <button id="expand-status-btn" class="w-6 h-6 bg-purple-500 hover:bg-purple-600 text-white rounded-full text-xs transition-colors">
                        +
                    </button>
                </div>
            </div>
        `;
    }

    // 绑定事件
    bindEvents() {
        // 登出按钮
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        // 用户资料按钮
        const profileBtn = document.getElementById('user-profile-btn');
        if (profileBtn) {
            profileBtn.addEventListener('click', () => this.handleProfile());
        }

        // 折叠按钮
        const toggleBtn = document.getElementById('toggle-status-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleStatus());
        }

        // 展开按钮
        const expandBtn = document.getElementById('expand-status-btn');
        if (expandBtn) {
            expandBtn.addEventListener('click', () => this.toggleStatus());
        }
    }

    // 处理登出
    async handleLogout() {
        if (confirm('ログアウトしますか？')) {
            console.log('🔐 用户请求登出');

            // 优先使用authManager登出
            if (this.authManager && this.authManager.logout) {
                try {
                    await this.authManager.logout();
                    window.location.href = '/auth/login.html';
                } catch (error) {
                    console.error('❌ AuthManager登出失败:', error);
                    // 回退到userManager
                    if (this.userManager && this.userManager.logoutUser) {
                        this.userManager.logoutUser();
                    }
                }
            } else if (this.userManager && this.userManager.logoutUser) {
                this.userManager.logoutUser();
            } else {
                // 手动清除认证数据
                localStorage.removeItem('goldenledger_session_token');
                localStorage.removeItem('goldenledger_user');
                window.location.href = '/auth/login.html';
            }
        }
    }

    // 处理用户资料
    handleProfile() {
        console.log('👤 打开用户资料');
        // 这里可以打开用户资料模态框或跳转到资料页面
        alert('用户资料功能开发中...');
    }

    // 切换显示状态
    toggleStatus() {
        const fullStatus = document.querySelector('.user-status-card');
        const minimizedStatus = document.getElementById('minimized-status');

        if (fullStatus && minimizedStatus) {
            if (fullStatus.style.display === 'none') {
                fullStatus.style.display = 'block';
                minimizedStatus.classList.add('hidden');
            } else {
                fullStatus.style.display = 'none';
                minimizedStatus.classList.remove('hidden');
            }
        }
    }

    // 获取角色显示名称
    getRoleDisplayName(role) {
        const roleNames = {
            admin: '管理者',
            accountant: '会計士',
            user: '一般ユーザー',
            development: '開発者'
        };
        return roleNames[role] || '一般ユーザー';
    }

    // 格式化登录时间
    formatLoginTime(loginTime) {
        if (!loginTime) return '不明';
        
        try {
            const date = new Date(loginTime);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffMins < 1) return 'たった今';
            if (diffMins < 60) return `${diffMins}分前`;
            if (diffHours < 24) return `${diffHours}時間前`;
            if (diffDays < 7) return `${diffDays}日前`;
            
            return date.toLocaleDateString('ja-JP');
        } catch (error) {
            return '不明';
        }
    }

    // 添加样式
    addStyles() {
        if (document.getElementById('user-status-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'user-status-styles';
        styles.textContent = `
            .user-status-card {
                animation: slideInLeft 0.3s ease-out;
            }
            
            .user-status-minimized {
                animation: slideInLeft 0.3s ease-out;
            }
            
            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            /* 移动端适配 */
            @media (max-width: 768px) {
                #user-status-component {
                    top: 60px;
                    left: 10px;
                    right: 10px;
                }
                
                .user-status-card {
                    min-width: auto;
                    width: 100%;
                }
                
                .user-status-minimized {
                    position: relative;
                    width: fit-content;
                }
            }
            
            /* 头像加载失败时的备用样式 */
            .user-status-card img {
                background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            }
        `;
        
        document.head.appendChild(styles);
    }

    // 更新用户信息
    updateUserInfo(newUserData) {
        this.currentUser = newUserData;
        
        // 重新创建组件
        const existingComponent = document.getElementById('user-status-component');
        if (existingComponent) {
            existingComponent.remove();
        }
        
        this.createStatusComponent();
        console.log('✅ 用户状态组件已更新');
    }

    // 销毁组件
    destroy() {
        const component = document.getElementById('user-status-component');
        if (component) {
            component.remove();
        }
        
        const styles = document.getElementById('user-status-styles');
        if (styles) {
            styles.remove();
        }
        
        console.log('🗑️ 用户状态组件已销毁');
    }
}

// 自动初始化用户状态组件
window.addEventListener('load', function() {
    // 延迟初始化，确保认证管理器已加载
    setTimeout(async () => {
        // 优先检查authManager
        if (window.authManager) {
            try {
                await window.authManager.initialize();
                if (window.authManager.isAuthenticated) {
                    window.userStatusComponent = new UserStatusComponent();
                    return;
                }
            } catch (error) {
                console.error('❌ AuthManager检查失败:', error);
            }
        }

        // 回退到userManager
        if (window.userManager && window.userManager.isLoggedIn()) {
            window.userStatusComponent = new UserStatusComponent();
        }
    }, 1000); // 增加延迟时间，确保认证管理器完全初始化
});

console.log('👤 用户状态组件已加载');

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智会云 GoTax｜下一代 AI 记账平台 - 演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .chat-bubble { max-width: 300px; padding: 12px 16px; border-radius: 18px; margin: 8px 0; }
        .chat-user { background: #00d4aa; color: white; margin-left: auto; }
        .chat-ai { background: #f3f4f6; color: #374151; }
        .journal-preview { background: linear-gradient(135deg, #f0fdf9 0%, #eff6ff 100%); border: 1px solid #00d4aa; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl font-bold mb-4">🚀 智会云 GoTax｜下一代 AI 记账平台</h1>
            <p class="text-xl opacity-90">一句話全自動記帳 - AI革命的会計ソリューション</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Features Grid -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-8">✨ 核心機能</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">💬</span>
                    </div>
                    <h3 class="font-semibold mb-2">自然言語記帳</h3>
                    <p class="text-gray-600 text-sm">日常会話のように話すだけで自動仕訳生成</p>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">📸</span>
                    </div>
                    <h3 class="font-semibold mb-2">写真アップロード</h3>
                    <p class="text-gray-600 text-sm">レシートや請求書を撮影して自動処理</p>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">🎤</span>
                    </div>
                    <h3 class="font-semibold mb-2">音声入力</h3>
                    <p class="text-gray-600 text-sm">音声認識で手を使わずに記帳</p>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">🤖</span>
                    </div>
                    <h3 class="font-semibold mb-2">AI智能分類</h3>
                    <p class="text-gray-600 text-sm">適切な勘定科目を自動選択</p>
                </div>
            </div>
        </section>

        <!-- Demo Chat -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-8">💬 AI記帳デモ</h2>
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Chat Header -->
                <div class="gradient-bg text-white p-4">
                    <h3 class="font-semibold">AI会計アシスタント</h3>
                    <p class="text-sm opacity-90">何でも話しかけてください</p>
                </div>
                
                <!-- Chat Messages -->
                <div class="p-4 space-y-4 h-96 overflow-y-auto">
                    <div class="chat-bubble chat-ai">
                        こんにちは！AI会計アシスタントです。取引内容を教えてください。
                    </div>
                    
                    <div class="chat-bubble chat-user">
                        今日コンビニで事務用品を1200円で購入しました
                    </div>
                    
                    <div class="chat-bubble chat-ai">
                        承知いたしました。以下の仕訳を生成しました：
                    </div>
                    
                    <!-- Journal Entry Preview -->
                    <div class="journal-preview rounded-lg p-4 mx-4">
                        <h4 class="font-semibold mb-3">生成された仕訳</h4>
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="text-xs text-gray-500 uppercase">借方</label>
                                <div class="bg-blue-50 border border-blue-200 rounded p-2">
                                    <div class="font-medium text-blue-900">消耗品費</div>
                                    <div class="text-sm text-blue-700">¥1,200</div>
                                </div>
                            </div>
                            <div>
                                <label class="text-xs text-gray-500 uppercase">貸方</label>
                                <div class="bg-green-50 border border-green-200 rounded p-2">
                                    <div class="font-medium text-green-900">現金</div>
                                    <div class="text-sm text-green-700">¥1,200</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">
                            <strong>摘要:</strong> コンビニで事務用品購入
                        </div>
                        <div class="flex space-x-2">
                            <button class="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600">
                                ✓ 確認して記録
                            </button>
                            <button class="border border-gray-300 px-4 py-2 rounded text-sm hover:bg-gray-50">
                                ✏️ 編集
                            </button>
                        </div>
                    </div>
                    
                    <div class="chat-bubble chat-user">
                        素晴らしいです！とても簡単ですね。
                    </div>
                    
                    <div class="chat-bubble chat-ai">
                        ありがとうございます！他にも記録したい取引はありますか？写真をアップロードすることも可能です。
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="border-t p-4">
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 p-2 rounded hover:bg-gray-200">🎤</button>
                        <input type="text" placeholder="取引内容を入力..." class="flex-1 border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button class="bg-blue-500 text-white p-2 rounded hover:bg-blue-600">📤</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Example Inputs -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-8">📝 入力例</h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <h3 class="font-semibold mb-4">自然言語の例</h3>
                    <div class="space-y-2 text-sm">
                        <div class="bg-gray-50 p-3 rounded">"今日収到田中先生的设计费50万日元"</div>
                        <div class="bg-gray-50 p-3 rounded">"在便利店买了1200日元的办公用品"</div>
                        <div class="bg-gray-50 p-3 rounded">"支付了这个月的房租15万日元"</div>
                        <div class="bg-gray-50 p-3 rounded">"收到客户的货款50万日元"</div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-sm border">
                    <h3 class="font-semibold mb-4">対応機能</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-sm">JPG, PNG, PDF対応</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-sm">日本語音声認識</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-sm">バッチファイル処理</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-sm">リアルタイム仕訳生成</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- System Status -->
        <section class="text-center">
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-4">🚀 システム状態</h2>
                <div class="grid md:grid-cols-3 gap-6">
                    <div>
                        <div class="text-3xl font-bold text-blue-500">94%</div>
                        <div class="text-gray-600">AI処理成功率</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-green-500">2.3秒</div>
                        <div class="text-gray-600">平均処理時間</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-purple-500">156</div>
                        <div class="text-gray-600">処理済み仕訳数</div>
                    </div>
                </div>
                
                <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                    <p class="text-blue-800">
                        <strong>🎉 システム開発完了！</strong><br>
                        完全なAI記帳システムが構築されました。<br>
                        <code>python3 start_system.py</code> でフルシステムを起動できます。
                    </p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 智会云 GoTax｜下一代 AI 记账平台 - AI革命的会計ソリューション</p>
        </div>
    </footer>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 智会云 GoTax｜下一代 AI 记账平台 演示页面已加载');
            
            // 添加一些简单的交互
            const cards = document.querySelectorAll('.bg-white');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
                    this.style.transition = 'all 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                });
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 管理员重定向测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 class="text-2xl font-bold text-center mb-6">🔧 管理员重定向测试</h1>
        
        <div class="space-y-4">
            <button onclick="testCreateSession()" 
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                创建测试会话
            </button>
            
            <button onclick="testRedirect()" 
                    class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded">
                测试跳转到管理页面
            </button>
            
            <button onclick="clearSession()" 
                    class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded">
                清除会话
            </button>
            
            <button onclick="checkSession()" 
                    class="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded">
                检查当前会话
            </button>
        </div>
        
        <div id="result" class="mt-6 p-4 bg-gray-50 rounded text-sm">
            点击按钮开始测试...
        </div>
        
        <div class="mt-4 text-center">
            <a href="/admin_login.html" class="text-blue-600 hover:text-blue-800 underline">
                ← 返回登录页面
            </a>
        </div>
    </div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            result.innerHTML += `[${timestamp}] ${message}<br>`;
            result.scrollTop = result.scrollHeight;
            console.log(message);
        }

        function testCreateSession() {
            const adminSession = {
                authenticated: true,
                username: 'souyousann',
                loginTime: Date.now(),
                expires: Date.now() + 8 * 60 * 60 * 1000 // 8小时
            };
            
            localStorage.setItem('admin_session', JSON.stringify(adminSession));
            log('✅ 测试会话已创建');
            log(`过期时间: ${new Date(adminSession.expires).toLocaleString()}`);
        }

        function testRedirect() {
            log('🔄 开始跳转测试...');
            
            // 检查会话
            const session = localStorage.getItem('admin_session');
            if (!session) {
                log('❌ 没有会话，无法跳转');
                return;
            }
            
            try {
                const data = JSON.parse(session);
                if (data.authenticated && data.expires > Date.now()) {
                    log('✅ 会话有效，准备跳转');
                    log('🚀 3秒后跳转到管理页面...');
                    
                    setTimeout(() => {
                        window.location.replace('/admin_usage_management.html');
                    }, 3000);
                } else {
                    log('❌ 会话无效或已过期');
                }
            } catch (error) {
                log('❌ 会话解析错误: ' + error.message);
            }
        }

        function clearSession() {
            localStorage.removeItem('admin_session');
            log('🗑️ 会话已清除');
        }

        function checkSession() {
            const session = localStorage.getItem('admin_session');
            
            if (!session) {
                log('❌ 没有找到会话');
                return;
            }
            
            try {
                const data = JSON.parse(session);
                log('📊 当前会话信息:');
                log(`- 认证状态: ${data.authenticated ? '✅ 已认证' : '❌ 未认证'}`);
                log(`- 用户名: ${data.username}`);
                log(`- 登录时间: ${new Date(data.loginTime).toLocaleString()}`);
                log(`- 过期时间: ${new Date(data.expires).toLocaleString()}`);
                log(`- 是否有效: ${data.expires > Date.now() ? '✅ 有效' : '❌ 已过期'}`);
            } catch (error) {
                log('❌ 会话解析错误: ' + error.message);
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', function() {
            log('🔧 管理员重定向测试页面已加载');
            checkSession();
        });
    </script>
</body>
</html>

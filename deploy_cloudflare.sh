#!/bin/bash

# Cloudflare Pages Deployment Script
# This script prepares the project for Cloudflare Pages deployment

echo "🚀 Preparing for Cloudflare Pages deployment..."

# Check if we have the required files
if [ ! -f "index.html" ]; then
    echo "❌ Error: index.html not found"
    exit 1
fi

if [ ! -f "env-config.js" ]; then
    echo "❌ Error: env-config.js not found"
    exit 1
fi

echo "✅ Required files found"

# Run the build script if in Cloudflare Pages environment
if [ "$CF_PAGES" = "1" ]; then
    echo "🔧 Running Cloudflare Pages build process..."
    node build.js
else
    echo "📱 Local environment detected - skipping build process"
fi

# Verify critical files exist
echo "🔍 Verifying deployment files..."

REQUIRED_FILES=(
    "index.html"
    "env-config.js"
    "chatbot/GeminiAPI.js"
    "chatbot/ChatFab.js"
    "chatbot/ChatInterface.js"
    "chatbot/UsageTracker.js"
    "chatbot/chatbot-init.js"
    "_redirects"
    "_headers"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "⚠️  $file (missing but may be optional)"
    fi
done

echo "🌸 Deployment preparation completed!"

# Show environment info
echo ""
echo "📋 Environment Information:"
echo "- CF_PAGES: ${CF_PAGES:-'Not set'}"
echo "- GEMINI_API_KEY: ${GEMINI_API_KEY:+'Set (hidden)'}"
echo "- NODE_ENV: ${NODE_ENV:-'Not set'}"

if [ "$CF_PAGES" = "1" ]; then
    echo ""
    echo "🔑 Cloudflare Pages Environment Variables:"
    echo "Make sure GEMINI_API_KEY is set in your Cloudflare Pages settings:"
    echo "https://dash.cloudflare.com/pages/view/goldenledger/settings/production"
fi

echo ""
echo "✨ Ready for deployment!"

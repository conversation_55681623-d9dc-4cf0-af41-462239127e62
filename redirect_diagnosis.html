<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重定向诊断 - GoldenLedger</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #00ff00;
        }
        .error {
            border-left-color: #ff0000;
            color: #ff6666;
        }
        .warning {
            border-left-color: #ffaa00;
            color: #ffcc66;
        }
        .info {
            border-left-color: #0088ff;
            color: #66ccff;
        }
        .log {
            background: #000;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
            font-family: monospace;
        }
        button:hover {
            background: #00cc00;
        }
        .status-ok { color: #00ff00; }
        .status-error { color: #ff0000; }
        .status-warning { color: #ffaa00; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #444;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 重定向问题诊断工具</h1>
        <p>当前时间: <span id="currentTime"></span></p>
        <p>当前URL: <span id="currentUrl"></span></p>
        
        <!-- 基本信息 -->
        <div class="section info">
            <h2>📊 基本信息</h2>
            <table>
                <tr><th>项目</th><th>值</th><th>状态</th></tr>
                <tr><td>用户代理</td><td id="userAgent"></td><td>-</td></tr>
                <tr><td>引用页面</td><td id="referrer"></td><td>-</td></tr>
                <tr><td>协议</td><td id="protocol"></td><td id="protocolStatus">-</td></tr>
                <tr><td>主机</td><td id="hostname"></td><td id="hostnameStatus">-</td></tr>
                <tr><td>路径</td><td id="pathname"></td><td>-</td></tr>
            </table>
        </div>
        
        <!-- 存储检查 -->
        <div class="section">
            <h2>💾 本地存储检查</h2>
            <div id="storageInfo"></div>
            <button onclick="clearAllStorage()">清除所有存储</button>
            <button onclick="checkStorage()">重新检查</button>
        </div>
        
        <!-- 脚本检查 -->
        <div class="section">
            <h2>📜 加载的脚本检查</h2>
            <div id="scriptsInfo"></div>
        </div>
        
        <!-- 重定向监控 -->
        <div class="section warning">
            <h2>🔄 重定向监控</h2>
            <p>监控状态: <span id="monitorStatus">启动中...</span></p>
            <p>检测到的重定向: <span id="redirectCount">0</span></p>
            <div class="log" id="redirectLog"></div>
            <button onclick="startMonitoring()">开始监控</button>
            <button onclick="stopMonitoring()">停止监控</button>
        </div>
        
        <!-- 网络请求监控 -->
        <div class="section">
            <h2>🌐 网络请求监控</h2>
            <div class="log" id="networkLog"></div>
        </div>
        
        <!-- 控制台日志 -->
        <div class="section">
            <h2>📝 控制台日志</h2>
            <div class="log" id="consoleLog"></div>
            <button onclick="clearLogs()">清除日志</button>
        </div>
        
        <!-- 测试链接 -->
        <div class="section info">
            <h2>🧪 测试链接</h2>
            <p>点击以下链接测试重定向行为：</p>
            <button onclick="testLink('login.html')">测试 login.html</button>
            <button onclick="testLink('register.html')">测试 register.html</button>
            <button onclick="testLink('login_minimal.html')">测试 login_minimal.html</button>
            <button onclick="testLink('register_minimal.html')">测试 register_minimal.html</button>
            <button onclick="testLink('master_dashboard.html')">测试 dashboard</button>
        </div>
        
        <!-- 解决方案 -->
        <div class="section">
            <h2>💡 可能的解决方案</h2>
            <ol>
                <li><button onclick="solution1()">清除浏览器缓存</button></li>
                <li><button onclick="solution2()">清除所有存储数据</button></li>
                <li><button onclick="solution3()">禁用所有扩展程序</button></li>
                <li><button onclick="solution4()">使用隐身模式</button></li>
                <li><button onclick="solution5()">检查Cloudflare缓存</button></li>
            </ol>
        </div>
    </div>

    <script>
        let monitoringInterval;
        let redirectCount = 0;
        let originalLocation = window.location.href;
        
        // 初始化
        function init() {
            updateBasicInfo();
            checkStorage();
            checkScripts();
            startMonitoring();
            interceptConsole();
            interceptFetch();
        }
        
        // 更新基本信息
        function updateBasicInfo() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('ja-JP');
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('referrer').textContent = document.referrer || '(无)';
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('hostname').textContent = window.location.hostname;
            document.getElementById('pathname').textContent = window.location.pathname;
            
            // 状态检查
            document.getElementById('protocolStatus').textContent = 
                window.location.protocol === 'https:' ? '✅' : '⚠️';
            document.getElementById('protocolStatus').className = 
                window.location.protocol === 'https:' ? 'status-ok' : 'status-warning';
                
            document.getElementById('hostnameStatus').textContent = 
                window.location.hostname.includes('goldenledger') ? '✅' : '⚠️';
            document.getElementById('hostnameStatus').className = 
                window.location.hostname.includes('goldenledger') ? 'status-ok' : 'status-warning';
        }
        
        // 检查存储
        function checkStorage() {
            const storageDiv = document.getElementById('storageInfo');
            let html = '<h3>LocalStorage:</h3>';
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                html += `<p><strong>${key}:</strong> ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}</p>`;
            }
            
            html += '<h3>SessionStorage:</h3>';
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                html += `<p><strong>${key}:</strong> ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}</p>`;
            }
            
            if (localStorage.length === 0 && sessionStorage.length === 0) {
                html += '<p class="status-ok">✅ 存储为空</p>';
            }
            
            storageDiv.innerHTML = html;
        }
        
        // 检查脚本
        function checkScripts() {
            const scriptsDiv = document.getElementById('scriptsInfo');
            const scripts = document.querySelectorAll('script');
            let html = '<h3>加载的脚本:</h3>';
            
            scripts.forEach((script, index) => {
                if (script.src) {
                    html += `<p>${index + 1}. <strong>外部:</strong> ${script.src}</p>`;
                } else {
                    html += `<p>${index + 1}. <strong>内联:</strong> ${script.innerHTML.substring(0, 50)}...</p>`;
                }
            });
            
            scriptsDiv.innerHTML = html;
        }
        
        // 开始监控重定向
        function startMonitoring() {
            if (monitoringInterval) clearInterval(monitoringInterval);
            
            document.getElementById('monitorStatus').textContent = '🟢 运行中';
            
            monitoringInterval = setInterval(() => {
                if (window.location.href !== originalLocation) {
                    redirectCount++;
                    const logDiv = document.getElementById('redirectLog');
                    const timestamp = new Date().toLocaleTimeString();
                    logDiv.innerHTML += `<p>[${timestamp}] 重定向检测: ${originalLocation} → ${window.location.href}</p>`;
                    document.getElementById('redirectCount').textContent = redirectCount;
                    originalLocation = window.location.href;
                    
                    // 滚动到底部
                    logDiv.scrollTop = logDiv.scrollHeight;
                }
            }, 100);
        }
        
        // 停止监控
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            document.getElementById('monitorStatus').textContent = '🔴 已停止';
        }
        
        // 拦截控制台输出
        function interceptConsole() {
            const logDiv = document.getElementById('consoleLog');
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            
            console.log = function(...args) {
                originalLog.apply(console, args);
                logDiv.innerHTML += `<p>[LOG] ${args.join(' ')}</p>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            };
            
            console.error = function(...args) {
                originalError.apply(console, args);
                logDiv.innerHTML += `<p style="color: #ff6666;">[ERROR] ${args.join(' ')}</p>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            };
            
            console.warn = function(...args) {
                originalWarn.apply(console, args);
                logDiv.innerHTML += `<p style="color: #ffcc66;">[WARN] ${args.join(' ')}</p>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            };
        }
        
        // 拦截网络请求
        function interceptFetch() {
            const networkDiv = document.getElementById('networkLog');
            const originalFetch = window.fetch;
            
            window.fetch = function(...args) {
                const timestamp = new Date().toLocaleTimeString();
                networkDiv.innerHTML += `<p>[${timestamp}] FETCH: ${args[0]}</p>`;
                networkDiv.scrollTop = networkDiv.scrollHeight;
                return originalFetch.apply(this, args);
            };
        }
        
        // 清除所有存储
        function clearAllStorage() {
            localStorage.clear();
            sessionStorage.clear();
            checkStorage();
            console.log('✅ 所有存储已清除');
        }
        
        // 清除日志
        function clearLogs() {
            document.getElementById('consoleLog').innerHTML = '';
            document.getElementById('networkLog').innerHTML = '';
            document.getElementById('redirectLog').innerHTML = '';
            redirectCount = 0;
            document.getElementById('redirectCount').textContent = '0';
        }
        
        // 测试链接
        function testLink(url) {
            console.log(`🔗 测试链接: ${url}`);
            const timestamp = new Date().toLocaleTimeString();
            document.getElementById('redirectLog').innerHTML += 
                `<p>[${timestamp}] 测试导航到: ${url}</p>`;
            
            // 延迟导航以便记录
            setTimeout(() => {
                window.location.href = url;
            }, 500);
        }
        
        // 解决方案
        function solution1() {
            alert('请手动清除浏览器缓存:\n1. 按 Ctrl+Shift+Delete (Windows) 或 Cmd+Shift+Delete (Mac)\n2. 选择"缓存的图片和文件"\n3. 点击"清除数据"');
        }
        
        function solution2() {
            clearAllStorage();
            alert('✅ 所有存储数据已清除');
        }
        
        function solution3() {
            alert('请手动禁用浏览器扩展:\n1. 打开浏览器设置\n2. 转到扩展程序页面\n3. 暂时禁用所有扩展\n4. 重新测试页面');
        }
        
        function solution4() {
            alert('请使用隐身/无痕模式:\n1. 按 Ctrl+Shift+N (Chrome) 或 Ctrl+Shift+P (Firefox)\n2. 在隐身窗口中访问页面\n3. 测试是否仍有重定向问题');
        }
        
        function solution5() {
            alert('Cloudflare缓存清除:\n1. 登录 Cloudflare 控制台\n2. 选择您的域名\n3. 转到"缓存"选项卡\n4. 点击"清除所有内容"');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', init);
        
        // 监听页面卸载
        window.addEventListener('beforeunload', function() {
            console.log('⚠️ 页面即将卸载');
        });
        
        console.log('🔍 重定向诊断工具已启动');
    </script>
</body>
</html>

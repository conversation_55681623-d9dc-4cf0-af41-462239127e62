#!/usr/bin/env python3
"""
简化的后端启动脚本
快速启动AI记账后端服务
"""
import os
import sys
import logging
from pathlib import Path

# 使用统一的导入管理器
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from app.core.import_manager import setup_imports
    setup_imports()
except ImportError:
    # 如果导入管理器不可用，使用传统方式
    pass

try:
    from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, UploadFile, File, Form, Request
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
    from fastapi.staticfiles import StaticFiles
    from pydantic import BaseModel
    import uvicorn
    import json
    from datetime import datetime
    import random
    import time
    import asyncio
    import uuid
    import base64
    import io
    import zipfile
    from PIL import Image
    import google.generativeai as genai

    # 导入数据库模块
    from backend.database import db

    # 导入AI智能体模块
    from backend.ai_agents import get_coordinator, initialize_coordinator

    # 导入WebSocket管理器
    from backend.websocket_manager import get_connection_manager, MessageType

    # 导入认证管理器
    from backend.auth_manager import get_auth_manager

    # 导入备份管理器
    from backend.backup_manager import get_backup_manager
except ImportError as e:
    print(f"❌ 缺少依赖: {e}")
    print("请运行: python3 -m pip install fastapi uvicorn pydantic google-generativeai websockets bcrypt pyotp qrcode")
    sys.exit(1)

# 创建FastAPI应用
app = FastAPI(
    title="GoldenLedger — Smart AI-Powered Finance System",
    description="AI全自動記帳システム",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="."), name="static")

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/files", StaticFiles(directory="attachments"), name="attachment_files")
app.mount("/music", StaticFiles(directory="music"), name="music")

# 添加HTML文件的直接访问路由
@app.get("/data_export.html")
async def serve_data_export():
    return FileResponse("data_export.html")

@app.get("/fixed_assets.html")
async def serve_fixed_assets():
    return FileResponse("fixed_assets.html")

@app.get("/background_control.js")
async def serve_background_control():
    return FileResponse("background_control.js", media_type="application/javascript")

@app.get("/auth.js")
async def serve_auth():
    return FileResponse("auth.js", media_type="application/javascript")

@app.get("/music_control.js")
async def serve_music_control():
    return FileResponse("music_control.js", media_type="application/javascript")

# 数据模型
class NaturalLanguageRequest(BaseModel):
    text: str

# PayPal支付验证模型
class PaymentVerification(BaseModel):
    orderID: str
    planId: str
    company_id: str = "default"

class JournalEntry(BaseModel):
    id: str
    entry_date: str = ""
    entry_time: str = ""
    entry_datetime: str = ""
    description: str
    debit_account: str
    credit_account: str
    amount: float
    reference_number: str = ""

class JournalEntryResponse(BaseModel):
    success: bool
    journal_entry: JournalEntry = None
    confidence: float = 0.0
    warnings: list = []
    suggestions: list = []
    error: str = ""

# 认证相关数据模型
class LoginRequest(BaseModel):
    username: str
    password: str
    mfa_code: str = ""
    ip_address: str = ""
    user_agent: str = ""

class RegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    role: str = "viewer"

class LoginResponse(BaseModel):
    success: bool
    access_token: str = ""
    refresh_token: str = ""
    expires_at: str = ""
    user: dict = {}
    error: str = ""
    require_mfa: bool = False

class RegisterResponse(BaseModel):
    success: bool
    user_id: str = ""
    message: str = ""
    error: str = ""

# 备份相关数据模型
class CreateBackupRequest(BaseModel):
    type: str = "full"  # full, incremental, differential
    description: str = ""

class CreateBackupResponse(BaseModel):
    success: bool
    backup_id: str = ""
    message: str = ""
    error: str = ""

class RestoreBackupRequest(BaseModel):
    backup_id: str
    restore_path: str = ""

class RestoreBackupResponse(BaseModel):
    success: bool
    restore_id: str = ""
    message: str = ""
    error: str = ""

# 模拟数据
MOCK_ACCOUNTS = {
    "資産": ["現金", "普通預金", "売掛金", "商品", "建物", "機械装置"],
    "負債": ["買掛金", "短期借入金", "長期借入金", "未払金"],
    "純資産": ["資本金", "利益剰余金"],
    "収益": ["売上高", "受取利息", "雑収入"],
    "費用": ["消耗品費", "給料手当", "地代家賃", "水道光熱費", "通信費", "広告宣伝費", "交通費"]
}

def parse_natural_language(text: str) -> JournalEntry:
    """简单的自然语言解析"""
    # 提取金额
    import re
    amount_match = re.search(r'(\d+(?:,\d{3})*(?:\.\d+)?)円', text)
    amount = float(amount_match.group(1).replace(',', '')) if amount_match else 1000
    
    # 简单的关键词匹配
    debit_account = "消耗品費"
    credit_account = "現金"
    
    if "売上" in text or "収入" in text:
        debit_account = "売掛金" if "売掛" in text else "現金"
        credit_account = "売上高"
    elif "家賃" in text:
        debit_account = "地代家賃"
        credit_account = "普通預金"
    elif "電気" in text or "水道" in text:
        debit_account = "水道光熱費"
        credit_account = "普通預金"
    elif "給与" in text or "給料" in text:
        debit_account = "給料手当"
        credit_account = "普通預金"
    elif "広告" in text:
        debit_account = "広告宣伝費"
        credit_account = "普通預金"
    elif "交通" in text:
        debit_account = "交通費"
        credit_account = "現金"
    
    now = datetime.now()
    return JournalEntry(
        id=f"J{now.strftime('%Y%m%d%H%M%S')}",
        entry_date=now.strftime('%Y-%m-%d'),
        entry_time=now.strftime('%H:%M:%S'),
        entry_datetime=now.isoformat(),
        description=text,
        debit_account=debit_account,
        credit_account=credit_account,
        amount=amount,
        reference_number=f"REF{random.randint(1000, 9999)}"
    )

# API路由
@app.get("/")
async def root():
    return {"message": "GoldenLedger — Smart AI-Powered Finance System API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# HTML文件路由
@app.get("/")
async def root():
    return FileResponse("index.html")

@app.get("/index.html")
async def index():
    return FileResponse("index.html")

@app.get("/login.html")
async def login():
    return FileResponse("login.html")

@app.get("/register.html")
async def register():
    return FileResponse("register.html")

@app.get("/user_settings.html")
async def user_settings():
    return FileResponse("user_settings.html")

@app.get("/master_dashboard.html")
@app.head("/master_dashboard.html")
async def master_dashboard():
    return FileResponse("master_dashboard.html")

@app.get("/ai_demo.html")
@app.head("/ai_demo.html")
async def ai_demo():
    return FileResponse("interactive_demo.html")

@app.get("/multilingual_interface.html")
@app.head("/multilingual_interface.html")
async def multilingual_interface():
    return FileResponse("multilingual_interface.html")

@app.get("/batch_import.html")
@app.head("/batch_import.html")
async def batch_import():
    return FileResponse("data_import_tool.html")

@app.get("/advanced_analytics.html")
@app.head("/advanced_analytics.html")
async def advanced_analytics():
    return FileResponse("advanced_dashboard.html")

@app.get("/financial_reports.html")
@app.head("/financial_reports.html")
async def financial_reports():
    return FileResponse("financial_reports.html")

@app.get("/ai_audit_system.html")
@app.head("/ai_audit_system.html")
async def ai_audit_system():
    return FileResponse("ai_audit_system.html")

@app.get("/system_monitor.html")
@app.head("/system_monitor.html")
async def system_monitor():
    return FileResponse("system_monitor.html")

@app.get("/realtime_monitoring.html")
@app.head("/realtime_monitoring.html")
async def realtime_monitoring():
    return FileResponse("realtime_monitoring.html")

@app.get("/user_management.html")
@app.head("/user_management.html")
async def user_management():
    return FileResponse("user_management.html")

@app.get("/backup_management.html")
@app.head("/backup_management.html")
async def backup_management():
    return FileResponse("backup_management.html")

@app.get("/performance_monitoring.html")
@app.head("/performance_monitoring.html")
async def performance_monitoring():
    return FileResponse("performance_monitoring.html")

@app.get("/system_overview.html")
@app.head("/system_overview.html")
async def system_overview():
    return FileResponse("system_overview.html")

@app.get("/api_documentation.html")
@app.head("/api_documentation.html")
async def api_documentation():
    return FileResponse("api_documentation.html")

# 兼容性路由 - 映射到实际文件
@app.get("/interactive_demo.html")
async def interactive_demo():
    return FileResponse("interactive_demo.html")

@app.get("/data_import_tool.html")
async def data_import_tool():
    return FileResponse("data_import_tool.html")

@app.get("/advanced_dashboard.html")
async def advanced_dashboard():
    return FileResponse("advanced_dashboard.html")

@app.get("/ai_test_simple.html")
async def ai_test_simple():
    return FileResponse("ai_test_simple.html")

@app.get("/journal_entries.html")
async def journal_entries():
    return FileResponse("journal_entries.html")

@app.get("/test_edit_delete_frontend.html")
async def test_edit_delete_frontend():
    return FileResponse("test_edit_delete_frontend.html")

@app.get("/test_frontend_functions.html")
async def test_frontend_functions():
    return FileResponse("test_frontend_functions.html")

@app.get("/test_invoice_frontend.html")
async def test_invoice_frontend():
    return FileResponse("test_invoice_frontend.html")

@app.get("/test_edit_modal.html")
async def test_edit_modal():
    return FileResponse("test_edit_modal.html")

@app.get("/test_edit_attachment_frontend.html")
async def test_edit_attachment_frontend():
    return FileResponse("test_edit_attachment_frontend.html")

@app.get("/debug_dashboard_frontend.html")
async def debug_dashboard_frontend():
    return FileResponse("debug_dashboard_frontend.html")

@app.get("/simple_dashboard_test.html")
async def simple_dashboard_test():
    return FileResponse("simple_dashboard_test.html")

@app.get("/minimal_dashboard.html")
async def minimal_dashboard():
    return FileResponse("minimal_dashboard.html")

@app.get("/test_chartjs.html")
async def test_chartjs():
    return FileResponse("test_chartjs.html")

@app.get("/fixed_dashboard.html")
async def fixed_dashboard():
    return FileResponse("fixed_dashboard.html")

@app.get("/advanced_dashboard_backup.html")
async def advanced_dashboard_backup():
    return FileResponse("advanced_dashboard_backup.html")

@app.get("/test_music_control.html")
async def test_music_control():
    return FileResponse("test_music_control.html")

@app.get("/debug_music.html")
async def debug_music():
    return FileResponse("debug_music.html")

@app.get("/simple_music_test.html")
async def simple_music_test():
    return FileResponse("simple_music_test.html")

@app.get("/final_music_test.html")
async def final_music_test():
    return FileResponse("final_music_test.html")

@app.post("/ai-bookkeeping/natural-language")
async def process_natural_language(request: NaturalLanguageRequest):
    start_time = time.time()

    try:
        # 使用多智能体系统处理
        coordinator = get_coordinator()
        result = await coordinator.process_natural_language_request(
            text=request.text,
            company_id=request.company_id
        )

        if not result['success']:
            # 记录失败日志
            processing_time = int((time.time() - start_time) * 1000)
            log_data = {
                'company_id': request.company_id,
                'input_text': request.text,
                'processing_type': 'multi_agent_nlp',
                'success': False,
                'response_time_ms': processing_time,
                'model_used': 'multi_agent_system'
            }
            db.log_ai_processing(log_data)

            return JournalEntryResponse(
                success=False,
                error=result.get('error', '处理失败')
            )

        # 转换为标准格式
        journal_data = result['journal_entry']

        # 确保日期时间字段有值
        now = datetime.now()
        entry_date = journal_data.get('date') or now.strftime('%Y-%m-%d')
        entry_time = journal_data.get('time') or now.strftime('%H:%M:%S')
        entry_datetime = journal_data.get('datetime') or now.isoformat()

        journal_entry = JournalEntry(
            id=journal_data['id'],
            entry_date=entry_date,
            entry_time=entry_time,
            entry_datetime=entry_datetime,
            description=journal_data['description'],
            debit_account=journal_data['debit_account'],
            credit_account=journal_data['credit_account'],
            amount=journal_data['amount'],
            reference_number=journal_data.get('reference', '')
        )

        # 注意：这里不自动保存，等用户点击"確認して記録"按钮后才保存
        # AI只负责生成仕訳记录，不直接保存到数据库

        # 记录AI处理日志
        processing_time = int((time.time() - start_time) * 1000)
        log_data = {
            'company_id': request.company_id,
            'input_text': request.text,
            'processing_type': 'multi_agent_nlp',
            'success': True,
            'confidence': result['confidence'],
            'response_time_ms': processing_time,
            'model_used': 'multi_agent_system'
        }
        db.log_ai_processing(log_data)

        return JournalEntryResponse(
            success=True,
            journal_entry=journal_entry,
            confidence=result['confidence'],
            warnings=result.get('warnings', []),
            suggestions=result.get('suggestions', [])
        )

    except Exception as e:
        # 记录失败日志
        processing_time = int((time.time() - start_time) * 1000)
        log_data = {
            'company_id': request.company_id,
            'input_text': request.text,
            'processing_type': 'multi_agent_nlp',
            'success': False,
            'response_time_ms': processing_time,
            'model_used': 'multi_agent_system'
        }
        db.log_ai_processing(log_data)

        return JournalEntryResponse(
            success=False,
            error=str(e)
        )

async def process_invoice_with_ai(file_content: bytes, content_type: str, company_id: str, filename: str = None):
    """使用AI处理发票OCR"""
    try:
        # 配置Gemini API - 从环境变量获取
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        genai.configure(api_key=api_key)

        # 处理图片
        if content_type.startswith('image/'):
            # 直接使用图片
            image_data = file_content
        elif content_type == 'application/pdf':
            # PDF转图片 (简化处理，只取第一页)
            try:
                import fitz  # PyMuPDF
                pdf_doc = fitz.open(stream=file_content, filetype="pdf")
                page = pdf_doc[0]
                pix = page.get_pixmap()
                image_data = pix.tobytes("png")
                pdf_doc.close()
            except ImportError:
                return {
                    "success": False,
                    "error": "PDF处理功能未安装，请安装PyMuPDF库"
                }
        else:
            return {
                "success": False,
                "error": "不支持的文件类型"
            }

        # 使用Gemini Vision模型
        model = genai.GenerativeModel('gemini-1.5-flash')

        # 准备图片数据
        image = Image.open(io.BytesIO(image_data))

        # OCR提示
        prompt = """
        请分析这张发票/收据图片，提取以下信息并以JSON格式返回：

        {
            "amount": "金额(数字，不含货币符号)",
            "currency": "货币代码(如JPY、CNY等)",
            "date": "日期(YYYY-MM-DD格式)",
            "vendor": "商户名称",
            "description": "商品/服务描述",
            "tax_amount": "税额(如果有)",
            "payment_method": "支付方式(如果能识别)",
            "invoice_number": "发票号码(如果有)",
            "confidence": "识别置信度(0-1)"
        }

        注意：
        1. 如果某些信息无法识别，请设为null
        2. 金额请提取数字部分，不要包含货币符号
        3. 日期请转换为标准格式
        4. 描述请简洁明了
        """

        # 调用Gemini Vision API
        response = model.generate_content([prompt, image])
        response_text = response.text

        # 解析JSON响应
        import re
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            json_text = json_match.group()
            # 清理注释
            json_text = re.sub(r'//.*', '', json_text)
            json_text = re.sub(r'/\*.*?\*/', '', json_text, flags=re.DOTALL)

            try:
                ocr_data = json.loads(json_text)

                # 验证和清理数据
                result = {
                    "success": True,
                    "amount": ocr_data.get('amount'),
                    "currency": ocr_data.get('currency', 'JPY'),
                    "date": ocr_data.get('date'),
                    "vendor": ocr_data.get('vendor'),
                    "description": ocr_data.get('description'),
                    "tax_amount": ocr_data.get('tax_amount'),
                    "payment_method": ocr_data.get('payment_method'),
                    "invoice_number": ocr_data.get('invoice_number'),
                    "confidence": float(ocr_data.get('confidence', 0.8)),
                    "original_filename": filename,
                    "file_content": base64.b64encode(file_content).decode('utf-8'),
                    "content_type": content_type
                }

                return result

            except json.JSONDecodeError as e:
                logger.error(f"OCR JSON解析失败: {e}")
                return {
                    "success": False,
                    "error": f"AI响应解析失败: {str(e)}"
                }
        else:
            return {
                "success": False,
                "error": "AI未返回有效的JSON格式"
            }

    except Exception as e:
        logger.error(f"OCR处理异常: {str(e)}")
        return {
            "success": False,
            "error": f"OCR处理失败: {str(e)}"
        }

@app.post("/ai-bookkeeping/invoice-ocr")
async def process_invoice_ocr(invoice: UploadFile = File(...), company_id: str = Form("default")):
    """处理发票OCR识别"""
    start_time = time.time()

    try:
        # 验证文件类型
        if not invoice.content_type.startswith(('image/', 'application/pdf')):
            return {
                "success": False,
                "error": "不支持的文件类型，请上传图片或PDF文件"
            }

        # 验证文件大小 (10MB限制)
        content = await invoice.read()
        if len(content) > 10 * 1024 * 1024:
            return {
                "success": False,
                "error": "文件大小超过10MB限制"
            }

        # 重置文件指针
        await invoice.seek(0)

        # 调用OCR处理
        ocr_result = await process_invoice_with_ai(content, invoice.content_type, company_id, invoice.filename)

        # 记录处理日志
        processing_time = int((time.time() - start_time) * 1000)
        log_data = {
            'company_id': company_id,
            'input_text': f"Invoice OCR: {invoice.filename}",
            'processing_type': 'invoice_ocr',
            'success': ocr_result.get('success', False),
            'response_time_ms': processing_time,
            'model_used': 'gemini_vision'
        }
        db.log_ai_processing(log_data)

        return ocr_result

    except Exception as e:
        logger.error(f"发票OCR处理失败: {str(e)}")

        # 记录错误日志
        processing_time = int((time.time() - start_time) * 1000)
        log_data = {
            'company_id': company_id,
            'input_text': f"Invoice OCR Error: {invoice.filename}",
            'processing_type': 'invoice_ocr',
            'success': False,
            'response_time_ms': processing_time,
            'model_used': 'gemini_vision'
        }
        db.log_ai_processing(log_data)

        return {
            "success": False,
            "error": f"OCR处理失败: {str(e)}"
        }

@app.get("/ai-bookkeeping/demo")
async def get_demo_examples():
    return {
        "natural_language_examples": [
            "今日コンビニで事務用品を1200円で購入",
            "ABC会社から売上50万円を銀行振込で受取",
            "今月の家賃15万円を支払い",
            "電気代8500円を口座振替で支払い",
            "従業員給与80万円を支払い"
        ],
        "tips": [
            "金額と支払い方法を明確に記載してください",
            "日付を含めるとより正確な仕訳が生成されます",
            "取引先名を含めると摘要が詳細になります"
        ]
    }

@app.get("/ai-bookkeeping/stats/{company_id}")
async def get_stats(company_id: str):
    # 从数据库获取真实统计
    stats = db.get_ai_stats(company_id)
    return {
        "total_processed": stats['total_processed'],
        "success_rate": stats['success_rate'],
        "avg_confidence": stats['avg_confidence'],
        "avg_processing_time": stats['avg_response_time']
    }

@app.get("/journal-entries/{company_id}")
async def get_journal_entries(company_id: str):
    # 从数据库获取真实仕訳数据
    entries = db.get_journal_entries(company_id)
    return entries

@app.get("/journal-entries/{company_id}/accounts")
async def get_account_subjects(company_id: str):
    # 从数据库获取真实科目数据
    accounts = db.get_account_subjects(company_id)
    return accounts

def save_attachment(file_content_base64: str, content_type: str, original_filename: str, entry_id: str) -> str:
    """保存附件文件并返回文件路径"""
    try:
        # 创建附件目录
        attachments_dir = Path("attachments")
        attachments_dir.mkdir(exist_ok=True)

        # 解码文件内容
        file_content = base64.b64decode(file_content_base64)

        # 生成文件名
        file_extension = Path(original_filename).suffix if original_filename else '.png'
        if not file_extension:
            if 'image' in content_type:
                file_extension = '.png'
            elif 'pdf' in content_type:
                file_extension = '.pdf'

        filename = f"{entry_id}_{uuid.uuid4().hex[:8]}{file_extension}"
        file_path = attachments_dir / filename

        # 保存文件
        with open(file_path, 'wb') as f:
            f.write(file_content)

        return str(file_path)

    except Exception as e:
        logger.error(f"保存附件失败: {e}")
        return None

@app.post("/journal-entries/save")
async def save_journal_entry(entry_data: dict):
    """保存仕訳记录到数据库"""
    try:
        # 提取数据
        company_id = entry_data.get('company_id', 'default')

        # 创建仕訳记录
        now = datetime.now()
        journal_entry = JournalEntry(
            id=entry_data.get('id', f"entry_{now.strftime('%Y%m%d_%H%M%S')}"),
            entry_date=entry_data.get('entry_date', now.strftime('%Y-%m-%d')),
            entry_time=entry_data.get('entry_time', now.strftime('%H:%M:%S')),
            entry_datetime=entry_data.get('entry_datetime', now.isoformat()),
            description=entry_data.get('description', ''),
            debit_account=entry_data.get('debit_account', ''),
            credit_account=entry_data.get('credit_account', ''),
            amount=float(entry_data.get('amount', 0)),
            reference_number=entry_data.get('reference_number', '')
        )

        # 处理附件
        attachment_path = None
        if 'attachment' in entry_data and entry_data['attachment']:
            attachment_data = entry_data['attachment']
            if attachment_data.get('file_content'):
                attachment_path = save_attachment(
                    attachment_data['file_content'],
                    attachment_data.get('content_type', 'image/png'),
                    attachment_data.get('original_filename', 'invoice.png'),
                    journal_entry.id
                )

        # 保存到数据库
        entry_dict = journal_entry.dict()
        entry_dict['company_id'] = company_id
        entry_dict['attachment_path'] = attachment_path
        success = db.save_journal_entry(entry_dict)

        if success:
            return {
                "success": True,
                "message": "仕訳记录已成功保存",
                "entry_id": journal_entry.id,
                "attachment_path": attachment_path
            }
        else:
            raise HTTPException(status_code=500, detail="保存失败")

    except Exception as e:
        logging.error(f"保存仕訳记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")

@app.get("/attachments/{entry_id}")
async def get_attachment(entry_id: str, preview: str = "false", json_preview: str = "false"):
    """获取仕訳记录的附件"""
    try:
        print(f"获取附件请求: entry_id={entry_id}, preview={preview}, json_preview={json_preview}")

        # 转换字符串参数为布尔值
        preview_bool = preview.lower() in ('true', '1', 'yes', 'on')
        json_preview_bool = json_preview.lower() in ('true', '1', 'yes', 'on')

        # 从数据库获取附件路径
        entries = db.get_journal_entries('default')  # 简化处理，实际应该根据company_id查询
        entry = next((e for e in entries if e.get('id') == entry_id), None)

        if not entry or not entry.get('attachment_path'):
            if preview_bool or json_preview_bool:
                # 如果是预览请求且没有附件，返回404
                raise HTTPException(status_code=404, detail="附件不存在")
            else:
                # 如果是获取附件列表，返回空列表
                return []

        attachment_path = Path(entry['attachment_path'])

        if json_preview_bool:
            # JSON预览模式：返回包含base64内容的JSON
            if not attachment_path.exists():
                raise HTTPException(status_code=404, detail="附件文件不存在")

            # 确定MIME类型
            content_type = "image/png"
            if attachment_path.suffix.lower() == '.pdf':
                content_type = "application/pdf"
            elif attachment_path.suffix.lower() in ['.jpg', '.jpeg']:
                content_type = "image/jpeg"
            elif attachment_path.suffix.lower() == '.gif':
                content_type = "image/gif"
            elif attachment_path.suffix.lower() == '.txt':
                content_type = "text/plain"

            # 读取文件并转换为base64
            with open(attachment_path, 'rb') as f:
                file_content = f.read()
                file_content_base64 = base64.b64encode(file_content).decode('utf-8')

            return {
                "success": True,
                "filename": attachment_path.name,
                "content_type": content_type,
                "file_content": file_content_base64,
                "size": len(file_content)
            }

        elif preview_bool:
            # 文件预览模式：直接返回文件内容
            if not attachment_path.exists():
                raise HTTPException(status_code=404, detail="附件文件不存在")

            # 确定MIME类型
            content_type = "image/png"
            if attachment_path.suffix.lower() == '.pdf':
                content_type = "application/pdf"
            elif attachment_path.suffix.lower() in ['.jpg', '.jpeg']:
                content_type = "image/jpeg"
            elif attachment_path.suffix.lower() == '.gif':
                content_type = "image/gif"

            # 对于预览模式，使用inline显示而不是下载
            from fastapi.responses import Response

            # 读取文件内容
            with open(attachment_path, 'rb') as f:
                file_content = f.read()

            # 返回Response而不是FileResponse，以便控制Content-Disposition
            return Response(
                content=file_content,
                media_type=content_type,
                headers={
                    "Content-Disposition": f"inline; filename={attachment_path.name}",
                    "Cache-Control": "public, max-age=3600"
                }
            )
        else:
            # 列表模式：返回附件信息列表
            if attachment_path.exists():
                return [{
                    "filename": attachment_path.name,
                    "size": attachment_path.stat().st_size,
                    "created_at": datetime.fromtimestamp(attachment_path.stat().st_ctime).isoformat()
                }]
            else:
                return []

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"获取附件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取附件失败: {str(e)}")

@app.delete("/attachments/{entry_id}")
async def delete_attachment(entry_id: str, request: Request):
    """删除仕訳记录的附件"""
    try:
        # 获取请求体中的文件名（如果有的话）
        body = await request.body()
        if body:
            data = json.loads(body)
            filename = data.get('filename')
        else:
            filename = None

        # 从数据库获取记录
        entries = db.get_journal_entries('default')
        entry = next((e for e in entries if e.get('id') == entry_id), None)

        if not entry:
            raise HTTPException(status_code=404, detail="记录不存在")

        if not entry.get('attachment_path'):
            raise HTTPException(status_code=404, detail="该记录没有附件")

        attachment_path = Path(entry['attachment_path'])

        # 如果指定了文件名，检查是否匹配
        if filename and attachment_path.name != filename:
            raise HTTPException(status_code=404, detail="指定的附件不存在")

        # 删除附件文件
        if attachment_path.exists():
            attachment_path.unlink()
            logging.info(f"已删除附件文件: {attachment_path}")

        # 更新数据库记录，移除附件路径
        entry['attachment_path'] = None
        db.update_journal_entry('default', entry_id, entry)

        return {
            "success": True,
            "message": "附件删除成功",
            "entry_id": entry_id,
            "deleted_file": attachment_path.name if attachment_path else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"删除附件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除附件失败: {str(e)}")

@app.post("/upload-attachment")
async def upload_attachment(file: UploadFile = File(...), entry_id: str = Form(...)):
    """上传附件到仕訳记录"""
    try:
        # 验证记录是否存在
        entries = db.get_journal_entries('default')
        entry = next((e for e in entries if e.get('id') == entry_id), None)

        if not entry:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 验证文件类型
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'}
        file_extension = Path(file.filename).suffix.lower()

        if file_extension not in allowed_extensions:
            raise HTTPException(status_code=400, detail="不支持的文件类型")

        # 验证文件大小 (10MB限制)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="文件大小不能超过10MB")

        # 创建附件目录
        attachments_dir = Path("attachments")
        attachments_dir.mkdir(exist_ok=True)

        # 生成唯一文件名
        import hashlib
        file_hash = hashlib.md5(file_content).hexdigest()[:8]
        safe_filename = f"{entry_id}_{file_hash}{file_extension}"
        file_path = attachments_dir / safe_filename

        # 保存文件
        with open(file_path, 'wb') as f:
            f.write(file_content)

        # 更新数据库记录
        entry['attachment_path'] = str(file_path)
        success = db.update_journal_entry('default', entry_id, entry)

        if not success:
            # 如果数据库更新失败，删除已保存的文件
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(status_code=500, detail="更新数据库失败")

        return {
            "success": True,
            "message": "附件上传成功",
            "entry_id": entry_id,
            "filename": safe_filename,
            "file_path": str(file_path),
            "file_size": len(file_content)
        }

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"上传附件失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传附件失败: {str(e)}")

@app.put("/journal-entries/{entry_id}")
async def update_journal_entry(entry_id: str, entry_data: dict):
    """更新仕訳记录"""
    try:
        # 验证记录是否存在
        entries = db.get_journal_entries('default')
        existing_entry = next((e for e in entries if e.get('id') == entry_id), None)

        if not existing_entry:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 准备更新数据
        update_data = {
            'id': entry_id,
            'company_id': 'default',
            'entry_date': entry_data.get('entry_date', existing_entry.get('entry_date')),
            'entry_time': entry_data.get('entry_time', existing_entry.get('entry_time')),
            'description': entry_data.get('description', existing_entry.get('description')),
            'debit_account': entry_data.get('debit_account', existing_entry.get('debit_account')),
            'credit_account': entry_data.get('credit_account', existing_entry.get('credit_account')),
            'amount': float(entry_data.get('amount', existing_entry.get('amount', 0))),
            'reference_number': entry_data.get('reference_number', existing_entry.get('reference_number', '')),
            'debit_tax_rate': float(entry_data.get('debit_tax_rate', existing_entry.get('debit_tax_rate', 0))),
            'credit_tax_rate': float(entry_data.get('credit_tax_rate', existing_entry.get('credit_tax_rate', 0))),
            'ai_generated': existing_entry.get('ai_generated', False),
            'ai_confidence': existing_entry.get('ai_confidence'),
            'attachment_path': existing_entry.get('attachment_path'),
            'updated_at': datetime.now().isoformat()
        }

        # 构造entry_datetime
        if update_data['entry_date'] and update_data['entry_time']:
            update_data['entry_datetime'] = f"{update_data['entry_date']}T{update_data['entry_time']}"
        elif update_data['entry_date']:
            update_data['entry_datetime'] = f"{update_data['entry_date']}T00:00:00"
        else:
            update_data['entry_datetime'] = datetime.now().isoformat()

        # 更新数据库
        success = db.update_journal_entry('default', entry_id, update_data)

        if success:
            return {
                "success": True,
                "message": "记录更新成功",
                "entry_id": entry_id
            }
        else:
            raise HTTPException(status_code=500, detail="更新失败")

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"更新仕訳记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@app.delete("/journal-entries/{entry_id}")
async def delete_journal_entry(entry_id: str):
    """删除仕訳记录"""
    try:
        # 验证记录是否存在
        entries = db.get_journal_entries('default')
        existing_entry = next((e for e in entries if e.get('id') == entry_id), None)

        if not existing_entry:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 删除附件文件（如果存在）
        if existing_entry.get('attachment_path'):
            attachment_path = Path(existing_entry['attachment_path'])
            if attachment_path.exists():
                try:
                    attachment_path.unlink()
                    logging.info(f"已删除附件文件: {attachment_path}")
                except Exception as e:
                    logging.warning(f"删除附件文件失败: {e}")

        # 从数据库删除记录
        success = db.delete_journal_entry('default', entry_id)

        if success:
            return {
                "success": True,
                "message": "记录删除成功",
                "entry_id": entry_id
            }
        else:
            raise HTTPException(status_code=500, detail="删除失败")

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"删除仕訳记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@app.put("/journal-entries/update")
async def update_journal_entry_batch(entry_data: dict):
    """更新仕訳记录"""
    try:
        # 提取数据
        company_id = entry_data.get('company_id', 'default')
        entry_id = entry_data.get('id')

        if not entry_id:
            raise HTTPException(status_code=400, detail="缺少记录ID")

        # 创建更新的仕訳记录
        now = datetime.now()
        updated_entry = JournalEntry(
            id=entry_id,
            entry_date=entry_data.get('entry_date', now.strftime('%Y-%m-%d')),
            entry_time=entry_data.get('entry_time', now.strftime('%H:%M:%S')),
            entry_datetime=entry_data.get('entry_datetime', now.isoformat()),
            description=entry_data.get('description', ''),
            debit_account=entry_data.get('debit_account', ''),
            credit_account=entry_data.get('credit_account', ''),
            amount=float(entry_data.get('amount', 0)),
            reference_number=entry_data.get('reference_number', '')
        )

        # 更新数据库
        success = db.update_journal_entry(company_id, entry_id, updated_entry.dict())

        if success:
            return {
                "success": True,
                "message": "仕訳记录已成功更新",
                "entry_id": entry_id
            }
        else:
            raise HTTPException(status_code=404, detail="记录不存在或更新失败")

    except Exception as e:
        logging.error(f"更新仕訳记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@app.delete("/journal-entries/{company_id}/{entry_id}")
async def delete_journal_entry(company_id: str, entry_id: str):
    """删除仕訳记录"""
    try:
        success = db.delete_journal_entry(company_id, entry_id)

        if success:
            return {
                "success": True,
                "message": "仕訳记录已成功删除"
            }
        else:
            raise HTTPException(status_code=404, detail="记录不存在")

    except Exception as e:
        logging.error(f"删除仕訳记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@app.get("/dashboard/summary/{company_id}")
async def get_dashboard_summary(company_id: str):
    # 获取财务汇总数据
    financial_summary = db.get_financial_summary(company_id)
    ai_stats = db.get_ai_stats(company_id)

    return {
        "financial": financial_summary,
        "ai_stats": ai_stats,
        "timestamp": datetime.now().isoformat()
    }

# WebSocket路由
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点"""
    manager = get_connection_manager()

    # 建立连接
    await manager.connect(websocket, client_id, {
        "connect_time": datetime.now().isoformat(),
        "user_agent": websocket.headers.get("user-agent", "unknown")
    })

    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # 处理客户端消息
            await manager.handle_client_message(client_id, message_data)

    except WebSocketDisconnect:
        manager.disconnect(client_id)
        print(f"客户端 {client_id} 断开连接")
    except Exception as e:
        print(f"WebSocket错误: {str(e)}")
        manager.disconnect(client_id)

@app.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket统计信息"""
    manager = get_connection_manager()
    return manager.get_stats()

@app.post("/ws/broadcast")
async def broadcast_message(message_data: dict):
    """广播消息到所有连接的客户端"""
    manager = get_connection_manager()

    from backend.websocket_manager import WebSocketMessage

    message = WebSocketMessage(
        type=MessageType.NOTIFICATION,
        data=message_data,
        timestamp=datetime.now().isoformat()
    )

    await manager.broadcast(message)

    return {"success": True, "message": "消息已广播"}

# 认证路由
@app.post("/api/auth/login", response_model=LoginResponse)
async def login_user(request: LoginRequest):
    """用户登录"""
    auth_manager = get_auth_manager()

    result = auth_manager.authenticate_user(
        username=request.username,
        password=request.password,
        ip_address=request.ip_address,
        user_agent=request.user_agent,
        mfa_code=request.mfa_code
    )

    return LoginResponse(**result)

@app.post("/api/auth/register", response_model=RegisterResponse)
async def register_user(request: RegisterRequest):
    """用户注册"""
    auth_manager = get_auth_manager()

    # 将字符串角色转换为枚举
    from backend.auth_manager import UserRole
    try:
        role = UserRole(request.role)
    except ValueError:
        return RegisterResponse(
            success=False,
            error="无效的用户角色"
        )

    result = auth_manager.register_user(
        username=request.username,
        email=request.email,
        password=request.password,
        role=role
    )

    return RegisterResponse(**result)

@app.post("/api/auth/verify-token")
async def verify_token(token: str):
    """验证访问令牌"""
    auth_manager = get_auth_manager()
    result = auth_manager.verify_token(token)
    return result

@app.post("/api/auth/logout")
async def logout_user(session_id: str):
    """用户登出"""
    auth_manager = get_auth_manager()
    success = auth_manager.logout(session_id)
    return {"success": success}

@app.get("/api/auth/login-history")
async def get_login_history(username: str = None, limit: int = 50):
    """获取登录历史"""
    auth_manager = get_auth_manager()
    history = auth_manager.get_login_history(username, limit)
    return {"history": history}

@app.post("/api/auth/enable-mfa")
async def enable_mfa(user_id: str):
    """启用多因子认证"""
    auth_manager = get_auth_manager()
    result = auth_manager.enable_mfa(user_id)
    return result

@app.post("/api/auth/confirm-mfa")
async def confirm_mfa(user_id: str, mfa_code: str):
    """确认MFA设置"""
    auth_manager = get_auth_manager()
    result = auth_manager.confirm_mfa(user_id, mfa_code)
    return result

# PayPal支付验证路由
@app.post("/api/paypal/verify-payment")
async def verify_payment(payment: PaymentVerification):
    """
    接收PayPal支付凭证，验证并更新用户套餐。
    """
    print(f"Received payment verification:")
    print(f"  Order ID: {payment.orderID}")
    print(f"  Plan ID: {payment.planId}")

    # TODO: 在此实现真实的PayPal API验证逻辑
    print("Simulating successful PayPal API verification.")

    # 模拟成功响应
    return {
        "success": True,
        "message": "支付验证成功，套餐已激活！",
        "order_id": payment.orderID,
        "plan_id": payment.planId
    }

# 备份管理路由
@app.post("/api/backup/create", response_model=CreateBackupResponse)
async def create_backup(request: CreateBackupRequest):
    """创建备份"""
    try:
        backup_manager = get_backup_manager()

        # 将字符串类型转换为枚举
        from backend.backup_manager import BackupType
        backup_type = BackupType(request.type)

        backup_id = backup_manager.create_backup(backup_type, request.description)

        return CreateBackupResponse(
            success=True,
            backup_id=backup_id,
            message=f"备份任务 {backup_id} 已启动"
        )
    except Exception as e:
        return CreateBackupResponse(
            success=False,
            error=f"创建备份失败: {str(e)}"
        )

@app.post("/api/backup/restore", response_model=RestoreBackupResponse)
async def restore_backup(request: RestoreBackupRequest):
    """恢复备份"""
    try:
        backup_manager = get_backup_manager()

        restore_id = backup_manager.restore_backup(
            backup_id=request.backup_id,
            restore_path=request.restore_path
        )

        return RestoreBackupResponse(
            success=True,
            restore_id=restore_id,
            message=f"恢复任务 {restore_id} 已启动"
        )
    except Exception as e:
        return RestoreBackupResponse(
            success=False,
            error=f"恢复备份失败: {str(e)}"
        )

@app.get("/api/backup/list")
async def get_backup_list():
    """获取备份列表"""
    try:
        backup_manager = get_backup_manager()
        backups = backup_manager.get_backup_list()
        return {"success": True, "backups": backups}
    except Exception as e:
        return {"success": False, "error": f"获取备份列表失败: {str(e)}"}

@app.get("/api/backup/restores")
async def get_restore_list():
    """获取恢复列表"""
    try:
        backup_manager = get_backup_manager()
        restores = backup_manager.get_restore_list()
        return {"success": True, "restores": restores}
    except Exception as e:
        return {"success": False, "error": f"获取恢复列表失败: {str(e)}"}

@app.delete("/api/backup/delete/{backup_id}")
async def delete_backup(backup_id: str):
    """删除备份"""
    try:
        backup_manager = get_backup_manager()
        success = backup_manager.delete_backup(backup_id)

        if success:
            return {"success": True, "message": f"备份 {backup_id} 已删除"}
        else:
            return {"success": False, "error": "备份不存在"}
    except Exception as e:
        return {"success": False, "error": f"删除备份失败: {str(e)}"}

# 系统监控和健康检查路由
@app.get("/api/system/health")
async def system_health():
    """系统健康检查"""
    import psutil
    import os
    from datetime import datetime

    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)

        # 内存使用情况
        memory = psutil.virtual_memory()

        # 磁盘使用情况
        disk = psutil.disk_usage('/')

        # 网络统计
        network = psutil.net_io_counters()

        # 系统运行时间
        boot_time = psutil.boot_time()
        uptime = datetime.now().timestamp() - boot_time

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "uptime_seconds": uptime
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@app.get("/api/system/performance")
async def system_performance():
    """系统性能指标"""
    try:
        # 模拟性能数据
        import random

        return {
            "timestamp": datetime.now().isoformat(),
            "metrics": {
                "requests_per_second": random.randint(20, 70),
                "avg_response_time": random.randint(50, 150),
                "active_users": random.randint(5, 25),
                "cache_hit_rate": random.randint(80, 99),
                "error_rate": round(random.uniform(0.1, 2.0), 2),
                "load_score": random.randint(75, 95)
            }
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/api/system/services")
async def system_services():
    """系统服务状态"""
    try:
        services = {
            "fastapi": {"status": "running", "uptime": "2d 5h 30m"},
            "database": {"status": "connected", "connections": 5},
            "ai_agents": {"status": "active", "agents": 5},
            "websocket": {"status": "connected", "connections": 3},
            "gemini_api": {"status": "available", "rate_limit": "90%"},
            "backup_service": {"status": "running", "last_backup": "2h ago"},
            "monitoring": {"status": "active", "alerts": 0}
        }

        return {
            "timestamp": datetime.now().isoformat(),
            "services": services,
            "overall_status": "healthy"
        }
    except Exception as e:
        return {"error": str(e)}

# ==================== 数据导出API ====================

@app.get("/api/export/statistics")
async def get_export_statistics():
    """获取导出统计数据"""
    try:
        # 获取仕訳记录数量
        journal_entries = db.get_journal_entries('default')
        journal_count = len(journal_entries)

        # 获取附件详细统计
        attachments_dir = Path("attachments")
        attachment_stats = await get_attachment_statistics(attachments_dir)

        # 获取AI处理记录数量（模拟）
        ai_log_count = 150  # 这里可以从实际的AI日志表获取

        # 获取数据库大小
        db_paths = [
            Path("goldenledger_accounting.db"),
            Path("backend/goldenledger_accounting.db"),
            Path("backend/database/accounting.db")
        ]
        db_size = 0
        for db_path in db_paths:
            if db_path.exists():
                db_size = db_path.stat().st_size
                break

        return {
            "journal_count": journal_count,
            "attachment_count": attachment_stats["total_count"],
            "attachment_size": attachment_stats["total_size"],
            "attachment_types": attachment_stats["file_types"],
            "ai_log_count": ai_log_count,
            "db_size": db_size
        }
    except Exception as e:
        logging.error(f"获取导出统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")

async def get_attachment_statistics(attachments_dir: Path):
    """获取附件统计信息"""
    try:
        if not attachments_dir.exists():
            return {"total_count": 0, "total_size": 0, "file_types": {}}

        total_count = 0
        total_size = 0
        file_types = {}

        for file_path in attachments_dir.glob("*"):
            if file_path.is_file():
                total_count += 1
                file_size = file_path.stat().st_size
                total_size += file_size

                # 统计文件类型
                file_ext = file_path.suffix.lower()
                if file_ext in file_types:
                    file_types[file_ext]["count"] += 1
                    file_types[file_ext]["size"] += file_size
                else:
                    file_types[file_ext] = {"count": 1, "size": file_size}

        return {
            "total_count": total_count,
            "total_size": total_size,
            "file_types": file_types
        }
    except Exception as e:
        logging.error(f"获取附件统计失败: {str(e)}")
        return {"total_count": 0, "total_size": 0, "file_types": {}}

@app.post("/api/export/quick")
async def quick_export(request: dict):
    """快速导出数据"""
    try:
        format_type = request.get('format', 'excel')

        # 获取所有仕訳记录
        journal_entries = db.get_journal_entries('default')

        if format_type == 'excel':
            return await export_to_excel(journal_entries)
        elif format_type == 'csv':
            return await export_to_csv(journal_entries)
        elif format_type == 'json':
            return await export_to_json(journal_entries)
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

    except Exception as e:
        logging.error(f"快速导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.post("/api/export/custom")
async def custom_export(request: dict):
    """自定义导出数据"""
    try:
        data_types = request.get('dataTypes', ['journal_entries'])
        start_date = request.get('startDate')
        end_date = request.get('endDate')
        format_type = request.get('format', 'excel')

        # 根据配置获取数据
        export_data = {}

        if 'journal_entries' in data_types:
            journal_entries = db.get_journal_entries('default')

            # 按日期筛选
            if start_date or end_date:
                filtered_entries = []
                for entry in journal_entries:
                    entry_date = entry.get('entry_date', '')
                    if start_date and entry_date < start_date:
                        continue
                    if end_date and entry_date > end_date:
                        continue
                    filtered_entries.append(entry)
                journal_entries = filtered_entries

            export_data['journal_entries'] = journal_entries

        if 'accounts' in data_types:
            # 获取科目数据
            accounts = db.get_accounts('default')
            export_data['accounts'] = accounts

        # 根据格式导出
        if format_type == 'excel':
            return await export_custom_to_excel(export_data)
        elif format_type == 'csv':
            return await export_custom_to_csv(export_data)
        elif format_type == 'json':
            return await export_to_json(export_data)
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

    except Exception as e:
        logging.error(f"自定义导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"自定义导出失败: {str(e)}")

@app.post("/api/export/full-backup")
async def full_backup():
    """创建完整数据备份"""
    try:
        # 创建临时目录
        backup_dir = Path(f"temp_backup_{int(time.time())}")
        backup_dir.mkdir(exist_ok=True)

        try:
            # 导出数据库
            db_backup_path = backup_dir / "database_backup.json"
            journal_entries = db.get_journal_entries('default')
            accounts = db.get_accounts('default')

            backup_data = {
                "export_time": datetime.now().isoformat(),
                "version": "1.0",
                "journal_entries": journal_entries,
                "accounts": accounts
            }

            with open(db_backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            # 复制附件文件
            attachments_dir = Path("attachments")
            if attachments_dir.exists():
                backup_attachments_dir = backup_dir / "attachments"
                shutil.copytree(attachments_dir, backup_attachments_dir)

            # 创建ZIP文件
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for file_path in backup_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(backup_dir)
                        zip_file.write(file_path, arcname)

            zip_buffer.seek(0)

            # 清理临时目录
            shutil.rmtree(backup_dir)

            # 返回ZIP文件
            return StreamingResponse(
                io.BytesIO(zip_buffer.read()),
                media_type="application/zip",
                headers={"Content-Disposition": f"attachment; filename=goldenledger_full_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"}
            )

        except Exception as e:
            # 清理临时目录
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            raise e

    except Exception as e:
        logging.error(f"完整备份失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"完整备份失败: {str(e)}")

@app.get("/api/export/history")
async def get_export_history():
    """获取导出历史"""
    try:
        # 模拟导出历史数据
        history = [
            {
                "filename": "goldenledger_export_2025-07-12.xlsx",
                "format": "excel",
                "export_time": "2025-07-12 10:30:00",
                "file_size": 1024000,
                "status": "completed"
            },
            {
                "filename": "goldenledger_custom_export_2025-07-11.csv",
                "format": "csv",
                "export_time": "2025-07-11 15:45:00",
                "file_size": 512000,
                "status": "completed"
            }
        ]
        return history
    except Exception as e:
        logging.error(f"获取导出历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取导出历史失败: {str(e)}")

# ==================== 附件导出专业功能 ====================

@app.get("/api/attachments/list")
async def list_all_attachments():
    """获取所有附件的详细列表"""
    try:
        attachments_dir = Path("attachments")
        if not attachments_dir.exists():
            return {"attachments": [], "total_count": 0, "total_size": 0}

        # 获取所有仕訳记录以建立附件与记录的关联
        journal_entries = db.get_journal_entries('default')
        entry_map = {entry.get('id'): entry for entry in journal_entries}

        attachments = []
        total_size = 0

        for file_path in attachments_dir.glob("*"):
            if file_path.is_file():
                file_stat = file_path.stat()
                file_size = file_stat.st_size
                total_size += file_size

                # 从文件名提取entry_id
                filename = file_path.name
                entry_id = filename.split('_')[0] if '_' in filename else None

                # 获取关联的记录信息
                related_entry = entry_map.get(entry_id) if entry_id else None

                attachment_info = {
                    "filename": filename,
                    "file_path": str(file_path),
                    "size": file_size,
                    "size_formatted": format_file_size(file_size),
                    "extension": file_path.suffix.lower(),
                    "mime_type": get_mime_type(file_path.suffix),
                    "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                    "entry_id": entry_id,
                    "entry_description": related_entry.get('description') if related_entry else None,
                    "entry_date": related_entry.get('entry_date') if related_entry else None,
                    "entry_amount": related_entry.get('amount') if related_entry else None
                }
                attachments.append(attachment_info)

        # 按创建时间排序
        attachments.sort(key=lambda x: x['created_at'], reverse=True)

        return {
            "attachments": attachments,
            "total_count": len(attachments),
            "total_size": total_size,
            "total_size_formatted": format_file_size(total_size)
        }

    except Exception as e:
        logging.error(f"获取附件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取附件列表失败: {str(e)}")

@app.post("/api/attachments/export")
async def export_attachments(request: dict):
    """导出附件文件"""
    try:
        export_type = request.get('export_type', 'selected')  # 'all', 'selected', 'by_date', 'by_type'
        export_format = request.get('format', 'zip')  # 'zip', 'folder_structure'
        selected_files = request.get('selected_files', [])
        date_range = request.get('date_range', {})
        file_types = request.get('file_types', [])
        include_metadata = request.get('include_metadata', True)

        attachments_dir = Path("attachments")
        if not attachments_dir.exists():
            raise HTTPException(status_code=404, detail="附件目录不存在")

        # 获取要导出的文件列表
        files_to_export = []

        if export_type == 'all':
            files_to_export = list(attachments_dir.glob("*"))
        elif export_type == 'selected':
            files_to_export = [attachments_dir / filename for filename in selected_files if (attachments_dir / filename).exists()]
        elif export_type == 'by_date':
            files_to_export = await filter_files_by_date(attachments_dir, date_range)
        elif export_type == 'by_type':
            files_to_export = await filter_files_by_type(attachments_dir, file_types)

        if not files_to_export:
            raise HTTPException(status_code=404, detail="没有找到要导出的附件")

        # 创建导出包
        if export_format == 'zip':
            return await create_attachment_zip(files_to_export, include_metadata)
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")

    except Exception as e:
        logging.error(f"导出附件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出附件失败: {str(e)}")

@app.post("/api/attachments/export/by-entries")
async def export_attachments_by_entries(request: dict):
    """按仕訳记录导出附件"""
    try:
        entry_ids = request.get('entry_ids', [])
        include_metadata = request.get('include_metadata', True)
        organize_by_entry = request.get('organize_by_entry', True)

        if not entry_ids:
            raise HTTPException(status_code=400, detail="请指定要导出的记录ID")

        # 获取指定记录的附件
        journal_entries = db.get_journal_entries('default')
        entries_with_attachments = []

        for entry in journal_entries:
            if entry.get('id') in entry_ids and entry.get('attachment_path'):
                attachment_path = Path(entry['attachment_path'])
                if attachment_path.exists():
                    entries_with_attachments.append({
                        'entry': entry,
                        'attachment_path': attachment_path
                    })

        if not entries_with_attachments:
            raise HTTPException(status_code=404, detail="指定的记录没有附件")

        return await create_entry_based_attachment_zip(entries_with_attachments, include_metadata, organize_by_entry)

    except Exception as e:
        logging.error(f"按记录导出附件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"按记录导出附件失败: {str(e)}")

@app.get("/api/attachments/analysis")
async def analyze_attachments():
    """分析附件使用情况"""
    try:
        attachments_dir = Path("attachments")
        if not attachments_dir.exists():
            return {"error": "附件目录不存在"}

        # 获取所有仕訳记录
        journal_entries = db.get_journal_entries('default')

        analysis = {
            "total_entries": len(journal_entries),
            "entries_with_attachments": 0,
            "entries_without_attachments": 0,
            "orphaned_attachments": 0,
            "file_type_distribution": {},
            "size_distribution": {
                "small": 0,    # < 1MB
                "medium": 0,   # 1MB - 10MB
                "large": 0     # > 10MB
            },
            "monthly_distribution": {},
            "largest_files": [],
            "oldest_files": [],
            "newest_files": []
        }

        # 统计有附件的记录
        entry_ids_with_attachments = set()
        for entry in journal_entries:
            if entry.get('attachment_path'):
                analysis["entries_with_attachments"] += 1
                entry_ids_with_attachments.add(entry.get('id'))
            else:
                analysis["entries_without_attachments"] += 1

        # 分析附件文件
        all_files = []
        for file_path in attachments_dir.glob("*"):
            if file_path.is_file():
                file_stat = file_path.stat()
                file_size = file_stat.st_size

                # 检查是否为孤立附件
                filename = file_path.name
                entry_id = filename.split('_')[0] if '_' in filename else None
                if entry_id not in entry_ids_with_attachments:
                    analysis["orphaned_attachments"] += 1

                # 文件类型分布
                file_ext = file_path.suffix.lower()
                if file_ext in analysis["file_type_distribution"]:
                    analysis["file_type_distribution"][file_ext] += 1
                else:
                    analysis["file_type_distribution"][file_ext] = 1

                # 文件大小分布
                if file_size < 1024 * 1024:  # < 1MB
                    analysis["size_distribution"]["small"] += 1
                elif file_size < 10 * 1024 * 1024:  # < 10MB
                    analysis["size_distribution"]["medium"] += 1
                else:  # > 10MB
                    analysis["size_distribution"]["large"] += 1

                # 月度分布
                created_date = datetime.fromtimestamp(file_stat.st_ctime)
                month_key = created_date.strftime("%Y-%m")
                if month_key in analysis["monthly_distribution"]:
                    analysis["monthly_distribution"][month_key] += 1
                else:
                    analysis["monthly_distribution"][month_key] = 1

                # 收集文件信息用于排序
                all_files.append({
                    "filename": filename,
                    "size": file_size,
                    "created_at": file_stat.st_ctime,
                    "size_formatted": format_file_size(file_size)
                })

        # 最大文件（前5个）
        analysis["largest_files"] = sorted(all_files, key=lambda x: x['size'], reverse=True)[:5]

        # 最旧文件（前5个）
        analysis["oldest_files"] = sorted(all_files, key=lambda x: x['created_at'])[:5]

        # 最新文件（前5个）
        analysis["newest_files"] = sorted(all_files, key=lambda x: x['created_at'], reverse=True)[:5]

        return analysis

    except Exception as e:
        logging.error(f"分析附件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析附件失败: {str(e)}")

@app.delete("/api/attachments/delete/{filename}")
async def delete_attachment_file(filename: str):
    """删除指定的附件文件"""
    try:
        attachments_dir = Path("attachments")
        file_path = attachments_dir / filename

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="附件文件不存在")

        # 检查文件是否在attachments目录内（安全检查）
        if not str(file_path.resolve()).startswith(str(attachments_dir.resolve())):
            raise HTTPException(status_code=400, detail="无效的文件路径")

        # 删除文件
        file_path.unlink()

        # 更新相关的仕訳记录，移除附件路径
        journal_entries = db.get_journal_entries('default')
        updated_entries = []

        for entry in journal_entries:
            if entry.get('attachment_path') == str(file_path):
                # 移除附件路径
                entry_copy = entry.copy()
                entry_copy['attachment_path'] = None
                updated_entries.append(entry_copy)

                # 更新数据库中的记录
                db.update_journal_entry('default', entry.get('id'), entry_copy)

        logging.info(f"成功删除附件: {filename}")

        return {
            "success": True,
            "message": f"附件 {filename} 已成功删除",
            "updated_entries": len(updated_entries)
        }

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="附件文件不存在")
    except PermissionError:
        raise HTTPException(status_code=403, detail="没有权限删除此文件")
    except Exception as e:
        logging.error(f"删除附件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除附件失败: {str(e)}")

@app.post("/api/attachments/batch-delete")
async def batch_delete_attachments(request: dict):
    """批量删除附件文件"""
    try:
        filenames = request.get('filenames', [])
        if not filenames:
            raise HTTPException(status_code=400, detail="请指定要删除的文件")

        attachments_dir = Path("attachments")
        deleted_files = []
        failed_files = []
        updated_entries = 0

        # 获取所有仕訳记录
        journal_entries = db.get_journal_entries('default')

        for filename in filenames:
            try:
                file_path = attachments_dir / filename

                if not file_path.exists():
                    failed_files.append({"filename": filename, "error": "文件不存在"})
                    continue

                # 安全检查
                if not str(file_path.resolve()).startswith(str(attachments_dir.resolve())):
                    failed_files.append({"filename": filename, "error": "无效的文件路径"})
                    continue

                # 删除文件
                file_path.unlink()
                deleted_files.append(filename)

                # 更新相关的仕訳记录
                for entry in journal_entries:
                    if entry.get('attachment_path') == str(file_path):
                        entry_copy = entry.copy()
                        entry_copy['attachment_path'] = None
                        db.update_journal_entry('default', entry.get('id'), entry_copy)
                        updated_entries += 1

            except Exception as e:
                failed_files.append({"filename": filename, "error": str(e)})

        logging.info(f"批量删除附件完成: 成功 {len(deleted_files)} 个, 失败 {len(failed_files)} 个")

        return {
            "success": True,
            "deleted_count": len(deleted_files),
            "failed_count": len(failed_files),
            "deleted_files": deleted_files,
            "failed_files": failed_files,
            "updated_entries": updated_entries
        }

    except Exception as e:
        logging.error(f"批量删除附件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除附件失败: {str(e)}")

# 辅助函数
def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f} {size_names[i]}"

def get_mime_type(file_extension):
    """根据文件扩展名获取MIME类型"""
    mime_types = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf',
        '.txt': 'text/plain',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
    return mime_types.get(file_extension.lower(), 'application/octet-stream')

async def filter_files_by_date(attachments_dir: Path, date_range: dict):
    """按日期范围筛选文件"""
    try:
        start_date = date_range.get('start_date')
        end_date = date_range.get('end_date')

        if not start_date and not end_date:
            return list(attachments_dir.glob("*"))

        filtered_files = []
        for file_path in attachments_dir.glob("*"):
            if file_path.is_file():
                file_stat = file_path.stat()
                file_date = datetime.fromtimestamp(file_stat.st_ctime).date()

                if start_date and file_date < datetime.fromisoformat(start_date).date():
                    continue
                if end_date and file_date > datetime.fromisoformat(end_date).date():
                    continue

                filtered_files.append(file_path)

        return filtered_files
    except Exception as e:
        logging.error(f"按日期筛选文件失败: {str(e)}")
        return []

async def filter_files_by_type(attachments_dir: Path, file_types: list):
    """按文件类型筛选文件"""
    try:
        if not file_types:
            return list(attachments_dir.glob("*"))

        filtered_files = []
        for file_path in attachments_dir.glob("*"):
            if file_path.is_file() and file_path.suffix.lower() in file_types:
                filtered_files.append(file_path)

        return filtered_files
    except Exception as e:
        logging.error(f"按类型筛选文件失败: {str(e)}")
        return []

async def create_attachment_zip(files_to_export: list, include_metadata: bool = True):
    """创建附件ZIP包"""
    try:
        zip_buffer = io.BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 添加附件文件
            for file_path in files_to_export:
                if file_path.is_file():
                    # 使用原始文件名作为ZIP内的文件名
                    arcname = file_path.name
                    zip_file.write(file_path, arcname)

            # 如果需要包含元数据，创建清单文件
            if include_metadata:
                manifest = await create_attachment_manifest(files_to_export)
                zip_file.writestr("attachment_manifest.json", json.dumps(manifest, ensure_ascii=False, indent=2))

                # 创建CSV格式的清单
                csv_manifest = await create_attachment_csv_manifest(files_to_export)
                zip_file.writestr("attachment_list.csv", csv_manifest)

        zip_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(zip_buffer.read()),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=goldenledger_attachments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"}
        )

    except Exception as e:
        logging.error(f"创建附件ZIP包失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建附件ZIP包失败: {str(e)}")

async def create_entry_based_attachment_zip(entries_with_attachments: list, include_metadata: bool = True, organize_by_entry: bool = True):
    """创建按记录组织的附件ZIP包"""
    try:
        zip_buffer = io.BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for item in entries_with_attachments:
                entry = item['entry']
                attachment_path = item['attachment_path']

                if organize_by_entry:
                    # 按记录ID组织文件夹结构
                    entry_folder = f"{entry.get('id')}_{entry.get('entry_date', 'unknown')}"
                    arcname = f"{entry_folder}/{attachment_path.name}"
                else:
                    # 平铺结构，文件名包含记录信息
                    arcname = f"{entry.get('id')}_{attachment_path.name}"

                zip_file.write(attachment_path, arcname)

            # 添加元数据
            if include_metadata:
                manifest = await create_entry_attachment_manifest(entries_with_attachments)
                zip_file.writestr("entry_attachment_manifest.json", json.dumps(manifest, ensure_ascii=False, indent=2))

                # 创建详细的CSV报告
                csv_report = await create_entry_attachment_csv_report(entries_with_attachments)
                zip_file.writestr("entry_attachment_report.csv", csv_report)

        zip_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(zip_buffer.read()),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=goldenledger_entry_attachments_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"}
        )

    except Exception as e:
        logging.error(f"创建记录附件ZIP包失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建记录附件ZIP包失败: {str(e)}")

async def create_attachment_manifest(files_to_export: list):
    """创建附件清单"""
    try:
        manifest = {
            "export_info": {
                "export_time": datetime.now().isoformat(),
                "export_type": "attachments",
                "total_files": len(files_to_export),
                "total_size": sum(f.stat().st_size for f in files_to_export if f.is_file())
            },
            "files": []
        }

        # 获取仕訳记录以建立关联
        journal_entries = db.get_journal_entries('default')
        entry_map = {entry.get('id'): entry for entry in journal_entries}

        for file_path in files_to_export:
            if file_path.is_file():
                file_stat = file_path.stat()
                filename = file_path.name
                entry_id = filename.split('_')[0] if '_' in filename else None
                related_entry = entry_map.get(entry_id) if entry_id else None

                file_info = {
                    "filename": filename,
                    "size": file_stat.st_size,
                    "size_formatted": format_file_size(file_stat.st_size),
                    "extension": file_path.suffix.lower(),
                    "mime_type": get_mime_type(file_path.suffix),
                    "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                    "entry_id": entry_id,
                    "entry_info": {
                        "description": related_entry.get('description') if related_entry else None,
                        "entry_date": related_entry.get('entry_date') if related_entry else None,
                        "amount": related_entry.get('amount') if related_entry else None,
                        "debit_account": related_entry.get('debit_account') if related_entry else None,
                        "credit_account": related_entry.get('credit_account') if related_entry else None
                    } if related_entry else None
                }
                manifest["files"].append(file_info)

        return manifest
    except Exception as e:
        logging.error(f"创建附件清单失败: {str(e)}")
        return {"error": str(e)}

async def create_attachment_csv_manifest(files_to_export: list):
    """创建附件CSV清单"""
    try:
        import io
        csv_buffer = io.StringIO()

        # CSV标题行
        csv_buffer.write("文件名,大小,格式化大小,文件类型,创建时间,修改时间,关联记录ID,记录描述,记录日期,金额,借方科目,贷方科目\n")

        # 获取仕訳记录以建立关联
        journal_entries = db.get_journal_entries('default')
        entry_map = {entry.get('id'): entry for entry in journal_entries}

        for file_path in files_to_export:
            if file_path.is_file():
                file_stat = file_path.stat()
                filename = file_path.name
                entry_id = filename.split('_')[0] if '_' in filename else None
                related_entry = entry_map.get(entry_id) if entry_id else None

                # 构建CSV行
                row = [
                    filename,
                    str(file_stat.st_size),
                    format_file_size(file_stat.st_size),
                    file_path.suffix.lower(),
                    datetime.fromtimestamp(file_stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                    datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                    entry_id or '',
                    related_entry.get('description', '') if related_entry else '',
                    related_entry.get('entry_date', '') if related_entry else '',
                    str(related_entry.get('amount', '')) if related_entry else '',
                    related_entry.get('debit_account', '') if related_entry else '',
                    related_entry.get('credit_account', '') if related_entry else ''
                ]

                # 转义CSV特殊字符
                escaped_row = [f'"{field.replace(chr(34), chr(34)+chr(34))}"' if ',' in field or '"' in field else field for field in row]
                csv_buffer.write(','.join(escaped_row) + '\n')

        return csv_buffer.getvalue()
    except Exception as e:
        logging.error(f"创建附件CSV清单失败: {str(e)}")
        return f"创建CSV清单失败: {str(e)}"

async def create_entry_attachment_manifest(entries_with_attachments: list):
    """创建记录附件清单"""
    try:
        manifest = {
            "export_info": {
                "export_time": datetime.now().isoformat(),
                "export_type": "entry_attachments",
                "total_entries": len(entries_with_attachments),
                "total_files": len(entries_with_attachments),
                "total_size": sum(item['attachment_path'].stat().st_size for item in entries_with_attachments)
            },
            "entries": []
        }

        for item in entries_with_attachments:
            entry = item['entry']
            attachment_path = item['attachment_path']
            file_stat = attachment_path.stat()

            entry_info = {
                "entry_id": entry.get('id'),
                "entry_date": entry.get('entry_date'),
                "entry_time": entry.get('entry_time'),
                "description": entry.get('description'),
                "debit_account": entry.get('debit_account'),
                "credit_account": entry.get('credit_account'),
                "amount": entry.get('amount'),
                "reference_number": entry.get('reference_number'),
                "attachment": {
                    "filename": attachment_path.name,
                    "size": file_stat.st_size,
                    "size_formatted": format_file_size(file_stat.st_size),
                    "extension": attachment_path.suffix.lower(),
                    "mime_type": get_mime_type(attachment_path.suffix),
                    "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                }
            }
            manifest["entries"].append(entry_info)

        return manifest
    except Exception as e:
        logging.error(f"创建记录附件清单失败: {str(e)}")
        return {"error": str(e)}

async def create_entry_attachment_csv_report(entries_with_attachments: list):
    """创建记录附件CSV报告"""
    try:
        import io
        csv_buffer = io.StringIO()

        # CSV标题行
        csv_buffer.write("记录ID,记录日期,记录时间,描述,借方科目,贷方科目,金额,参考号,附件文件名,附件大小,格式化大小,文件类型,创建时间,修改时间\n")

        for item in entries_with_attachments:
            entry = item['entry']
            attachment_path = item['attachment_path']
            file_stat = attachment_path.stat()

            # 构建CSV行
            row = [
                entry.get('id', ''),
                entry.get('entry_date', ''),
                entry.get('entry_time', ''),
                entry.get('description', ''),
                entry.get('debit_account', ''),
                entry.get('credit_account', ''),
                str(entry.get('amount', '')),
                entry.get('reference_number', ''),
                attachment_path.name,
                str(file_stat.st_size),
                format_file_size(file_stat.st_size),
                attachment_path.suffix.lower(),
                datetime.fromtimestamp(file_stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            ]

            # 转义CSV特殊字符
            escaped_row = [f'"{field.replace(chr(34), chr(34)+chr(34))}"' if ',' in field or '"' in field else field for field in row]
            csv_buffer.write(','.join(escaped_row) + '\n')

        return csv_buffer.getvalue()
    except Exception as e:
        logging.error(f"创建记录附件CSV报告失败: {str(e)}")
        return f"创建CSV报告失败: {str(e)}"

# 导出辅助函数
async def export_to_excel(data):
    """导出为Excel格式"""
    try:
        import pandas as pd

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 创建Excel文件
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='仕訳记录', index=False)

        excel_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(excel_buffer.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=goldenledger_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"}
        )
    except ImportError:
        raise HTTPException(status_code=500, detail="缺少pandas依赖，无法导出Excel格式")

async def export_to_csv(data):
    """导出为CSV格式"""
    try:
        import pandas as pd

        df = pd.DataFrame(data)
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')

        return StreamingResponse(
            io.BytesIO(csv_buffer.getvalue().encode('utf-8-sig')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename=goldenledger_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"}
        )
    except ImportError:
        raise HTTPException(status_code=500, detail="缺少pandas依赖，无法导出CSV格式")

async def export_to_json(data):
    """导出为JSON格式"""
    json_str = json.dumps(data, ensure_ascii=False, indent=2)

    return StreamingResponse(
        io.BytesIO(json_str.encode('utf-8')),
        media_type="application/json",
        headers={"Content-Disposition": f"attachment; filename=goldenledger_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"}
    )

async def export_custom_to_excel(data):
    """自定义导出为Excel格式"""
    try:
        import pandas as pd

        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            for sheet_name, sheet_data in data.items():
                if sheet_data:
                    df = pd.DataFrame(sheet_data)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        excel_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(excel_buffer.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=goldenledger_custom_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"}
        )
    except ImportError:
        raise HTTPException(status_code=500, detail="缺少pandas依赖，无法导出Excel格式")

async def export_custom_to_csv(data):
    """自定义导出为CSV格式（多个文件压缩）"""
    try:
        import pandas as pd

        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for sheet_name, sheet_data in data.items():
                if sheet_data:
                    df = pd.DataFrame(sheet_data)
                    csv_buffer = io.StringIO()
                    df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')
                    zip_file.writestr(f"{sheet_name}.csv", csv_buffer.getvalue().encode('utf-8-sig'))

        zip_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(zip_buffer.read()),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=goldenledger_custom_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"}
        )
    except ImportError:
        raise HTTPException(status_code=500, detail="缺少pandas依赖，无法导出CSV格式")

if __name__ == "__main__":
    print("🚀 启动GoldenLedger — Smart AI-Powered Finance System后端服务")
    print("=" * 50)
    print("📖 API文档: http://localhost:8000/docs")
    print("🔧 健康检查: http://localhost:8000/health")
    print("💬 AI记账: http://localhost:8000/ai-bookkeeping/natural-language")
    print("=" * 50)
    
    uvicorn.run(
        "start_backend_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

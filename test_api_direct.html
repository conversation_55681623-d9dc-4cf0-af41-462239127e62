<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 直接API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid #ccc;
        }
        .success { background: #d4edda; border-left-color: #28a745; }
        .error { background: #f8d7da; border-left-color: #dc3545; }
        .warning { background: #fff3cd; border-left-color: #ffc107; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 直接API测试页面</h1>
        <p>这个页面直接测试API连接，不依赖其他脚本。</p>
        
        <div id="results"></div>
        
        <button onclick="testEnvironment()">🌍 测试环境</button>
        <button onclick="checkEnvironmentStatus()">🔍 检查环境状态</button>
        <button onclick="testApiKey()">🔑 测试API密钥</button>
        <button onclick="testApiCall()">🧪 测试API调用</button>
        <button onclick="clearResults()">🗑️ 清除结果</button>
    </div>

    <!-- 加载环境配置 -->
    <script src="env-config.js?v=3.6.3"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testEnvironment() {
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            const isLocal = hostname === 'localhost' || hostname === '127.0.0.1';
            
            addResult(`
                <h3>🌍 环境信息</h3>
                <p><strong>主机名:</strong> ${hostname}</p>
                <p><strong>协议:</strong> ${protocol}</p>
                <p><strong>环境类型:</strong> ${isLocal ? '本地开发' : '生产环境'}</p>
                <p><strong>完整URL:</strong> ${window.location.href}</p>
            `, isLocal ? 'warning' : 'success');
        }

        function testApiKey() {
            // 从环境配置获取API密钥
            const testKey = window.GEMINI_API_KEY || 'NOT_CONFIGURED';
            
            if (testKey && testKey.startsWith('AIzaSy')) {
                addResult(`
                    <h3>🔑 API密钥检查</h3>
                    <p><strong>✅ 找到API密钥</strong></p>
                    <p><strong>密钥前缀:</strong> ${testKey.substring(0, 10)}...</p>
                    <p><strong>密钥长度:</strong> ${testKey.length} 字符</p>
                    <p><strong>格式:</strong> 正确的Google API密钥格式</p>
                `, 'success');
            } else {
                addResult(`
                    <h3>🔑 API密钥检查</h3>
                    <p><strong>❌ 未找到有效API密钥</strong></p>
                `, 'error');
            }
        }

        async function checkEnvironmentStatus() {
            try {
                const response = await fetch('/api/env-status');
                const status = await response.json();

                addResult('🔍 环境状态检查', 'info');
                addResult(`时间: ${status.timestamp}`, 'info');
                addResult(`API密钥配置: ${status.api_key_configured ? '✅ 已配置' : '❌ 未配置'}`, status.api_key_configured ? 'success' : 'error');
                if (status.api_key_configured) {
                    addResult(`API密钥前缀: ${status.api_key_prefix}`, 'info');
                    addResult(`API密钥长度: ${status.api_key_length}`, 'info');
                }

                return status.api_key_configured;
            } catch (error) {
                addResult(`环境状态检查失败: ${error.message}`, 'error');
                return false;
            }
        }

        async function testApiCall() {
            // 首先检查环境状态
            const hasApiKey = await checkEnvironmentStatus();

            if (!hasApiKey) {
                addResult('❌ 无法进行API测试：API密钥未配置', 'error');
                return;
            }

            // 从环境配置获取API密钥
            const apiKey = window.GEMINI_API_KEY;
            
            if (!apiKey) {
                addResult(`
                    <h3>🧪 API调用测试</h3>
                    <p><strong>❌ 无法测试</strong></p>
                    <p>API密钥未配置</p>
                `, 'error');
                return;
            }

            addResult(`
                <h3>🧪 API调用测试</h3>
                <p>⏳ 正在测试API连接...</p>
            `, 'info');

            try {
                const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;
                const requestBody = {
                    contents: [{
                        parts: [{ text: '你好，请简单回复一下，确认API正常工作。' }]
                    }]
                };

                console.log('🔍 发送API请求:', url);
                console.log('📤 请求体:', requestBody);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('📥 响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('📥 响应数据:', result);
                    
                    const responseText = result.candidates?.[0]?.content?.parts?.[0]?.text || '无响应内容';
                    
                    addResult(`
                        <h3>🧪 API调用测试</h3>
                        <p><strong>✅ API调用成功！</strong></p>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>AI响应:</strong></p>
                        <pre>${responseText}</pre>
                    `, 'success');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    console.error('❌ API错误:', errorData);
                    
                    addResult(`
                        <h3>🧪 API调用测试</h3>
                        <p><strong>❌ API调用失败</strong></p>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>错误信息:</strong> ${errorData.error?.message || '未知错误'}</p>
                        <pre>${JSON.stringify(errorData, null, 2)}</pre>
                    `, 'error');
                }
            } catch (error) {
                console.error('❌ 网络错误:', error);
                
                addResult(`
                    <h3>🧪 API调用测试</h3>
                    <p><strong>❌ 网络错误</strong></p>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p>可能的原因：网络连接问题、CORS限制或服务器不可达</p>
                `, 'error');
            }
        }

        // 页面加载时自动运行基本检查
        window.addEventListener('load', function() {
            addResult(`
                <h3>🚀 页面加载完成</h3>
                <p>直接API测试页面已加载，可以开始测试。</p>
                <p><strong>时间:</strong> ${new Date().toLocaleString()}</p>
            `, 'info');
        });
    </script>
</body>
</html>

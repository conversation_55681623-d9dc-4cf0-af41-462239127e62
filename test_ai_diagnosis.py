#!/usr/bin/env python3
"""
AI功能诊断脚本
"""
import asyncio
import json
import sys
import os

# 添加backend目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# 直接测试API端点
import requests
import time

def test_ai_processing():
    """测试AI处理功能"""
    print("🧪 开始AI功能诊断...")

    BASE_URL = "http://localhost:8000"

    # 测试用例
    test_cases = [
        "今天买了咖啡500円",
        "スイカを買いました",
        "今日コンビニで事務用品を1200円で購入",
        "午餐花费1200円"
    ]

    for i, test_text in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ 测试: {test_text}")
        print("-" * 50)

        try:
            # 发送POST请求
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}/ai-bookkeeping/natural-language",
                json={"text": test_text, "company_id": "test"},
                timeout=30
            )
            processing_time = time.time() - start_time

            print(f"⏱️  处理时间: {processing_time:.2f}s")
            print(f"📊 状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 处理结果:")
                print(f"  成功: {result.get('success', False)}")

                if result.get('success'):
                    print(f"  置信度: {result.get('confidence', 0):.2f}")
                    journal = result.get('journal_entry', {})
                    print(f"  描述: {journal.get('description', 'N/A')}")
                    print(f"  借方: {journal.get('debit_account', 'N/A')}")
                    print(f"  贷方: {journal.get('credit_account', 'N/A')}")
                    print(f"  金额: ¥{journal.get('amount', 0)}")
                    print(f"  日期: {journal.get('entry_date', 'N/A')}")
                    print(f"  时间: {journal.get('entry_time', 'N/A')}")

                    if result.get('warnings'):
                        print(f"  警告: {result['warnings']}")
                else:
                    print(f"  ❌ 错误: {result.get('error', '未知错误')}")
                    if result.get('warnings'):
                        print(f"  警告: {result['warnings']}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                print(f"  响应: {response.text[:200]}...")

        except Exception as e:
            print(f"  ❌ 异常: {str(e)}")
            import traceback
            traceback.print_exc()

def test_health_check():
    """测试健康检查"""
    print("\n🔍 测试服务器健康状态...")

    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器健康: {data}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")

def test_simple_request():
    """测试简单请求"""
    print("\n📝 测试简单AI请求...")

    try:
        # 发送一个简单的请求
        response = requests.post(
            "http://localhost:8000/ai-bookkeeping/natural-language",
            json={"text": "测试", "company_id": "test"},
            timeout=10
        )

        print(f"📊 状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")
        print(f"📝 响应内容: {response.text[:500]}...")

        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON解析成功: {json.dumps(data, ensure_ascii=False, indent=2)}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {str(e)}")

    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

def main():
    """主函数"""
    print("🚀 AI功能诊断开始")
    print("=" * 60)

    # 1. 测试健康检查
    test_health_check()

    # 2. 测试简单请求
    test_simple_request()

    # 3. 测试完整流程
    test_ai_processing()

    print("\n" + "=" * 60)
    print("🎉 AI功能诊断完成")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
调试后端完整流程
"""
import asyncio
import sys
import os
import json

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

async def debug_backend_flow():
    """调试后端完整流程"""
    
    print("🧪 调试后端完整流程")
    print("=" * 50)
    
    try:
        # 导入后端模块
        from ai_agents import get_coordinator
        
        # 获取协调器
        coordinator = get_coordinator()
        print(f"✅ 协调器初始化成功")
        
        # 测试文本
        test_text = "今日コンビニで事務用品を1200円で購入"
        print(f"📝 测试文本: {test_text}")
        
        # 调用处理方法
        result = await coordinator.process_natural_language_request(test_text)
        
        print(f"📊 处理结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        if result.get('success'):
            print("✅ 处理成功!")
        else:
            print(f"❌ 处理失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_backend_flow())

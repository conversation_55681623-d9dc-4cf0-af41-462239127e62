<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal 支付测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- PayPal 配置 -->
    <script src="paypal_config.js"></script>
    
    <!-- API 配置 -->
    <script src="api_config.js"></script>
    
    <!-- 订阅管理器 -->
    <script src="subscription_manager.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-card {
            background: rgba(0, 20, 40, 0.3);
            border: 1px solid rgba(120, 219, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-success { background-color: #10b981; }
        .status-error { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
        .status-info { background-color: #3b82f6; }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-center mb-8 text-cyan-400">
            <i class="fas fa-credit-card mr-3"></i>PayPal 支付系统测试
        </h1>
        
        <!-- 系统状态 -->
        <div class="test-card rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-cyan-300">
                <i class="fas fa-heartbeat mr-2"></i>系统状态
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-gray-800 rounded p-4">
                    <div class="flex items-center mb-2">
                        <span class="status-indicator status-info" id="paypal-status"></span>
                        <span class="font-medium">PayPal SDK</span>
                    </div>
                    <div class="text-sm text-gray-300" id="paypal-status-text">检查中...</div>
                </div>
                
                <div class="bg-gray-800 rounded p-4">
                    <div class="flex items-center mb-2">
                        <span class="status-indicator status-info" id="api-status"></span>
                        <span class="font-medium">后端API</span>
                    </div>
                    <div class="text-sm text-gray-300" id="api-status-text">检查中...</div>
                </div>
                
                <div class="bg-gray-800 rounded p-4">
                    <div class="flex items-center mb-2">
                        <span class="status-indicator status-info" id="auth-status"></span>
                        <span class="font-medium">用户认证</span>
                    </div>
                    <div class="text-sm text-gray-300" id="auth-status-text">检查中...</div>
                </div>
                
                <div class="bg-gray-800 rounded p-4">
                    <div class="flex items-center mb-2">
                        <span class="status-indicator status-info" id="subscription-status"></span>
                        <span class="font-medium">订阅状态</span>
                    </div>
                    <div class="text-sm text-gray-300" id="subscription-status-text">检查中...</div>
                </div>
            </div>
        </div>
        
        <!-- 用户信息 -->
        <div class="test-card rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-cyan-300">
                <i class="fas fa-user mr-2"></i>用户信息
            </h2>
            
            <div class="bg-gray-800 rounded p-4">
                <pre id="user-info" class="text-sm text-green-400">加载中...</pre>
            </div>
        </div>
        
        <!-- 套餐测试 -->
        <div class="test-card rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 text-cyan-300">
                <i class="fas fa-box mr-2"></i>套餐测试
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 基础套餐 -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-2 text-blue-400">基础套餐</h3>
                    <p class="text-2xl font-bold mb-4 text-white">¥980/月</p>
                    <!-- 内测提示 -->
                    <div class="mb-4 bg-red-900 border-2 border-red-600 rounded-lg p-3 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>
                            <span class="text-red-300 font-bold text-sm">⚠️ 重要なお知らせ</span>
                        </div>
                        <p class="text-red-200 text-xs font-medium">
                            現在ベータテスト中です。お支払いはしないでください。お支払いもできません。
                        </p>
                    </div>

                    <div id="paypal-basic" class="mb-4"></div>
                    <button onclick="testCreateSubscription('basic')" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors">
                        API测试
                    </button>
                </div>
                
                <!-- 专业套餐 -->
                <div class="bg-gray-800 rounded-lg p-6 border-2 border-cyan-400">
                    <div class="text-center mb-2">
                        <span class="bg-cyan-400 text-black px-2 py-1 rounded text-sm font-bold">推荐</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 text-cyan-400">专业套餐</h3>
                    <p class="text-2xl font-bold mb-4 text-white">¥2980/月</p>
                    <!-- 内测提示 -->
                    <div class="mb-4 bg-red-900 border-2 border-red-600 rounded-lg p-3 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>
                            <span class="text-red-300 font-bold text-sm">⚠️ 重要なお知らせ</span>
                        </div>
                        <p class="text-red-200 text-xs font-medium">
                            現在ベータテスト中です。お支払いはしないでください。お支払いもできません。
                        </p>
                    </div>

                    <div id="paypal-pro" class="mb-4"></div>
                    <button onclick="testCreateSubscription('pro')" 
                            class="w-full bg-cyan-600 hover:bg-cyan-700 text-white py-2 px-4 rounded transition-colors">
                        API测试
                    </button>
                </div>
                
                <!-- 企业套餐 -->
                <div class="bg-gray-800 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-2 text-purple-400">企业套餐</h3>
                    <p class="text-2xl font-bold mb-4 text-white">¥9800/月</p>
                    <!-- 内测提示 -->
                    <div class="mb-4 bg-red-900 border-2 border-red-600 rounded-lg p-3 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>
                            <span class="text-red-300 font-bold text-sm">⚠️ 重要なお知らせ</span>
                        </div>
                        <p class="text-red-200 text-xs font-medium">
                            現在ベータテスト中です。お支払いはしないでください。お支払いもできません。
                        </p>
                    </div>

                    <div id="paypal-enterprise" class="mb-4"></div>
                    <button onclick="testCreateSubscription('enterprise')" 
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition-colors">
                        API测试
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-card rounded-lg p-6">
            <h2 class="text-2xl font-semibold mb-4 text-cyan-300">
                <i class="fas fa-terminal mr-2"></i>测试日志
            </h2>
            
            <div class="bg-black rounded p-4 h-64 overflow-y-auto">
                <div id="test-log" class="text-sm font-mono text-green-400"></div>
            </div>
            
            <div class="mt-4 flex gap-4">
                <button onclick="clearLog()" 
                        class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded transition-colors">
                    <i class="fas fa-trash mr-2"></i>清空日志
                </button>
                
                <button onclick="runFullTest()" 
                        class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors">
                    <i class="fas fa-play mr-2"></i>运行完整测试
                </button>
                
                <button onclick="exportLog()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors">
                    <i class="fas fa-download mr-2"></i>导出日志
                </button>
            </div>
        </div>
    </div>

    <script>
        let testLog = [];
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            log('🚀 PayPal 支付测试页面初始化...');
            
            await checkSystemStatus();
            await loadUserInfo();
            await initPayPalButtons();
            
            log('✅ 初始化完成');
        });
        
        // 检查系统状态
        async function checkSystemStatus() {
            // 检查 PayPal SDK
            try {
                await window.initPayPal();
                updateStatus('paypal', 'success', 'SDK 已加载');
            } catch (error) {
                updateStatus('paypal', 'error', 'SDK 加载失败');
                log('❌ PayPal SDK 加载失败: ' + error.message);
            }
            
            // 检查后端API
            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/health'));
                if (response.ok) {
                    updateStatus('api', 'success', 'API 正常');
                } else {
                    updateStatus('api', 'error', 'API 异常');
                }
            } catch (error) {
                updateStatus('api', 'error', 'API 连接失败');
                log('❌ 后端API检查失败: ' + error.message);
            }
            
            // 检查用户认证
            const token = localStorage.getItem('goldenledger_session_token');
            if (token) {
                updateStatus('auth', 'success', '已登录');
            } else {
                updateStatus('auth', 'warning', '未登录');
            }
            
            // 检查订阅状态
            if (window.SubscriptionManager) {
                try {
                    await window.SubscriptionManager.refresh();
                    const plan = window.SubscriptionManager.getCurrentPlan();
                    updateStatus('subscription', 'success', plan.name);
                } catch (error) {
                    updateStatus('subscription', 'warning', '无订阅');
                }
            }
        }
        
        // 更新状态指示器
        function updateStatus(type, status, text) {
            const indicator = document.getElementById(`${type}-status`);
            const textElement = document.getElementById(`${type}-status-text`);
            
            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }
        
        // 加载用户信息
        async function loadUserInfo() {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    document.getElementById('user-info').textContent = '用户未登录';
                    return;
                }
                
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/verify'), {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const result = await response.json();
                document.getElementById('user-info').textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                document.getElementById('user-info').textContent = '加载用户信息失败: ' + error.message;
                log('❌ 用户信息加载失败: ' + error.message);
            }
        }
        
        // 初始化 PayPal 按钮
        async function initPayPalButtons() {
            if (!window.paypal) {
                log('⚠️ PayPal SDK 未加载，跳过按钮初始化');
                return;
            }
            
            const plans = ['basic', 'pro', 'enterprise'];
            
            plans.forEach(planId => {
                const container = document.getElementById(`paypal-${planId}`);
                if (!container) return;
                
                window.paypal.Buttons({
                    style: {
                        color: 'blue',
                        shape: 'rect',
                        label: 'subscribe',
                        height: 40
                    },
                    
                    createSubscription: function(data, actions) {
                        log(`🔄 创建 ${planId} 订阅...`);
                        return testCreateSubscription(planId, true);
                    },
                    
                    onApprove: function(data, actions) {
                        log(`✅ ${planId} 订阅批准: ${data.subscriptionID}`);
                        return testApproveSubscription(data.subscriptionID, planId);
                    },
                    
                    onError: function(err) {
                        log(`❌ ${planId} 支付错误: ${err.message || err}`);
                    },
                    
                    onCancel: function(data) {
                        log(`⚠️ ${planId} 支付取消`);
                    }
                }).render(container);
            });
            
            log('✅ PayPal 按钮初始化完成');
        }
        
        // 测试创建订阅
        async function testCreateSubscription(planId, returnPromise = false) {
            try {
                log(`🔄 测试创建 ${planId} 订阅...`);
                
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    throw new Error('用户未登录');
                }
                
                const response = await fetch(window.GoldenLedgerAPI.url('/api/payment/create-subscription'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        plan_id: planId,
                        user_id: 'test_user_123'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 订阅创建成功: ${result.subscription_id}`);
                    if (returnPromise) {
                        return result.subscription_id;
                    }
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                log(`❌ 订阅创建失败: ${error.message}`);
                if (returnPromise) {
                    throw error;
                }
            }
        }
        
        // 测试批准订阅
        async function testApproveSubscription(subscriptionId, planId) {
            try {
                log(`🔄 测试批准订阅: ${subscriptionId}`);
                
                const token = localStorage.getItem('goldenledger_session_token');
                const response = await fetch(window.GoldenLedgerAPI.url('/api/payment/approve-subscription'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        subscription_id: subscriptionId,
                        plan_id: planId,
                        user_id: 'test_user_123'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 订阅批准成功`);
                    // 重定向到成功页面
                    window.location.href = `payment-success.html?plan=${planId}&subscription=${subscriptionId}`;
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                log(`❌ 订阅批准失败: ${error.message}`);
                // 重定向到失败页面
                window.location.href = `payment-failed.html?plan=${planId}&error=APPROVAL_FAILED&message=${encodeURIComponent(error.message)}`;
            }
        }
        
        // 运行完整测试
        async function runFullTest() {
            log('🚀 开始运行完整测试...');
            
            await checkSystemStatus();
            await loadUserInfo();
            
            // 测试各个API端点
            const tests = [
                { name: '健康检查', url: '/api/health' },
                { name: '订阅状态', url: '/api/subscription/status' },
                { name: '使用量统计', url: '/api/usage/stats' }
            ];
            
            for (const test of tests) {
                try {
                    log(`🔄 测试 ${test.name}...`);
                    const response = await fetch(window.GoldenLedgerAPI.url(test.url));
                    const result = await response.json();
                    log(`✅ ${test.name} 测试通过`);
                } catch (error) {
                    log(`❌ ${test.name} 测试失败: ${error.message}`);
                }
            }
            
            log('✅ 完整测试完成');
        }
        
        // 日志相关函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('test-log');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('test-log').innerHTML = '';
        }
        
        function exportLog() {
            const logContent = testLog.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `paypal-test-log-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>

/**
 * 用户管理系统
 * GoldenLedger - Smart AI-Powered Finance System
 */

class UserManager {
    constructor() {
        this.currentUser = null;
        this.users = new Map(); // 模拟数据库
        this.initializeStorage();
        this.loadCurrentUser();
    }

    // 初始化存储
    initializeStorage() {
        try {
            const savedUsers = localStorage.getItem('goldenledger_users_db');
            if (savedUsers) {
                const usersArray = JSON.parse(savedUsers);
                this.users = new Map(usersArray);
            }
        } catch (error) {
            console.error('❌ 用户数据库初始化失败:', error);
            this.users = new Map();
        }
    }

    // 保存用户数据库到localStorage
    saveUsersDatabase() {
        try {
            const usersArray = Array.from(this.users.entries());
            localStorage.setItem('goldenledger_users_db', JSON.stringify(usersArray));
            console.log('✅ 用户数据库已保存');
        } catch (error) {
            console.error('❌ 保存用户数据库失败:', error);
        }
    }

    // 注册新用户
    registerUser(userData) {
        try {
            const userId = userData.id || this.generateUserId();
            const user = {
                id: userId,
                name: userData.name,
                email: userData.email,
                picture: userData.picture || this.getDefaultAvatar(userData.name),
                provider: userData.provider || 'email',
                role: userData.role || 'user',
                createdAt: new Date().toISOString(),
                lastLoginAt: new Date().toISOString(),
                isActive: true,
                preferences: {
                    language: 'ja',
                    theme: 'light',
                    notifications: true
                }
            };

            this.users.set(userId, user);
            this.saveUsersDatabase();
            
            console.log('✅ 用户注册成功:', user.name);
            return user;
        } catch (error) {
            console.error('❌ 用户注册失败:', error);
            throw error;
        }
    }

    // 用户登录
    loginUser(userData) {
        try {
            let user = this.getUserByEmail(userData.email);
            
            if (!user) {
                // 新用户自动注册
                user = this.registerUser(userData);
            } else {
                // 更新登录时间和信息
                user.lastLoginAt = new Date().toISOString();
                user.picture = userData.picture || user.picture;
                user.name = userData.name || user.name;
                this.users.set(user.id, user);
                this.saveUsersDatabase();
            }

            // 设置当前用户
            this.currentUser = user;
            this.saveCurrentUserSession(user);
            
            console.log('✅ 用户登录成功:', user.name);
            return user;
        } catch (error) {
            console.error('❌ 用户登录失败:', error);
            throw error;
        }
    }

    // 保存当前用户会话
    saveCurrentUserSession(user) {
        try {
            localStorage.setItem('goldenledger_current_user', JSON.stringify(user));
            localStorage.setItem('goldenledger_user_token', `token_${user.id}_${Date.now()}`);
            localStorage.setItem('goldenledger_login_time', user.lastLoginAt);
        } catch (error) {
            console.error('❌ 保存用户会话失败:', error);
        }
    }

    // 加载当前用户
    loadCurrentUser() {
        try {
            const userStr = localStorage.getItem('goldenledger_current_user');
            if (userStr) {
                this.currentUser = JSON.parse(userStr);
                console.log('✅ 当前用户已加载:', this.currentUser.name);
            }
        } catch (error) {
            console.error('❌ 加载当前用户失败:', error);
            this.currentUser = null;
        }
    }

    // 用户登出
    logoutUser() {
        try {
            if (this.currentUser) {
                console.log('🔐 用户登出:', this.currentUser.name);
            }
            
            this.currentUser = null;
            localStorage.removeItem('goldenledger_current_user');
            localStorage.removeItem('goldenledger_user_token');
            localStorage.removeItem('goldenledger_login_time');
            localStorage.removeItem('goldenledger_user'); // 兼容旧版本
            localStorage.removeItem('goldenledger_token'); // 兼容旧版本
            
            console.log('✅ 用户已登出');
            
            // 跳转到登录页面
            window.location.href = 'login.html';
        } catch (error) {
            console.error('❌ 用户登出失败:', error);
        }
    }

    // 检查用户是否已登录
    isLoggedIn() {
        return this.currentUser !== null && localStorage.getItem('goldenledger_user_token');
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    // 根据邮箱获取用户
    getUserByEmail(email) {
        for (const user of this.users.values()) {
            if (user.email === email) {
                return user;
            }
        }
        return null;
    }

    // 根据ID获取用户
    getUserById(id) {
        return this.users.get(id);
    }

    // 获取所有用户
    getAllUsers() {
        return Array.from(this.users.values());
    }

    // 更新用户信息
    updateUser(userId, updates) {
        try {
            const user = this.users.get(userId);
            if (!user) {
                throw new Error('用户不存在');
            }

            const updatedUser = { ...user, ...updates };
            this.users.set(userId, updatedUser);
            this.saveUsersDatabase();

            // 如果更新的是当前用户，同步更新会话
            if (this.currentUser && this.currentUser.id === userId) {
                this.currentUser = updatedUser;
                this.saveCurrentUserSession(updatedUser);
            }

            console.log('✅ 用户信息已更新:', updatedUser.name);
            return updatedUser;
        } catch (error) {
            console.error('❌ 更新用户信息失败:', error);
            throw error;
        }
    }

    // 生成用户ID
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 获取默认头像
    getDefaultAvatar(name) {
        const encodedName = encodeURIComponent(name || 'User');
        return `https://ui-avatars.com/api/?name=${encodedName}&background=6c5ce7&color=fff&size=128`;
    }

    // 获取用户统计信息
    getUserStats() {
        const users = this.getAllUsers();
        return {
            totalUsers: users.length,
            activeUsers: users.filter(u => u.isActive).length,
            providers: {
                email: users.filter(u => u.provider === 'email').length,
                google: users.filter(u => u.provider === 'google').length,
                facebook: users.filter(u => u.provider === 'facebook').length,
                development: users.filter(u => u.provider === 'development').length
            },
            roles: {
                admin: users.filter(u => u.role === 'admin').length,
                user: users.filter(u => u.role === 'user').length,
                accountant: users.filter(u => u.role === 'accountant').length
            }
        };
    }

    // 验证用户权限
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        const rolePermissions = {
            admin: ['read', 'write', 'delete', 'manage_users', 'system_config'],
            accountant: ['read', 'write', 'financial_reports'],
            user: ['read', 'write_own']
        };

        const userPermissions = rolePermissions[this.currentUser.role] || [];
        return userPermissions.includes(permission);
    }

    // 强制登录检查
    requireAuth() {
        if (!this.isLoggedIn()) {
            console.log('🔐 需要登录，跳转到登录页面');
            const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
            window.location.href = `login.html?return=${currentUrl}`;
            return false;
        }
        return true;
    }
}

// 创建全局用户管理器实例
window.userManager = new UserManager();

console.log('👤 用户管理系统已初始化');

#!/usr/bin/env python3
"""
测试表格换行功能
"""
import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def create_test_entries_with_long_text():
    """创建包含长文本的测试记录"""
    print("🧪 创建包含长文本的测试记录...")
    
    test_entries = [
        {
            "description": "购买办公用品包括打印纸、文件夹、笔记本、圆珠笔、荧光笔、订书机、回形针、便利贴等各种日常办公所需物品",
            "debit_account": "事務用品費・消耗品費・オフィス用品費",
            "credit_account": "現金・銀行預金・普通預金口座",
            "amount": 15000
        },
        {
            "description": "支付本月电费包括办公室照明、空调、电脑设备、打印机等电器设备的用电费用",
            "debit_account": "水道光熱費・電気代",
            "credit_account": "銀行預金・自動振替",
            "amount": 25000
        },
        {
            "description": "购买员工午餐便当和下午茶点心以及饮料等福利费用支出",
            "debit_account": "福利厚生費・従業員福利費",
            "credit_account": "現金・小口現金・petty cash",
            "amount": 8500
        }
    ]
    
    saved_entries = []
    
    for i, entry_data in enumerate(test_entries, 1):
        print(f"  创建测试记录 {i}...")
        
        try:
            # 添加必要的字段
            full_entry = {
                "company_id": "default",
                "id": f"test_long_text_{i}_{int(datetime.now().timestamp())}",
                "entry_date": datetime.now().strftime('%Y-%m-%d'),
                "entry_time": datetime.now().strftime('%H:%M:%S'),
                "entry_datetime": datetime.now().isoformat(),
                **entry_data,
                "reference_number": f"TEST-LONG-{i:03d}",
                "confirmed": True
            }
            
            response = requests.post(
                f"{BASE_URL}/journal-entries/save",
                json=full_entry,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"    ✅ 创建成功: {result.get('message', '')}")
                saved_entries.append(full_entry)
            else:
                print(f"    ❌ 创建失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"    ❌ 创建异常: {str(e)}")
    
    return saved_entries

def test_page_display():
    """测试页面显示效果"""
    print("\n🧪 测试页面显示效果...")
    
    try:
        response = requests.get(f"{BASE_URL}/journal_entries.html", timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含换行相关的CSS类
            css_checks = [
                'table-cell-wrap',
                'description-cell',
                'account-cell',
                'word-wrap: break-word',
                'word-break: break-word'
            ]
            
            print("  检查CSS样式:")
            for css_class in css_checks:
                if css_class in content:
                    print(f"    ✅ {css_class} - 存在")
                else:
                    print(f"    ❌ {css_class} - 缺失")
            
            # 检查HTML结构
            html_checks = [
                'description-cell table-cell-wrap',
                'account-cell table-cell-wrap'
            ]
            
            print("  检查HTML结构:")
            for html_class in html_checks:
                if html_class in content:
                    print(f"    ✅ {html_class} - 应用")
                else:
                    print(f"    ❌ {html_class} - 未应用")
            
            return True
        else:
            print(f"  ❌ 页面加载失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试异常: {str(e)}")
        return False

def test_api_data():
    """测试API数据"""
    print("\n🧪 测试API数据...")
    
    try:
        response = requests.get(f"{BASE_URL}/journal-entries/default", timeout=10)
        
        if response.status_code == 200:
            entries = response.json()
            print(f"  ✅ 获取记录成功: 共 {len(entries)} 条")
            
            # 查找长文本记录
            long_text_entries = [
                entry for entry in entries 
                if len(entry.get('description', '')) > 30 or 
                   len(entry.get('debit_account', '')) > 20 or 
                   len(entry.get('credit_account', '')) > 20
            ]
            
            if long_text_entries:
                print(f"  ✅ 找到 {len(long_text_entries)} 条长文本记录")
                
                # 显示示例
                for i, entry in enumerate(long_text_entries[:3], 1):
                    print(f"    {i}. 描述长度: {len(entry.get('description', ''))} 字符")
                    print(f"       借方长度: {len(entry.get('debit_account', ''))} 字符")
                    print(f"       贷方长度: {len(entry.get('credit_account', ''))} 字符")
            else:
                print("  ⚠️  没有找到长文本记录")
            
            return True
        else:
            print(f"  ❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ API测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试表格换行功能")
    print("=" * 60)
    
    # 1. 创建测试数据
    print("1️⃣ 创建测试数据")
    saved_entries = create_test_entries_with_long_text()
    
    # 2. 测试API数据
    print("\n2️⃣ 测试API数据")
    api_ok = test_api_data()
    
    # 3. 测试页面显示
    print("\n3️⃣ 测试页面显示")
    page_ok = test_page_display()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    if api_ok and page_ok:
        print("✅ 所有测试通过！")
        print("\n📋 修复内容:")
        print("✅ 移除了描述列的 truncate 类")
        print("✅ 移除了借方/贷方列的 whitespace-nowrap 类")
        print("✅ 添加了 break-words 类支持自动换行")
        print("✅ 添加了专门的CSS样式优化显示")
        print("✅ 设置了合适的最大宽度和最小宽度")
        print("✅ 优化了表格行高度自适应")
        
        print("\n🎨 样式特点:")
        print("• 描述列: 最大200px，最小150px，自动换行")
        print("• 借方/贷方列: 最大150px，最小120px，自动换行")
        print("• 行高自适应内容，垂直对齐顶部")
        print("• 保持良好的可读性和美观性")
        
        print(f"\n🔗 测试页面: {BASE_URL}/journal_entries.html")
        print("💡 现在长文本内容会自动换行显示，不会被截断！")
        
    else:
        print("⚠️  部分测试失败，请检查:")
        if not api_ok:
            print("  - API数据获取")
        if not page_ok:
            print("  - 页面样式应用")

if __name__ == "__main__":
    main()

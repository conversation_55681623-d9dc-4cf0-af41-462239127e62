<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>附件API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>附件API测试页面</h1>
    
    <div class="test-section">
        <h2>测试1: 基本连接测试</h2>
        <button onclick="testBasicConnection()">测试基本连接</button>
        <div id="basic-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 获取记录列表</h2>
        <button onclick="testGetEntries()">获取记录列表</button>
        <div id="entries-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 附件列表API</h2>
        <button onclick="testAttachmentList()">测试附件列表</button>
        <div id="attachment-list-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试4: JSON预览API</h2>
        <button onclick="testJsonPreview()">测试JSON预览</button>
        <div id="json-preview-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试5: 直接fetch测试</h2>
        <button onclick="testDirectFetch()">直接fetch测试</button>
        <div id="direct-fetch-result" class="result"></div>
    </div>

    <script>
        const TEST_ENTRY_ID = 'J20250711165206';
        
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }
        
        async function testBasicConnection() {
            try {
                log('basic-result', '正在测试基本连接...');
                const response = await fetch('/health');
                const result = await response.text();
                log('basic-result', `✅ 连接成功!\n状态: ${response.status}\n响应: ${result}`);
            } catch (error) {
                log('basic-result', `❌ 连接失败: ${error.message}`, true);
            }
        }
        
        async function testGetEntries() {
            try {
                log('entries-result', '正在获取记录列表...');
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                
                const entriesWithAttachments = entries.filter(e => e.attachment_path);
                log('entries-result', 
                    `✅ 获取成功!\n总记录数: ${entries.length}\n有附件记录数: ${entriesWithAttachments.length}\n` +
                    `测试记录ID: ${TEST_ENTRY_ID}\n` +
                    `测试记录存在: ${entries.some(e => e.id === TEST_ENTRY_ID) ? '是' : '否'}`
                );
            } catch (error) {
                log('entries-result', `❌ 获取失败: ${error.message}`, true);
            }
        }
        
        async function testAttachmentList() {
            try {
                log('attachment-list-result', '正在测试附件列表API...');
                const response = await fetch(`/attachments/${TEST_ENTRY_ID}`);
                const result = await response.json();
                log('attachment-list-result', 
                    `✅ 附件列表API成功!\n状态: ${response.status}\n` +
                    `响应类型: ${Array.isArray(result) ? '数组' : typeof result}\n` +
                    `内容: ${JSON.stringify(result, null, 2)}`
                );
            } catch (error) {
                log('attachment-list-result', `❌ 附件列表API失败: ${error.message}`, true);
            }
        }
        
        async function testJsonPreview() {
            try {
                log('json-preview-result', '正在测试JSON预览API...');
                const response = await fetch(`/attachments/${TEST_ENTRY_ID}?json_preview=true`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                log('json-preview-result', 
                    `✅ JSON预览API成功!\n状态: ${response.status}\n` +
                    `成功标志: ${result.success}\n` +
                    `文件名: ${result.filename}\n` +
                    `内容类型: ${result.content_type}\n` +
                    `文件大小: ${result.size} bytes\n` +
                    `Base64长度: ${result.file_content ? result.file_content.length : 0} 字符`
                );
            } catch (error) {
                log('json-preview-result', `❌ JSON预览API失败: ${error.message}`, true);
            }
        }
        
        async function testDirectFetch() {
            try {
                log('direct-fetch-result', '正在进行直接fetch测试...');
                
                // 模拟journal_entries.html中的fetch调用
                const response = await fetch(`/attachments/${TEST_ENTRY_ID}?json_preview=true`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Direct fetch response:', response);
                
                if (!response.ok) {
                    throw new Error(`获取附件失败: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('Direct fetch result:', result);
                
                if (!result.success) {
                    throw new Error(result.error || '获取附件失败');
                }
                
                log('direct-fetch-result', 
                    `✅ 直接fetch测试成功!\n` +
                    `这与journal_entries.html中的调用完全相同\n` +
                    `状态: ${response.status}\n` +
                    `成功标志: ${result.success}\n` +
                    `文件名: ${result.filename}\n` +
                    `可以正常创建预览模态框`
                );
                
                // 测试创建预览模态框
                createTestPreviewModal(result);
                
            } catch (error) {
                console.error('Direct fetch error:', error);
                log('direct-fetch-result', `❌ 直接fetch测试失败: ${error.message}`, true);
            }
        }
        
        function createTestPreviewModal(result) {
            // 移除现有的测试模态框
            const existingModal = document.getElementById('test-preview-modal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 创建测试预览模态框
            const modal = document.createElement('div');
            modal.id = 'test-preview-modal';
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.75);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; max-width: 90vw; max-height: 90vh; overflow: hidden;">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; border-bottom: 1px solid #ddd;">
                        <h3 style="margin: 0; font-size: 18px;">附件预览测试 - ${result.filename}</h3>
                        <button onclick="document.getElementById('test-preview-modal').remove()" 
                                style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">×</button>
                    </div>
                    <div style="padding: 16px; max-height: 70vh; overflow: auto; text-align: center;">
                        <img src="data:${result.content_type};base64,${result.file_content}" 
                             style="max-width: 100%; height: auto;" alt="附件预览">
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }
        
        // 页面加载时自动运行基本测试
        window.addEventListener('load', function() {
            setTimeout(testBasicConnection, 500);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jiajibo Style Chatbot Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
        }

        .status-panel {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            text-align: left;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .btn {
            background: #FF69B4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .btn.success {
            background: #00FF00;
            color: #000;
        }

        .btn.error {
            background: #FF4444;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card h3 {
            margin: 0 0 10px 0;
            color: #FFD700;
        }

        .feature-card p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 Jiajibo Style Chatbot Test</h1>
        <p>Testing the beautiful chatbot design from jiajibo project with Gemini AI integration</p>

        <div>
            <button class="btn" onclick="testChatbotStatus()">🔍 Check Status</button>
            <button class="btn" onclick="testOpenChat()">💬 Open Chat</button>
            <button class="btn" onclick="testSendMessage()">📤 Send Test Message</button>
            <button class="btn" onclick="testAPIHealth()">🏥 API Health Check</button>
            <button class="btn" onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <div class="status-panel" id="status-log">
            <div style="color: #00FF00;">[开始] Jiajibo Style Chatbot Test Page Loaded</div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🌸 Beautiful Design</h3>
                <p>Inspired by jiajibo's elegant Japanese aesthetic with sakura themes</p>
            </div>
            <div class="feature-card">
                <h3>🤖 Gemini AI</h3>
                <p>Powered by Google's Gemini 2.0 Flash for intelligent conversations</p>
            </div>
            <div class="feature-card">
                <h3>💬 Rich Interface</h3>
                <p>Material Design inspired chat interface with animations</p>
            </div>
            <div class="feature-card">
                <h3>📱 Responsive</h3>
                <p>Works perfectly on desktop and mobile devices</p>
            </div>
        </div>
    </div>

    <!-- Load Chatbot Scripts -->
    <script src="chatbot/GeminiAPI.js"></script>
    <script src="chatbot/ChatFab.js"></script>
    <script src="chatbot/ChatInterface.js"></script>
    <script src="chatbot/chatbot-init.js"></script>

    <script>
        function log(message, color = '#00BFFF') {
            const statusLog = document.getElementById('status-log');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = color;
            div.textContent = `[${timestamp}] ${message}`;
            statusLog.appendChild(div);
            statusLog.scrollTop = statusLog.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function testChatbotStatus() {
            log('🔍 Testing chatbot status...', '#00BFFF');
            
            if (window.chatbotManager) {
                const status = window.chatbotManager.getStatus();
                log(`✅ Chatbot Manager: Found`, '#00FF00');
                log(`  - Initialized: ${status.initialized}`, status.initialized ? '#00FF00' : '#FF4444');
                log(`  - FAB Visible: ${status.fabVisible}`, status.fabVisible ? '#00FF00' : '#FFD700');
                log(`  - Chat Open: ${status.chatOpen}`, status.chatOpen ? '#00FF00' : '#FFD700');
                log(`  - API Healthy: ${status.apiHealthy}`, status.apiHealthy ? '#00FF00' : '#FF4444');
            } else {
                log('❌ Chatbot Manager: Not found', '#FF4444');
            }

            if (window.chatFab) {
                log(`✅ Chat FAB: Found`, '#00FF00');
            } else {
                log('❌ Chat FAB: Not found', '#FF4444');
            }

            if (window.chatInterface) {
                log(`✅ Chat Interface: Found`, '#00FF00');
            } else {
                log('❌ Chat Interface: Not found', '#FF4444');
            }

            if (window.GeminiAPI) {
                log(`✅ Gemini API: Found`, '#00FF00');
            } else {
                log('❌ Gemini API: Not found', '#FF4444');
            }
        }

        function testOpenChat() {
            log('💬 Testing chat open...', '#00BFFF');
            
            if (window.chatbotManager) {
                window.chatbotManager.openChat();
                log('✅ Chat open command sent', '#00FF00');
            } else {
                log('❌ Cannot open chat - manager not found', '#FF4444');
            }
        }

        function testSendMessage() {
            log('📤 Testing message send...', '#00BFFF');
            
            const testMessage = 'こんにちは！テストメッセージです。GoldenLedgerについて教えてください。';
            
            if (window.chatbotManager) {
                window.chatbotManager.sendMessage(testMessage, 'general');
                log('✅ Test message sent: ' + testMessage.substring(0, 30) + '...', '#00FF00');
            } else {
                log('❌ Cannot send message - manager not found', '#FF4444');
            }
        }

        async function testAPIHealth() {
            log('🏥 Testing API health...', '#00BFFF');
            
            if (window.GeminiAPI) {
                try {
                    const isHealthy = await window.GeminiAPI.healthCheck();
                    if (isHealthy) {
                        log('✅ API Health Check: PASSED', '#00FF00');
                    } else {
                        log('⚠️ API Health Check: FAILED', '#FFD700');
                    }
                } catch (error) {
                    log('❌ API Health Check: ERROR - ' + error.message, '#FF4444');
                }
            } else {
                log('❌ Cannot test API - not found', '#FF4444');
            }
        }

        function clearLog() {
            const statusLog = document.getElementById('status-log');
            statusLog.innerHTML = '<div style="color: #00FF00;">[清除] Log cleared</div>';
        }

        // Auto-run status check after page load
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Test page loaded', '#00FF00');
            
            setTimeout(() => {
                log('🚀 Running automatic status check...', '#00BFFF');
                testChatbotStatus();
                
                setTimeout(() => {
                    log('💡 Click buttons above to test chatbot features', '#FFD700');
                    log('💡 The chatbot FAB should appear in bottom-right corner after 2 seconds', '#FFD700');
                }, 1000);
            }, 1000);
        });

        // Listen for chatbot events
        document.addEventListener('chatbot:open', () => {
            log('🎉 Event: Chatbot opened', '#FF69B4');
        });

        document.addEventListener('chatbot:close', () => {
            log('🎉 Event: Chatbot closed', '#FF69B4');
        });

        document.addEventListener('chatbot:message', (event) => {
            log('🎉 Event: Message sent - ' + event.detail.message.substring(0, 30) + '...', '#FF69B4');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最简仪表盘</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .kpi { display: flex; justify-content: space-between; align-items: center; margin: 10px 0; }
        .kpi-label { font-weight: bold; color: #666; }
        .kpi-value { font-size: 1.2em; font-weight: bold; color: #333; }
        .loading { color: #999; }
        .error { color: #e74c3c; }
        .success { color: #27ae60; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>最简仪表盘测试</h1>
    
    <div class="card">
        <h2>财务数据</h2>
        <div class="kpi">
            <span class="kpi-label">本月收入:</span>
            <span id="revenue" class="kpi-value loading">加载中...</span>
        </div>
        <div class="kpi">
            <span class="kpi-label">本月支出:</span>
            <span id="expenses" class="kpi-value loading">加载中...</span>
        </div>
        <div class="kpi">
            <span class="kpi-label">本月利润:</span>
            <span id="profit" class="kpi-value loading">加载中...</span>
        </div>
        <div class="kpi">
            <span class="kpi-label">总记录数:</span>
            <span id="total" class="kpi-value loading">加载中...</span>
        </div>
    </div>
    
    <div class="card">
        <h2>AI统计</h2>
        <div class="kpi">
            <span class="kpi-label">处理总数:</span>
            <span id="ai-total" class="kpi-value loading">加载中...</span>
        </div>
        <div class="kpi">
            <span class="kpi-label">成功率:</span>
            <span id="ai-success" class="kpi-value loading">加载中...</span>
        </div>
        <div class="kpi">
            <span class="kpi-label">平均置信度:</span>
            <span id="ai-confidence" class="kpi-value loading">加载中...</span>
        </div>
    </div>
    
    <div class="card">
        <h2>控制</h2>
        <button onclick="loadData()" style="padding: 10px 20px; margin: 5px;">重新加载</button>
        <button onclick="clearLog()" style="padding: 10px 20px; margin: 5px;">清空日志</button>
    </div>
    
    <div class="card">
        <h2>日志</h2>
        <div id="log" class="log">等待加载...</div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function loadData() {
            log('🔄 开始加载数据...');
            
            try {
                // 测试API
                log('📡 调用API: /dashboard/summary/default');
                const response = await fetch('/dashboard/summary/default');
                
                log(`📊 响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`API错误: ${response.status}`);
                }
                
                const data = await response.json();
                log('✅ 数据解析成功');
                
                // 显示原始数据
                log(`📋 原始数据: ${JSON.stringify(data, null, 2)}`);
                
                // 更新UI
                if (data.financial) {
                    const f = data.financial;
                    document.getElementById('revenue').textContent = `¥${(f.monthly_revenue || 0).toLocaleString()}`;
                    document.getElementById('expenses').textContent = `¥${(f.monthly_expenses || 0).toLocaleString()}`;
                    document.getElementById('profit').textContent = `¥${(f.monthly_profit || 0).toLocaleString()}`;
                    document.getElementById('total').textContent = (f.total_entries || 0).toLocaleString();
                    
                    document.getElementById('revenue').className = 'kpi-value success';
                    document.getElementById('expenses').className = 'kpi-value success';
                    document.getElementById('profit').className = 'kpi-value success';
                    document.getElementById('total').className = 'kpi-value success';
                    
                    log('💰 财务数据更新完成');
                } else {
                    log('⚠️ 缺少财务数据');
                }
                
                if (data.ai_stats) {
                    const ai = data.ai_stats;
                    document.getElementById('ai-total').textContent = (ai.total_processed || 0).toLocaleString();
                    document.getElementById('ai-success').textContent = `${((ai.success_rate || 0) * 100).toFixed(1)}%`;
                    document.getElementById('ai-confidence').textContent = `${((ai.avg_confidence || 0) * 100).toFixed(1)}%`;
                    
                    document.getElementById('ai-total').className = 'kpi-value success';
                    document.getElementById('ai-success').className = 'kpi-value success';
                    document.getElementById('ai-confidence').className = 'kpi-value success';
                    
                    log('🤖 AI统计更新完成');
                } else {
                    log('⚠️ 缺少AI统计数据');
                }
                
                log('🎉 所有数据加载完成');
                
            } catch (error) {
                log(`❌ 加载失败: ${error.message}`);
                
                // 显示错误状态
                const elements = ['revenue', 'expenses', 'profit', 'total', 'ai-total', 'ai-success', 'ai-confidence'];
                elements.forEach(id => {
                    const el = document.getElementById(id);
                    el.textContent = '错误';
                    el.className = 'kpi-value error';
                });
            }
        }
        
        // 页面加载时自动加载数据
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成');
            loadData();
        });
    </script>
</body>
</html>

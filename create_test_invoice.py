#!/usr/bin/env python3
"""
创建测试发票图片
"""
from PIL import Image, ImageDraw, ImageFont
import os

def create_test_invoice():
    """创建一个简单的测试发票图片"""
    
    # 创建白色背景图片
    width, height = 400, 600
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体
    try:
        # macOS系统字体
        font_large = ImageFont.truetype('/System/Library/Fonts/Arial.ttf', 24)
        font_medium = ImageFont.truetype('/System/Library/Fonts/Arial.ttf', 18)
        font_small = ImageFont.truetype('/System/Library/Fonts/Arial.ttf', 14)
    except:
        # 使用默认字体
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 绘制发票内容
    y = 30
    
    # 标题
    draw.text((width//2 - 50, y), "領収書", fill='black', font=font_large)
    y += 50
    
    # 分割线
    draw.line([(20, y), (width-20, y)], fill='black', width=2)
    y += 30
    
    # 商户信息
    draw.text((30, y), "セブンイレブン 渋谷店", fill='black', font=font_medium)
    y += 30
    draw.text((30, y), "東京都渋谷区渋谷1-1-1", fill='black', font=font_small)
    y += 40
    
    # 日期
    draw.text((30, y), "日付: 2025年07月11日", fill='black', font=font_medium)
    y += 40
    
    # 商品明细
    draw.text((30, y), "商品明細:", fill='black', font=font_medium)
    y += 30
    draw.text((40, y), "・事務用品 (ボールペン)", fill='black', font=font_small)
    y += 25
    draw.text((40, y), "・ノート", fill='black', font=font_small)
    y += 25
    draw.text((40, y), "・クリップ", fill='black', font=font_small)
    y += 40
    
    # 金额
    draw.text((30, y), "小計: ¥1,080", fill='black', font=font_medium)
    y += 25
    draw.text((30, y), "消費税: ¥120", fill='black', font=font_medium)
    y += 25
    
    # 分割线
    draw.line([(30, y+5), (200, y+5)], fill='black', width=1)
    y += 20
    
    # 总金额
    draw.text((30, y), "合計: ¥1,200", fill='black', font=font_large)
    y += 50
    
    # 支付方式
    draw.text((30, y), "支払方法: 現金", fill='black', font=font_medium)
    y += 40
    
    # 发票号码
    draw.text((30, y), "領収書番号: R2025071101", fill='black', font=font_small)
    
    # 保存图片
    image.save('test_invoice.png')
    print("✅ 测试发票图片已创建: test_invoice.png")

if __name__ == "__main__":
    create_test_invoice()

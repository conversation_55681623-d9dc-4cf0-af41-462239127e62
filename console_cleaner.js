/**
 * 控制台日志清理器 v1.0
 * 清理生产环境中的敏感日志和调试信息
 */

class ConsoleCleaner {
    constructor() {
        this.isProduction = window.location.hostname !== 'localhost' && 
                           window.location.hostname !== '127.0.0.1';
        this.originalMethods = {};
        this.init();
    }

    init() {
        if (this.isProduction) {
            this.cleanProductionConsole();
        } else {
            this.setupDevelopmentConsole();
        }
    }

    // 生产环境：清理所有敏感日志
    cleanProductionConsole() {
        // 保存原始方法
        this.originalMethods = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info,
            debug: console.debug
        };

        // 重写console方法
        console.log = this.createFilteredLogger('log');
        console.warn = this.createFilteredLogger('warn');
        console.error = this.createFilteredLogger('error');
        console.info = this.createFilteredLogger('info');
        console.debug = () => {}; // 完全禁用debug

        // 清理现有控制台
        if (console.clear) {
            console.clear();
        }
    }

    // 开发环境：保留重要日志，过滤噪音
    setupDevelopmentConsole() {
        this.originalMethods = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };

        console.warn = this.createFilteredLogger('warn');
        console.log = this.createFilteredLogger('log');
    }

    createFilteredLogger(level) {
        const originalMethod = this.originalMethods[level];
        
        return (...args) => {
            const message = args.join(' ');
            
            // 敏感信息过滤
            const sensitivePatterns = [
                /API.*key/i,
                /token/i,
                /password/i,
                /secret/i,
                /auth.*header/i,
                /bearer/i,
                /session.*id/i
            ];

            // 检查是否包含敏感信息
            if (sensitivePatterns.some(pattern => pattern.test(message))) {
                if (this.isProduction) {
                    return; // 生产环境完全屏蔽
                } else {
                    // 开发环境脱敏显示
                    const sanitized = this.sanitizeMessage(message);
                    originalMethod.call(console, `[SANITIZED] ${sanitized}`);
                    return;
                }
            }

            // 噪音过滤
            const noisePatterns = [
                /cdn\.tailwindcss\.com should not be used in production/,
                /HEAD request failed/,
                /前端页面检查失败/,
                /🎵.*音乐/,
                /Fetch.*success/i,
                /Loading.*complete/i
            ];

            if (noisePatterns.some(pattern => pattern.test(message))) {
                return; // 过滤噪音
            }

            // 错误级别始终显示（但要脱敏）
            if (level === 'error') {
                const sanitized = this.sanitizeMessage(message);
                originalMethod.call(console, sanitized);
                return;
            }

            // 生产环境只显示重要信息
            if (this.isProduction) {
                const importantPatterns = [
                    /error/i,
                    /failed/i,
                    /warning/i,
                    /critical/i,
                    /unauthorized/i,
                    /forbidden/i
                ];

                if (importantPatterns.some(pattern => pattern.test(message))) {
                    const sanitized = this.sanitizeMessage(message);
                    originalMethod.call(console, sanitized);
                }
                return;
            }

            // 开发环境显示过滤后的日志
            originalMethod.apply(console, args);
        };
    }

    // 脱敏处理
    sanitizeMessage(message) {
        return message
            .replace(/([a-zA-Z0-9]{20,})/g, '***REDACTED***') // 长字符串
            .replace(/(token|key|secret|password)[\s:=]+[^\s]+/gi, '$1: ***REDACTED***')
            .replace(/Bearer\s+[^\s]+/gi, 'Bearer ***REDACTED***')
            .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '***EMAIL***');
    }

    // 恢复原始控制台（调试用）
    restore() {
        if (this.originalMethods.log) {
            console.log = this.originalMethods.log;
            console.warn = this.originalMethods.warn;
            console.error = this.originalMethods.error;
            console.info = this.originalMethods.info;
            console.debug = this.originalMethods.debug;
        }
    }

    // 清理控制台历史
    clearHistory() {
        if (console.clear) {
            console.clear();
        }
    }
}

// 全局错误处理器
class ErrorHandler {
    constructor() {
        this.setupGlobalErrorHandling();
    }

    setupGlobalErrorHandling() {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.handleError(event.error, event.filename, event.lineno);
        });

        // 捕获Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'Promise', 0);
        });
    }

    handleError(error, filename, lineno) {
        // 过滤常见的无害错误
        const ignoredErrors = [
            'ResizeObserver loop limit exceeded',
            'Non-Error promise rejection captured',
            'Script error',
            'Network request failed'
        ];

        const errorMessage = error?.message || error?.toString() || 'Unknown error';
        
        if (ignoredErrors.some(ignored => errorMessage.includes(ignored))) {
            return;
        }

        // 只在开发环境显示详细错误
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.error('🚨 Unhandled Error:', {
                message: errorMessage,
                file: filename,
                line: lineno,
                stack: error?.stack
            });
        }
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.ConsoleCleaner = new ConsoleCleaner();
    window.ErrorHandler = new ErrorHandler();
}

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 数据备份和恢复管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .status-completed { color: #10b981; }
        .status-in_progress { color: #f59e0b; }
        .status-failed { color: #ef4444; }
        .status-pending { color: #6b7280; }
        .type-full { background: #dbeafe; color: #1e40af; }
        .type-incremental { background: #dcfce7; color: #166534; }
        .type-differential { background: #fef3c7; color: #92400e; }
        .progress-bar { transition: width 0.3s ease; }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">💾 数据备份和恢复管理</h1>
                    <p class="text-lg opacity-90">企业级数据保护和灾难恢复解决方案</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div id="auto-backup-status" class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        🔄 自动备份: 启用
                    </div>
                    <button id="create-backup-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        ➕ 创建备份
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Backup Statistics -->
        <section class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总备份数</p>
                        <p id="total-backups" class="text-2xl font-bold text-blue-600">0</p>
                        <p class="text-xs text-gray-500">所有备份</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">💾</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">成功备份</p>
                        <p id="successful-backups" class="text-2xl font-bold text-green-600">0</p>
                        <p class="text-xs text-gray-500">完成状态</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">✅</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">备份大小</p>
                        <p id="total-backup-size" class="text-2xl font-bold text-purple-600">0 MB</p>
                        <p class="text-xs text-gray-500">存储占用</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📊</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">最后备份</p>
                        <p id="last-backup-time" class="text-2xl font-bold text-orange-600">--</p>
                        <p class="text-xs text-gray-500">时间</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">⏰</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Backup Management -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Backup List -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">📋 备份列表</h3>
                    <div class="flex space-x-2">
                        <button id="refresh-backups-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                            🔄 刷新
                        </button>
                        <button id="cleanup-backups-btn" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            🧹 清理
                        </button>
                    </div>
                </div>
                
                <div id="backups-list" class="space-y-3 max-h-96 overflow-y-auto">
                    <!-- 备份列表将在这里动态生成 -->
                </div>
            </div>

            <!-- Restore History -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">🔄 恢复历史</h3>
                    <button id="refresh-restores-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                        🔄 刷新
                    </button>
                </div>
                
                <div id="restores-list" class="space-y-3 max-h-96 overflow-y-auto">
                    <!-- 恢复历史将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- Backup Configuration -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <h3 class="text-lg font-semibold mb-4">⚙️ 备份配置</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">自动备份</label>
                    <div class="flex items-center space-x-3">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto-backup-toggle" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                        <span class="text-sm text-gray-600">启用自动备份</span>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">备份间隔 (小时)</label>
                    <select id="backup-interval" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="1">1小时</option>
                        <option value="3">3小时</option>
                        <option value="6" selected>6小时</option>
                        <option value="12">12小时</option>
                        <option value="24">24小时</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">最大保留备份数</label>
                    <input 
                        type="number" 
                        id="max-backups" 
                        value="30" 
                        min="5" 
                        max="100"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">压缩备份</label>
                    <div class="flex items-center space-x-3">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="compression-toggle" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                        <span class="text-sm text-gray-600">启用压缩</span>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">备份类型</label>
                    <select id="default-backup-type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="full">完整备份</option>
                        <option value="incremental" selected>增量备份</option>
                        <option value="differential">差异备份</option>
                    </select>
                </div>

                <div class="flex items-end">
                    <button id="save-config-btn" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        💾 保存配置
                    </button>
                </div>
            </div>
        </section>

        <!-- Backup Charts -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📊 备份趋势</h3>
                <canvas id="backup-trend-chart" width="400" height="300"></canvas>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🥧 备份类型分布</h3>
                <canvas id="backup-type-chart" width="400" height="300"></canvas>
            </div>
        </section>

        <!-- Storage Usage -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <h3 class="text-lg font-semibold mb-4">💽 存储使用情况</h3>
            
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">备份存储</span>
                        <span class="text-sm text-gray-600">2.3 GB / 10 GB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full progress-bar" style="width: 23%"></div>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">数据目录</span>
                        <span class="text-sm text-gray-600">156 MB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full progress-bar" style="width: 15%"></div>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">日志文件</span>
                        <span class="text-sm text-gray-600">45 MB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full progress-bar" style="width: 8%"></div>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">配置文件</span>
                        <span class="text-sm text-gray-600">2 MB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-500 h-2 rounded-full progress-bar" style="width: 1%"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Create Backup Modal -->
    <div id="create-backup-modal" class="fixed inset-0 bg-black bg-opacity-50 items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <h3 class="text-xl font-bold text-gray-800">💾 创建新备份</h3>
                <p class="text-gray-600 mt-2">选择备份类型和配置</p>
            </div>

            <form id="create-backup-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">备份类型</label>
                    <select id="backup-type-select" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="full">完整备份 - 备份所有数据</option>
                        <option value="incremental">增量备份 - 仅备份新增和修改的数据</option>
                        <option value="differential">差异备份 - 备份自上次完整备份以来的变化</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">备份描述</label>
                    <input 
                        type="text" 
                        id="backup-description" 
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入备份描述 (可选)"
                    >
                </div>

                <div class="flex items-center space-x-3">
                    <input type="checkbox" id="immediate-backup" class="rounded" checked>
                    <label for="immediate-backup" class="text-sm text-gray-700">立即开始备份</label>
                </div>

                <div class="flex space-x-3 pt-4">
                    <button 
                        type="button" 
                        id="cancel-backup-btn"
                        class="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        取消
                    </button>
                    <button 
                        type="submit" 
                        class="flex-1 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors"
                    >
                        创建备份
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Notification Area -->
    <div id="notification-area" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- 动态通知将在这里显示 -->
    </div>

    <script>
        // 全局变量
        let backupTrendChart, backupTypeChart;
        let backups = [];
        let restores = [];

        // 模拟备份数据
        const mockBackups = [
            {
                id: 'backup_20240109_140000',
                type: 'full',
                status: 'completed',
                start_time: '2024-01-09T14:00:00',
                end_time: '2024-01-09T14:15:00',
                file_size: 1024 * 1024 * 500, // 500MB
                description: '完整备份'
            },
            {
                id: 'backup_20240109_200000',
                type: 'incremental',
                status: 'completed',
                start_time: '2024-01-09T20:00:00',
                end_time: '2024-01-09T20:03:00',
                file_size: 1024 * 1024 * 50, // 50MB
                description: '自动增量备份'
            },
            {
                id: 'backup_20240110_020000',
                type: 'full',
                status: 'in_progress',
                start_time: '2024-01-10T02:00:00',
                end_time: '',
                file_size: 0,
                description: '自动完整备份'
            }
        ];

        const mockRestores = [
            {
                id: 'restore_20240109_150000',
                backup_id: 'backup_20240109_140000',
                status: 'completed',
                start_time: '2024-01-09T15:00:00',
                end_time: '2024-01-09T15:10:00',
                restored_files: ['data/', 'config/', 'logs/']
            }
        ];

        // 创建备份
        async function createBackup(type, description) {
            try {
                const response = await fetch('/api/backup/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: type,
                        description: description
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('备份创建成功', `备份任务 ${result.backup_id} 已启动`, 'success');
                    loadBackupsList();
                    return { success: true, backup_id: result.backup_id };
                } else {
                    showNotification('备份创建失败', result.error, 'error');
                    return { success: false, error: result.error };
                }
            } catch (error) {
                // 模拟创建备份成功
                const backupId = `backup_${new Date().toISOString().replace(/[:.]/g, '').slice(0, 15)}`;

                const newBackup = {
                    id: backupId,
                    type: type,
                    status: 'in_progress',
                    start_time: new Date().toISOString(),
                    end_time: '',
                    file_size: 0,
                    description: description || `${getTypeDisplayName(type)}备份`
                };

                mockBackups.unshift(newBackup);

                showNotification('备份创建成功', `备份任务 ${backupId} 已启动`, 'success');
                loadBackupsList();
                updateStatistics();

                // 模拟备份进度
                setTimeout(() => {
                    newBackup.status = 'completed';
                    newBackup.end_time = new Date().toISOString();
                    newBackup.file_size = Math.floor(Math.random() * 1024 * 1024 * 200) + 1024 * 1024 * 50; // 50-250MB

                    showNotification('备份完成', `备份 ${backupId} 已完成`, 'success');
                    loadBackupsList();
                    updateStatistics();
                }, 5000);

                return { success: true, backup_id: backupId };
            }
        }

        // 恢复备份
        async function restoreBackup(backupId) {
            try {
                const response = await fetch('/api/backup/restore', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        backup_id: backupId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('恢复启动成功', `恢复任务 ${result.restore_id} 已启动`, 'success');
                    loadRestoresList();
                    return { success: true, restore_id: result.restore_id };
                } else {
                    showNotification('恢复启动失败', result.error, 'error');
                    return { success: false, error: result.error };
                }
            } catch (error) {
                // 模拟恢复成功
                const restoreId = `restore_${new Date().toISOString().replace(/[:.]/g, '').slice(0, 15)}`;

                const newRestore = {
                    id: restoreId,
                    backup_id: backupId,
                    status: 'in_progress',
                    start_time: new Date().toISOString(),
                    end_time: '',
                    restored_files: []
                };

                mockRestores.unshift(newRestore);

                showNotification('恢复启动成功', `恢复任务 ${restoreId} 已启动`, 'success');
                loadRestoresList();

                // 模拟恢复进度
                setTimeout(() => {
                    newRestore.status = 'completed';
                    newRestore.end_time = new Date().toISOString();
                    newRestore.restored_files = ['data/', 'config/', 'logs/'];

                    showNotification('恢复完成', `恢复 ${restoreId} 已完成`, 'success');
                    loadRestoresList();
                }, 3000);

                return { success: true, restore_id: restoreId };
            }
        }

        // 删除备份
        async function deleteBackup(backupId) {
            if (!confirm('确定要删除这个备份吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`/api/backup/delete/${backupId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('备份删除成功', `备份 ${backupId} 已删除`, 'success');
                    loadBackupsList();
                    updateStatistics();
                } else {
                    showNotification('备份删除失败', result.error, 'error');
                }
            } catch (error) {
                // 模拟删除成功
                const index = mockBackups.findIndex(b => b.id === backupId);
                if (index !== -1) {
                    mockBackups.splice(index, 1);
                    showNotification('备份删除成功', `备份 ${backupId} 已删除`, 'success');
                    loadBackupsList();
                    updateStatistics();
                }
            }
        }

        // 加载备份列表
        function loadBackupsList() {
            const backupsList = document.getElementById('backups-list');

            if (mockBackups.length === 0) {
                backupsList.innerHTML = '<div class="text-center text-gray-500 py-8">暂无备份记录</div>';
                return;
            }

            backupsList.innerHTML = mockBackups.map(backup => `
                <div class="flex items-center justify-between p-4 border rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-lg flex items-center justify-center ${getTypeClass(backup.type)}">
                            <span class="text-lg">${getTypeIcon(backup.type)}</span>
                        </div>
                        <div>
                            <div class="font-medium">${backup.id}</div>
                            <div class="text-sm text-gray-500">${backup.description}</div>
                            <div class="flex items-center space-x-3 mt-1">
                                <span class="text-xs px-2 py-1 rounded ${getTypeClass(backup.type)}">${getTypeDisplayName(backup.type)}</span>
                                <span class="status-${backup.status} text-xs font-medium">${getStatusDisplayName(backup.status)}</span>
                                <span class="text-xs text-gray-500">${formatFileSize(backup.file_size)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="text-right text-sm text-gray-600">
                            <div>${formatDateTime(backup.start_time)}</div>
                            ${backup.end_time ? `<div class="text-xs">${formatDuration(backup.start_time, backup.end_time)}</div>` : ''}
                        </div>
                        <div class="flex space-x-1">
                            ${backup.status === 'completed' ? `
                                <button class="text-blue-500 hover:text-blue-600 text-sm" onclick="restoreBackup('${backup.id}')" title="恢复">
                                    🔄
                                </button>
                            ` : ''}
                            <button class="text-green-500 hover:text-green-600 text-sm" onclick="downloadBackup('${backup.id}')" title="下载">
                                📥
                            </button>
                            <button class="text-red-500 hover:text-red-600 text-sm" onclick="deleteBackup('${backup.id}')" title="删除">
                                🗑️
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 加载恢复列表
        function loadRestoresList() {
            const restoresList = document.getElementById('restores-list');

            if (mockRestores.length === 0) {
                restoresList.innerHTML = '<div class="text-center text-gray-500 py-8">暂无恢复记录</div>';
                return;
            }

            restoresList.innerHTML = mockRestores.map(restore => `
                <div class="flex items-center justify-between p-4 border rounded-lg">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span class="text-lg">🔄</span>
                        </div>
                        <div>
                            <div class="font-medium">${restore.id}</div>
                            <div class="text-sm text-gray-500">从备份: ${restore.backup_id}</div>
                            <div class="flex items-center space-x-3 mt-1">
                                <span class="status-${restore.status} text-xs font-medium">${getStatusDisplayName(restore.status)}</span>
                                ${restore.restored_files.length > 0 ? `<span class="text-xs text-gray-500">${restore.restored_files.length} 个文件</span>` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-600">
                        <div>${formatDateTime(restore.start_time)}</div>
                        ${restore.end_time ? `<div class="text-xs">${formatDuration(restore.start_time, restore.end_time)}</div>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 更新统计信息
        function updateStatistics() {
            const totalBackups = mockBackups.length;
            const successfulBackups = mockBackups.filter(b => b.status === 'completed').length;
            const totalSize = mockBackups.reduce((sum, b) => sum + b.file_size, 0);
            const lastBackup = mockBackups.length > 0 ? mockBackups[0] : null;

            document.getElementById('total-backups').textContent = totalBackups;
            document.getElementById('successful-backups').textContent = successfulBackups;
            document.getElementById('total-backup-size').textContent = formatFileSize(totalSize);
            document.getElementById('last-backup-time').textContent = lastBackup ?
                formatDateTime(lastBackup.start_time).split(' ')[1] : '--';
        }

        // 初始化图表
        function initCharts() {
            // 备份趋势图表
            const trendCtx = document.getElementById('backup-trend-chart').getContext('2d');
            backupTrendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['1月1日', '1月2日', '1月3日', '1月4日', '1月5日', '1月6日', '1月7日'],
                    datasets: [{
                        label: '备份数量',
                        data: [2, 3, 1, 4, 2, 3, 2],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '备份大小 (MB)',
                        data: [150, 200, 80, 300, 120, 180, 160],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '备份数量'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '备份大小 (MB)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });

            // 备份类型分布图表
            const typeCtx = document.getElementById('backup-type-chart').getContext('2d');
            backupTypeChart = new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['完整备份', '增量备份', '差异备份'],
                    datasets: [{
                        data: [30, 60, 10],
                        backgroundColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 工具函数
        function getTypeDisplayName(type) {
            const typeNames = {
                'full': '完整备份',
                'incremental': '增量备份',
                'differential': '差异备份'
            };
            return typeNames[type] || type;
        }

        function getTypeIcon(type) {
            const typeIcons = {
                'full': '💾',
                'incremental': '📈',
                'differential': '📊'
            };
            return typeIcons[type] || '💾';
        }

        function getTypeClass(type) {
            return `type-${type}`;
        }

        function getStatusDisplayName(status) {
            const statusNames = {
                'pending': '等待中',
                'in_progress': '进行中',
                'completed': '已完成',
                'failed': '失败',
                'cancelled': '已取消'
            };
            return statusNames[status] || status;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDateTime(isoString) {
            const date = new Date(isoString);
            return date.toLocaleString('ja-JP');
        }

        function formatDuration(startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);
            const duration = Math.floor((end - start) / 1000); // 秒

            if (duration < 60) {
                return `${duration}秒`;
            } else if (duration < 3600) {
                return `${Math.floor(duration / 60)}分${duration % 60}秒`;
            } else {
                const hours = Math.floor(duration / 3600);
                const minutes = Math.floor((duration % 3600) / 60);
                return `${hours}小时${minutes}分`;
            }
        }

        function showNotification(title, message, type = 'info') {
            const notificationArea = document.getElementById('notification-area');

            const notification = document.createElement('div');
            notification.className = `max-w-sm bg-white border-l-4 p-4 shadow-lg rounded-lg`;

            switch(type) {
                case 'success':
                    notification.classList.add('border-green-500');
                    break;
                case 'error':
                    notification.classList.add('border-red-500');
                    break;
                case 'warning':
                    notification.classList.add('border-yellow-500');
                    break;
                default:
                    notification.classList.add('border-blue-500');
            }

            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800">${title}</h4>
                        <p class="text-sm text-gray-600">${message}</p>
                    </div>
                    <button class="ml-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                        ✕
                    </button>
                </div>
            `;

            notificationArea.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        function showModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.getElementById(modalId).classList.add('flex');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.getElementById(modalId).classList.remove('flex');
        }

        function downloadBackup(backupId) {
            showNotification('下载开始', `正在下载备份 ${backupId}`, 'info');
            // 这里可以实现实际的下载逻辑
        }

        function cleanupOldBackups() {
            if (confirm('确定要清理旧备份吗？这将删除超过保留数量限制的备份。')) {
                showNotification('清理完成', '已清理旧备份文件', 'success');
                // 这里可以实现实际的清理逻辑
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();

            // 加载数据
            loadBackupsList();
            loadRestoresList();
            updateStatistics();

            // 创建备份按钮
            document.getElementById('create-backup-btn').addEventListener('click', function() {
                showModal('create-backup-modal');
            });

            // 创建备份表单
            document.getElementById('create-backup-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const type = document.getElementById('backup-type-select').value;
                const description = document.getElementById('backup-description').value;

                const result = await createBackup(type, description);

                if (result.success) {
                    closeModal('create-backup-modal');
                    // 清空表单
                    document.getElementById('backup-description').value = '';
                }
            });

            // 取消创建备份
            document.getElementById('cancel-backup-btn').addEventListener('click', function() {
                closeModal('create-backup-modal');
            });

            // 刷新按钮
            document.getElementById('refresh-backups-btn').addEventListener('click', function() {
                loadBackupsList();
                updateStatistics();
                showNotification('刷新完成', '备份列表已更新', 'success');
            });

            document.getElementById('refresh-restores-btn').addEventListener('click', function() {
                loadRestoresList();
                showNotification('刷新完成', '恢复列表已更新', 'success');
            });

            // 清理按钮
            document.getElementById('cleanup-backups-btn').addEventListener('click', cleanupOldBackups);

            // 保存配置按钮
            document.getElementById('save-config-btn').addEventListener('click', function() {
                const autoBackup = document.getElementById('auto-backup-toggle').checked;
                const interval = document.getElementById('backup-interval').value;
                const maxBackups = document.getElementById('max-backups').value;
                const compression = document.getElementById('compression-toggle').checked;
                const defaultType = document.getElementById('default-backup-type').value;

                // 这里可以保存配置到后端
                showNotification('配置保存成功', '备份配置已更新', 'success');

                // 更新状态显示
                document.getElementById('auto-backup-status').textContent =
                    `🔄 自动备份: ${autoBackup ? '启用' : '禁用'}`;
            });

            // 定期更新进行中的备份状态
            setInterval(() => {
                const inProgressBackups = mockBackups.filter(b => b.status === 'in_progress');
                if (inProgressBackups.length > 0) {
                    loadBackupsList();
                }

                const inProgressRestores = mockRestores.filter(r => r.status === 'in_progress');
                if (inProgressRestores.length > 0) {
                    loadRestoresList();
                }
            }, 2000);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👑 使用量管理 - 管理者パネル</title>

    <!-- 单一认证检查 -->
    <script>
        // 全局标记，防止重复重定向
        window.adminAuthChecked = false;
        window.adminRedirecting = false;
    </script>

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- User Status Component -->
    <link rel="stylesheet" href="components/UserStatusComponent.css?v=3.9.7">
    <script src="components/UserStatusComponent.js?v=3.9.7"></script>
    <script src="/console_cleaner.js"></script>
    <style>
        .plan-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .plan-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .plan-free { border-color: #10b981; background: linear-gradient(135deg, #ecfdf5, #d1fae5); }
        .plan-basic { border-color: #3b82f6; background: linear-gradient(135deg, #eff6ff, #dbeafe); }
        .plan-pro { border-color: #8b5cf6; background: linear-gradient(135deg, #f3e8ff, #e9d5ff); }
        .plan-admin { border-color: #f59e0b; background: linear-gradient(135deg, #fffbeb, #fef3c7); }
        
        .usage-bar {
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            background: #e5e7eb;
        }
        .usage-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .usage-low { background: #10b981; }
        .usage-medium { background: #f59e0b; }
        .usage-high { background: #ef4444; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 统一认证检查 -->
    <script>
        (function() {
            // 防止重复执行
            if (window.adminAuthChecked || window.adminRedirecting) {
                return;
            }
            window.adminAuthChecked = true;

            let isAuthenticated = false;

            try {
                const session = localStorage.getItem('admin_session');
                if (session) {
                    const data = JSON.parse(session);
                    if (data.authenticated && data.expires > Date.now()) {
                        isAuthenticated = true;
                        console.log('✅ Admin session valid');
                    } else {
                        console.log('❌ Session expired');
                        localStorage.removeItem('admin_session');
                    }
                }
            } catch (error) {
                console.error('Session error:', error);
                localStorage.removeItem('admin_session');
            }

            if (!isAuthenticated) {
                window.adminRedirecting = true;
                console.log('🔄 Redirecting to login');

                // 立即重定向，不显示内容
                const returnUrl = encodeURIComponent(window.location.pathname);
                window.location.replace('/admin_login.html?return=' + returnUrl);
                return;
            }

            // 认证成功，继续加载页面
            console.log('✅ Admin access granted');
        })();
    </script>

    <!-- 管理员验证警告 -->
    <div id="auth-warning" class="bg-red-500 text-white p-4 text-center hidden">
        ⚠️ 管理员权限验证失败。请确保您有管理员权限。
    </div>

    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <div class="flex justify-between items-center mb-4">
                <div></div>
                <h1 class="text-4xl font-bold text-gray-800">
                    👑 使用量管理システム
                </h1>
                <div class="flex items-center space-x-4">
                    <span id="admin-username" class="text-sm text-gray-600"></span>
                    <button onclick="logout()"
                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                        🚪 ログアウト
                    </button>
                </div>
            </div>
            <p class="text-gray-600">
                ユーザープランと使用量の包括的な管理
            </p>
        </div>

        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-green-600" id="total-users">-</div>
                <div class="text-gray-600">総ユーザー数</div>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-blue-600" id="active-users">-</div>
                <div class="text-gray-600">アクティブユーザー</div>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-purple-600" id="total-queries">-</div>
                <div class="text-gray-600">本日の総質問数</div>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-orange-600" id="revenue">-</div>
                <div class="text-gray-600">月間収益</div>
            </div>
        </div>

        <!-- 计划管理 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-6">📋 プラン設定管理</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- フリープラン -->
                <div class="plan-card plan-free rounded-lg p-6">
                    <div class="text-center mb-4">
                        <h3 class="text-xl font-bold text-green-700">フリープラン</h3>
                        <div class="text-2xl font-bold text-green-600">¥0</div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>日次制限:</span>
                            <input type="number" id="free-limit" value="20" 
                                   class="w-16 px-2 py-1 border rounded text-center">
                        </div>
                        <div class="text-sm text-gray-600">
                            • 基本機能のみ<br>
                            • コミュニティサポート
                        </div>
                    </div>
                </div>

                <!-- ベーシックプラン -->
                <div class="plan-card plan-basic rounded-lg p-6">
                    <div class="text-center mb-4">
                        <h3 class="text-xl font-bold text-blue-700">ベーシックプラン</h3>
                        <div class="text-2xl font-bold text-blue-600">¥980</div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>日次制限:</span>
                            <input type="number" id="basic-limit" value="200" 
                                   class="w-16 px-2 py-1 border rounded text-center">
                        </div>
                        <div class="text-sm text-gray-600">
                            • 高度な機能<br>
                            • 優先サポート
                        </div>
                    </div>
                </div>

                <!-- プロプラン -->
                <div class="plan-card plan-pro rounded-lg p-6">
                    <div class="text-center mb-4">
                        <h3 class="text-xl font-bold text-purple-700">プロプラン</h3>
                        <div class="text-2xl font-bold text-purple-600">¥2,980</div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>日次制限:</span>
                            <input type="number" id="pro-limit" value="700" 
                                   class="w-16 px-2 py-1 border rounded text-center">
                        </div>
                        <div class="text-sm text-gray-600">
                            • 全機能アクセス<br>
                            • プレミアムサポート
                        </div>
                    </div>
                </div>

                <!-- 管理者プラン -->
                <div class="plan-card plan-admin rounded-lg p-6">
                    <div class="text-center mb-4">
                        <h3 class="text-xl font-bold text-orange-700">管理者</h3>
                        <div class="text-2xl font-bold text-orange-600">無制限</div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>日次制限:</span>
                            <span class="font-bold text-orange-600">∞</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            • 完全アクセス<br>
                            • 管理機能
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 text-center space-x-4">
                <button onclick="updatePlanLimits()"
                        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                    💾 設定を保存
                </button>
                <button onclick="resetPlanLimits()"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                    🔄 デフォルトに戻す
                </button>
            </div>
        </div>

        <!-- 用户使用量监控 -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold">📊 ユーザー使用量監視</h2>
                <button onclick="refreshUsageData()" 
                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                    🔄 更新
                </button>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left">ユーザー</th>
                            <th class="px-4 py-2 text-left">プラン</th>
                            <th class="px-4 py-2 text-left">本日の使用量</th>
                            <th class="px-4 py-2 text-left">使用率</th>
                            <th class="px-4 py-2 text-left">最終使用</th>
                            <th class="px-4 py-2 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody id="usage-table-body">
                        <tr>
                            <td colspan="6" class="text-center py-8 text-gray-500">
                                データを読み込み中...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-semibold mb-6">📝 操作ログ</h2>
            <div id="admin-logs" class="space-y-2 max-h-64 overflow-y-auto bg-gray-50 p-4 rounded">
                <div class="text-gray-500 text-center">ログを読み込み中...</div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <!-- 页面就绪检查 -->
    <script>
        // 确保页面完全加载后再执行
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.adminRedirecting) {
                console.log('🎉 Admin panel ready');
            }
        });
    </script>

    <script src="/admin_auth_guard.js"></script>
    <script src="/env-config.js"></script>
    <script src="/chatbot/UsageTracker.js"></script>
    <script>
        let usageTracker = null;
        let adminToken = null;
        let adminSession = null;

        // 显示管理员信息
        function displayAdminInfo() {
            try {
                const session = localStorage.getItem('admin_session');
                if (session) {
                    const data = JSON.parse(session);
                    const usernameElement = document.getElementById('admin-username');
                    if (usernameElement && data.username) {
                        usernameElement.textContent = `👤 ${data.username}`;
                    }
                }
            } catch (error) {
                console.error('Failed to display admin info:', error);
            }
        }

        // 登出功能
        function logout() {
            if (confirm('ログアウトしますか？')) {
                localStorage.removeItem('admin_session');
                localStorage.removeItem('admin_login_attempts');
                addLog('管理者がログアウトしました');
                window.location.href = '/admin_login.html';
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 如果正在重定向，不执行初始化
            if (window.adminRedirecting) {
                return;
            }

            // 简单验证（前面已经检查过了）
            const session = localStorage.getItem('admin_session');
            if (!session) {
                console.log('⚠️ No session in DOMContentLoaded');
                return;
            }

            await initializeAdminPanel();
        });

        async function initializeAdminPanel() {
            addLog('管理者パネルを初期化中...');

            try {
                // 显示管理员信息
                displayAdminInfo();

                // 恢复保存的计划设置
                loadSavedPlanSettings();

                // 初始化使用量跟踪器
                usageTracker = new UsageTracker();
                await usageTracker.init();

                // 加载数据
                await loadStatistics();
                await loadUsageData();

                addLog('管理者パネルの初期化が完了しました');

            } catch (error) {
                addLog(`初期化エラー: ${error.message}`, 'error');
                console.error('Admin panel initialization failed:', error);
            }
        }

        function showAuthWarning(message) {
            const warning = document.getElementById('auth-warning');
            warning.textContent = `⚠️ ${message}`;
            warning.classList.remove('hidden');
        }

        async function loadStatistics() {
            // 模拟统计数据加载
            document.getElementById('total-users').textContent = '1,234';
            document.getElementById('active-users').textContent = '567';
            document.getElementById('total-queries').textContent = '8,901';
            document.getElementById('revenue').textContent = '¥234,567';
        }

        async function loadUsageData() {
            const tbody = document.getElementById('usage-table-body');
            
            // 模拟用户数据
            const mockUsers = [
                { id: 'user1', plan: 'FREE', used: 15, limit: 20, lastUsed: '2 hours ago' },
                { id: 'user2', plan: 'BASIC', used: 150, limit: 200, lastUsed: '30 minutes ago' },
                { id: 'user3', plan: 'PRO', used: 450, limit: 700, lastUsed: '5 minutes ago' }
            ];

            tbody.innerHTML = mockUsers.map(user => {
                const usagePercent = (user.used / user.limit) * 100;
                const usageClass = usagePercent > 80 ? 'usage-high' : 
                                 usagePercent > 60 ? 'usage-medium' : 'usage-low';
                
                return `
                    <tr class="border-b hover:bg-gray-50">
                        <td class="px-4 py-3">${user.id}</td>
                        <td class="px-4 py-3">
                            <span class="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                ${user.plan}
                            </span>
                        </td>
                        <td class="px-4 py-3">${user.used}/${user.limit}</td>
                        <td class="px-4 py-3">
                            <div class="usage-bar">
                                <div class="usage-fill ${usageClass}" style="width: ${usagePercent}%"></div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">${usagePercent.toFixed(1)}%</div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-600">${user.lastUsed}</td>
                        <td class="px-4 py-3">
                            <button onclick="resetUserUsage('${user.id}')" 
                                    class="text-blue-600 hover:text-blue-800 text-sm">
                                リセット
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function updatePlanLimits() {
            const limits = {
                FREE: parseInt(document.getElementById('free-limit').value) || 20,
                BASIC: parseInt(document.getElementById('basic-limit').value) || 200,
                PRO: parseInt(document.getElementById('pro-limit').value) || 700
            };

            // 验证输入值
            if (limits.FREE < 0 || limits.BASIC < 0 || limits.PRO < 0) {
                alert('制限値は0以上の数値を入力してください。');
                return;
            }

            if (limits.FREE > limits.BASIC || limits.BASIC > limits.PRO) {
                alert('制限値は FREE ≤ BASIC ≤ PRO の順序で設定してください。');
                return;
            }

            try {
                // 保存到本地存储
                const planConfig = {
                    limits: limits,
                    lastUpdated: Date.now(),
                    updatedBy: adminSession?.username || 'admin'
                };

                localStorage.setItem('admin_plan_config', JSON.stringify(planConfig));

                // 同时更新UsageTracker的配置（如果存在）
                if (window.usageTracker) {
                    window.usageTracker.updatePlanLimits(limits);
                }

                addLog(`プラン制限を更新: FREE=${limits.FREE}, BASIC=${limits.BASIC}, PRO=${limits.PRO}`);
                addLog('設定がローカルストレージに保存されました', 'success');

                // 显示成功消息
                showSuccessMessage('プラン設定が正常に保存されました！');

            } catch (error) {
                addLog(`設定保存エラー: ${error.message}`, 'error');
                alert('設定の保存に失敗しました。再試行してください。');
            }
        }

        function refreshUsageData() {
            addLog('使用量データを更新中...');
            loadUsageData();
        }

        // 重置计划限制到默认值
        function resetPlanLimits() {
            if (confirm('プラン制限をデフォルト値にリセットしますか？\n\nFREE: 20回/日\nBASIC: 200回/日\nPRO: 700回/日')) {
                // 设置默认值
                document.getElementById('free-limit').value = 20;
                document.getElementById('basic-limit').value = 200;
                document.getElementById('pro-limit').value = 700;

                // 清除保存的设置
                localStorage.removeItem('admin_plan_config');
                localStorage.removeItem('usage_tracker_plan_limits');

                addLog('プラン制限をデフォルト値にリセットしました');
                showSuccessMessage('プラン制限がデフォルト値にリセットされました！');
            }
        }

        function resetUserUsage(userId) {
            if (confirm(`${userId}の使用量をリセットしますか？`)) {
                addLog(`${userId}の使用量をリセットしました`);
                loadUsageData();
            }
        }

        // 加载保存的计划设置
        function loadSavedPlanSettings() {
            try {
                const savedConfig = localStorage.getItem('admin_plan_config');
                if (savedConfig) {
                    const config = JSON.parse(savedConfig);

                    // 确保数据有效
                    if (config && config.limits) {
                        const limits = config.limits;

                        // 更新输入框
                        document.getElementById('free-limit').value = limits.FREE || 20;
                        document.getElementById('basic-limit').value = limits.BASIC || 200;
                        document.getElementById('pro-limit').value = limits.PRO || 700;

                        // 记录日志
                        const lastUpdated = new Date(config.lastUpdated).toLocaleString('ja-JP');
                        addLog(`保存された設定を読み込みました (${lastUpdated})`);

                        return true;
                    }
                }
            } catch (error) {
                console.error('Failed to load saved plan settings:', error);
                addLog('保存された設定の読み込みに失敗しました', 'error');
            }

            return false;
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center';
            messageDiv.innerHTML = `
                <span class="mr-2">✅</span>
                <span>${message}</span>
            `;

            // 添加到页面
            document.body.appendChild(messageDiv);

            // 淡出动画
            messageDiv.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';

            // 3秒后移除
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-20px)';

                setTimeout(() => {
                    document.body.removeChild(messageDiv);
                }, 500);
            }, 3000);
        }

        function addLog(message, type = 'info') {
            const logs = document.getElementById('admin-logs');
            const timestamp = new Date().toLocaleTimeString('ja-JP');
            const colors = {
                info: 'text-blue-600',
                error: 'text-red-600',
                success: 'text-green-600'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = `text-sm ${colors[type] || colors.info}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logs.insertBefore(logEntry, logs.firstChild);
            
            // 保持最多50条日志
            while (logs.children.length > 50) {
                logs.removeChild(logs.lastChild);
            }
        }

        // ユーザーステータスコンポーネントを初期化（管理者ページ用）
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof UserStatusComponent !== 'undefined' && UserStatusComponent.isLoggedIn()) {
                const currentUser = UserStatusComponent.getCurrentUser();
                if (currentUser && (currentUser.role === 'admin' || currentUser.isAdmin)) {
                    window.userStatus = new UserStatusComponent({
                        position: 'top-right', // 管理者ページでは右上に配置
                        showRole: true,
                        showLoginTime: true,
                        autoHide: false // 管理者ページでは常に表示
                    });
                    console.log('👤 管理者ユーザーステータスコンポーネント初期化完了');
                }
            }
        });
    </script>
</body>
</html>

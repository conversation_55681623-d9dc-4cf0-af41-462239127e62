<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>为Journal Entries添加用户字段</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-600">👤 为Journal Entries添加用户字段</h1>
        
        <!-- 当前问题说明 -->
        <div class="bg-red-100 border-l-4 border-red-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        <strong>问题:</strong> journal_entries表中没有用户ID和用户名字段，无法追踪记账人员
                    </p>
                </div>
            </div>
        </div>

        <!-- 解决方案 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 解决方案</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="step1()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    步骤1: 检查当前表结构
                </button>
                <button onclick="step2()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    步骤2: 添加用户字段
                </button>
                <button onclick="step3()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    步骤3: 关联现有数据
                </button>
                <button onclick="step4()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    步骤4: 验证修复结果
                </button>
            </div>
        </div>

        <!-- 一键修复 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">⚡ 一键修复</h2>
            <button onclick="fixAllUserFields()" class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                执行完整用户字段添加流程
            </button>
        </div>

        <!-- 数据预览 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 当前Journal Entries -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">📊 当前Journal Entries数据</h3>
                <div id="current-entries" class="text-sm">
                    <p class="text-gray-500">点击"检查当前表结构"查看数据...</p>
                </div>
            </div>

            <!-- Users表数据 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">👥 Users表数据</h3>
                <div id="users-data" class="text-sm">
                    <p class="text-gray-500">加载中...</p>
                </div>
            </div>
        </div>

        <!-- 操作结果 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 操作结果</h2>
            <div id="results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始操作...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        function addResult(title, success, message, data = null) {
            const container = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
                ${data ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto max-h-32 overflow-y-auto">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 步骤1: 检查当前表结构
        async function step1() {
            try {
                // 检查journal_entries表结构
                const response = await fetch(`${API_BASE_URL}/api/database/check-structure`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        table: 'journal_entries'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('检查表结构', true, '表结构检查完成', result);
                    
                    // 显示当前数据
                    displayCurrentEntries(result.sample_data);
                } else {
                    addResult('检查表结构', false, result.error || '检查失败', result);
                }
            } catch (error) {
                addResult('检查表结构', false, error.message);
            }
        }

        // 步骤2: 添加用户字段
        async function step2() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/add-user-fields`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        table: 'journal_entries',
                        fields: ['user_id', 'user_name', 'created_by']
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('添加用户字段', true, '用户字段添加成功', result);
                } else {
                    addResult('添加用户字段', false, result.error || '添加失败', result);
                }
            } catch (error) {
                addResult('添加用户字段', false, error.message);
            }
        }

        // 步骤3: 关联现有数据
        async function step3() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/link-user-data`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'link_existing_entries'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('关联现有数据', true, '数据关联完成', result);
                } else {
                    addResult('关联现有数据', false, result.error || '关联失败', result);
                }
            } catch (error) {
                addResult('关联现有数据', false, error.message);
            }
        }

        // 步骤4: 验证修复结果
        async function step4() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/verify-user-fields`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('验证修复结果', true, '验证完成，用户字段正常工作', result);
                } else {
                    addResult('验证修复结果', false, result.error || '验证失败', result);
                }
            } catch (error) {
                addResult('验证修复结果', false, error.message);
            }
        }

        // 一键修复所有用户字段问题
        async function fixAllUserFields() {
            addResult('开始修复', true, '开始执行完整用户字段添加流程...');
            
            // 按顺序执行所有步骤
            await step1();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step2();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step3();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step4();
            
            addResult('修复完成', true, '所有用户字段添加步骤已完成');
            
            // 提示用户下一步操作
            setTimeout(() => {
                if (confirm('用户字段添加完成！是否立即测试journal_entries页面？')) {
                    window.open('/journal_entries', '_blank');
                }
            }, 2000);
        }

        // 显示当前entries数据
        function displayCurrentEntries(data) {
            const container = document.getElementById('current-entries');
            if (data && data.length > 0) {
                let html = '<div class="space-y-2">';
                data.slice(0, 5).forEach(entry => {
                    html += `
                        <div class="p-2 bg-gray-50 rounded text-xs">
                            <div><strong>ID:</strong> ${entry.id}</div>
                            <div><strong>描述:</strong> ${entry.description}</div>
                            <div><strong>金额:</strong> ¥${entry.amount}</div>
                            <div><strong>日期:</strong> ${entry.entry_date}</div>
                            <div class="text-red-600"><strong>用户ID:</strong> ${entry.user_id || '❌ 缺失'}</div>
                            <div class="text-red-600"><strong>用户名:</strong> ${entry.user_name || '❌ 缺失'}</div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<p class="text-gray-500">暂无数据</p>';
            }
        }

        // 加载用户数据
        async function loadUsersData() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/users/list`);
                const result = await response.json();
                
                const container = document.getElementById('users-data');
                if (result.success && result.data) {
                    let html = '<div class="space-y-2">';
                    result.data.slice(0, 10).forEach(user => {
                        html += `
                            <div class="p-2 bg-blue-50 rounded text-xs">
                                <div><strong>ID:</strong> ${user.id}</div>
                                <div><strong>用户名:</strong> ${user.username || user.name}</div>
                                <div><strong>邮箱:</strong> ${user.email}</div>
                                <div><strong>创建时间:</strong> ${user.created_at}</div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<p class="text-gray-500">无法加载用户数据</p>';
                }
            } catch (error) {
                document.getElementById('users-data').innerHTML = '<p class="text-red-500">加载失败: ' + error.message + '</p>';
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            addResult('系统检查', false, 'journal_entries表缺少用户字段，需要添加user_id和user_name');
            loadUsersData();
        });
    </script>
</body>
</html>

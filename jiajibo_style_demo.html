<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 Jiajibo Style Chatbot Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
            overflow-x: hidden;
        }

        .hero-section {
            text-align: center;
            padding: 80px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
            padding: 0 20px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #FFD700;
        }

        .feature-description {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 60px 20px;
            margin: 60px 0;
        }

        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .demo-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: #FF69B4;
        }

        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
        }

        .demo-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.5);
        }

        .demo-btn:active {
            transform: translateY(-1px);
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-family: monospace;
            z-index: 10000;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }

        .instructions h3 {
            color: #FFD700;
            margin-bottom: 15px;
        }

        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin: 10px 0;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }

            .demo-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Status Indicator -->
    <div class="status-indicator" id="status-indicator">
        🌸 Status: <span id="status-text" style="color: #FFD700;">Loading...</span>
    </div>

    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="hero-title">🌸 花咲家計簿</h1>
        <p class="hero-subtitle">
            美しいMaterial-UIデザインのAIチャットボット<br>
            jiajibo風の洗練されたインターフェース
        </p>

        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🎨</span>
                <h3 class="feature-title">Material-UI Design</h3>
                <p class="feature-description">
                    完全にjiajibo風のMaterial-UIスタイルを再現。
                    美しいグラデーション、リップル効果、アニメーションを搭載。
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🤖</span>
                <h3 class="feature-title">Gemini AI Integration</h3>
                <p class="feature-description">
                    Google Gemini 2.0 Flash搭載で自然な日本語対話。
                    家計管理の専門知識を持つ「さくらちゃん」がサポート。
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">💬</span>
                <h3 class="feature-title">Rich Chat Interface</h3>
                <p class="feature-description">
                    ウェルカムメッセージ、クイックリプライ、
                    タイピングインジケーター等の豊富な機能。
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📱</span>
                <h3 class="feature-title">Responsive Design</h3>
                <p class="feature-description">
                    デスクトップからモバイルまで完璧に対応。
                    どのデバイスでも美しく動作します。
                </p>
            </div>
        </div>
    </div>

    <!-- Demo Section -->
    <div class="demo-section">
        <div class="demo-container">
            <h2 class="demo-title">🚀 インタラクティブデモ</h2>
            
            <div class="instructions">
                <h3>📋 使用方法</h3>
                <ol>
                    <li>ページ読み込み後、2秒で右下にFABボタンが表示されます</li>
                    <li>5秒後にウェルカムメッセージが表示されます</li>
                    <li>FABボタンをクリックしてチャットを開始</li>
                    <li>「さくらちゃん」とAI会話をお楽しみください</li>
                </ol>
            </div>

            <div class="demo-buttons">
                <button class="demo-btn" onclick="testWelcomeMessage()">
                    💬 ウェルカムメッセージ表示
                </button>
                <button class="demo-btn" onclick="testBadgeNotification()">
                    🔔 通知バッジテスト
                </button>
                <button class="demo-btn" onclick="testChatOpen()">
                    🌸 チャット強制オープン
                </button>
                <button class="demo-btn" onclick="testAIResponse()">
                    🤖 AI応答テスト
                </button>
            </div>
        </div>
    </div>

    <!-- Load Chatbot Scripts -->
    <script src="chatbot/GeminiAPI.js"></script>
    <script src="chatbot/ChatFab.js"></script>
    <script src="chatbot/ChatInterface.js"></script>
    <script src="chatbot/chatbot-init.js"></script>

    <script>
        function updateStatus(message, color = '#FFD700') {
            const statusText = document.getElementById('status-text');
            if (statusText) {
                statusText.textContent = message;
                statusText.style.color = color;
            }
            console.log(`🌸 Status: ${message}`);
        }

        function testWelcomeMessage() {
            if (window.chatFab) {
                window.chatFab.displayWelcomeMessage();
                updateStatus('Welcome Message Displayed', '#00FF00');
            } else {
                updateStatus('ChatFab Not Available', '#FF4444');
            }
        }

        function testBadgeNotification() {
            if (window.chatFab) {
                window.chatFab.showBadge(3);
                updateStatus('Badge Set to 3', '#00FF00');
                
                setTimeout(() => {
                    window.chatFab.clearBadge();
                    updateStatus('Badge Cleared', '#FFD700');
                }, 4000);
            } else {
                updateStatus('ChatFab Not Available', '#FF4444');
            }
        }

        function testChatOpen() {
            if (window.chatInterface) {
                window.chatInterface.open();
                updateStatus('Chat Opened', '#00FF00');
            } else {
                updateStatus('ChatInterface Not Available', '#FF4444');
            }
        }

        async function testAIResponse() {
            if (window.chatbotManager) {
                await window.chatbotManager.sendMessage('こんにちは！テストメッセージです。', 'general');
                updateStatus('AI Test Message Sent', '#00FF00');
            } else {
                updateStatus('ChatbotManager Not Available', '#FF4444');
            }
        }

        // Initialize status updates
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('Page Loaded', '#00FF00');
            
            setTimeout(() => {
                updateStatus('Initializing Chatbot...', '#FFD700');
            }, 1000);
            
            setTimeout(() => {
                if (window.chatbotManager) {
                    updateStatus('Chatbot Ready! 🌸', '#FF69B4');
                } else {
                    updateStatus('Chatbot Failed to Load', '#FF4444');
                }
            }, 3000);
        });

        // Listen for chatbot events
        document.addEventListener('chatbot:open', () => {
            updateStatus('Chat Opened 💬', '#FF69B4');
        });

        document.addEventListener('chatbot:close', () => {
            updateStatus('Chat Closed', '#FFD700');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <title>📱 手机预览测试 - goldenledger会計</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        
        .test-section h3::before {
            content: "🔧";
            margin-right: 10px;
            font-size: 20px;
        }
        
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .test-button:hover, .test-button:active {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
        }
        
        .test-button.preview {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .test-button.preview:hover {
            box-shadow: 0 8px 20px rgba(255, 152, 0, 0.3);
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .status.success {
            background: #E8F5E8;
            color: #2E7D32;
            border-left: 4px solid #4CAF50;
        }
        
        .status.error {
            background: #FFEBEE;
            color: #C62828;
            border-left: 4px solid #F44336;
        }
        
        .status.info {
            background: #E3F2FD;
            color: #1565C0;
            border-left: 4px solid #2196F3;
        }
        
        .network-info {
            background: #F5F5F5;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .network-info h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .network-info code {
            background: #E0E0E0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .test-button {
                padding: 12px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 手机预览测试</h1>
            <p>goldenledger会計系统移动端测试</p>
        </div>
        
        <div class="content">
            <div class="network-info">
                <h4>🌐 网络信息</h4>
                <p><strong>服务器IP:</strong> <code>************</code></p>
                <p><strong>端口:</strong> <code>8000</code></p>
                <p><strong>状态:</strong> <span id="server-status">检测中...</span></p>
            </div>
            
            <div class="test-section">
                <h3>基础连接测试</h3>
                <button class="test-button" onclick="testHealth()">🔍 健康检查</button>
                <button class="test-button" onclick="testAPI()">📡 API连接测试</button>
            </div>
            
            <div class="test-section">
                <h3>页面功能测试</h3>
                <a href="http://************:8000/journal_entries.html" class="test-button" target="_blank">
                    📊 记账页面
                </a>
                <a href="http://************:8000/master_dashboard.html" class="test-button" target="_blank">
                    📈 仪表板
                </a>
                <a href="http://************:8000/interactive_demo.html" class="test-button" target="_blank">
                    🤖 AI演示
                </a>
            </div>
            
            <div class="test-section">
                <h3>附件预览测试</h3>
                <a href="http://************:8000/attachments/J20250711211125?preview=true" 
                   class="test-button preview" target="_blank">
                    🖼️ 直接图片预览
                </a>
                <button class="test-button preview" onclick="testAttachmentPreview()">
                    📎 测试预览功能
                </button>
            </div>
            
            <div id="test-results"></div>
        </div>
        
        <div class="footer">
            <p>goldenledger会計 © 2025</p>
            <p>移动端测试版本</p>
        </div>
    </div>

    <script>
        const baseURL = 'http://************:8000';
        const resultsDiv = document.getElementById('test-results');
        
        // 页面加载时自动检测服务器状态
        window.onload = function() {
            testHealth();
        };
        
        function showResult(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            resultsDiv.appendChild(statusDiv);
            
            // 自动滚动到结果
            statusDiv.scrollIntoView({ behavior: 'smooth' });
            
            // 5秒后自动移除旧结果
            setTimeout(() => {
                if (resultsDiv.children.length > 3) {
                    resultsDiv.removeChild(resultsDiv.firstChild);
                }
            }, 5000);
        }
        
        async function testHealth() {
            showResult('🔍 正在检测服务器健康状态...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('server-status').innerHTML = 
                        '<span style="color: #4CAF50;">✅ 在线</span>';
                    showResult(`✅ 服务器健康检查成功！<br>状态: ${data.status}<br>时间: ${new Date(data.timestamp).toLocaleString()}`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('server-status').innerHTML = 
                    '<span style="color: #F44336;">❌ 离线</span>';
                showResult(`❌ 服务器连接失败: ${error.message}<br>请检查网络连接和服务器状态`, 'error');
            }
        }
        
        async function testAPI() {
            showResult('📡 正在测试API连接...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/journal-entries/default`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ API连接成功！<br>获取到 ${data.length} 条记录`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                showResult(`❌ API连接失败: ${error.message}`, 'error');
            }
        }
        
        async function testAttachmentPreview() {
            showResult('📎 正在测试附件预览功能...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/attachments/J20250711211125?json_preview=true`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult(`✅ 附件预览功能正常！<br>文件: ${data.filename}<br>类型: ${data.content_type}<br>大小: ${Math.round(data.size/1024)}KB`, 'success');
                    
                    // 自动打开预览
                    setTimeout(() => {
                        window.open(`${baseURL}/attachments/J20250711211125?preview=true`, '_blank');
                    }, 1000);
                } else {
                    throw new Error(data.error || '未知错误');
                }
            } catch (error) {
                showResult(`❌ 附件预览测试失败: ${error.message}`, 'error');
            }
        }
        
        // 添加触摸反馈
        document.querySelectorAll('.test-button').forEach(button => {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            button.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>

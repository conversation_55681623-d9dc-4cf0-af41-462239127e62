<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 批量数据导入工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="/background_control.js"></script>
    <script src="/music_control.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .drop-zone { 
            border: 2px dashed #d1d5db; 
            transition: all 0.3s ease;
        }
        .drop-zone.dragover { 
            border-color: #3b82f6; 
            background-color: #eff6ff; 
        }
        .processing { animation: pulse 2s infinite; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold">📥 批量数据导入工具</h1>
            <p class="text-lg opacity-90">CSV、Excel、自然语言批量导入</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Import Methods -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">📄</span>
                    </div>
                    <h3 class="font-semibold mb-2">CSV/Excel导入</h3>
                    <p class="text-gray-600 text-sm mb-4">标准格式的财务数据文件</p>
                    <button id="csv-import-btn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 w-full">
                        选择文件
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">💬</span>
                    </div>
                    <h3 class="font-semibold mb-2">自然语言批量</h3>
                    <p class="text-gray-600 text-sm mb-4">多行自然语言描述</p>
                    <button id="text-import-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                        文本导入
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🤖</span>
                    </div>
                    <h3 class="font-semibold mb-2">AI智能识别</h3>
                    <p class="text-gray-600 text-sm mb-4">图片OCR和智能解析</p>
                    <button id="ai-import-btn" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 w-full">
                        AI识别
                    </button>
                </div>
            </div>
        </section>

        <!-- Import Area -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <h3 class="text-lg font-semibold mb-4">📥 导入区域</h3>
            
            <!-- File Drop Zone -->
            <div id="drop-zone" class="drop-zone rounded-lg p-8 text-center mb-6">
                <div class="text-6xl mb-4">📁</div>
                <h4 class="text-lg font-semibold mb-2">ファイルをドラッグ&ドロップ</h4>
                <p class="text-gray-600 mb-4">または下のボタンでファイルを選択</p>
                <input type="file" id="file-input" class="hidden" accept=".csv,.xlsx,.xls,.txt,.jpg,.png,.pdf">
                <button id="select-file-btn" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    ファイルを選択
                </button>
            </div>

            <!-- Text Input Area -->
            <div id="text-input-area" class="hidden">
                <h4 class="text-lg font-semibold mb-4">📝 自然语言批量输入</h4>
                <textarea 
                    id="bulk-text-input" 
                    class="w-full h-64 border rounded-lg p-4 font-mono text-sm"
                    placeholder="例:
今日コンビニで事務用品を1200円で購入
ABC会社から売上50万円を銀行振込で受取
今月の家賃15万円を支払い
電気代8500円を口座振替で支払い
従業員給与80万円を支払い

一行一个交易，AI会自动解析每一行..."
                ></textarea>
                <div class="flex justify-between items-center mt-4">
                    <div class="text-sm text-gray-600">
                        <span id="line-count">0</span> 行入力済み
                    </div>
                    <button id="process-text-btn" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                        🤖 AI処理開始
                    </button>
                </div>
            </div>
        </section>

        <!-- Processing Status -->
        <section id="processing-section" class="hidden bg-white rounded-xl p-6 shadow-sm border mb-8">
            <h3 class="text-lg font-semibold mb-4">⚡ 処理状況</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span>処理進捗</span>
                    <div class="flex items-center space-x-2">
                        <div class="w-32 bg-gray-200 rounded-full h-2">
                            <div id="progress-bar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <span id="progress-text">0%</span>
                    </div>
                </div>
                <div id="processing-log" class="bg-gray-50 rounded-lg p-4 h-32 overflow-y-auto font-mono text-sm">
                </div>
            </div>
        </section>

        <!-- Results -->
        <section id="results-section" class="hidden bg-white rounded-xl p-6 shadow-sm border">
            <h3 class="text-lg font-semibold mb-4">📊 処理結果</h3>
            
            <!-- Summary -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-600" id="success-count">0</div>
                    <div class="text-sm text-green-700">成功</div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-red-600" id="error-count">0</div>
                    <div class="text-sm text-red-700">エラー</div>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="warning-count">0</div>
                    <div class="text-sm text-yellow-700">警告</div>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600" id="total-amount">¥0</div>
                    <div class="text-sm text-blue-700">総金額</div>
                </div>
            </div>

            <!-- Detailed Results -->
            <div id="detailed-results" class="space-y-3">
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-3 mt-6 pt-6 border-t">
                <button id="export-results-btn" class="border border-gray-300 px-4 py-2 rounded hover:bg-gray-50">
                    📤 結果をエクスポート
                </button>
                <button id="save-all-btn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    💾 すべて保存
                </button>
            </div>
        </section>
    </main>

    <script>
        // 全局变量
        let processedResults = [];
        let currentProcessing = false;

        // 添加处理日志
        function addProcessingLog(message) {
            const timestamp = new Date().toLocaleTimeString('ja-JP');
            const logContainer = document.getElementById('processing-log');
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新进度
        function updateProgress(current, total) {
            const percentage = Math.round((current / total) * 100);
            document.getElementById('progress-bar').style.width = `${percentage}%`;
            document.getElementById('progress-text').textContent = `${percentage}%`;
        }

        // 处理自然语言文本
        async function processNaturalLanguageText(text) {
            const lines = text.split('\n').filter(line => line.trim());
            const results = [];
            
            document.getElementById('processing-section').classList.remove('hidden');
            addProcessingLog(`${lines.length}行のテキストを処理開始`);
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                try {
                    addProcessingLog(`処理中: ${line.substring(0, 30)}...`);
                    
                    const response = await fetch('http://localhost:8000/ai-bookkeeping/natural-language', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            text: line,
                            company_id: 'default'
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        results.push({
                            status: 'success',
                            input: line,
                            output: result.journal_entry,
                            confidence: result.confidence,
                            warnings: result.warnings || [],
                            suggestions: result.suggestions || []
                        });
                        addProcessingLog(`✅ 成功: ${result.journal_entry.debit_account} → ${result.journal_entry.credit_account}`);
                    } else {
                        results.push({
                            status: 'error',
                            input: line,
                            error: result.error
                        });
                        addProcessingLog(`❌ エラー: ${result.error}`);
                    }
                    
                } catch (error) {
                    results.push({
                        status: 'error',
                        input: line,
                        error: error.message
                    });
                    addProcessingLog(`❌ 通信エラー: ${error.message}`);
                }
                
                updateProgress(i + 1, lines.length);
                
                // 添加小延迟避免过快请求
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            addProcessingLog('✅ 全処理完了');
            displayResults(results);
        }

        // 显示结果
        function displayResults(results) {
            processedResults = results;
            
            const successCount = results.filter(r => r.status === 'success').length;
            const errorCount = results.filter(r => r.status === 'error').length;
            const warningCount = results.filter(r => r.status === 'success' && r.warnings.length > 0).length;
            const totalAmount = results
                .filter(r => r.status === 'success')
                .reduce((sum, r) => sum + (r.output?.amount || 0), 0);
            
            document.getElementById('success-count').textContent = successCount;
            document.getElementById('error-count').textContent = errorCount;
            document.getElementById('warning-count').textContent = warningCount;
            document.getElementById('total-amount').textContent = `¥${totalAmount.toLocaleString()}`;
            
            const detailedResults = document.getElementById('detailed-results');
            detailedResults.innerHTML = results.map((result, index) => {
                if (result.status === 'success') {
                    return `
                        <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="text-sm text-gray-600 mb-2">${result.input}</div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <span class="text-xs text-gray-500">借方</span>
                                            <div class="font-medium text-blue-900">${result.output.debit_account}</div>
                                        </div>
                                        <div>
                                            <span class="text-xs text-gray-500">貸方</span>
                                            <div class="font-medium text-green-900">${result.output.credit_account}</div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-lg font-semibold">¥${result.output.amount.toLocaleString()}</div>
                                    <div class="text-xs text-gray-500">信頼度: ${(result.confidence * 100).toFixed(1)}%</div>
                                </div>
                                <div class="text-green-500">✅</div>
                            </div>
                            ${result.warnings.length > 0 ? `
                                <div class="mt-2 text-xs text-yellow-600">
                                    ⚠️ ${result.warnings.join(', ')}
                                </div>
                            ` : ''}
                        </div>
                    `;
                } else {
                    return `
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="text-sm text-gray-600 mb-2">${result.input}</div>
                                    <div class="text-red-600 text-sm">${result.error}</div>
                                </div>
                                <div class="text-red-500">❌</div>
                            </div>
                        </div>
                    `;
                }
            }).join('');
            
            document.getElementById('results-section').classList.remove('hidden');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.getElementById('drop-zone');
            const fileInput = document.getElementById('file-input');
            const textInputArea = document.getElementById('text-input-area');
            const bulkTextInput = document.getElementById('bulk-text-input');
            
            // 文件拖拽
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });
            
            // 文件选择
            document.getElementById('select-file-btn').addEventListener('click', function() {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFile(e.target.files[0]);
                }
            });
            
            // 文本导入按钮
            document.getElementById('text-import-btn').addEventListener('click', function() {
                textInputArea.classList.remove('hidden');
                dropZone.classList.add('hidden');
            });
            
            // 行数统计
            bulkTextInput.addEventListener('input', function() {
                const lines = this.value.split('\n').filter(line => line.trim()).length;
                document.getElementById('line-count').textContent = lines;
            });
            
            // 处理文本按钮
            document.getElementById('process-text-btn').addEventListener('click', function() {
                const text = bulkTextInput.value.trim();
                if (text) {
                    processNaturalLanguageText(text);
                }
            });
            
            // 保存所有按钮
            document.getElementById('save-all-btn').addEventListener('click', async function() {
                await saveAllResults();
            });
        });

        // 保存所有成功的结果到数据库
        async function saveAllResults() {
            const successResults = processedResults.filter(r => r.status === 'success');

            if (successResults.length === 0) {
                alert('保存できる仕訳がありません。');
                return;
            }

            const saveBtn = document.getElementById('save-all-btn');
            const originalText = saveBtn.textContent;
            saveBtn.disabled = true;
            saveBtn.textContent = '保存中...';

            let savedCount = 0;
            let errorCount = 0;

            addProcessingLog(`${successResults.length}件の仕訳を保存開始`);

            for (let i = 0; i < successResults.length; i++) {
                const result = successResults[i];

                try {
                    // 准备保存数据
                    const saveData = {
                        company_id: 'default',
                        ...result.output,
                        ai_generated: true,
                        ai_confidence: result.confidence,
                        confirmed: true
                    };

                    // 调用保存接口
                    const response = await fetch('/journal-entries/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(saveData)
                    });

                    if (response.ok) {
                        const saveResult = await response.json();
                        savedCount++;
                        addProcessingLog(`✅ 保存成功: ${result.output.description}`);

                        // 更新结果状态
                        result.saved = true;
                        result.entry_id = saveResult.entry_id;
                    } else {
                        errorCount++;
                        addProcessingLog(`❌ 保存失败: ${result.output.description}`);
                    }

                } catch (error) {
                    errorCount++;
                    addProcessingLog(`❌ 保存错误: ${error.message}`);
                }

                // 添加小延迟避免过快请求
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // 恢复按钮状态
            saveBtn.disabled = false;
            saveBtn.textContent = originalText;

            // 显示保存结果
            const message = `保存完了！\n成功: ${savedCount}件\n失敗: ${errorCount}件`;
            alert(message);
            addProcessingLog(`📊 保存完了: 成功${savedCount}件、失敗${errorCount}件`);

            // 更新显示
            updateSaveStatus();
        }

        // 更新保存状态显示
        function updateSaveStatus() {
            const savedCount = processedResults.filter(r => r.saved).length;
            const totalSuccess = processedResults.filter(r => r.status === 'success').length;

            if (savedCount > 0) {
                const statusDiv = document.createElement('div');
                statusDiv.className = 'bg-green-100 border border-green-300 rounded p-3 mt-4';
                statusDiv.innerHTML = `
                    <div class="flex items-center">
                        <span class="text-green-600 mr-2">💾</span>
                        <span class="text-green-800">
                            ${savedCount}/${totalSuccess}件の仕訳がデータベースに保存されました
                        </span>
                    </div>
                    <div class="mt-2">
                        <a href="/journal_entries.html" class="text-blue-600 hover:text-blue-800 underline">
                            📊 保存された記録を確認する
                        </a>
                    </div>
                `;

                // 添加到结果区域
                const resultsSection = document.querySelector('#processing-section');
                const existingStatus = resultsSection.querySelector('.bg-green-100');
                if (existingStatus) {
                    existingStatus.replaceWith(statusDiv);
                } else {
                    resultsSection.appendChild(statusDiv);
                }
            }
        }

        // 处理文件
        function handleFile(file) {
            console.log('File selected:', file.name);
            // 这里可以添加文件处理逻辑
            alert(`ファイル "${file.name}" を選択しました。\n実装予定: CSV/Excel解析機能`);
        }
    </script>
</body>
</html>

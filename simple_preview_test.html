<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化附件预览测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.75);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }
        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow: auto;
            text-align: center;
        }
        .close-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            text-align: left;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6 text-center">简化附件预览测试</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试控制</h2>
            <div class="flex flex-wrap justify-center">
                <button class="test-btn" onclick="testBasicFetch()">1. 基本Fetch测试</button>
                <button class="test-btn" onclick="testAPICall()">2. API调用测试</button>
                <button class="test-btn" onclick="testPreviewFunction()">3. 预览功能测试</button>
                <button class="test-btn" onclick="clearLog()">清除日志</button>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">测试日志</h2>
            <div id="log" class="log">点击上方按钮开始测试...</div>
        </div>
    </div>

    <script>
        const TEST_ENTRY_ID = 'J20250711165206';
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`${prefix} ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            log('日志已清除');
        }
        
        async function testBasicFetch() {
            log('开始基本Fetch测试...');
            
            try {
                log('测试1: 健康检查API');
                const healthResponse = await fetch('/health');
                log(`健康检查状态: ${healthResponse.status}`, healthResponse.ok ? 'success' : 'error');
                
                const healthData = await healthResponse.json();
                log(`健康检查响应: ${JSON.stringify(healthData)}`);
                
                log('测试2: 记录列表API');
                const entriesResponse = await fetch('/journal-entries/default');
                log(`记录列表状态: ${entriesResponse.status}`, entriesResponse.ok ? 'success' : 'error');
                
                const entries = await entriesResponse.json();
                log(`记录数量: ${entries.length}`);
                
                const testEntry = entries.find(e => e.id === TEST_ENTRY_ID);
                log(`测试记录存在: ${testEntry ? '是' : '否'}`, testEntry ? 'success' : 'error');
                
                if (testEntry) {
                    log(`测试记录附件: ${testEntry.attachment_path || '无'}`);
                }
                
                log('基本Fetch测试完成', 'success');
                
            } catch (error) {
                log(`基本Fetch测试失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        }
        
        async function testAPICall() {
            log('开始API调用测试...');
            
            try {
                log('测试1: 附件列表API');
                const listResponse = await fetch(`/attachments/${TEST_ENTRY_ID}`);
                log(`列表API状态: ${listResponse.status}`, listResponse.ok ? 'success' : 'error');
                
                if (listResponse.ok) {
                    const listData = await listResponse.json();
                    log(`列表API响应: ${JSON.stringify(listData, null, 2)}`);
                }
                
                log('测试2: JSON预览API');
                const previewResponse = await fetch(`/attachments/${TEST_ENTRY_ID}?json_preview=true`);
                log(`预览API状态: ${previewResponse.status}`, previewResponse.ok ? 'success' : 'error');
                log(`预览API Content-Type: ${previewResponse.headers.get('content-type')}`);
                
                if (previewResponse.ok) {
                    const previewData = await previewResponse.json();
                    log(`预览API成功: ${previewData.success}`, previewData.success ? 'success' : 'error');
                    log(`文件名: ${previewData.filename}`);
                    log(`内容类型: ${previewData.content_type}`);
                    log(`文件大小: ${previewData.size} bytes`);
                    log(`Base64长度: ${previewData.file_content ? previewData.file_content.length : 0} 字符`);
                    
                    // 保存数据供预览测试使用
                    window.testPreviewData = previewData;
                    log('预览数据已保存到 window.testPreviewData', 'success');
                } else {
                    const errorText = await previewResponse.text();
                    log(`预览API错误: ${errorText}`, 'error');
                }
                
                log('API调用测试完成', 'success');
                
            } catch (error) {
                log(`API调用测试失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        }
        
        async function testPreviewFunction() {
            log('开始预览功能测试...');
            
            try {
                // 如果没有预览数据，先获取
                if (!window.testPreviewData) {
                    log('没有预览数据，先调用API...');
                    await testAPICall();
                }
                
                if (!window.testPreviewData) {
                    throw new Error('无法获取预览数据');
                }
                
                const result = window.testPreviewData;
                log('使用已保存的预览数据创建模态框...');
                
                // 创建预览模态框
                createPreviewModal(result);
                log('预览模态框创建成功！', 'success');
                
            } catch (error) {
                log(`预览功能测试失败: ${error.message}`, 'error');
                log(`错误详情: ${error.stack}`, 'error');
            }
        }
        
        function createPreviewModal(result) {
            // 移除现有的预览模态框
            const existingModal = document.getElementById('preview-modal');
            if (existingModal) {
                existingModal.remove();
                log('移除了现有的预览模态框');
            }
            
            // 创建预览模态框
            const modal = document.createElement('div');
            modal.id = 'preview-modal';
            modal.className = 'modal';
            
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 style="margin: 0; font-size: 18px; color: #333;">🖼️ 附件预览 - ${result.filename}</h3>
                        <button class="close-btn" onclick="closePreviewModal()">关闭</button>
                    </div>
                    <div class="modal-body">
                        <div style="margin-bottom: 15px; font-size: 14px; color: #666;">
                            <strong>文件信息:</strong><br>
                            类型: ${result.content_type}<br>
                            大小: ${result.size} bytes<br>
                            Base64长度: ${result.file_content.length} 字符
                        </div>
                        <img src="data:${result.content_type};base64,${result.file_content}" 
                             style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px;" 
                             alt="附件预览"
                             onload="console.log('✅ 图片加载成功'); window.logFunction && window.logFunction('图片加载成功', 'success')"
                             onerror="console.error('❌ 图片加载失败'); window.logFunction && window.logFunction('图片加载失败', 'error')">
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            log('预览模态框已添加到页面');
            
            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePreviewModal();
                }
            });
            
            // 设置全局日志函数供图片事件使用
            window.logFunction = log;
            
            log('预览模态框事件监听器已设置');
        }
        
        function closePreviewModal() {
            const modal = document.getElementById('preview-modal');
            if (modal) {
                modal.remove();
                log('预览模态框已关闭', 'success');
            } else {
                log('未找到预览模态框', 'error');
            }
        }
        
        // 页面加载完成后自动运行基本测试
        window.addEventListener('load', function() {
            log('页面加载完成，准备开始测试');
            setTimeout(() => {
                log('自动运行基本Fetch测试...');
                testBasicFetch();
            }, 1000);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            log(`页面错误: ${e.error.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            log(`未处理的Promise拒绝: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>

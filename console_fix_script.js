/**
 * 在生产环境控制台中运行的403错误修复脚本
 * 使用方法：
 * 1. 在 https://ledger.goldenorangetech.com/interactive_demo 页面打开开发者工具
 * 2. 切换到 Console 标签
 * 3. 复制粘贴这个脚本并按回车执行
 */

(async function fix403Error() {
    console.log('🔧 开始修复403错误...');
    
    // 获取token
    const token = localStorage.getItem('goldenledger_session_token');
    if (!token) {
        console.error('❌ 未找到session token，请先登录');
        return;
    }
    
    console.log('✅ Token找到，长度:', token.length);
    
    const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
    
    try {
        // 步骤1: 检查当前AI使用情况
        console.log('📊 检查AI使用情况...');
        const statsResponse = await fetch(`${API_BASE_URL}/api/ai/reset-usage?action=stats`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        const statsData = await statsResponse.json();
        if (statsData.success) {
            console.log('📈 当前AI使用统计:');
            console.log('  - 当月使用:', statsData.data.monthly_usage, '次');
            console.log('  - 总使用:', statsData.data.total_usage, '次');
            console.log('  - 当前月份:', statsData.data.current_month);
            
            if (statsData.data.monthly_usage >= 10) {
                console.log('⚠️ 当月使用已超过原限制(10次)，需要重置');
                
                // 步骤2: 重置AI使用记录
                console.log('🔄 重置AI使用记录...');
                const resetResponse = await fetch(`${API_BASE_URL}/api/ai/reset-usage?action=reset`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const resetData = await resetResponse.json();
                if (resetData.success) {
                    console.log('✅ 重置成功!');
                    console.log('  - 删除记录数:', resetData.deleted_records);
                    console.log('  - 重置月份:', resetData.month);
                } else {
                    console.error('❌ 重置失败:', resetData.error);
                    return;
                }
            } else {
                console.log('✅ 使用次数正常，无需重置');
            }
        } else {
            console.error('❌ 检查AI使用情况失败:', statsData.error);
        }
        
        // 步骤3: 测试AI处理
        console.log('🧪 测试AI处理...');
        const testResponse = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                text: '今天买了办公用品100元',
                company_id: 'default'
            })
        });
        
        const testData = await testResponse.json();
        if (testResponse.status === 200 && testData.success) {
            console.log('🎉 AI处理成功! 403错误已修复!');
            console.log('📝 处理结果:');
            console.log('  - 借方科目:', testData.data.debit_account);
            console.log('  - 贷方科目:', testData.data.credit_account);
            console.log('  - 金额:', testData.data.amount);
            console.log('  - 描述:', testData.data.description);
            console.log('');
            console.log('✅ 现在可以正常使用AI记账功能了!');
        } else {
            console.error('❌ AI处理仍然失败:');
            console.error('  - 状态码:', testResponse.status);
            console.error('  - 错误:', testData.error || '未知错误');
            
            if (testResponse.status === 403) {
                console.log('💡 如果仍然是403错误，可能需要等待几分钟让数据库更新生效');
            }
        }
        
    } catch (error) {
        console.error('❌ 修复过程中发生错误:', error.message);
    }
})();

// 使用说明
console.log(`
🔧 403错误修复脚本使用说明:
1. 确保您已经登录 https://ledger.goldenorangetech.com
2. 在页面上按 F12 打开开发者工具
3. 切换到 Console 标签
4. 复制粘贴上面的脚本并按回车执行
5. 等待脚本自动检查和修复403错误

如果修复成功，您就可以正常使用AI记账功能了！
`);

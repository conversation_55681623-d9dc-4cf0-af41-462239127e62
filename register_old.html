<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新規登録 - GoldenLedger | Smart AI-Powered Finance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .register-card { 
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-field {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }
        .input-field:focus {
            border-color: #6c5ce7;
            box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(108, 92, 231, 0.3);
        }
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-indicator.active {
            background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            color: white;
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="floating-element absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full"></div>
        <div class="floating-element absolute top-40 right-32 w-24 h-24 bg-white/5 rounded-full" style="animation-delay: -2s;"></div>
        <div class="floating-element absolute bottom-32 left-1/4 w-40 h-40 bg-white/5 rounded-full" style="animation-delay: -4s;"></div>
    </div>

    <!-- Register Container -->
    <div class="w-full max-w-lg relative z-10">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-2xl mb-4">
                <span class="text-4xl">🚀</span>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">GoldenLedger</h1>
            <p class="text-white/80 text-lg">Smart AI-Powered Finance System</p>
            <p class="text-white/70 text-sm mt-2">企業級AI記帳システムへようこそ</p>
        </div>

        <!-- Progress Steps -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4">
                <div class="step-indicator active flex items-center justify-center w-10 h-10 rounded-full bg-white/20 text-white font-semibold">
                    1
                </div>
                <div class="w-12 h-1 bg-white/20 rounded"></div>
                <div class="step-indicator flex items-center justify-center w-10 h-10 rounded-full bg-white/20 text-white font-semibold">
                    2
                </div>
                <div class="w-12 h-1 bg-white/20 rounded"></div>
                <div class="step-indicator flex items-center justify-center w-10 h-10 rounded-full bg-white/20 text-white font-semibold">
                    3
                </div>
            </div>
        </div>

        <!-- Register Form -->
        <div class="register-card rounded-2xl p-8 shadow-2xl">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">新規アカウント作成</h2>
                <p class="text-gray-600 mt-2">数分でAI記帳システムを開始できます</p>
            </div>

            <form id="registerForm" class="space-y-6">
                <!-- Step 1: Basic Info -->
                <div id="step1" class="step-content">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                                👤 名前
                            </label>
                            <input 
                                type="text" 
                                id="firstName" 
                                name="firstName" 
                                required
                                class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                placeholder="太郎"
                            >
                        </div>
                        <div>
                            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                                👤 姓
                            </label>
                            <input 
                                type="text" 
                                id="lastName" 
                                name="lastName" 
                                required
                                class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                placeholder="田中"
                            >
                        </div>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            📧 メールアドレス
                        </label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            required
                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                            placeholder="<EMAIL>"
                        >
                    </div>

                    <div>
                        <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                            🏢 会社名
                        </label>
                        <input 
                            type="text" 
                            id="company" 
                            name="company" 
                            required
                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                            placeholder="株式会社サンプル"
                        >
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            🔒 パスワード
                        </label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required
                            minlength="8"
                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                            placeholder="8文字以上"
                        >
                        <div class="mt-2 text-xs text-gray-500">
                            パスワードは8文字以上で、数字と文字を含めてください
                        </div>
                    </div>

                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            🔒 パスワード確認
                        </label>
                        <input 
                            type="password" 
                            id="confirmPassword" 
                            name="confirmPassword" 
                            required
                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                            placeholder="パスワードを再入力"
                        >
                    </div>
                </div>

                <!-- Terms and Privacy -->
                <div class="space-y-4">
                    <label class="flex items-start space-x-3">
                        <input type="checkbox" id="terms" required class="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                        <span class="text-sm text-gray-600">
                            <a href="#" class="text-purple-600 hover:text-purple-800">利用規約</a>
                            および
                            <a href="#" class="text-purple-600 hover:text-purple-800">プライバシーポリシー</a>
                            に同意します
                        </span>
                    </label>
                    
                    <label class="flex items-start space-x-3">
                        <input type="checkbox" id="newsletter" class="mt-1 rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                        <span class="text-sm text-gray-600">
                            製品アップデートやニュースレターを受け取る（任意）
                        </span>
                    </label>
                </div>

                <button 
                    type="submit" 
                    class="btn-primary w-full py-3 px-4 rounded-xl text-white font-semibold text-lg"
                >
                    🚀 アカウントを作成
                </button>
            </form>

            <!-- Divider -->
            <div class="my-6 flex items-center">
                <div class="flex-1 border-t border-gray-200"></div>
                <span class="px-4 text-sm text-gray-500">または</span>
                <div class="flex-1 border-t border-gray-200"></div>
            </div>

            <!-- Social Register -->
            <div class="space-y-3">
                <button class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors">
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Googleで登録
                </button>
            </div>

            <!-- Login Link -->
            <div class="mt-8 text-center">
                <p class="text-gray-600">
                    すでにアカウントをお持ちの方は
                    <a href="login.html" class="text-purple-600 hover:text-purple-800 font-semibold transition-colors">
                        ログイン
                    </a>
                </p>
            </div>
        </div>

        <!-- Features Preview -->
        <div class="mt-8">
            <div class="grid grid-cols-3 gap-4 text-white/80 text-center">
                <div>
                    <div class="text-2xl mb-2">🤖</div>
                    <div class="text-xs">AI自動記帳</div>
                </div>
                <div>
                    <div class="text-2xl mb-2">📊</div>
                    <div class="text-xs">リアルタイム分析</div>
                </div>
                <div>
                    <div class="text-2xl mb-2">🔒</div>
                    <div class="text-xs">企業級セキュリティ</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Form validation and submission
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);

            // Validate passwords match
            if (data.password !== data.confirmPassword) {
                alert('パスワードが一致しません。');
                return;
            }

            // Validate terms acceptance
            if (!document.getElementById('terms').checked) {
                alert('利用規約に同意してください。');
                return;
            }

            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '🔄 アカウント作成中...';
            submitBtn.disabled = true;

            try {
                // Call registration API
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: data.email,
                        email: data.email,
                        password: data.password,
                        first_name: data.firstName,
                        last_name: data.lastName,
                        company: data.company,
                        role: 'user',
                        ip_address: '127.0.0.1',
                        user_agent: navigator.userAgent
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // Store user data
                    const userData = {
                        email: data.email,
                        firstName: data.firstName,
                        lastName: data.lastName,
                        company: data.company,
                        registrationTime: new Date().toISOString(),
                        sessionId: result.session_id || 'demo_' + Date.now(),
                        accessToken: result.access_token,
                        refreshToken: result.refresh_token
                    };

                    window.fireAuth.login(userData);

                    // Show success message
                    alert('🎉 アカウントが正常に作成されました！\nダッシュボードにリダイレクトします。');

                    // Redirect to dashboard
                    window.location.href = 'master_dashboard.html';
                } else {
                    // Handle registration error
                    alert(result.message || 'アカウント作成に失敗しました。入力内容を確認してください。');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            } catch (error) {
                console.error('Registration error:', error);

                // Fallback to demo mode for development
                console.log('API接続失敗、デモモードで続行');
                const userData = {
                    email: data.email,
                    firstName: data.firstName,
                    lastName: data.lastName,
                    company: data.company,
                    registrationTime: new Date().toISOString(),
                    sessionId: 'demo_' + Date.now()
                };

                window.fireAuth.login(userData);

                alert('🎉 アカウントが正常に作成されました！\nダッシュボードにリダイレクトします。');
                window.location.href = 'master_dashboard.html';
            }
        });

        // Password strength indicator
        document.getElementById('password').addEventListener('input', function(e) {
            const password = e.target.value;
            const strength = calculatePasswordStrength(password);
            // Could add visual password strength indicator here
        });

        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }

        // 认证检查已禁用 - 开发测试模式
        console.log('Register page loaded - authentication check disabled for development');
    </script>
</body>
</html>

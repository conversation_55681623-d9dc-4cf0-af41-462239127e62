#!/usr/bin/env python3
"""
调试附件问题
"""
import sys
import os
sys.path.append('backend')

from database import DatabaseManager
import sqlite3

def debug_attachment_issue():
    """调试附件问题"""
    
    print("🔍 调试附件问题")
    print("=" * 30)
    
    # 1. 检查数据库表结构
    print("\n📊 检查数据库表结构:")
    try:
        conn = sqlite3.connect('backend/goldenledger_accounting.db')
        cursor = conn.cursor()

        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='journal_entries';")
        table_exists = cursor.fetchone()

        if table_exists:
            print("✅ journal_entries表存在")

            # 检查表结构
            cursor.execute("PRAGMA table_info(journal_entries);")
            columns = cursor.fetchall()
            print("表结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")

            # 检查是否有attachment_path字段
            attachment_col = next((col for col in columns if col[1] == 'attachment_path'), None)
            if attachment_col:
                print("✅ attachment_path字段存在")
            else:
                print("❌ attachment_path字段不存在")

            # 查询最近的记录
            cursor.execute('''
                SELECT id, description, attachment_path, entry_date, created_at
                FROM journal_entries
                WHERE id LIKE 'J20250711%'
                ORDER BY created_at DESC
                LIMIT 5
            ''')

            records = cursor.fetchall()

            print(f"\n找到 {len(records)} 条记录:")
            for record in records:
                entry_id, description, attachment_path, entry_date, created_at = record
                print(f"  ID: {entry_id}")
                print(f"  描述: {description}")
                print(f"  附件路径: {attachment_path}")
                print(f"  日期: {entry_date}")
                print("-" * 20)
        else:
            print("❌ journal_entries表不存在")

        conn.close()

    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 使用DatabaseManager查询
    print("\n📋 使用DatabaseManager查询:")
    try:
        db = DatabaseManager("backend/goldenledger_accounting.db")
        print(f"DatabaseManager数据库路径: {db.db_path}")

        entries = db.get_journal_entries('default')

        print(f"总记录数: {len(entries)}")

        # 查找最近的记录
        recent_entries = [e for e in entries if e.get('id', '').startswith('J20250711')]
        print(f"今日记录数: {len(recent_entries)}")

        for entry in recent_entries[-3:]:  # 最近3条
            print(f"  ID: {entry.get('id')}")
            print(f"  描述: {entry.get('description')}")
            print(f"  附件: {entry.get('attachment_path')}")
            print("-" * 15)

    except Exception as e:
        print(f"❌ DatabaseManager查询失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 检查附件文件
    print("\n📁 检查附件文件:")
    try:
        import os
        if os.path.exists('attachments'):
            files = os.listdir('attachments')
            print(f"附件目录中的文件: {files}")
            
            for file in files:
                if 'J20250711' in file:
                    file_path = os.path.join('attachments', file)
                    file_size = os.path.getsize(file_path)
                    print(f"  {file}: {file_size} bytes")
        else:
            print("附件目录不存在")
    except Exception as e:
        print(f"❌ 检查附件文件失败: {e}")

if __name__ == "__main__":
    debug_attachment_issue()

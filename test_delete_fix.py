#!/usr/bin/env python3
"""
测试删除功能修复
"""
import requests
import json

def test_delete_fix():
    """测试删除功能修复"""
    
    print("🔧 测试删除功能修复")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 创建测试记录
        print("\n📝 步骤1: 创建测试记录")
        test_record = {
            "id": "DELETE_FIX_TEST",
            "company_id": "default",
            "entry_date": "2025-07-11",
            "entry_time": "23:59:59",
            "description": "【删除修复测试】临时记录",
            "debit_account": "测试科目",
            "credit_account": "现金",
            "amount": 888.88,
            "reference_number": "DELETE_FIX",
            "ai_generated": False
        }
        
        create_response = requests.post(f"{base_url}/journal-entries/save", json=test_record)
        if create_response.status_code == 200:
            print("✅ 测试记录创建成功")
        else:
            print(f"❌ 测试记录创建失败: {create_response.status_code}")
            return False
        
        # 2. 验证记录存在
        print("\n🔍 步骤2: 验证记录存在")
        verify_response = requests.get(f"{base_url}/journal-entries/default")
        if verify_response.status_code == 200:
            entries = verify_response.json()
            test_entry = next((e for e in entries if e['id'] == 'DELETE_FIX_TEST'), None)
            if test_entry:
                print("✅ 测试记录存在")
                print(f"   ID: {test_entry['id']}")
                print(f"   描述: {test_entry['description']}")
                print(f"   金额: ¥{test_entry['amount']}")
            else:
                print("❌ 测试记录不存在")
                return False
        else:
            print(f"❌ 获取记录失败: {verify_response.status_code}")
            return False
        
        # 3. 测试删除功能
        print("\n🗑️ 步骤3: 测试删除功能")
        delete_response = requests.delete(f"{base_url}/journal-entries/DELETE_FIX_TEST")
        
        if delete_response.status_code == 200:
            delete_result = delete_response.json()
            print(f"✅ 删除成功: {delete_result.get('message')}")
            print(f"   删除的记录ID: {delete_result.get('entry_id')}")
        else:
            print(f"❌ 删除失败: {delete_response.status_code}")
            print(f"   错误信息: {delete_response.text}")
            return False
        
        # 4. 验证删除结果
        print("\n✅ 步骤4: 验证删除结果")
        final_verify_response = requests.get(f"{base_url}/journal-entries/default")
        if final_verify_response.status_code == 200:
            final_entries = final_verify_response.json()
            deleted_entry = next((e for e in final_entries if e['id'] == 'DELETE_FIX_TEST'), None)
            
            if deleted_entry is None:
                print("✅ 删除验证成功: 记录已被删除")
                return True
            else:
                print("❌ 删除验证失败: 记录仍然存在")
                return False
        else:
            print(f"❌ 验证删除结果失败: {final_verify_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_delete_fix()
    if success:
        print("\n🎉 删除功能修复成功！")
    else:
        print("\n❌ 删除功能仍有问题")

#!/usr/bin/env python3
"""
测试AI记账API响应格式
"""
import requests
import json
import time

def test_api_response():
    """测试API响应"""
    url = "http://localhost:8000/ai-bookkeeping/natural-language"
    
    test_data = {
        "text": "今日コンビニで事務用品を1200円で購入",
        "company_id": "default"
    }
    
    print("🧪 测试AI记账API...")
    print(f"📤 请求: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        start_time = time.time()
        response = requests.post(url, json=test_data, timeout=30)
        end_time = time.time()
        
        print(f"⏱️ 响应时间: {(end_time - start_time):.2f}秒")
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📥 响应数据:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
            # 检查关键字段
            print("\n🔍 字段检查:")
            print(f"  ✓ success: {data.get('success')}")
            print(f"  ✓ journal_entry: {data.get('journal_entry') is not None}")
            print(f"  ✓ confidence: {data.get('confidence')}")
            print(f"  ✓ error: {data.get('error')}")
            
            if data.get('success') and data.get('journal_entry'):
                entry = data['journal_entry']
                print(f"\n📋 仕訳内容:")
                print(f"  借方: {entry.get('debit_account')} ¥{entry.get('amount', 0):,}")
                print(f"  貸方: {entry.get('credit_account')} ¥{entry.get('amount', 0):,}")
                print(f"  摘要: {entry.get('description')}")
                print(f"  日期: {entry.get('date')}")
                
        else:
            print(f"❌ API错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_api_response()

/**
 * GoldenLedger - 重新设计的音乐控制系统
 * 独立按钮设计，初始状态完全关闭
 */

class MusicController {
    constructor() {
        this.audio = null;
        this.isPlaying = false;
        this.volume = 0.3;
        this.storageKey = 'golden_ledger_music_settings';
        this.settingsExpiry = 30 * 24 * 60 * 60 * 1000; // 30天
        this.musicUrl = '/music/love.mp3';
        
        this.init();
    }

    init() {
        this.createAudioElement();
        this.loadUserSettings();

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeComponents();
            });
        } else {
            this.initializeComponents();
        }
    }

    initializeComponents() {
        console.log('🎵 初始化音乐控制器...');
        console.log('🎵 document.readyState:', document.readyState);
        console.log('🎵 document.body存在:', !!document.body);

        this.createMusicControl();
        this.setupEventListeners();

        // 延迟加载按钮位置，确保DOM已完全渲染
        setTimeout(() => {
            this.loadButtonPosition();

            // 验证音乐控制器是否成功创建
            const musicController = document.getElementById('music-controller');
            const musicBtn = document.getElementById('music-toggle');
            console.log('🎵 验证创建结果:');
            console.log('  - 音乐控制器元素:', !!musicController);
            console.log('  - 音乐按钮元素:', !!musicBtn);
            if (musicController) {
                console.log('  - 控制器位置:', musicController.style.left, musicController.style.top);
                console.log('  - 控制器可见性:', musicController.style.display, musicController.style.visibility);
            }
        }, 500);

        console.log('✅ 音乐控制器初始化完成');
    }

    createAudioElement() {
        console.log('🎵 创建音频元素...');
        try {
            this.audio = new Audio(this.musicUrl);
            this.audio.loop = true;
            this.audio.volume = this.volume;
            this.audio.preload = 'auto';

            // 添加事件监听器
            this.audio.addEventListener('loadstart', () => {
                console.log('🎵 开始加载音频');
            });

            this.audio.addEventListener('canplay', () => {
                console.log('✅ 音频可以播放');
            });

            this.audio.addEventListener('error', (e) => {
                console.error('❌ 音频加载错误:', e);
            });

            console.log('✅ 音频元素创建成功');
        } catch (error) {
            console.error('❌ 创建音频元素失败:', error);
        }
    }

    createMusicControl() {
        if (document.getElementById('music-controller')) {
            return;
        }

        const controller = document.createElement('div');
        controller.id = 'music-controller';
        controller.innerHTML = `
            <!-- 独立音乐按钮 -->
            <button id="music-toggle" class="music-btn" title="背景音乐">
                🎵
            </button>
            
            <!-- 音量控制面板（完全隐藏） -->
            <div id="music-volume-panel" class="music-volume-panel">
                <div class="volume-arrow"></div>
                <div class="volume-content">
                    <div class="volume-header">音量控制</div>
                    <input type="range" id="music-volume" min="0" max="100" value="${this.volume * 100}" class="music-volume-slider">
                    <span class="volume-label">${Math.round(this.volume * 100)}%</span>
                </div>
            </div>
        `;

        // 添加新的样式
        const style = document.createElement('style');
        style.id = 'music-control-styles';
        style.textContent = `
            /* 音乐控制器容器 - 可拖动 */
            #music-controller {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 99999;
                transition: none; /* 禁用过渡，避免干扰拖动 */
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                #music-controller {
                    top: 60px;
                    right: 10px;
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                }

                .music-btn {
                    width: 45px;
                    height: 45px;
                    font-size: 18px;
                }

                .volume-panel {
                    right: 0;
                    top: 55px;
                    width: 140px;
                }
            }
            
            /* 独立音乐按钮 - 可拖拽 */
            .music-btn {
                width: 50px;
                height: 50px;
                border: none;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                position: relative;
                border: 3px solid rgba(255, 255, 255, 0.2);
                user-select: none;
            }

            .music-btn:hover:not(.dragging) {
                transform: scale(1.05);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
                background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            }

            .music-btn.dragging {
                cursor: grabbing !important;
                transform: scale(1.1) !important;
                box-shadow: 0 8px 30px rgba(102, 126, 234, 0.8);
                z-index: 100000;
                transition: none !important;
            }

            .music-btn:active:not(.dragging) {
                transform: scale(0.98);
            }
            
            .music-btn.playing {
                animation: musicPulse 2s ease-in-out infinite;
                background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            }
            
            .music-btn.paused {
                background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
            }
            
            @keyframes musicPulse {
                0%, 100% { 
                    transform: scale(1);
                    box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
                }
                50% { 
                    transform: scale(1.1);
                    box-shadow: 0 8px 30px rgba(17, 153, 142, 0.8);
                }
            }
            
            /* 音量控制面板 */
            .music-volume-panel {
                position: absolute;
                top: 60px;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                padding: 20px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                border: 2px solid rgba(102, 126, 234, 0.3);
                min-width: 180px;
                z-index: 100000;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px) scale(0.95);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            
            .music-volume-panel.show {
                opacity: 1;
                visibility: visible;
                transform: translateY(0) scale(1);
            }
            
            /* 指向音乐按钮的箭头 */
            .volume-arrow {
                position: absolute;
                top: -10px;
                right: 20px;
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-bottom: 10px solid rgba(102, 126, 234, 0.3);
            }
            
            .volume-arrow::after {
                content: '';
                position: absolute;
                top: 2px;
                left: -8px;
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-bottom: 8px solid rgba(255, 255, 255, 0.98);
            }
            
            .volume-header {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin-bottom: 15px;
                text-align: center;
            }
            
            .volume-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 12px;
            }
            
            .music-volume-slider {
                width: 120px;
                height: 6px;
                border-radius: 3px;
                background: linear-gradient(to right, #e0e0e0 0%, #e0e0e0 100%);
                outline: none;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .music-volume-slider:hover {
                background: linear-gradient(to right, #d0d0d0 0%, #d0d0d0 100%);
            }
            
            .music-volume-slider::-webkit-slider-thumb {
                appearance: none;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                cursor: pointer;
                box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4);
                transition: all 0.3s ease;
            }
            
            .music-volume-slider::-webkit-slider-thumb:hover {
                transform: scale(1.2);
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.6);
            }
            
            .music-volume-slider::-moz-range-thumb {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                cursor: pointer;
                border: none;
                box-shadow: 0 3px 10px rgba(102, 126, 234, 0.4);
            }
            
            .volume-label {
                font-size: 14px;
                font-weight: 600;
                color: #667eea;
                min-width: 40px;
                text-align: center;
                background: rgba(102, 126, 234, 0.1);
                padding: 5px 10px;
                border-radius: 15px;
            }
        `;

        // 移除旧样式
        const oldStyle = document.getElementById('music-control-styles');
        if (oldStyle) {
            oldStyle.remove();
        }

        // 确保DOM已加载
        if (document.head) {
            document.head.appendChild(style);
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                document.head.appendChild(style);
            });
        }

        // 强制等待 DOM 准备好
        const appendController = () => {
            if (document.body) {
                document.body.appendChild(controller);

                // 强制显示，确保移动端可见
                controller.style.display = 'block';
                controller.style.visibility = 'visible';
                controller.style.opacity = '1';
                controller.style.zIndex = '99999';

                // 添加拖动功能
                this.makeDraggable(controller);
                console.log('🎵 音乐控制器已添加到页面，强制显示');

                // 移动端额外检查
                if (window.innerWidth <= 768) {
                    setTimeout(() => {
                        const musicBtn = document.getElementById('music-toggle');
                        if (musicBtn) {
                            console.log('🎵 移动端音乐按钮确认可见');
                        } else {
                            console.log('❌ 移动端音乐按钮未找到');
                        }
                    }, 500);
                }
            } else {
                console.log('🎵 等待 DOM 准备...');
                setTimeout(appendController, 100);
            }
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', appendController);
        } else {
            appendController();
        }
    }

    setupEventListeners() {
        setTimeout(() => {
            const controller = document.getElementById('music-controller');
            const toggleBtn = document.getElementById('music-toggle');
            const volumePanel = document.getElementById('music-volume-panel');
            const volumeSlider = document.getElementById('music-volume');

            if (toggleBtn && volumePanel && controller) {
                let isPanelVisible = false;
                let isDragging = false;
                let dragStartTime = 0;

                // 拖拽功能 - 使用新的拖动系统
                // this.setupDragFunctionality(controller, toggleBtn); // 禁用旧系统

                // 音乐按钮点击事件（避免与拖动冲突）
                let clickStartTime = 0;
                let clickStartPos = { x: 0, y: 0 };

                // 保存 this 引用
                const musicController = this;

                toggleBtn.addEventListener('mousedown', (e) => {
                    clickStartTime = Date.now();
                    clickStartPos = { x: e.clientX, y: e.clientY };
                });

                toggleBtn.addEventListener('touchstart', (e) => {
                    clickStartTime = Date.now();
                    clickStartPos = { x: e.touches[0].clientX, y: e.touches[0].clientY };
                });

                toggleBtn.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // 移动端简化处理：直接播放，不检查拖动
                    const isMobile = window.innerWidth <= 768;

                    if (isMobile) {
                        console.log('🎵 移动端点击，直接播放');
                        // 移动端直接播放，不做复杂检测
                        try {
                            musicController.toggleMusic();
                            console.log('🎵 移动端 toggleMusic 调用成功');
                        } catch (error) {
                            console.error('🎵 移动端 toggleMusic 调用失败:', error);
                        }

                        // 显示音量面板
                        if (!isPanelVisible) {
                            volumePanel.classList.add('show');
                            isPanelVisible = true;
                            console.log('🎵 移动端显示音量面板');
                        }
                        return;
                    }

                    // 桌面端保持原有检测逻辑
                    const musicControllerElement = document.getElementById('music-controller');
                    if (musicControllerElement && musicControllerElement._isDragging) {
                        console.log('🎵 拖动状态中，忽略点击事件');
                        return;
                    }

                    const timeDiff = Date.now() - clickStartTime;
                    let currentPos;
                    if (e.type === 'click' && e.clientX !== undefined) {
                        currentPos = { x: e.clientX, y: e.clientY };
                    } else {
                        currentPos = { x: clickStartPos.x, y: clickStartPos.y };
                    }

                    const distance = Math.sqrt(
                        Math.pow(currentPos.x - clickStartPos.x, 2) +
                        Math.pow(currentPos.y - clickStartPos.y, 2)
                    );

                    const maxDistance = 5;
                    const maxTime = 300;
                    const shouldTriggerClick = (distance < maxDistance && timeDiff < maxTime);

                    console.log('🎵 点击检测详情:',
                        'distance=' + distance,
                        'timeDiff=' + timeDiff,
                        'isMobile=' + isMobile,
                        'maxDistance=' + maxDistance,
                        'maxTime=' + maxTime,
                        'shouldTriggerClick=' + shouldTriggerClick
                    );

                    if (shouldTriggerClick) {
                        console.log('🎵 音乐按钮被点击 - 开始播放切换');

                        // 切换音乐播放状态 - 使用保存的引用
                        try {
                            musicController.toggleMusic();
                            console.log('🎵 toggleMusic 调用成功');
                        } catch (error) {
                            console.error('🎵 toggleMusic 调用失败:', error);
                        }

                        // 显示音量面板
                        if (!isPanelVisible) {
                            volumePanel.classList.add('show');
                            isPanelVisible = true;
                            console.log('🎵 显示音量面板');
                        }
                    } else {
                        const reason = isMobile ?
                            (timeDiff >= maxTime ? '时间超限' : '距离超限') :
                            '距离或时间超限';
                        console.log('🎵 点击被忽略:',
                            'reason=' + reason,
                            'distance=' + distance,
                            'timeDiff=' + timeDiff,
                            'maxDistance=' + maxDistance,
                            'maxTime=' + maxTime
                        );
                    }
                });

                // 音量控制
                if (volumeSlider) {
                    volumeSlider.addEventListener('input', (e) => {
                        this.setVolume(e.target.value / 100);
                    });
                }

                // 点击外部关闭面板
                document.addEventListener('click', (e) => {
                    if (isPanelVisible &&
                        !volumePanel.contains(e.target) &&
                        !toggleBtn.contains(e.target)) {
                        volumePanel.classList.remove('show');
                        isPanelVisible = false;
                    }
                });

                // 阻止面板内部点击事件冒泡
                volumePanel.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }
        }, 100);
    }

    setupDragFunctionality(controller, button) {
        let isDragging = false;
        let hasMoved = false;
        let startX, startY, startLeft, startTop;
        let dragStartTime = 0;

        const startDrag = (e) => {
            // 只有长按才开始拖拽
            dragStartTime = Date.now();
            hasMoved = false;

            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);

            startX = clientX;
            startY = clientY;

            const rect = controller.getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;

            // 延迟启动拖拽，避免影响点击
            setTimeout(() => {
                if (!hasMoved && Date.now() - dragStartTime > 300) {
                    isDragging = true;
                    button.classList.add('dragging');
                    console.log('🎵 开始拖拽模式');
                }
            }, 300);

            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
            document.addEventListener('touchmove', drag, { passive: false });
            document.addEventListener('touchend', stopDrag);
        };

        const drag = (e) => {
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);

            const deltaX = clientX - startX;
            const deltaY = clientY - startY;

            // 检测是否有移动
            if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
                hasMoved = true;

                if (!isDragging && Date.now() - dragStartTime > 100) {
                    isDragging = true;
                    button.classList.add('dragging');
                    console.log('🎵 检测到移动，开始拖拽');
                }
            }

            if (!isDragging) return;

            let newLeft = startLeft + deltaX;
            let newTop = startTop + deltaY;

            // 边界检查
            const maxLeft = window.innerWidth - 50;
            const maxTop = window.innerHeight - 50;

            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));

            controller.style.left = newLeft + 'px';
            controller.style.top = newTop + 'px';
            controller.style.right = 'auto';

            e.preventDefault();
        };

        const stopDrag = (e) => {
            const wasDragging = isDragging;

            if (isDragging) {
                isDragging = false;
                button.classList.remove('dragging');

                // 保存位置
                this.saveButtonPosition();
                console.log('🎵 拖拽结束，保存位置');

                // 如果是拖拽操作，阻止点击事件
                if (wasDragging) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }

            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('touchend', stopDrag);
        };

        // 鼠标事件
        button.addEventListener('mousedown', startDrag);

        // 触摸事件
        button.addEventListener('touchstart', startDrag, { passive: false });
    }

    saveButtonPosition() {
        const controller = document.getElementById('music-controller');
        if (controller) {
            const rect = controller.getBoundingClientRect();
            const position = {
                left: rect.left,
                top: rect.top,
                timestamp: Date.now()
            };
            localStorage.setItem('music_button_position', JSON.stringify(position));
        }
    }

    loadButtonPosition() {
        try {
            const saved = localStorage.getItem('music_button_position');
            if (saved) {
                const position = JSON.parse(saved);
                const controller = document.getElementById('music-controller');
                if (controller && position.left !== undefined && position.top !== undefined) {
                    controller.style.left = position.left + 'px';
                    controller.style.top = position.top + 'px';
                    controller.style.right = 'auto';
                }
            }
        } catch (error) {
            console.error('加载按钮位置失败:', error);
        }
    }

    toggleMusic() {
        console.log('🎵 切换音乐状态，当前播放状态:', this.isPlaying);
        if (this.isPlaying) {
            this.pauseMusic();
        } else {
            this.playMusic();
        }
    }

    playMusic() {
        console.log('🎵 尝试播放音乐...');
        if (this.audio) {
            this.audio.play().then(() => {
                this.isPlaying = true;
                this.updateButtonState();
                this.saveUserSettings();
                console.log('✅ 音乐播放成功');
            }).catch((error) => {
                console.error('❌ 音乐播放失败:', error);
                // 尝试用户交互后再播放
                this.setupUserInteractionPlay();
            });
        } else {
            console.error('❌ 音频对象不存在');
        }
    }

    pauseMusic() {
        console.log('🎵 暂停音乐...');
        if (this.audio) {
            this.audio.pause();
            this.isPlaying = false;
            this.updateButtonState();
            this.saveUserSettings();
            console.log('✅ 音乐已暂停');
        }
    }

    setupUserInteractionPlay() {
        // 现代浏览器需要用户交互才能播放音频
        const playOnInteraction = () => {
            if (this.audio && !this.isPlaying) {
                this.audio.play().then(() => {
                    this.isPlaying = true;
                    this.updateButtonState();
                    this.saveUserSettings();
                    console.log('✅ 用户交互后音乐播放成功');
                    document.removeEventListener('click', playOnInteraction);
                }).catch(console.error);
            }
        };

        document.addEventListener('click', playOnInteraction, { once: true });
        console.log('🎵 等待用户交互以播放音乐...');
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        if (this.audio) {
            this.audio.volume = this.volume;
        }
        
        const volumeLabel = document.querySelector('.volume-label');
        if (volumeLabel) {
            volumeLabel.textContent = `${Math.round(this.volume * 100)}%`;
        }
        
        this.saveUserSettings();
    }

    updateButtonState() {
        const toggleBtn = document.getElementById('music-toggle');
        if (toggleBtn) {
            toggleBtn.classList.toggle('playing', this.isPlaying);
            toggleBtn.classList.toggle('paused', !this.isPlaying);
            toggleBtn.title = this.isPlaying ? '暂停音乐' : '播放音乐';
        }
    }

    loadUserSettings() {
        try {
            const settings = localStorage.getItem(this.storageKey);
            if (settings) {
                const parsed = JSON.parse(settings);
                const now = Date.now();
                
                if (now - parsed.timestamp < this.settingsExpiry) {
                    this.volume = parsed.volume || 0.3;
                    this.isPlaying = false; // 初始状态总是关闭
                }
            }
        } catch (error) {
            console.error('加载音乐设置失败:', error);
        }
    }

    saveUserSettings() {
        try {
            const settings = {
                volume: this.volume,
                isPlaying: this.isPlaying,
                timestamp: Date.now()
            };
            localStorage.setItem(this.storageKey, JSON.stringify(settings));
        } catch (error) {
            console.error('保存音乐设置失败:', error);
        }
    }

    // 添加拖动功能
    makeDraggable(element) {
        let isDragging = false;
        let hasDragged = false; // 标记是否发生了拖动
        let startX, startY, initialX, initialY;
        let currentX = 0, currentY = 0;

        const musicBtn = element.querySelector('#music-toggle');

        // 鼠标事件
        musicBtn.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);

        // 触摸事件（移动端）
        musicBtn.addEventListener('touchstart', startDrag, { passive: false });
        document.addEventListener('touchmove', drag, { passive: false });
        document.addEventListener('touchend', endDrag, { passive: true });

        function startDrag(e) {
            e.preventDefault();
            isDragging = true;
            hasDragged = false; // 重置拖动标记

            const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

            // 获取当前元素的实际位置
            const rect = element.getBoundingClientRect();
            currentX = rect.left;
            currentY = rect.top;

            startX = clientX - currentX;
            startY = clientY - currentY;

            // 不立即设置拖动状态，等到实际移动时再设置
            console.log('🎵 准备拖动:', { currentX, currentY, clientX, clientY });
        }

        function drag(e) {
            if (!isDragging) return;

            e.preventDefault();

            const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

            // 计算移动距离
            const moveX = clientX - (startX + currentX);
            const moveY = clientY - (startY + currentY);
            const moveDistance = Math.sqrt(moveX * moveX + moveY * moveY);

            // 只有移动距离超过阈值才认为是真正的拖动
            if (moveDistance > 5 && !hasDragged) {
                // 第一次真正开始拖动
                hasDragged = true;
                element.style.transition = 'none';
                element.style.cursor = 'grabbing';
                musicBtn.classList.add('dragging');
                element._isDragging = true;
                console.log('🎵 开始真正拖动:', { moveDistance, currentX, currentY });
            }

            if (hasDragged) {
                // 只有在真正拖动时才更新位置
                currentX = clientX - startX;
                currentY = clientY - startY;

                // 限制在屏幕范围内
                const maxX = window.innerWidth - element.offsetWidth;
                const maxY = window.innerHeight - element.offsetHeight;

                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));

                // 直接设置 left 和 top
                element.style.left = `${currentX}px`;
                element.style.top = `${currentY}px`;
                element.style.right = 'auto';
                element.style.bottom = 'auto';

                // 调试信息（仅在拖动时偶尔输出）
                if (Math.random() < 0.1) {
                    console.log('🎵 拖动中:', { currentX, currentY, maxX, maxY });
                }
            }
        }

        function endDrag(e) {
            if (!isDragging) return;

            isDragging = false;

            if (hasDragged) {
                // 只有真正拖动过才需要恢复样式和保存位置
                element.style.transition = 'all 0.3s ease';
                element.style.cursor = 'grab';
                musicBtn.classList.remove('dragging');

                // 保存位置
                try {
                    const settings = JSON.parse(localStorage.getItem('golden_ledger_music_settings') || '{}');
                    settings.position = { x: currentX, y: currentY };
                    localStorage.setItem('golden_ledger_music_settings', JSON.stringify(settings));
                    console.log('🎵 拖动结束，位置已保存:', { currentX, currentY });
                } catch (error) {
                    console.error('保存按钮位置失败:', error);
                }

                // 延迟清除拖动状态，防止立即触发点击
                setTimeout(() => {
                    element._isDragging = false;
                }, 150);
            } else {
                // 没有真正拖动，立即清除状态，允许点击
                element._isDragging = false;
                console.log('🎵 结束准备拖动（未实际拖动）');

                // 移动端：如果没有拖动，直接触发音乐播放
                const isMobile = window.innerWidth <= 768;
                if (isMobile) {
                    console.log('🎵 移动端检测到点击（通过拖动系统）');
                    try {
                        // 获取音乐控制器实例
                        if (window.goldenLedgerMusic) {
                            window.goldenLedgerMusic.toggleMusic();
                            console.log('🎵 移动端音乐切换成功');

                            // 显示音量面板
                            const volumePanel = element.querySelector('.volume-panel');
                            if (volumePanel && !volumePanel.classList.contains('show')) {
                                volumePanel.classList.add('show');
                                console.log('🎵 移动端显示音量面板');
                            }
                        } else {
                            console.error('🎵 音乐控制器不存在');
                        }
                    } catch (error) {
                        console.error('🎵 移动端音乐播放失败:', error);
                    }
                }
            }
        }

        // 加载保存的位置
        try {
            const settings = JSON.parse(localStorage.getItem('golden_ledger_music_settings') || '{}');
            if (settings.position) {
                currentX = settings.position.x || 0;
                currentY = settings.position.y || 0;
                // 直接设置位置
                element.style.left = `${currentX}px`;
                element.style.top = `${currentY}px`;
                element.style.right = 'auto';
                element.style.bottom = 'auto';
                console.log('🎵 加载保存的位置:', currentX, currentY);
            }
        } catch (error) {
            console.error('加载按钮位置失败:', error);
        }

        // 设置初始样式
        element.style.cursor = 'grab';
        element.style.userSelect = 'none';
    }
}

// 创建全局音乐控制器实例
window.goldenLedgerMusic = new MusicController();

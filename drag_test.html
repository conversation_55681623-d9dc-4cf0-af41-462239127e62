<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖动测试 - GoldenLedger</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .draggable-btn {
            position: fixed;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: grab;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            user-select: none;
            touch-action: none;
            z-index: 1000;
            top: 100px;
            left: 100px;
        }
        
        .draggable-btn:active {
            cursor: grabbing;
        }
        
        .info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="info" id="info">
        位置: (0, 0)<br>
        屏幕: 0 x 0<br>
        状态: 待机
    </div>

    <div class="test-container">
        <h1>🎵 拖动功能测试</h1>
        <p>测试音乐按钮的拖动功能是否正常工作</p>
        <ul>
            <li>✅ 鼠标拖动（桌面端）</li>
            <li>✅ 触摸拖动（移动端）</li>
            <li>✅ 边界限制</li>
            <li>✅ 位置保存</li>
        </ul>
        <button onclick="resetPosition()">重置位置</button>
        <button onclick="showPosition()">显示位置</button>
    </div>

    <button class="draggable-btn" id="testBtn">🎵</button>

    <script>
        const btn = document.getElementById('testBtn');
        const info = document.getElementById('info');
        let isDragging = false;
        let startX, startY, currentX = 100, currentY = 100;

        // 更新信息显示
        function updateInfo() {
            info.innerHTML = `
                位置: (${Math.round(currentX)}, ${Math.round(currentY)})<br>
                屏幕: ${window.innerWidth} x ${window.innerHeight}<br>
                状态: ${isDragging ? '拖动中' : '待机'}
            `;
        }

        // 鼠标事件
        btn.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', endDrag);

        // 触摸事件
        btn.addEventListener('touchstart', startDrag, { passive: false });
        document.addEventListener('touchmove', drag, { passive: false });
        document.addEventListener('touchend', endDrag);

        function startDrag(e) {
            e.preventDefault();
            isDragging = true;
            
            const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;
            
            const rect = btn.getBoundingClientRect();
            currentX = rect.left;
            currentY = rect.top;
            
            startX = clientX - currentX;
            startY = clientY - currentY;
            
            btn.style.transition = 'none';
            updateInfo();
            console.log('开始拖动:', { currentX, currentY });
        }

        function drag(e) {
            if (!isDragging) return;
            e.preventDefault();
            
            const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;
            
            currentX = clientX - startX;
            currentY = clientY - startY;
            
            // 边界限制
            const maxX = window.innerWidth - btn.offsetWidth;
            const maxY = window.innerHeight - btn.offsetHeight;
            
            currentX = Math.max(0, Math.min(currentX, maxX));
            currentY = Math.max(0, Math.min(currentY, maxY));
            
            btn.style.left = `${currentX}px`;
            btn.style.top = `${currentY}px`;
            
            updateInfo();
        }

        function endDrag() {
            if (!isDragging) return;
            isDragging = false;
            btn.style.transition = 'all 0.3s ease';
            updateInfo();
            
            // 保存位置
            localStorage.setItem('testBtnPosition', JSON.stringify({ x: currentX, y: currentY }));
            console.log('拖动结束，保存位置:', { currentX, currentY });
        }

        // 重置位置
        function resetPosition() {
            currentX = 100;
            currentY = 100;
            btn.style.left = `${currentX}px`;
            btn.style.top = `${currentY}px`;
            localStorage.removeItem('testBtnPosition');
            updateInfo();
        }

        // 显示位置
        function showPosition() {
            alert(`当前位置: (${Math.round(currentX)}, ${Math.round(currentY)})`);
        }

        // 加载保存的位置
        function loadPosition() {
            try {
                const saved = localStorage.getItem('testBtnPosition');
                if (saved) {
                    const pos = JSON.parse(saved);
                    currentX = pos.x;
                    currentY = pos.y;
                    btn.style.left = `${currentX}px`;
                    btn.style.top = `${currentY}px`;
                    console.log('加载保存的位置:', pos);
                }
            } catch (error) {
                console.error('加载位置失败:', error);
            }
        }

        // 初始化
        loadPosition();
        updateInfo();
        
        // 窗口大小变化时更新信息
        window.addEventListener('resize', updateInfo);
        
        console.log('🎵 拖动测试页面加载完成');
    </script>
</body>
</html>

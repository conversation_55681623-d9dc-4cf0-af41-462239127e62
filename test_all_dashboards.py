#!/usr/bin/env python3
"""
测试所有仪表盘页面
"""
import requests
import time

def test_all_dashboards():
    """测试所有仪表盘页面"""
    
    print("🎯 测试所有仪表盘页面")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 测试页面列表
    pages = [
        ("高级仪表盘", "/advanced_dashboard.html"),
        ("简单测试", "/simple_dashboard_test.html"),
        ("最简仪表盘", "/minimal_dashboard.html"),
        ("调试页面", "/debug_dashboard_frontend.html")
    ]
    
    # API测试
    apis = [
        ("仪表盘汇总", "/dashboard/summary/default"),
        ("仕訳记录", "/journal-entries/default")
    ]
    
    try:
        # 1. 测试API
        print("\n📡 步骤1: 测试API端点")
        api_results = {}
        
        for name, endpoint in apis:
            print(f"\n  测试 {name}: {endpoint}")
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    api_results[endpoint] = True
                    print(f"  ✅ {name} API正常")
                    
                    if endpoint == "/dashboard/summary/default":
                        financial = data.get('financial', {})
                        print(f"    💰 本月收入: ¥{financial.get('monthly_revenue', 0):,}")
                        print(f"    💸 本月支出: ¥{financial.get('monthly_expenses', 0):,}")
                        print(f"    📋 总记录数: {financial.get('total_entries', 0):,}")
                    
                    elif endpoint == "/journal-entries/default":
                        print(f"    📝 记录数量: {len(data)}")
                        
                else:
                    api_results[endpoint] = False
                    print(f"  ❌ {name} API失败: {response.status_code}")
                    
            except Exception as e:
                api_results[endpoint] = False
                print(f"  ❌ {name} API错误: {e}")
        
        # 2. 测试页面访问
        print(f"\n🌐 步骤2: 测试页面访问")
        page_results = {}
        
        for name, path in pages:
            print(f"\n  测试 {name}: {path}")
            try:
                response = requests.get(f"{base_url}{path}", timeout=10)
                
                if response.status_code == 200:
                    page_results[path] = True
                    print(f"  ✅ {name} 页面正常")
                    print(f"    Content-Type: {response.headers.get('content-type', 'N/A')}")
                    print(f"    Content-Length: {len(response.content)} bytes")
                else:
                    page_results[path] = False
                    print(f"  ❌ {name} 页面失败: {response.status_code}")
                    
            except Exception as e:
                page_results[path] = False
                print(f"  ❌ {name} 页面错误: {e}")
        
        # 3. 综合测试结果
        print(f"\n📊 步骤3: 综合测试结果")
        
        api_success = all(api_results.values())
        page_success = all(page_results.values())
        
        print(f"\n  API测试结果:")
        for endpoint, success in api_results.items():
            status = "✅ 正常" if success else "❌ 异常"
            print(f"    {endpoint}: {status}")
        
        print(f"\n  页面测试结果:")
        for path, success in page_results.items():
            status = "✅ 正常" if success else "❌ 异常"
            print(f"    {path}: {status}")
        
        # 4. 问题诊断
        print(f"\n🔍 步骤4: 问题诊断")
        
        if not api_success:
            print("  ⚠️ API问题诊断:")
            print("    - 检查后端服务是否正在运行")
            print("    - 检查数据库连接是否正常")
            print("    - 检查数据库中是否有数据")
        
        if not page_success:
            print("  ⚠️ 页面问题诊断:")
            print("    - 检查静态文件路由是否正确")
            print("    - 检查文件是否存在")
            print("    - 检查文件权限")
        
        if api_success and page_success:
            print("  🎉 所有测试通过！")
            print("  💡 如果仪表盘仍然没有数据，可能的原因:")
            print("    - JavaScript错误（检查浏览器控制台）")
            print("    - CORS问题（已修复为相对URL）")
            print("    - Chart.js加载问题（已添加错误处理）")
            print("    - 网络连接问题")
        
        # 5. 推荐访问顺序
        print(f"\n🚀 步骤5: 推荐测试顺序")
        print("  建议按以下顺序测试页面:")
        print("  1. 最简仪表盘: http://localhost:8000/minimal_dashboard.html")
        print("     (最基础的测试，无图表依赖)")
        print("  2. 简单测试: http://localhost:8000/simple_dashboard_test.html")
        print("     (带有详细日志的测试)")
        print("  3. 调试页面: http://localhost:8000/debug_dashboard_frontend.html")
        print("     (手动测试API调用)")
        print("  4. 高级仪表盘: http://localhost:8000/advanced_dashboard.html")
        print("     (完整功能的仪表盘)")
        
        return api_success and page_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_backend_status():
    """检查后端状态"""
    
    print("\n🔧 检查后端状态")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    try:
        # 检查根路径
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正在运行")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务")
        print("💡 请确保后端服务正在运行:")
        print("   python3 start_backend_simple.py")
        return False
        
    except Exception as e:
        print(f"❌ 后端检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始全面测试仪表盘功能")
    
    # 检查后端状态
    backend_ok = check_backend_status()
    
    if backend_ok:
        # 测试所有仪表盘
        success = test_all_dashboards()
        
        print("\n" + "=" * 80)
        print("📊 最终测试结果:")
        
        if success:
            print("🎉 所有测试通过！仪表盘功能正常")
            print("\n🌐 推荐访问:")
            print("   主仪表盘: http://localhost:8000/advanced_dashboard.html")
            print("   简单测试: http://localhost:8000/minimal_dashboard.html")
        else:
            print("❌ 部分测试失败，请检查错误信息")
            print("\n🔧 故障排除:")
            print("   1. 检查浏览器控制台是否有JavaScript错误")
            print("   2. 尝试访问简单测试页面")
            print("   3. 检查网络连接")
    else:
        print("\n❌ 后端服务未运行，请先启动后端服务")

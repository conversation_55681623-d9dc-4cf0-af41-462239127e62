<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌍 多语言身份识别测试 - GoldenLedger</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            white-space: pre-wrap;
            font-family: 'Segoe UI', sans-serif;
            line-height: 1.6;
        }
        .result.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .language-flag {
            font-size: 20px;
            margin-right: 8px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            margin: 10px 0;
        }
        .custom-test {
            background: #e3f2fd;
            border-color: #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 多语言身份识别测试</h1>
        <p>测试AI聊天机器人的多语言身份识别功能（通过系统提示词实现，安全可靠）</p>
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <strong>🔒 安全说明：</strong> 身份识别逻辑现在完全通过AI的系统提示词实现，不在客户端暴露任何检测规则，确保安全性。
        </div>

        <!-- 日语测试 -->
        <div class="test-section">
            <h3><span class="language-flag">🇯🇵</span>日语身份询问测试</h3>
            <button class="test-button" onclick="testIdentity('あなたは誰ですか？')">あなたは誰ですか？</button>
            <button class="test-button" onclick="testIdentity('君は誰？')">君は誰？</button>
            <button class="test-button" onclick="testIdentity('自己紹介してください')">自己紹介してください</button>
            <button class="test-button" onclick="testIdentity('あなたについて教えて')">あなたについて教えて</button>
            <button class="test-button" onclick="testIdentity('どんなAIですか？')">どんなAIですか？</button>
            <div id="japanese-result" class="result" style="display: none;"></div>
        </div>

        <!-- 中文测试 -->
        <div class="test-section">
            <h3><span class="language-flag">🇨🇳</span>中文身份询问测试</h3>
            <button class="test-button" onclick="testIdentity('你是谁？')">你是谁？</button>
            <button class="test-button" onclick="testIdentity('您是谁？')">您是谁？</button>
            <button class="test-button" onclick="testIdentity('自我介绍一下')">自我介绍一下</button>
            <button class="test-button" onclick="testIdentity('你是什么AI？')">你是什么AI？</button>
            <button class="test-button" onclick="testIdentity('哪家公司开发的？')">哪家公司开发的？</button>
            <div id="chinese-result" class="result" style="display: none;"></div>
        </div>

        <!-- 英语测试 -->
        <div class="test-section">
            <h3><span class="language-flag">🇺🇸</span>English Identity Questions</h3>
            <button class="test-button" onclick="testIdentity('Who are you?')">Who are you?</button>
            <button class="test-button" onclick="testIdentity('What are you?')">What are you?</button>
            <button class="test-button" onclick="testIdentity('Introduce yourself')">Introduce yourself</button>
            <button class="test-button" onclick="testIdentity('Tell me about yourself')">Tell me about yourself</button>
            <button class="test-button" onclick="testIdentity('Who created you?')">Who created you?</button>
            <button class="test-button" onclick="testIdentity('Which company made you?')">Which company made you?</button>
            <div id="english-result" class="result" style="display: none;"></div>
        </div>

        <!-- 自定义测试 -->
        <div class="test-section custom-test">
            <h3>🧪 自定义测试</h3>
            <input type="text" class="test-input" id="custom-input" placeholder="输入您想测试的问题...">
            <button class="test-button" onclick="testCustom()">测试自定义问题</button>
            <div id="custom-result" class="result" style="display: none;"></div>
        </div>

        <!-- 非身份问题测试 -->
        <div class="test-section">
            <h3>❌ 非身份问题测试（应该返回null）</h3>
            <button class="test-button" onclick="testIdentity('今天天气怎么样？')">今天天气怎么样？</button>
            <button class="test-button" onclick="testIdentity('How is the weather?')">How is the weather?</button>
            <button class="test-button" onclick="testIdentity('今月の支出を教えて')">今月の支出を教えて</button>
            <div id="non-identity-result" class="result" style="display: none;"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" onclick="clearAllResults()">🗑️ 清除所有结果</button>
            <button class="test-button" onclick="goBack()">← 返回主页</button>
        </div>
    </div>

    <!-- 加载环境配置和聊天机器人 -->
    <script src="env-config.js?v=3.6.4"></script>
    <script src="chatbot/UsageTracker.js?v=3.6.4"></script>
    <script src="chatbot/GeminiAPI.js?v=3.6.4"></script>

    <script>
        // 初始化GeminiAPI实例
        const geminiAPI = new GeminiAPI();

        async function testIdentity(question) {
            console.log('🧪 Testing question:', question);

            // 确定结果显示区域
            let resultId;
            if (question.match(/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/)) {
                if (question.match(/[\u4E00-\u9FAF]/)) {
                    resultId = 'chinese-result';
                } else {
                    resultId = 'japanese-result';
                }
            } else {
                resultId = 'english-result';
            }

            // 特殊处理非身份问题
            if (question === '今天天气怎么样？' || question === 'How is the weather?' || question === '今月の支出を教えて') {
                resultId = 'non-identity-result';
            }

            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = `<strong>🔄 正在测试...</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>状态:</strong> 发送到AI进行处理...`;

            try {
                // 通过实际API调用测试身份识别
                const response = await geminiAPI.sendMessage(question);

                // 检查回答是否包含公司信息
                const hasCompanyInfo = response.includes('GoldenOrangeTech') || response.includes('株式会社');
                const hasTrainingInfo = response.includes('训练') || response.includes('訓練') || response.includes('training');
                const hasPrivacyInfo = response.includes('不会采集') || response.includes('収集') || response.includes('do not collect');

                if (hasCompanyInfo && hasTrainingInfo && hasPrivacyInfo) {
                    resultDiv.className = 'result';
                    resultDiv.innerHTML = `<strong>✅ AI正确识别身份问题</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>AI回答:</strong>\n${response}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>⚠️ AI回答不完整</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>AI回答:</strong>\n${response}\n\n<strong>缺少信息:</strong>\n${!hasCompanyInfo ? '- 公司信息\n' : ''}${!hasTrainingInfo ? '- 训练状态\n' : ''}${!hasPrivacyInfo ? '- 隐私声明\n' : ''}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 测试失败</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>错误:</strong> ${error.message}`;
            }
        }

        async function testCustom() {
            const input = document.getElementById('custom-input');
            const question = input.value.trim();

            if (!question) {
                alert('请输入测试问题');
                return;
            }

            console.log('🧪 Testing custom question:', question);

            const resultDiv = document.getElementById('custom-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = `<strong>🔄 正在测试...</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>状态:</strong> 发送到AI进行处理...`;

            try {
                const response = await geminiAPI.sendMessage(question);

                // 检查回答是否包含身份信息
                const hasCompanyInfo = response.includes('GoldenOrangeTech') || response.includes('株式会社');
                const hasTrainingInfo = response.includes('训练') || response.includes('訓練') || response.includes('training');
                const hasPrivacyInfo = response.includes('不会采集') || response.includes('収集') || response.includes('do not collect');

                if (hasCompanyInfo && hasTrainingInfo && hasPrivacyInfo) {
                    resultDiv.className = 'result';
                    resultDiv.innerHTML = `<strong>✅ AI识别为身份问题</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>AI回答:</strong>\n${response}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>ℹ️ AI未识别为身份问题</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>AI回答:</strong>\n${response}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 测试失败</strong>\n\n<strong>问题:</strong> ${question}\n\n<strong>错误:</strong> ${error.message}`;
            }

            input.value = '';
        }

        function clearAllResults() {
            const results = document.querySelectorAll('.result');
            results.forEach(result => {
                result.style.display = 'none';
            });
        }

        function goBack() {
            window.location.href = '/';
        }

        // 页面加载时显示状态
        window.addEventListener('load', function() {
            console.log('🌍 多语言身份识别测试页面已加载');
            console.log('🔑 API Key status:', window.GEMINI_API_KEY ? 'Available' : 'Not configured');
        });
    </script>
</body>
</html>

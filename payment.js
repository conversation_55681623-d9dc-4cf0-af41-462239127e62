/**
 * 付费页面 JavaScript 逻辑
 * 处理套餐显示、PayPal 支付集成等
 */

let paypalLoaded = false;
let currentUser = null;

// 页面初始化
document.addEventListener('DOMContentLoaded', async function() {
    console.log('💳 Payment page initializing...');
    
    try {
        // 检查用户登录状态
        await checkUserAuth();
        
        // 加载 PayPal SDK
        await initPayPal();
        paypalLoaded = true;
        
        // 渲染套餐卡片
        renderPlanCards();
        
        // 检查当前订阅状态
        await checkCurrentSubscription();
        
        console.log('✅ Payment page initialized successfully');
    } catch (error) {
        console.error('❌ Payment page initialization failed:', error);
        showError('ページの初期化に失敗しました: ' + error.message);
    }
});

// 检查用户认证状态
async function checkUserAuth() {
    const token = localStorage.getItem('goldenledger_session_token');
    if (!token) {
        // 未登录，重定向到登录页面
        window.location.href = 'auth/login.html?return=' + encodeURIComponent(window.location.pathname);
        return;
    }
    
    try {
        // 验证 token 有效性
        const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/verify'), {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            throw new Error('Token validation failed');
        }
        
        const result = await response.json();
        if (result.success) {
            currentUser = result.user;
            console.log('✅ User authenticated:', currentUser.email);
        } else {
            throw new Error('Invalid token');
        }
    } catch (error) {
        console.error('❌ Auth check failed:', error);
        localStorage.removeItem('goldenledger_session_token');
        window.location.href = 'auth/login.html?return=' + encodeURIComponent(window.location.pathname);
    }
}

// 渲染套餐卡片
function renderPlanCards() {
    const container = document.getElementById('plansContainer');
    const plans = window.PayPalConfig.plans;
    
    container.innerHTML = '';
    
    Object.values(plans).forEach(plan => {
        const card = createPlanCard(plan);
        container.appendChild(card);
    });
}

// 创建套餐卡片
function createPlanCard(plan) {
    const isRecommended = plan.id === 'pro';
    const isFree = plan.id === 'free';
    
    const card = document.createElement('div');
    card.className = `plan-card cyber-glass rounded-3xl p-8 ${isRecommended ? 'recommended' : ''}`;
    
    card.innerHTML = `
        <div class="text-center">
            <h3 class="text-2xl font-bold text-cyan-400 mb-4">${plan.name}</h3>
            <div class="mb-6">
                <span class="price-display text-4xl font-bold">¥${plan.price.toLocaleString()}</span>
                ${!isFree ? '<span class="text-cyan-300">/月</span>' : ''}
            </div>
            
            <ul class="feature-list text-left text-cyan-200 mb-8">
                ${plan.features.map(feature => `<li>${feature}</li>`).join('')}
            </ul>
            
            <div class="paypal-button-container" id="paypal-button-${plan.id}">
                ${isFree ? 
                    `<button onclick="selectFreePlan()" class="w-full bg-gradient-to-r from-cyan-500 to-blue-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-cyan-600 hover:to-blue-700 transition-all duration-300">
                        無料で開始
                    </button>` : 
                    `<div class="paypal-buttons" data-plan-id="${plan.id}"></div>`
                }
            </div>
        </div>
    `;
    
    return card;
}

// 初始化 PayPal 按钮
async function initPayPalButtons() {
    if (!paypalLoaded || !window.paypal) {
        console.error('PayPal SDK not loaded');
        return;
    }
    
    const plans = window.PayPalConfig.plans;
    
    Object.values(plans).forEach(plan => {
        if (plan.id === 'free') return; // 跳过免费套餐
        
        const container = document.querySelector(`[data-plan-id="${plan.id}"]`);
        if (!container) return;
        
        window.paypal.Buttons({
            style: {
                color: 'blue',
                shape: 'rect',
                label: 'subscribe',
                height: 50
            },
            
            createSubscription: function(data, actions) {
                return createPayPalSubscription(plan.id);
            },
            
            onApprove: function(data, actions) {
                return handlePaymentApproval(data, plan.id);
            },
            
            onError: function(err) {
                console.error('PayPal error:', err);
                showError('PayPal支払いエラーが発生しました');
            },
            
            onCancel: function(data) {
                console.log('Payment cancelled:', data);
                showError('支払いがキャンセルされました');
            }
        }).render(container);
    });
}

// 创建 PayPal 订阅
async function createPayPalSubscription(planId) {
    showPaymentModal();
    
    try {
        const response = await fetch(window.GoldenLedgerAPI.url('/api/payment/create-subscription'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('goldenledger_session_token')}`
            },
            body: JSON.stringify({
                plan_id: planId,
                user_id: currentUser.id
            })
        });
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error || 'Failed to create subscription');
        }
        
        return result.subscription_id;
    } catch (error) {
        hidePaymentModal();
        console.error('Failed to create subscription:', error);
        showError('サブスクリプション作成に失敗しました: ' + error.message);
        throw error;
    }
}

// 处理支付批准
async function handlePaymentApproval(data, planId) {
    try {
        const response = await fetch(window.GoldenLedgerAPI.url('/api/payment/approve-subscription'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('goldenledger_session_token')}`
            },
            body: JSON.stringify({
                subscription_id: data.subscriptionID,
                plan_id: planId,
                user_id: currentUser.id
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 支付成功，重定向到成功页面
            window.location.href = `payment-success.html?plan=${planId}&subscription=${data.subscriptionID}`;
        } else {
            throw new Error(result.error || 'Payment approval failed');
        }
    } catch (error) {
        console.error('Payment approval failed:', error);
        showError('支払い承認に失敗しました: ' + error.message);
    } finally {
        hidePaymentModal();
    }
}

// 选择免费套餐
async function selectFreePlan() {
    try {
        const response = await fetch(window.GoldenLedgerAPI.url('/api/subscription/activate-free'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('goldenledger_session_token')}`
            },
            body: JSON.stringify({
                user_id: currentUser.id
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 免费套餐激活成功
            window.location.href = 'dashboard.html?welcome=free';
        } else {
            throw new Error(result.error || 'Failed to activate free plan');
        }
    } catch (error) {
        console.error('Failed to activate free plan:', error);
        showError('無料プランの有効化に失敗しました: ' + error.message);
    }
}

// 检查当前订阅状态
async function checkCurrentSubscription() {
    try {
        const response = await fetch(window.GoldenLedgerAPI.url('/api/subscription/status'), {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('goldenledger_session_token')}`
            }
        });
        
        const result = await response.json();
        
        if (result.success && result.subscription) {
            updateUIForCurrentSubscription(result.subscription);
        }
    } catch (error) {
        console.error('Failed to check subscription status:', error);
    }
}

// 根据当前订阅更新UI
function updateUIForCurrentSubscription(subscription) {
    const currentPlanId = subscription.plan_id;
    const status = subscription.status;
    
    // 更新当前套餐的按钮状态
    const currentPlanButton = document.querySelector(`#paypal-button-${currentPlanId}`);
    if (currentPlanButton) {
        if (status === 'active') {
            currentPlanButton.innerHTML = `
                <button class="w-full bg-green-500 text-white py-3 px-6 rounded-xl font-semibold cursor-not-allowed" disabled>
                    <i class="fas fa-check mr-2"></i>現在のプラン
                </button>
            `;
        } else if (status === 'cancelled') {
            currentPlanButton.innerHTML = `
                <button class="w-full bg-yellow-500 text-white py-3 px-6 rounded-xl font-semibold cursor-not-allowed" disabled>
                    <i class="fas fa-pause mr-2"></i>キャンセル済み
                </button>
            `;
        }
    }
}

// 显示支付处理模态框
function showPaymentModal() {
    document.getElementById('paymentModal').classList.remove('hidden');
}

// 隐藏支付处理模态框
function hidePaymentModal() {
    document.getElementById('paymentModal').classList.add('hidden');
}

// 显示错误信息
function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorModal').classList.remove('hidden');
}

// 关闭错误模态框
function closeErrorModal() {
    document.getElementById('errorModal').classList.add('hidden');
}

// PayPal SDK 加载完成后初始化按钮
window.addEventListener('load', async function() {
    if (paypalLoaded) {
        await initPayPalButtons();
    }
});

// 等待 PayPal SDK 加载完成
async function waitForPayPal() {
    let attempts = 0;
    const maxAttempts = 50;

    while (!window.paypal && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    if (!window.paypal) {
        throw new Error('PayPal SDK failed to load');
    }

    return window.paypal;
}

// 重新初始化 PayPal（在 SDK 加载后调用）
async function reinitializePayPal() {
    try {
        await waitForPayPal();
        await initPayPalButtons();
        console.log('✅ PayPal buttons initialized');
    } catch (error) {
        console.error('❌ Failed to initialize PayPal buttons:', error);
        showError('PayPal初期化に失敗しました');
    }
}

// 监听 PayPal SDK 加载事件
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化 PayPal 按钮
    setTimeout(reinitializePayPal, 1000);
});

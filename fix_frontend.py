#!/usr/bin/env python3
"""
前端问题修复脚本
自动检测和修复前端启动问题
"""
import os
import sys
import subprocess
import json
from pathlib import Path

def check_node_environment():
    """检查Node.js环境"""
    print("🔍 检查Node.js环境...")
    
    try:
        # 检查Node.js
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js未安装")
            return False
            
        # 检查npm
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm: {result.stdout.strip()}")
        else:
            print("❌ npm未安装")
            return False
            
        return True
    except FileNotFoundError:
        print("❌ Node.js环境未找到")
        return False

def check_frontend_files():
    """检查前端文件"""
    print("📁 检查前端文件...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 检查关键文件
    required_files = [
        "package.json",
        "index.html",
        "vite.config.ts",
        "src/main.tsx",
        "src/App.tsx"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def install_dependencies():
    """安装前端依赖"""
    print("📦 安装前端依赖...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    try:
        # 检查node_modules
        node_modules = frontend_dir / "node_modules"
        if not node_modules.exists():
            print("⚠️ node_modules不存在，正在安装依赖...")
            result = subprocess.run(['npm', 'install'], cwd=frontend_dir, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 依赖安装成功")
            else:
                print(f"❌ 依赖安装失败: {result.stderr}")
                return False
        else:
            print("✅ node_modules已存在")
        
        return True
    except Exception as e:
        print(f"❌ 安装依赖时出错: {e}")
        return False

def start_dev_server():
    """启动开发服务器"""
    print("🚀 启动开发服务器...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    try:
        print("正在启动Vite开发服务器...")
        print("访问地址: http://localhost:3000")
        print("按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 启动开发服务器
        process = subprocess.Popen(
            ['npm', 'run', 'dev'],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时输出日志
        for line in process.stdout:
            print(line.strip())
            if "Local:" in line and "localhost:3000" in line:
                print("\n🎉 前端服务器启动成功！")
                print("请在浏览器中访问: http://localhost:3000")
                break
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n👋 开发服务器已停止")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"❌ 启动开发服务器失败: {e}")
        return False

def create_simple_test_page():
    """创建简单测试页面"""
    print("📄 创建测试页面...")
    
    test_html = """<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端环境测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
        <h1 class="text-3xl font-bold text-center mb-6">🚀 GoldenLedger — Smart AI-Powered Finance System</h1>
        <div class="text-center">
            <p class="text-gray-600 mb-4">前端环境测试成功！</p>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <p class="text-green-800">✅ 如果您看到这个页面，说明基础环境正常</p>
            </div>
        </div>
    </div>
</body>
</html>"""
    
    try:
        with open("frontend_test.html", "w", encoding="utf-8") as f:
            f.write(test_html)
        print("✅ 测试页面已创建: frontend_test.html")
        print("请在浏览器中打开此文件进行测试")
        return True
    except Exception as e:
        print(f"❌ 创建测试页面失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 GoldenLedger — Smart AI-Powered Finance System前端修复工具")
    print("=" * 50)
    
    # 检查Node.js环境
    if not check_node_environment():
        print("\n💡 请先安装Node.js:")
        print("   brew install node")
        print("   或访问: https://nodejs.org/")
        return False
    
    # 检查前端文件
    if not check_frontend_files():
        print("\n⚠️ 前端文件不完整，但我们可以创建测试页面")
        create_simple_test_page()
        return False
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败")
        create_simple_test_page()
        return False
    
    # 启动开发服务器
    print("\n🚀 准备启动开发服务器...")
    input("按回车键继续...")
    start_dev_server()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 前端启动成功！")
        else:
            print("\n⚠️ 前端启动遇到问题，但已创建测试页面")
    except KeyboardInterrupt:
        print("\n👋 操作已取消")
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        sys.exit(1)

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API配置测试</h1>
        
        <div class="info">
            <h3>环境信息</h3>
            <p><strong>当前URL:</strong> <span id="currentUrl"></span></p>
            <p><strong>Hostname:</strong> <span id="hostname"></span></p>
            <p><strong>Protocol:</strong> <span id="protocol"></span></p>
            <p><strong>Port:</strong> <span id="port"></span></p>
        </div>

        <div class="info">
            <h3>API配置</h3>
            <p><strong>API基础URL:</strong> <span id="apiBaseUrl"></span></p>
            <p><strong>PayPal验证端点:</strong> <span id="paypalEndpoint"></span></p>
            <p><strong>配置状态:</strong> <span id="configStatus"></span></p>
        </div>

        <div>
            <h3>测试功能</h3>
            <button onclick="testApiConnection()">测试API连接</button>
            <button onclick="testPayPalEndpoint()">测试PayPal端点</button>
            <button onclick="reloadConfig()">重新加载配置</button>
        </div>

        <div id="testResults"></div>
    </div>

    <script src="api_config.js?v=3.0.3"></script>
    <script>
        function updateEnvironmentInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('hostname').textContent = window.location.hostname;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('port').textContent = window.location.port || '默认端口';
        }

        function updateApiInfo() {
            if (window.GoldenLedgerAPI) {
                document.getElementById('apiBaseUrl').textContent = window.GoldenLedgerAPI.baseURL;
                document.getElementById('paypalEndpoint').textContent = window.GoldenLedgerAPI.url('/api/paypal/verify-payment');
                document.getElementById('configStatus').textContent = '✅ 已加载';
                document.getElementById('configStatus').className = 'success';
            } else {
                document.getElementById('apiBaseUrl').textContent = '未加载';
                document.getElementById('paypalEndpoint').textContent = '未加载';
                document.getElementById('configStatus').textContent = '❌ 未加载';
                document.getElementById('configStatus').className = 'error';
            }
        }

        async function testApiConnection() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">🔄 测试API连接...</div>';

            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/health'), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.text();
                    resultsDiv.innerHTML = `
                        <div class="info success">
                            <h4>✅ API连接成功</h4>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>响应:</strong></p>
                            <pre>${data}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="info error">
                            <h4>❌ API连接失败</h4>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>状态文本:</strong> ${response.statusText}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="info error">
                        <h4>❌ 连接错误</h4>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testPayPalEndpoint() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">🔄 测试PayPal端点...</div>';

            try {
                const testData = {
                    orderID: 'test_' + Date.now(),
                    planId: 'basic'
                };

                const response = await fetch(window.GoldenLedgerAPI.url('/api/paypal/verify-payment'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `
                        <div class="info success">
                            <h4>✅ PayPal端点测试成功</h4>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>响应数据:</strong></p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    resultsDiv.innerHTML = `
                        <div class="info error">
                            <h4>❌ PayPal端点测试失败</h4>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>状态文本:</strong> ${response.statusText}</p>
                            <p><strong>响应:</strong></p>
                            <pre>${errorText}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="info error">
                        <h4>❌ 请求错误</h4>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        function reloadConfig() {
            location.reload();
        }

        // 页面加载时更新信息
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvironmentInfo();
            updateApiInfo();
            
            // 每秒检查一次API配置是否加载
            const checkConfig = setInterval(() => {
                if (window.GoldenLedgerAPI) {
                    updateApiInfo();
                    clearInterval(checkConfig);
                }
            }, 100);
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
添加缺少的会计科目
"""
import sys
from pathlib import Path
import sqlite3

# 添加backend路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

from database import DatabaseManager

def add_missing_accounts():
    """添加缺少的会计科目"""
    
    print("📊 添加缺少的会计科目")
    print("=" * 50)
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("backend/goldenledger_accounting.db")
        
        # 需要添加的科目
        missing_accounts = [
            # 费用科目
            ('費用', '事務用品費'),      # 办公用品费
            ('費用', '接待交際費'),      # 接待交际费
            ('費用', '旅費交通費'),      # 旅费交通费（可能已有交通費，但这是更标准的名称）
            ('費用', '支払利息'),        # 支付利息
            ('費用', '雑費'),           # 杂费
            ('費用', '減価償却費'),      # 折旧费
            ('費用', '租税公課'),        # 税金
            ('費用', '保険料'),         # 保险费
            ('費用', '修繕費'),         # 修缮费
            ('費用', '外注費'),         # 外包费
            
            # 收益科目
            ('収益', '雑収入'),         # 杂收入
            ('収益', '受取配当金'),      # 收到股息
            ('収益', '為替差益'),        # 汇兑收益
            
            # 资产科目
            ('資産', '当座預金'),        # 活期存款
            ('資産', '定期預金'),        # 定期存款
            ('資産', '商品'),           # 商品
            ('資産', '原材料'),         # 原材料
            ('資産', '建物'),           # 建筑物
            ('資産', '機械装置'),        # 机械设备
            ('資産', '車両運搬具'),      # 车辆运输工具
            ('資産', '工具器具備品'),    # 工具器具设备
            ('資産', '土地'),           # 土地
            ('資産', '投資有価証券'),    # 投资有价证券
            ('資産', '前払費用'),        # 预付费用
            ('資産', '未収入金'),        # 未收入金
            
            # 负债科目
            ('負債', '未払金'),         # 未付金
            ('負債', '未払費用'),        # 未付费用
            ('負債', '預り金'),         # 预收金
            ('負債', '前受金'),         # 预收款
            ('負債', '長期借入金'),      # 长期借款
            ('負債', '社会保険料預り金'), # 社会保险费预收金
            ('負債', '源泉所得税預り金'), # 源泉所得税预收金
            
            # 纯资产科目
            ('純資産', '繰越利益剰余金'), # 结转利润余额
            ('純資産', '当期純利益'),    # 当期纯利润
        ]
        
        # 获取当前科目
        current_subjects = db.get_account_subjects('default')
        
        # 检查并添加缺少的科目
        added_count = 0
        skipped_count = 0
        
        for category, name in missing_accounts:
            # 检查科目是否已存在
            if category in current_subjects and name in current_subjects[category]:
                print(f"⏭️ 跳过已存在的科目: {category} - {name}")
                skipped_count += 1
                continue
            
            try:
                # 连接数据库并添加科目
                conn = sqlite3.connect("backend/goldenledger_accounting.db")
                cursor = conn.cursor()
                
                # 生成科目ID
                import uuid
                subject_id = str(uuid.uuid4())
                
                cursor.execute('''
                    INSERT INTO account_subjects (id, company_id, category, name, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', (subject_id, 'default', category, name, True))
                
                conn.commit()
                conn.close()
                
                print(f"✅ 添加科目: {category} - {name}")
                added_count += 1
                
            except Exception as e:
                print(f"❌ 添加科目失败 {category} - {name}: {e}")
        
        print(f"\n📊 添加结果:")
        print(f"  新增科目: {added_count}")
        print(f"  跳过科目: {skipped_count}")
        print(f"  总计处理: {len(missing_accounts)}")
        
        # 显示更新后的科目列表
        print(f"\n📋 更新后的科目列表:")
        updated_subjects = db.get_account_subjects('default')
        
        for category, accounts in updated_subjects.items():
            print(f"\n{category} ({len(accounts)}个):")
            for account in sorted(accounts):
                print(f"  - {account}")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加科目失败: {e}")
        return False

def verify_accounts():
    """验证科目添加结果"""
    
    print(f"\n🔍 验证科目添加结果")
    print("=" * 30)
    
    try:
        db = DatabaseManager("backend/goldenledger_accounting.db")
        subjects = db.get_account_subjects('default')
        
        # 检查关键科目是否存在
        key_accounts = [
            ('費用', '事務用品費'),
            ('費用', '接待交際費'),
            ('収益', '雑収入'),
            ('資産', '当座預金'),
            ('負債', '未払金')
        ]
        
        print("关键科目检查:")
        for category, name in key_accounts:
            exists = category in subjects and name in subjects[category]
            status = "✅" if exists else "❌"
            print(f"  {status} {category} - {name}")
        
        # 统计各类科目数量
        print(f"\n科目数量统计:")
        total_count = 0
        for category, accounts in subjects.items():
            count = len(accounts)
            total_count += count
            print(f"  {category}: {count}个")
        
        print(f"  总计: {total_count}个科目")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始添加缺少的会计科目")
    
    # 添加科目
    success = add_missing_accounts()
    
    if success:
        # 验证结果
        verify_accounts()
        
        print("\n🎉 会计科目更新完成！")
        print("\n💡 现在可以在以下页面使用新科目:")
        print("  - 记账页面: http://localhost:8000/journal_entries.html")
        print("  - AI演示页面: http://localhost:8000/interactive_demo.html")
        
    else:
        print("\n❌ 科目添加失败，请检查错误信息")

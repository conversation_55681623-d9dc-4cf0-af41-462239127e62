<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fetch问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error { color: #ff0000; }
        .success { color: #00ff00; }
        .info { color: #0099ff; }
    </style>
</head>
<body>
    <h1>Fetch问题调试页面</h1>
    
    <div class="debug-section">
        <h2>环境信息</h2>
        <div id="env-info" class="console-output"></div>
        <button onclick="checkEnvironment()">检查环境</button>
    </div>
    
    <div class="debug-section">
        <h2>网络连接测试</h2>
        <div id="network-info" class="console-output"></div>
        <button onclick="testNetwork()">测试网络</button>
    </div>
    
    <div class="debug-section">
        <h2>API调用测试</h2>
        <div id="api-info" class="console-output"></div>
        <button onclick="testAPI()">测试API</button>
    </div>
    
    <div class="debug-section">
        <h2>完整的预览功能测试</h2>
        <div id="preview-info" class="console-output"></div>
        <button onclick="testPreviewFunction()">测试预览功能</button>
    </div>
    
    <div class="debug-section">
        <h2>浏览器控制台</h2>
        <p>请打开浏览器开发者工具(F12)查看控制台输出</p>
        <button onclick="logToConsole()">输出到控制台</button>
    </div>

    <script>
        const TEST_ENTRY_ID = 'J20250711165206';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        function checkEnvironment() {
            clearLog('env-info');
            log('env-info', '=== 环境信息检查 ===');
            log('env-info', `用户代理: ${navigator.userAgent}`);
            log('env-info', `当前URL: ${window.location.href}`);
            log('env-info', `协议: ${window.location.protocol}`);
            log('env-info', `主机: ${window.location.host}`);
            log('env-info', `端口: ${window.location.port}`);
            log('env-info', `Fetch支持: ${typeof fetch !== 'undefined' ? '是' : '否'}`, typeof fetch !== 'undefined' ? 'success' : 'error');
            log('env-info', `Promise支持: ${typeof Promise !== 'undefined' ? '是' : '否'}`, typeof Promise !== 'undefined' ? 'success' : 'error');
            log('env-info', `async/await支持: ${(async () => {})().constructor.name === 'AsyncFunction' ? '是' : '否'}`, 'success');
        }
        
        async function testNetwork() {
            clearLog('network-info');
            log('network-info', '=== 网络连接测试 ===');
            
            try {
                log('network-info', '测试1: 基本健康检查...');
                const healthResponse = await fetch('/health');
                log('network-info', `健康检查状态: ${healthResponse.status}`, healthResponse.ok ? 'success' : 'error');
                const healthText = await healthResponse.text();
                log('network-info', `健康检查响应: ${healthText}`);
                
                log('network-info', '测试2: 获取记录列表...');
                const entriesResponse = await fetch('/journal-entries/default');
                log('network-info', `记录列表状态: ${entriesResponse.status}`, entriesResponse.ok ? 'success' : 'error');
                const entries = await entriesResponse.json();
                log('network-info', `记录数量: ${entries.length}`);
                
                const testEntry = entries.find(e => e.id === TEST_ENTRY_ID);
                log('network-info', `测试记录存在: ${testEntry ? '是' : '否'}`, testEntry ? 'success' : 'error');
                if (testEntry) {
                    log('network-info', `测试记录附件: ${testEntry.attachment_path || '无'}`);
                }
                
            } catch (error) {
                log('network-info', `网络测试失败: ${error.message}`, 'error');
                log('network-info', `错误类型: ${error.constructor.name}`);
                log('network-info', `错误堆栈: ${error.stack}`);
            }
        }
        
        async function testAPI() {
            clearLog('api-info');
            log('api-info', '=== API调用测试 ===');
            
            try {
                log('api-info', '测试1: 附件列表API...');
                const listResponse = await fetch(`/attachments/${TEST_ENTRY_ID}`);
                log('api-info', `列表API状态: ${listResponse.status}`, listResponse.ok ? 'success' : 'error');
                log('api-info', `列表API Content-Type: ${listResponse.headers.get('content-type')}`);
                const listResult = await listResponse.json();
                log('api-info', `列表API响应: ${JSON.stringify(listResult, null, 2)}`);
                
                log('api-info', '测试2: JSON预览API...');
                const previewResponse = await fetch(`/attachments/${TEST_ENTRY_ID}?json_preview=true`);
                log('api-info', `预览API状态: ${previewResponse.status}`, previewResponse.ok ? 'success' : 'error');
                log('api-info', `预览API Content-Type: ${previewResponse.headers.get('content-type')}`);
                
                if (previewResponse.ok) {
                    const previewResult = await previewResponse.json();
                    log('api-info', `预览API成功标志: ${previewResult.success}`, previewResult.success ? 'success' : 'error');
                    log('api-info', `预览API文件名: ${previewResult.filename}`);
                    log('api-info', `预览API内容类型: ${previewResult.content_type}`);
                    log('api-info', `预览API文件大小: ${previewResult.size} bytes`);
                    log('api-info', `预览API Base64长度: ${previewResult.file_content ? previewResult.file_content.length : 0} 字符`);
                } else {
                    const errorText = await previewResponse.text();
                    log('api-info', `预览API错误响应: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log('api-info', `API测试失败: ${error.message}`, 'error');
                log('api-info', `错误类型: ${error.constructor.name}`);
                log('api-info', `错误详情: ${JSON.stringify({
                    message: error.message,
                    name: error.name,
                    stack: error.stack
                }, null, 2)}`);
            }
        }
        
        async function testPreviewFunction() {
            clearLog('preview-info');
            log('preview-info', '=== 完整预览功能测试 ===');
            
            try {
                log('preview-info', '步骤1: 开始预览附件...');
                log('preview-info', `测试记录ID: ${TEST_ENTRY_ID}`);
                
                log('preview-info', '步骤2: 发送API请求...');
                const response = await fetch(`/attachments/${TEST_ENTRY_ID}?json_preview=true`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                log('preview-info', `步骤3: API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`获取附件失败: ${response.status} ${response.statusText}`);
                }
                
                log('preview-info', '步骤4: 解析JSON响应...');
                const result = await response.json();
                log('preview-info', `JSON解析成功: ${result.success}`, result.success ? 'success' : 'error');
                
                if (!result.success) {
                    throw new Error(result.error || '获取附件失败');
                }
                
                log('preview-info', '步骤5: 创建预览模态框...');
                createPreviewModal(result);
                log('preview-info', '预览模态框创建成功！', 'success');
                
                log('preview-info', '=== 预览功能测试完成 ===', 'success');
                
            } catch (error) {
                log('preview-info', `预览功能测试失败: ${error.message}`, 'error');
                log('preview-info', `错误详情: ${JSON.stringify({
                    message: error.message,
                    stack: error.stack,
                    entryId: TEST_ENTRY_ID
                }, null, 2)}`, 'error');
                
                // 显示更详细的错误信息
                const errorMessage = error.message.includes('Failed to fetch') 
                    ? '网络连接失败，请检查服务器是否正常运行'
                    : `预览附件失败: ${error.message}`;
                
                log('preview-info', `用户友好错误信息: ${errorMessage}`, 'error');
            }
        }
        
        function createPreviewModal(result) {
            // 移除现有的预览模态框
            const existingModal = document.getElementById('debug-preview-modal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 创建预览模态框
            const modal = document.createElement('div');
            modal.id = 'debug-preview-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.75);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; max-width: 90vw; max-height: 90vh; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; border-bottom: 1px solid #ddd; background: #f8f9fa;">
                        <h3 style="margin: 0; font-size: 18px; color: #333;">🖼️ 附件预览测试 - ${result.filename}</h3>
                        <button onclick="document.getElementById('debug-preview-modal').remove()" 
                                style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 8px 12px; cursor: pointer; font-size: 16px;">关闭</button>
                    </div>
                    <div style="padding: 20px; max-height: 70vh; overflow: auto; text-align: center;">
                        <div style="margin-bottom: 15px; font-size: 14px; color: #666;">
                            <strong>文件信息:</strong><br>
                            类型: ${result.content_type}<br>
                            大小: ${result.size} bytes<br>
                            Base64长度: ${result.file_content.length} 字符
                        </div>
                        <img src="data:${result.content_type};base64,${result.file_content}" 
                             style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px;" 
                             alt="附件预览"
                             onload="console.log('✅ 图片加载成功')"
                             onerror="console.error('❌ 图片加载失败')">
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
            
            console.log('✅ 预览模态框已创建并添加到页面');
        }
        
        function logToConsole() {
            console.log('=== 调试信息输出到控制台 ===');
            console.log('测试记录ID:', TEST_ENTRY_ID);
            console.log('当前页面URL:', window.location.href);
            console.log('Fetch函数:', fetch);
            console.log('测试API调用...');
            
            fetch(`/attachments/${TEST_ENTRY_ID}?json_preview=true`)
                .then(response => {
                    console.log('✅ API响应:', response);
                    return response.json();
                })
                .then(result => {
                    console.log('✅ API数据:', result);
                })
                .catch(error => {
                    console.error('❌ API调用失败:', error);
                });
        }
        
        // 页面加载时自动检查环境
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkEnvironment();
                console.log('🔧 调试页面已加载，请使用各个测试按钮进行诊断');
            }, 500);
        });
        
        // 监听所有未捕获的错误
        window.addEventListener('error', function(e) {
            console.error('🚨 页面错误:', e.error);
            log('preview-info', `页面错误: ${e.error.message}`, 'error');
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(e) {
            console.error('🚨 未处理的Promise拒绝:', e.reason);
            log('preview-info', `Promise拒绝: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>

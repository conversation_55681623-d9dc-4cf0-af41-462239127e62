#!/usr/bin/env python3
"""
检查仪表盘数据详情
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加backend路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

from database import DatabaseManager

def check_dashboard_data():
    """检查仪表盘数据详情"""
    
    print("🔍 检查仪表盘数据详情")
    print("=" * 60)
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("backend/goldenledger_accounting.db")
        
        # 1. 检查所有仕訳记录
        print("\n📋 步骤1: 检查所有仕訳记录")
        entries = db.get_journal_entries('default')
        print(f"总记录数: {len(entries)}")
        
        current_month = datetime.now().strftime('%Y-%m')
        print(f"当前月份: {current_month}")
        
        for i, entry in enumerate(entries):
            entry_date = entry.get('entry_date', '')
            entry_month = entry_date[:7] if len(entry_date) >= 7 else 'N/A'
            is_current_month = entry_month == current_month
            
            print(f"\n记录 {i+1}:")
            print(f"  ID: {entry.get('id')}")
            print(f"  日期: {entry_date} {'✅ 本月' if is_current_month else '❌ 非本月'}")
            print(f"  描述: {entry.get('description', 'N/A')}")
            print(f"  借方科目: {entry.get('debit_account', 'N/A')}")
            print(f"  贷方科目: {entry.get('credit_account', 'N/A')}")
            print(f"  金额: ¥{entry.get('amount', 0)}")
        
        # 2. 检查科目表
        print(f"\n📊 步骤2: 检查科目表")
        subjects = db.get_account_subjects('default')
        
        for category, accounts in subjects.items():
            print(f"\n{category}:")
            for account in accounts:
                print(f"  - {account}")
        
        # 3. 手动计算财务数据
        print(f"\n💰 步骤3: 手动计算财务数据")
        
        # 本月收入（贷方科目包含"売上"的记录）
        monthly_revenue = 0
        revenue_entries = []
        
        for entry in entries:
            entry_date = entry.get('entry_date', '')
            entry_month = entry_date[:7] if len(entry_date) >= 7 else ''
            credit_account = entry.get('credit_account', '')
            
            if entry_month == current_month and '売上' in credit_account:
                amount = float(entry.get('amount', 0))
                monthly_revenue += amount
                revenue_entries.append(entry)
        
        print(f"本月收入计算:")
        print(f"  匹配条件: 贷方科目包含'売上' AND 日期为{current_month}")
        print(f"  匹配记录数: {len(revenue_entries)}")
        print(f"  总收入: ¥{monthly_revenue:,}")
        
        # 本月支出（借方科目为费用类的记录）
        monthly_expenses = 0
        expense_entries = []
        expense_accounts = subjects.get('費用', [])
        
        for entry in entries:
            entry_date = entry.get('entry_date', '')
            entry_month = entry_date[:7] if len(entry_date) >= 7 else ''
            debit_account = entry.get('debit_account', '')
            
            if entry_month == current_month and debit_account in expense_accounts:
                amount = float(entry.get('amount', 0))
                monthly_expenses += amount
                expense_entries.append(entry)
        
        print(f"\n本月支出计算:")
        print(f"  匹配条件: 借方科目在费用类 AND 日期为{current_month}")
        print(f"  费用科目: {expense_accounts}")
        print(f"  匹配记录数: {len(expense_entries)}")
        print(f"  总支出: ¥{monthly_expenses:,}")
        
        # 4. 检查AI统计
        print(f"\n🤖 步骤4: 检查AI统计")
        ai_stats = db.get_ai_stats('default')
        print(f"AI统计数据:")
        for key, value in ai_stats.items():
            print(f"  {key}: {value}")
        
        # 5. 建议
        print(f"\n💡 步骤5: 数据建议")
        
        if monthly_revenue == 0 and monthly_expenses == 0:
            print("⚠️ 本月财务数据为0的可能原因:")
            print("  1. 所有记录都不是本月的")
            print("  2. 科目名称不匹配查询条件")
            print("  3. 需要添加本月的收入和支出记录")
            
            # 检查是否有本月记录
            current_month_entries = [e for e in entries if e.get('entry_date', '')[:7] == current_month]
            if len(current_month_entries) == 0:
                print(f"\n📅 建议: 添加{current_month}的记录")
            else:
                print(f"\n📅 本月有{len(current_month_entries)}条记录，但科目不匹配")
                print("建议检查科目设置或添加收入/支出记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    success = check_dashboard_data()
    if success:
        print("\n🎯 数据检查完成！")
    else:
        print("\n❌ 检查失败")

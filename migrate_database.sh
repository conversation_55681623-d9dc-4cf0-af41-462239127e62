#!/bin/bash

# GoldenLedger 数据库迁移脚本
# 用于在 Cloudflare D1 数据库中创建必要的表结构

set -e  # 遇到错误时退出

echo "🗄️ 开始数据库迁移..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查 wrangler 是否已安装
if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ Wrangler CLI 未安装${NC}"
    echo "请运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录 Cloudflare
echo -e "${BLUE}🔐 检查 Cloudflare 认证...${NC}"
if ! wrangler whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️  需要登录 Cloudflare${NC}"
    echo "请运行: wrangler login"
    exit 1
fi

echo -e "${GREEN}✅ Cloudflare 认证通过${NC}"

# 执行数据库迁移
echo -e "${BLUE}📊 执行数据库迁移...${NC}"

# 检查数据库是否存在
echo -e "${BLUE}🔍 检查数据库连接...${NC}"
if wrangler d1 execute goldenledger-db --command "SELECT name FROM sqlite_master WHERE type='table';" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 数据库连接成功${NC}"
else
    echo -e "${RED}❌ 无法连接到数据库 goldenledger-db${NC}"
    echo "请确认数据库名称是否正确"
    exit 1
fi

# 执行迁移脚本
echo -e "${BLUE}🚀 执行迁移脚本...${NC}"
if wrangler d1 execute goldenledger-db --file=database_migration.sql; then
    echo -e "${GREEN}✅ 数据库迁移成功完成${NC}"
else
    echo -e "${RED}❌ 数据库迁移失败${NC}"
    exit 1
fi

# 验证表是否创建成功
echo -e "${BLUE}🔍 验证表结构...${NC}"
echo "检查已创建的表:"
wrangler d1 execute goldenledger-db --command "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"

echo ""
echo -e "${GREEN}🎉 数据库迁移完成！${NC}"
echo ""
echo -e "${BLUE}📋 已创建的表:${NC}"
echo -e "  📄 users - 用户信息表"
echo -e "  📄 user_subscriptions - 用户订阅表"
echo -e "  📄 payment_records - 支付记录表"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo -e "  现在可以测试 PayPal 支付功能了"
echo -e "  支付成功后，用户信息将自动保存到数据库"
echo ""
echo -e "${GREEN}✨ 迁移脚本执行完成！${NC}"

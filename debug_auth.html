<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">🔍 认证状态调试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- localStorage 状态 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">📦 localStorage 状态</h2>
                <div id="localStorageInfo" class="space-y-2 text-sm font-mono"></div>
            </div>
            
            <!-- API 验证 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🔐 API 验证</h2>
                <button onclick="testAuth()" class="bg-blue-500 text-white px-4 py-2 rounded mb-4">测试认证</button>
                <div id="authTestResult" class="text-sm font-mono"></div>
            </div>
            
            <!-- 页面路径检查 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🛣️ 页面路径检查</h2>
                <div id="pathInfo" class="space-y-2 text-sm font-mono"></div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🔧 操作</h2>
                <div class="space-y-2">
                    <button onclick="clearAuth()" class="bg-red-500 text-white px-4 py-2 rounded block">清除认证数据</button>
                    <button onclick="goToMasterDashboard()" class="bg-green-500 text-white px-4 py-2 rounded block">访问 Master Dashboard</button>
                    <button onclick="goToGoogleLogin()" class="bg-blue-500 text-white px-4 py-2 rounded block">Google 登录</button>
                </div>
            </div>
        </div>
    </div>

    <script src="api_config.js"></script>
    <script>
        // 显示 localStorage 信息
        function displayLocalStorageInfo() {
            const container = document.getElementById('localStorageInfo');
            const keys = [
                'goldenledger_session_token',
                'session_token',
                'goldenledger_user',
                'goldenledger_login_attempts',
                'goldenledger_lockout_until'
            ];
            
            let html = '';
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                const status = value ? '✅' : '❌';
                html += `<div>${status} <strong>${key}:</strong> ${value ? (key.includes('user') ? JSON.stringify(JSON.parse(value), null, 2) : value) : 'null'}</div>`;
            });
            
            container.innerHTML = html;
        }
        
        // 显示页面路径信息
        function displayPathInfo() {
            const container = document.getElementById('pathInfo');
            const info = {
                'pathname': window.location.pathname,
                'search': window.location.search,
                'hash': window.location.hash,
                'href': window.location.href
            };
            
            let html = '';
            Object.entries(info).forEach(([key, value]) => {
                html += `<div><strong>${key}:</strong> ${value || 'empty'}</div>`;
            });
            
            container.innerHTML = html;
        }
        
        // 测试认证
        async function testAuth() {
            const container = document.getElementById('authTestResult');
            container.innerHTML = '🔄 测试中...';
            
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    container.innerHTML = '❌ 没有找到 session token';
                    return;
                }
                
                // 等待 API 配置加载
                let retries = 0;
                while (!window.GoldenLedgerAPI && retries < 10) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    retries++;
                }
                
                if (!window.GoldenLedgerAPI) {
                    container.innerHTML = '❌ API 配置未加载';
                    return;
                }
                
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/verify'), {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success && result.authenticated) {
                    container.innerHTML = `✅ 认证成功<br>用户: ${result.user.name || result.user.username}<br>邮箱: ${result.user.email}`;
                } else {
                    container.innerHTML = `❌ 认证失败<br>错误: ${result.error || '未知错误'}`;
                }
            } catch (error) {
                container.innerHTML = `❌ 请求失败<br>错误: ${error.message}`;
            }
        }
        
        // 清除认证数据
        function clearAuth() {
            const keys = [
                'goldenledger_session_token',
                'session_token',
                'goldenledger_user',
                'goldenledger_login_attempts',
                'goldenledger_lockout_until'
            ];
            
            keys.forEach(key => localStorage.removeItem(key));
            alert('认证数据已清除');
            displayLocalStorageInfo();
        }
        
        // 跳转到 Master Dashboard
        function goToMasterDashboard() {
            window.location.href = '/master_dashboard.html';
        }
        
        // 跳转到 Google 登录
        function goToGoogleLogin() {
            window.location.href = '/auth/login.html';
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayLocalStorageInfo();
            displayPathInfo();
            
            // 每5秒刷新一次状态
            setInterval(() => {
                displayLocalStorageInfo();
                displayPathInfo();
            }, 5000);
        });
    </script>
</body>
</html>

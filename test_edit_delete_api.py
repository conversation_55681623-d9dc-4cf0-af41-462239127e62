#!/usr/bin/env python3
"""
测试编辑和删除API功能
"""
import requests
import json

def test_edit_delete_api():
    """测试编辑和删除API"""
    
    print("🧪 测试编辑和删除API功能")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. 获取现有记录列表
    print("\n📋 步骤1: 获取现有记录列表")
    try:
        response = requests.get(f"{base_url}/journal-entries/default")
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ 获取到 {len(entries)} 条记录")
            
            if len(entries) > 0:
                # 选择第一条记录进行测试
                test_entry = entries[0]
                entry_id = test_entry['id']
                print(f"📝 选择记录进行测试: {entry_id}")
                print(f"   原始描述: {test_entry.get('description', 'N/A')}")
                print(f"   原始金额: {test_entry.get('amount', 'N/A')}")
                
                # 2. 测试编辑记录
                print(f"\n✏️ 步骤2: 测试编辑记录 {entry_id}")
                
                # 准备编辑数据
                edit_data = {
                    "entry_date": test_entry.get('entry_date', '2025-07-11'),
                    "entry_time": test_entry.get('entry_time', '12:00:00'),
                    "description": f"【已编辑】{test_entry.get('description', '测试记录')}",
                    "debit_account": test_entry.get('debit_account', '事務用品費'),
                    "credit_account": test_entry.get('credit_account', '現金'),
                    "amount": float(test_entry.get('amount', 1000)) + 100,  # 增加100
                    "reference_number": f"EDIT-{test_entry.get('reference_number', '')}"
                }
                
                # 发送编辑请求
                edit_response = requests.put(f"{base_url}/journal-entries/{entry_id}", json=edit_data)
                
                if edit_response.status_code == 200:
                    edit_result = edit_response.json()
                    print(f"✅ 编辑成功: {edit_result.get('message')}")
                    
                    # 验证编辑结果
                    verify_response = requests.get(f"{base_url}/journal-entries/default")
                    if verify_response.status_code == 200:
                        updated_entries = verify_response.json()
                        updated_entry = next((e for e in updated_entries if e['id'] == entry_id), None)
                        
                        if updated_entry:
                            print(f"✅ 编辑验证成功:")
                            print(f"   新描述: {updated_entry.get('description')}")
                            print(f"   新金额: {updated_entry.get('amount')}")
                            print(f"   新参考号: {updated_entry.get('reference_number')}")
                        else:
                            print("❌ 编辑验证失败: 找不到更新的记录")
                else:
                    print(f"❌ 编辑失败: {edit_response.status_code} - {edit_response.text}")
                
                # 3. 测试删除记录（可选，注释掉以保留数据）
                print(f"\n🗑️ 步骤3: 测试删除记录功能（仅测试API，不实际删除）")
                
                # 创建一个测试记录用于删除
                print("📝 创建测试记录用于删除测试...")
                test_record = {
                    "id": "TEST_DELETE_001",
                    "company_id": "default",
                    "entry_date": "2025-07-11",
                    "entry_time": "23:59:59",
                    "description": "【测试删除】临时记录",
                    "debit_account": "测试科目",
                    "credit_account": "现金",
                    "amount": 1.0,
                    "reference_number": "DELETE_TEST",
                    "ai_generated": False
                }
                
                # 保存测试记录
                save_response = requests.post(f"{base_url}/journal-entries/save", json=test_record)
                if save_response.status_code == 200:
                    print("✅ 测试记录创建成功")
                    
                    # 删除测试记录
                    delete_response = requests.delete(f"{base_url}/journal-entries/TEST_DELETE_001")
                    
                    if delete_response.status_code == 200:
                        delete_result = delete_response.json()
                        print(f"✅ 删除成功: {delete_result.get('message')}")
                        
                        # 验证删除结果
                        verify_delete_response = requests.get(f"{base_url}/journal-entries/default")
                        if verify_delete_response.status_code == 200:
                            final_entries = verify_delete_response.json()
                            deleted_entry = next((e for e in final_entries if e['id'] == 'TEST_DELETE_001'), None)
                            
                            if deleted_entry is None:
                                print("✅ 删除验证成功: 记录已被删除")
                            else:
                                print("❌ 删除验证失败: 记录仍然存在")
                    else:
                        print(f"❌ 删除失败: {delete_response.status_code} - {delete_response.text}")
                else:
                    print("❌ 测试记录创建失败")
                
                print("\n🎉 编辑和删除API测试完成！")
                return True
                
            else:
                print("❌ 没有找到可测试的记录")
                return False
        else:
            print(f"❌ 获取记录失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_edit_delete_api()
    if success:
        print("\n🎯 编辑和删除功能测试通过！")
    else:
        print("\n❌ 测试失败，请检查错误信息")

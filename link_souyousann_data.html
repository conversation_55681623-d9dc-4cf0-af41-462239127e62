<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关联souyousann用户数据</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-600">🔗 关联souyousann用户数据</h1>
        
        <!-- 任务说明 -->
        <div class="bg-blue-100 border-l-4 border-blue-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        <strong>任务:</strong> 将users表中的souyousann用户数据与journal_entries表关联，并添加email字段作为双保险
                    </p>
                </div>
            </div>
        </div>

        <!-- 用户信息显示 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- souyousann用户信息 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">👤 souyousann用户信息</h3>
                <div id="souyousann-info" class="text-sm">
                    <p class="text-gray-500">加载中...</p>
                </div>
            </div>

            <!-- Journal Entries统计 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">📊 Journal Entries统计</h3>
                <div id="entries-stats" class="text-sm">
                    <p class="text-gray-500">加载中...</p>
                </div>
            </div>
        </div>

        <!-- 操作步骤 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🛠️ 关联步骤</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="step1()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    步骤1: 查找souyousann用户
                </button>
                <button onclick="step2()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    步骤2: 添加email字段
                </button>
                <button onclick="step3()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    步骤3: 关联所有数据
                </button>
                <button onclick="step4()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    步骤4: 验证关联结果
                </button>
            </div>
        </div>

        <!-- 一键关联 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">⚡ 一键关联</h2>
            <button onclick="linkAllSouyousannData()" class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                执行完整souyousann数据关联流程
            </button>
        </div>

        <!-- 关联预览 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🔍 关联预览</h2>
            <div id="link-preview" class="text-sm">
                <p class="text-gray-500">点击上方按钮开始关联预览...</p>
            </div>
        </div>

        <!-- 操作结果 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 操作结果</h2>
            <div id="results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始操作...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        let souyousannUser = null;

        function addResult(title, success, message, data = null) {
            const container = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
                ${data ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto max-h-32 overflow-y-auto">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 步骤1: 查找souyousann用户
        async function step1() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/find-user`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        search_term: 'souyousann'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    souyousannUser = result.user;
                    addResult('查找souyousann用户', true, '用户查找成功', result.user);
                    displaySouyousannInfo(result.user);
                } else {
                    addResult('查找souyousann用户', false, result.error || '用户查找失败', result);
                }
            } catch (error) {
                addResult('查找souyousann用户', false, error.message);
            }
        }

        // 步骤2: 添加email字段
        async function step2() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/add-email-field`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        table: 'journal_entries'
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('添加email字段', true, 'email字段添加成功', result);
                } else {
                    addResult('添加email字段', false, result.error || '字段添加失败', result);
                }
            } catch (error) {
                addResult('添加email字段', false, error.message);
            }
        }

        // 步骤3: 关联所有数据
        async function step3() {
            if (!souyousannUser) {
                addResult('关联数据', false, '请先执行步骤1查找用户');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/database/link-souyousann-data`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: souyousannUser.id,
                        user_name: souyousannUser.name || souyousannUser.username,
                        user_email: souyousannUser.email
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('关联souyousann数据', true, `成功关联 ${result.updated_count} 条记录`, result);
                    updateLinkPreview(result);
                } else {
                    addResult('关联souyousann数据', false, result.error || '数据关联失败', result);
                }
            } catch (error) {
                addResult('关联souyousann数据', false, error.message);
            }
        }

        // 步骤4: 验证关联结果
        async function step4() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/database/verify-souyousann-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('验证关联结果', true, '数据关联验证通过', result.verification);
                    displayEntriesStats(result.verification);
                } else {
                    addResult('验证关联结果', false, result.error || '验证失败', result);
                }
            } catch (error) {
                addResult('验证关联结果', false, error.message);
            }
        }

        // 一键关联所有souyousann数据
        async function linkAllSouyousannData() {
            addResult('开始关联', true, '开始执行完整souyousann数据关联流程...');
            
            // 按顺序执行所有步骤
            await step1();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step2();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step3();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await step4();
            
            addResult('关联完成', true, 'souyousann用户数据关联完成，包含双保险email字段');
            
            // 提示用户下一步操作
            setTimeout(() => {
                if (confirm('数据关联完成！是否立即查看journal_entries页面？')) {
                    window.open('/journal_entries', '_blank');
                }
            }, 2000);
        }

        // 显示souyousann用户信息
        function displaySouyousannInfo(user) {
            const container = document.getElementById('souyousann-info');
            if (user) {
                container.innerHTML = `
                    <div class="space-y-2">
                        <div class="p-2 bg-blue-50 rounded">
                            <div><strong>用户ID:</strong> ${user.id}</div>
                            <div><strong>用户名:</strong> ${user.username || user.name}</div>
                            <div><strong>姓名:</strong> ${user.name || '未设置'}</div>
                            <div><strong>邮箱:</strong> ${user.email}</div>
                            <div><strong>创建时间:</strong> ${user.created_at}</div>
                        </div>
                    </div>
                `;
            } else {
                container.innerHTML = '<p class="text-red-500">未找到souyousann用户</p>';
            }
        }

        // 显示entries统计
        function displayEntriesStats(stats) {
            const container = document.getElementById('entries-stats');
            if (stats) {
                container.innerHTML = `
                    <div class="space-y-2">
                        <div class="p-2 bg-green-50 rounded">
                            <div><strong>总记录数:</strong> ${stats.total_entries}</div>
                            <div><strong>已关联用户ID:</strong> ${stats.entries_with_user_id}</div>
                            <div><strong>已关联邮箱:</strong> ${stats.entries_with_email}</div>
                            <div><strong>关联完成率:</strong> ${stats.completion_rate}%</div>
                            <div><strong>souyousann记录:</strong> ${stats.souyousann_entries}</div>
                        </div>
                    </div>
                `;
            }
        }

        // 更新关联预览
        function updateLinkPreview(result) {
            const container = document.getElementById('link-preview');
            if (result && result.sample_entries) {
                let html = '<div class="space-y-2"><h4 class="font-semibold mb-2">关联后的记录示例:</h4>';
                result.sample_entries.slice(0, 3).forEach(entry => {
                    html += `
                        <div class="p-2 bg-gray-50 rounded text-xs">
                            <div><strong>记录ID:</strong> ${entry.id}</div>
                            <div><strong>描述:</strong> ${entry.description}</div>
                            <div><strong>用户ID:</strong> <span class="text-green-600">${entry.user_id}</span></div>
                            <div><strong>用户名:</strong> <span class="text-green-600">${entry.user_name}</span></div>
                            <div><strong>邮箱:</strong> <span class="text-blue-600">${entry.user_email}</span></div>
                            <div><strong>金额:</strong> ¥${entry.amount}</div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            addResult('系统检查', true, '准备关联souyousann用户数据到journal_entries表');
            
            // 自动查找用户
            setTimeout(() => {
                step1();
            }, 1000);
        });
    </script>
</body>
</html>

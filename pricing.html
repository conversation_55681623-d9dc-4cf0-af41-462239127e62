<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>料金プラン - GoldenLedger AI記帳システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- User Status Component -->
    <link rel="stylesheet" href="components/UserStatusComponent.css?v=3.9.7">
    <script src="components/UserStatusComponent.js?v=3.9.7"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .plan-card {
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .popular-badge {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            /* The following properties are for positioning the badge without relying on a relative parent */
            display: block;
            width: -moz-fit-content;
            width: fit-content;
            margin: -2.5rem auto 1rem; /* Adjust top margin to pull it up */
            transform: translateY(-50%);
        }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- ヘッダー -->
    <header class="gradient-bg text-white py-16">
        <div class="container mx-auto px-6 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                <i class="fas fa-coins mr-3"></i>料金プラン
            </h1>
            <p class="text-xl opacity-90 max-w-2xl mx-auto">
                あなたのビジネスに最適なプランを選択してください。<br>
                すべてのプランで7日間の無料トライアルをご利用いただけます。
            </p>
        </div>
    </header>

    <!-- 料金プラン -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8 max-w-7xl mx-auto">
                
                <!-- フリープラン -->
                <div class="plan-card bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-200">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">フリープラン</h3>
                        <div class="text-4xl font-bold text-gray-600 mb-2">¥0</div>
                        <div class="text-gray-500">永続無料</div>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>AI記帳：月10回まで</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>データ保存：30日間</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>基本的な記帳機能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>簡単なレポート</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times mr-3"></i>
                            <span>PDFエクスポート</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times mr-3"></i>
                            <span>OCR機能</span>
                        </li>
                    </ul>
                    
                    <button class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        現在のプラン
                    </button>
                </div>

                <!-- ベーシックプラン -->
                <div class="plan-card bg-white rounded-2xl shadow-lg p-8 border-2 border-blue-200">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-blue-600 mb-2">ベーシックプラン</h3>
                        <div class="text-4xl font-bold text-blue-600 mb-2">¥980</div>
                        <div class="text-gray-500">月額（税込）</div>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>AI記帳：月100回まで</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>データ保存：1年間</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>PDFエクスポート</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>基本AI分析</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>メールサポート</span>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="fas fa-times mr-3"></i>
                            <span>OCR機能</span>
                        </li>
                    </ul>
                    
                    <button onclick="startTrial('basic')" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        7日間無料トライアル
                    </button>

                    <!-- 内测提示 -->
                    <div class="mt-4 bg-red-50 border-2 border-red-200 rounded-lg p-3 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                            <span class="text-red-700 font-bold text-sm">⚠️ 重要なお知らせ</span>
                        </div>
                        <p class="text-red-600 text-xs font-medium">
                            現在ベータテスト中です。お支払いはしないでください。お支払いもできません。
                        </p>
                    </div>

                    <div id="paypal-basic" class="mt-4"></div>
                </div>

                <!-- プロプラン（人気） -->
                <div class="plan-card bg-white rounded-2xl shadow-xl p-8 border-2 border-purple-300">
                    <div class="popular-badge text-white px-6 py-2 rounded-full text-sm font-bold">
                        人気No.1
                    </div>
                    
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-purple-600 mb-2">プロプラン</h3>
                        <div class="text-4xl font-bold text-purple-600 mb-2">¥2,980</div>
                        <div class="text-gray-500">月額（税込）</div>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span><strong>AI記帳：無制限</strong></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>データ保存：無制限</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>OCR領収書認識</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>複数会社管理</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>高度な分析機能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>優先サポート</span>
                        </li>
                    </ul>
                    
                    <button onclick="startTrial('pro')" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        7日間無料トライアル
                    </button>

                    <!-- 内测提示 -->
                    <div class="mt-4 bg-red-50 border-2 border-red-200 rounded-lg p-3 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                            <span class="text-red-700 font-bold text-sm">⚠️ 重要なお知らせ</span>
                        </div>
                        <p class="text-red-600 text-xs font-medium">
                            現在ベータテスト中です。お支払いはしないでください。お支払いもできません。
                        </p>
                    </div>

                    <div id="paypal-pro" class="mt-4"></div>
                </div>

                <!-- エンタープライズプラン -->
                <div class="plan-card bg-white rounded-2xl shadow-lg p-8 border-2 border-gray-800">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">エンタープライズ</h3>
                        <div class="text-4xl font-bold text-gray-800 mb-2">¥9,800</div>
                        <div class="text-gray-500">月額（税込）</div>
                    </div>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>プロプランの全機能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>マルチユーザー対応</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>API アクセス</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>カスタム統合</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>専任カスタマーマネージャー</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>SLA保証</span>
                        </li>
                    </ul>
                    
                    <button onclick="contactSales()" class="w-full bg-gray-800 hover:bg-gray-900 text-white py-3 rounded-lg font-semibold transition-colors">
                        お問い合わせ
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12">よくある質問</h2>
            
            <div class="max-w-3xl mx-auto space-y-6">
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="font-semibold mb-2">無料トライアルはありますか？</h3>
                    <p class="text-gray-600">はい、すべての有料プランで7日間の無料トライアルをご利用いただけます。クレジットカード情報は必要ありません。</p>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="font-semibold mb-2">プランの変更はいつでもできますか？</h3>
                    <p class="text-gray-600">はい、いつでもプランのアップグレードやダウングレードが可能です。変更は次の請求サイクルから適用されます。</p>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="font-semibold mb-2">データのセキュリティは大丈夫ですか？</h3>
                    <p class="text-gray-600">はい、すべてのデータは暗号化され、日本国内のセキュアなサーバーで管理されています。GDPR、個人情報保護法に完全準拠しています。</p>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-6">
                    <h3 class="font-semibold mb-2">解約はいつでもできますか？</h3>
                    <p class="text-gray-600">はい、いつでも解約可能です。解約後も現在の請求期間終了まではサービスをご利用いただけます。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA -->
    <section class="gradient-bg text-white py-16">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl font-bold mb-4">今すぐ始めましょう</h2>
            <p class="text-xl opacity-90 mb-8">7日間の無料トライアルで、AI記帳の威力を体験してください</p>
            <button onclick="startTrial('pro')" class="bg-white text-purple-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors">
                無料トライアルを開始
            </button>
        </div>
    </section>

    <script src="api_config.js?v=3.0.3"></script>
    <!-- PayPal JS SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id=AWsbCScHx90XhQyHDUIKLLm6VJyFggiW1ZvScLvATwxZ0wgShnNwxYZU2jfgs20AY8LHaSCPxOzQZh8i&currency=JPY" data-sdk-integration-source="button-factory"></script>
    <script>
        // Global scope functions for onclick handlers
        async function startTrial(planId) {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    alert('トライアルを開始するにはログインが必要です');
                    window.location.href = '/auth/login.html?return=' + encodeURIComponent('/pricing.html');
                    return;
                }
                const response = await fetch(window.GoldenLedgerAPI.url('/api/subscription/trial'), {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
                    body: JSON.stringify({ plan_id: planId, trial_days: 7 })
                });
                const result = await response.json();
                if (result.success) {
                    alert(`${planId}プランの7日間無料トライアルが開始されました！`);
                    window.location.href = '/master_dashboard.html';
                } else {
                    alert('トライアル開始に失敗しました: ' + result.error);
                }
            } catch (error) {
                console.error('Trial start error:', error);
                alert('エラーが発生しました。しばらく後でお試しください。');
            }
        }

        function contactSales() {
            window.location.href = 'mailto:<EMAIL>?subject=エンタープライズプランについて';
        }

        document.addEventListener('DOMContentLoaded', function() {
            // API設定チェック
            console.log('🔍 API設定が読み込まれました:', window.GoldenLedgerAPI?.baseURL);

            async function checkCurrentPlan() {
                try {
                    const token = localStorage.getItem('goldenledger_session_token');
                    if (!token) return;
                    const response = await fetch(window.GoldenLedgerAPI.url('/api/subscription/status'), {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    const result = await response.json();
                    if (result.success && result.subscription) {
                        updatePlanButtons(result.subscription.plan_id, result.subscription.status);
                    }
                } catch (error) {
                    console.error('Failed to check current plan:', error);
                }
            }

            function updatePlanButtons(currentPlan, status) {
                document.querySelectorAll('button[onclick^="startTrial"]').forEach(button => {
                    const planId = button.getAttribute('onclick').match(/startTrial\('(\w+)'\)/)[1];
                    if (planId === currentPlan) {
                        button.textContent = status === 'trial' ? 'トライアル中' : '現在のプラン';
                        button.className = button.className.replace(/bg-\w+-600/, status === 'trial' ? 'bg-orange-600' : 'bg-green-600');
                        button.disabled = true;
                    }
                });
            }

            // ログインページにリダイレクト
            function redirectToLogin() {
                const currentUrl = encodeURIComponent(window.location.href);
                window.location.href = `/login_simple.html?return=${currentUrl}`;
            }

            function renderPayPalButtons() {
                if (typeof paypal === 'undefined') {
                    console.error('PayPal SDK not loaded.');
                    return;
                }

                // ログイン状態をチェック
                const token = localStorage.getItem('goldenledger_session_token');
                const user = localStorage.getItem('goldenledger_user');

                if (!token || !user) {
                    // 未ログイン、PayPalボタンの代わりにログイン案内を表示
                    document.getElementById('paypal-basic').innerHTML = `
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mb-2"></i>
                            <p class="text-yellow-800 font-medium mb-3">購入するにはログインが必要です</p>
                            <button onclick="redirectToLogin()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                ログインして購入
                            </button>
                        </div>
                    `;

                    document.getElementById('paypal-pro').innerHTML = `
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mb-2"></i>
                            <p class="text-yellow-800 font-medium mb-3">購入するにはログインが必要です</p>
                            <button onclick="redirectToLogin()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                ログインして購入
                            </button>
                        </div>
                    `;
                    return;
                }
                
                // Basic Plan Button
                paypal.Buttons({
                    style: { layout: 'vertical', color: 'blue', shape: 'rect', label: 'paypal' },
                    createOrder: (data, actions) => actions.order.create({
                        purchase_units: [{ description: 'ベーシックプラン 月額', amount: { currency_code: 'JPY', value: '980' } }]
                    }),
                    onApprove: (data, actions) => actions.order.capture().then(details => {
                        console.log('Payment successful, sending to backend for verification.', details);
                        return fetch(window.GoldenLedgerAPI.url('/api/paypal/verify-payment'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + token
                            },
                            body: JSON.stringify({
                                orderID: details.id,
                                planId: 'basic'
                            })
                        }).then(res => res.json()).then(data => {
                            if (data.success) {
                                // プロフェッショナルな決済処理ページにリダイレクト
                                window.location.href = `/payment-success-processing.html?orderID=${data.order_id}&planId=basic`;
                            } else {
                                alert('決済認証に失敗しました: ' + (data.error || '不明なエラー'));
                            }
                        });
                    }),
                    onError: err => console.error('PayPal Basic Button Error:', err)
                }).render('#paypal-basic');

                // Pro Plan Button
                paypal.Buttons({
                    style: { layout: 'vertical', color: 'gold', shape: 'rect', label: 'paypal' }, // 公式サポートカラー gold に修正
                    createOrder: (data, actions) => actions.order.create({
                        purchase_units: [{ description: 'プロプラン 月額', amount: { currency_code: 'JPY', value: '2980' } }]
                    }),
                    onApprove: (data, actions) => actions.order.capture().then(details => {
                        console.log('Payment successful, sending to backend for verification.', details);
                        return fetch(window.GoldenLedgerAPI.url('/api/paypal/verify-payment'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + token
                            },
                            body: JSON.stringify({
                                orderID: details.id,
                                planId: 'pro'
                            })
                        }).then(res => res.json()).then(data => {
                            if (data.success) {
                                // プロフェッショナルな決済処理ページにリダイレクト
                                window.location.href = `/payment-success-processing.html?orderID=${data.order_id}&planId=pro`;
                            } else {
                                alert('決済認証に失敗しました: ' + (data.error || '不明なエラー'));
                            }
                        });
                    }),
                    onError: err => console.error('PayPal Pro Button Error:', err)
                }).render('#paypal-pro');
            }

            checkCurrentPlan();
            renderPayPalButtons();

            // ユーザーステータスコンポーネントを初期化
            if (typeof UserStatusComponent !== 'undefined' && UserStatusComponent.isLoggedIn()) {
                window.userStatus = new UserStatusComponent({
                    position: 'top-right', // 料金ページでは右上に配置
                    showRole: true,
                    showLoginTime: false, // 料金ページでは時間は非表示
                    autoHide: true,
                    hideDelay: 8000
                });
                console.log('👤 ユーザーステータスコンポーネント初期化完了 (料金ページ)');
            }
        });
    </script>
</body>
</html>

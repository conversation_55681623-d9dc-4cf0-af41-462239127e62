#!/usr/bin/env python3
"""
高级配置管理系统
支持动态配置、环境变量、配置验证等
"""
import os
import json
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConfigSource(Enum):
    """配置源类型"""
    FILE = "file"
    ENVIRONMENT = "environment"
    DATABASE = "database"
    REMOTE = "remote"
    DEFAULT = "default"

@dataclass
class ConfigItem:
    """配置项"""
    key: str
    value: Any
    source: ConfigSource
    description: str = ""
    is_sensitive: bool = False
    last_updated: str = ""
    
    def __post_init__(self):
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()

@dataclass
class DatabaseConfig:
    """数据库配置"""
    type: str = "sqlite"
    host: str = "localhost"
    port: int = 5432
    database: str = "goldenledger_accounting"
    username: str = ""
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20
    echo: bool = False

@dataclass
class AIModelConfig:
    """AI模型配置"""
    provider: str = "google"
    model_id: str = "gemini-pro"
    api_key: str = ""
    temperature: float = 0.1
    max_tokens: int = 4096
    timeout: int = 30
    retry_count: int = 3

@dataclass
class SystemConfig:
    """系统配置"""
    debug: bool = False
    log_level: str = "INFO"
    secret_key: str = ""
    cors_origins: List[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_interval: int = 6  # hours
    session_timeout: int = 3600  # seconds
    
    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["*"]

@dataclass
class WebSocketConfig:
    """WebSocket配置"""
    enabled: bool = True
    max_connections: int = 100
    heartbeat_interval: int = 30
    message_queue_size: int = 1000
    compression: bool = True

@dataclass
class SecurityConfig:
    """安全配置"""
    password_min_length: int = 8
    password_require_special: bool = True
    session_secure: bool = True
    rate_limit_enabled: bool = True
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600  # seconds
    encryption_algorithm: str = "AES-256-GCM"

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置项存储
        self.config_items: Dict[str, ConfigItem] = {}
        
        # 配置文件路径
        self.config_files = {
            "main": self.config_dir / "main.yaml",
            "database": self.config_dir / "database.yaml",
            "ai_models": self.config_dir / "ai_models.yaml",
            "security": self.config_dir / "security.yaml"
        }
        
        # 敏感配置键
        self.sensitive_keys = {
            "password", "api_key", "secret_key", "token", 
            "private_key", "certificate", "credentials"
        }
        
        # 加载配置
        self.load_all_configs()
    
    def load_all_configs(self):
        """加载所有配置"""
        try:
            # 1. 加载默认配置
            self._load_default_configs()
            
            # 2. 加载文件配置
            self._load_file_configs()
            
            # 3. 加载环境变量配置
            self._load_environment_configs()
            
            # 4. 验证配置
            self._validate_configs()
            
            logger.info(f"配置加载完成，共 {len(self.config_items)} 个配置项")
            
        except Exception as e:
            logger.error(f"配置加载失败: {str(e)}")
            raise
    
    def _load_default_configs(self):
        """加载默认配置"""
        defaults = {
            # 系统配置
            "system.debug": False,
            "system.log_level": "INFO",
            "system.secret_key": self._generate_secret_key(),
            "system.cors_origins": ["*"],
            "system.max_file_size": 10 * 1024 * 1024,
            "system.backup_interval": 6,
            "system.session_timeout": 3600,
            
            # 数据库配置
            "database.type": "sqlite",
            "database.host": "localhost",
            "database.port": 5432,
            "database.database": "data/databases/goldenledger_accounting.db",
            "database.pool_size": 10,
            "database.echo": False,
            "database.encryption_enabled": True,
            "database.backup_enabled": True,
            "database.audit_enabled": True,
            
            # AI模型配置
            "ai.default_provider": "google",
            "ai.default_model": "gemini-pro",
            "ai.temperature": 0.1,
            "ai.max_tokens": 4096,
            "ai.timeout": 30,
            "ai.retry_count": 3,
            
            # WebSocket配置
            "websocket.enabled": True,
            "websocket.max_connections": 100,
            "websocket.heartbeat_interval": 30,
            "websocket.message_queue_size": 1000,
            
            # 安全配置
            "security.password_min_length": 8,
            "security.password_require_special": True,
            "security.session_secure": True,
            "security.rate_limit_enabled": True,
            "security.rate_limit_requests": 100,
            "security.rate_limit_window": 3600,
        }
        
        for key, value in defaults.items():
            self.config_items[key] = ConfigItem(
                key=key,
                value=value,
                source=ConfigSource.DEFAULT,
                description=f"默认配置: {key}",
                is_sensitive=any(sensitive in key.lower() for sensitive in self.sensitive_keys)
            )
    
    def _load_file_configs(self):
        """加载文件配置"""
        for config_name, config_file in self.config_files.items():
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        if config_file.suffix == '.yaml' or config_file.suffix == '.yml':
                            data = yaml.safe_load(f)
                        else:
                            data = json.load(f)
                    
                    # 扁平化配置
                    flat_config = self._flatten_dict(data, prefix=config_name)
                    
                    for key, value in flat_config.items():
                        self.config_items[key] = ConfigItem(
                            key=key,
                            value=value,
                            source=ConfigSource.FILE,
                            description=f"文件配置: {config_file}",
                            is_sensitive=any(sensitive in key.lower() for sensitive in self.sensitive_keys)
                        )
                    
                    logger.info(f"加载配置文件: {config_file}")
                    
                except Exception as e:
                    logger.warning(f"加载配置文件失败 {config_file}: {str(e)}")
    
    def _load_environment_configs(self):
        """加载环境变量配置"""
        env_prefix = "GOLDENLEDGER_"
        
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower().replace('_', '.')
                
                # 尝试转换数据类型
                converted_value = self._convert_env_value(value)
                
                self.config_items[config_key] = ConfigItem(
                    key=config_key,
                    value=converted_value,
                    source=ConfigSource.ENVIRONMENT,
                    description=f"环境变量: {key}",
                    is_sensitive=any(sensitive in key.lower() for sensitive in self.sensitive_keys)
                )
        
        logger.info(f"加载环境变量配置: {len([k for k in os.environ.keys() if k.startswith(env_prefix)])} 个")
    
    def _validate_configs(self):
        """验证配置"""
        errors = []
        
        # 验证必需配置
        required_configs = [
            "system.secret_key",
            "database.type",
            "ai.default_provider"
        ]
        
        for required_key in required_configs:
            if required_key not in self.config_items or not self.config_items[required_key].value:
                errors.append(f"缺少必需配置: {required_key}")
        
        # 验证数据类型
        type_validations = {
            "system.debug": bool,
            "database.port": int,
            "ai.temperature": float,
            "websocket.max_connections": int
        }
        
        for key, expected_type in type_validations.items():
            if key in self.config_items:
                value = self.config_items[key].value
                if not isinstance(value, expected_type):
                    try:
                        # 尝试类型转换
                        converted_value = expected_type(value)
                        self.config_items[key].value = converted_value
                    except (ValueError, TypeError):
                        errors.append(f"配置类型错误 {key}: 期望 {expected_type.__name__}, 实际 {type(value).__name__}")
        
        if errors:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        if key in self.config_items:
            return self.config_items[key].value
        return default
    
    def set(self, key: str, value: Any, source: ConfigSource = ConfigSource.REMOTE, 
            description: str = "", persist: bool = False) -> bool:
        """设置配置值"""
        try:
            self.config_items[key] = ConfigItem(
                key=key,
                value=value,
                source=source,
                description=description or f"动态设置: {key}",
                is_sensitive=any(sensitive in key.lower() for sensitive in self.sensitive_keys)
            )
            
            if persist:
                self._persist_config(key, value)
            
            logger.info(f"配置已更新: {key}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置失败 {key}: {str(e)}")
            return False
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        return DatabaseConfig(
            type=self.get("database.type", "sqlite"),
            host=self.get("database.host", "localhost"),
            port=self.get("database.port", 5432),
            database=self.get("database.database", "goldenledger_accounting.db"),
            username=self.get("database.username", ""),
            password=self.get("database.password", ""),
            pool_size=self.get("database.pool_size", 10),
            max_overflow=self.get("database.max_overflow", 20),
            echo=self.get("database.echo", False)
        )
    
    def get_ai_model_config(self, provider: str = None) -> AIModelConfig:
        """获取AI模型配置"""
        provider = provider or self.get("ai.default_provider", "google")
        
        return AIModelConfig(
            provider=provider,
            model_id=self.get(f"ai.{provider}.model_id", self.get("ai.default_model", "gemini-pro")),
            api_key=self.get(f"ai.{provider}.api_key", self.get("ai.api_key", "")),
            temperature=self.get(f"ai.{provider}.temperature", self.get("ai.temperature", 0.1)),
            max_tokens=self.get(f"ai.{provider}.max_tokens", self.get("ai.max_tokens", 4096)),
            timeout=self.get(f"ai.{provider}.timeout", self.get("ai.timeout", 30)),
            retry_count=self.get(f"ai.{provider}.retry_count", self.get("ai.retry_count", 3))
        )
    
    def get_system_config(self) -> SystemConfig:
        """获取系统配置"""
        return SystemConfig(
            debug=self.get("system.debug", False),
            log_level=self.get("system.log_level", "INFO"),
            secret_key=self.get("system.secret_key", ""),
            cors_origins=self.get("system.cors_origins", ["*"]),
            max_file_size=self.get("system.max_file_size", 10 * 1024 * 1024),
            backup_interval=self.get("system.backup_interval", 6),
            session_timeout=self.get("system.session_timeout", 3600)
        )
    
    def get_websocket_config(self) -> WebSocketConfig:
        """获取WebSocket配置"""
        return WebSocketConfig(
            enabled=self.get("websocket.enabled", True),
            max_connections=self.get("websocket.max_connections", 100),
            heartbeat_interval=self.get("websocket.heartbeat_interval", 30),
            message_queue_size=self.get("websocket.message_queue_size", 1000),
            compression=self.get("websocket.compression", True)
        )
    
    def get_security_config(self) -> SecurityConfig:
        """获取安全配置"""
        return SecurityConfig(
            password_min_length=self.get("security.password_min_length", 8),
            password_require_special=self.get("security.password_require_special", True),
            session_secure=self.get("security.session_secure", True),
            rate_limit_enabled=self.get("security.rate_limit_enabled", True),
            rate_limit_requests=self.get("security.rate_limit_requests", 100),
            rate_limit_window=self.get("security.rate_limit_window", 3600),
            encryption_algorithm=self.get("security.encryption_algorithm", "AES-256-GCM")
        )
    
    def export_config(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """导出配置"""
        config_dict = {}
        
        for key, item in self.config_items.items():
            if item.is_sensitive and not include_sensitive:
                config_dict[key] = "***HIDDEN***"
            else:
                config_dict[key] = {
                    "value": item.value,
                    "source": item.source.value,
                    "description": item.description,
                    "last_updated": item.last_updated
                }
        
        return config_dict
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("重新加载配置...")
        self.config_items.clear()
        self.load_all_configs()
    
    def _flatten_dict(self, d: Dict[str, Any], prefix: str = "", separator: str = ".") -> Dict[str, Any]:
        """扁平化字典"""
        items = []
        
        for key, value in d.items():
            new_key = f"{prefix}{separator}{key}" if prefix else key
            
            if isinstance(value, dict):
                items.extend(self._flatten_dict(value, new_key, separator).items())
            else:
                items.append((new_key, value))
        
        return dict(items)
    
    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值"""
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 整数
        try:
            return int(value)
        except ValueError:
            pass
        
        # 浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # JSON
        if value.startswith('{') or value.startswith('['):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                pass
        
        # 字符串
        return value
    
    def _generate_secret_key(self) -> str:
        """生成密钥"""
        import secrets
        return secrets.token_urlsafe(32)
    
    def _persist_config(self, key: str, value: Any):
        """持久化配置"""
        # 这里可以实现配置持久化逻辑
        # 例如保存到数据库或配置文件
        pass

# 全局配置管理器实例
config_manager = None

def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager

def get_config(key: str, default: Any = None) -> Any:
    """快捷获取配置"""
    return get_config_manager().get(key, default)

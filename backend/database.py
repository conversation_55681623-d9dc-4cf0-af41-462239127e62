#!/usr/bin/env python3
"""
数据库管理模块
支持SQLite和PostgreSQL
"""
import sqlite3
import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import uuid

class DatabaseManager:
    def __init__(self, db_path: str = "data/databases/goldenledger_accounting.db"):
        # 使用安全的数据库路径
        from app.core.database_security import get_security_manager

        self.security_manager = get_security_manager()
        self.db_name = Path(db_path).stem
        self.db_path = str(self.security_manager.get_secure_db_path(self.db_name))

        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        # 使用安全连接
        conn = self.security_manager.create_secure_connection(self.db_name)
        cursor = conn.cursor()
        
        # 创建公司表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS companies (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                tax_number TEXT,
                address TEXT,
                fiscal_year_end TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建仕訳表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                entry_date DATE NOT NULL,
                entry_time TIME,
                entry_datetime TIMESTAMP,
                description TEXT NOT NULL,
                debit_account TEXT NOT NULL,
                credit_account TEXT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                reference_number TEXT,
                ai_generated BOOLEAN DEFAULT FALSE,
                ai_confidence DECIMAL(3,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies (id)
            )
        ''')

        # 为现有记录添加新字段（如果不存在）
        try:
            cursor.execute('ALTER TABLE journal_entries ADD COLUMN entry_time TIME')
        except:
            pass  # 字段已存在

        try:
            cursor.execute('ALTER TABLE journal_entries ADD COLUMN entry_datetime TIMESTAMP')
        except:
            pass  # 字段已存在

        try:
            cursor.execute('ALTER TABLE journal_entries ADD COLUMN attachment_path TEXT')
        except sqlite3.OperationalError:
            pass  # 字段已存在

        try:
            cursor.execute('ALTER TABLE journal_entries ADD COLUMN debit_tax_rate DECIMAL(5,2) DEFAULT 0')
        except sqlite3.OperationalError:
            pass  # 字段已存在

        try:
            cursor.execute('ALTER TABLE journal_entries ADD COLUMN credit_tax_rate DECIMAL(5,2) DEFAULT 0')
        except sqlite3.OperationalError:
            pass  # 字段已存在
        
        # 创建科目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_subjects (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                category TEXT NOT NULL,
                name TEXT NOT NULL,
                code TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies (id)
            )
        ''')
        
        # 创建AI处理记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_processing_logs (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                input_text TEXT NOT NULL,
                processing_type TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                confidence DECIMAL(3,2),
                response_time_ms INTEGER,
                model_used TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies (id)
            )
        ''')
        
        # 创建LLM配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS llm_configurations (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                provider TEXT NOT NULL,
                model_name TEXT NOT NULL,
                model_id TEXT NOT NULL,
                task_types TEXT NOT NULL,
                priority INTEGER DEFAULT 1,
                is_active BOOLEAN DEFAULT TRUE,
                temperature DECIMAL(3,2) DEFAULT 0.1,
                max_tokens INTEGER DEFAULT 4096,
                api_key_encrypted TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # 插入默认数据
        self.insert_default_data()
    
    def insert_default_data(self):
        """插入默认数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 检查是否已有默认公司
        cursor.execute("SELECT COUNT(*) FROM companies WHERE id = 'default'")
        if cursor.fetchone()[0] == 0:
            # 插入默认公司
            cursor.execute('''
                INSERT INTO companies (id, name, tax_number, address, fiscal_year_end)
                VALUES (?, ?, ?, ?, ?)
            ''', ('default', 'デモ会社', '*************', '東京都渋谷区', '03-31'))
            
            # 插入默认科目
            default_accounts = [
                ('資産', '現金'), ('資産', '普通預金'), ('資産', '売掛金'),
                ('負債', '買掛金'), ('負債', '短期借入金'),
                ('純資産', '資本金'), ('純資産', '利益剰余金'),
                ('収益', '売上高'), ('収益', '受取利息'),
                ('費用', '消耗品費'), ('費用', '給料手当'), ('費用', '地代家賃'),
                ('費用', '水道光熱費'), ('費用', '通信費'), ('費用', '広告宣伝費'),
                ('費用', '交通費'), ('費用', '会議費')
            ]
            
            for category, name in default_accounts:
                account_id = str(uuid.uuid4())
                cursor.execute('''
                    INSERT INTO account_subjects (id, company_id, category, name)
                    VALUES (?, ?, ?, ?)
                ''', (account_id, 'default', category, name))
        
        conn.commit()
        conn.close()
    
    def save_journal_entry(self, entry_data: Dict) -> str:
        """保存仕訳记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        entry_id = entry_data.get('id', str(uuid.uuid4()))
        
        # 处理日期时间
        entry_date = entry_data.get('entry_date')
        entry_time = entry_data.get('entry_time')
        entry_datetime = entry_data.get('entry_datetime')

        # 如果提供了完整的datetime，解析出date和time
        if entry_datetime and not entry_date:
            from datetime import datetime
            dt = datetime.fromisoformat(entry_datetime.replace('Z', '+00:00'))
            entry_date = dt.date().isoformat()
            entry_time = dt.time().isoformat()

        cursor.execute('''
            INSERT OR REPLACE INTO journal_entries
            (id, company_id, entry_date, entry_time, entry_datetime, description, debit_account, credit_account,
             amount, reference_number, ai_generated, ai_confidence, attachment_path, debit_tax_rate, credit_tax_rate, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (
            entry_id,
            entry_data.get('company_id', 'default'),
            entry_date,
            entry_time,
            entry_datetime,
            entry_data.get('description'),
            entry_data.get('debit_account'),
            entry_data.get('credit_account'),
            entry_data.get('amount'),
            entry_data.get('reference_number'),
            entry_data.get('ai_generated', False),
            entry_data.get('ai_confidence', 0.0),
            entry_data.get('attachment_path'),
            entry_data.get('debit_tax_rate', 0.0),
            entry_data.get('credit_tax_rate', 0.0)
        ))
        
        conn.commit()
        conn.close()
        
        return entry_id
    
    def get_journal_entries(self, company_id: str = 'default', limit: int = 100) -> List[Dict]:
        """获取仕訳记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, company_id, entry_date, entry_time, entry_datetime, description, debit_account, credit_account,
                   amount, reference_number, ai_generated, ai_confidence, attachment_path, created_at, updated_at,
                   debit_tax_rate, credit_tax_rate
            FROM journal_entries
            WHERE company_id = ?
            ORDER BY entry_date DESC, entry_time DESC, created_at DESC
            LIMIT ?
        ''', (company_id, limit))
        
        columns = [desc[0] for desc in cursor.description]
        entries = []
        
        for row in cursor.fetchall():
            entry = dict(zip(columns, row))
            entries.append(entry)
        
        conn.close()
        return entries
    
    def get_account_subjects(self, company_id: str = 'default') -> Dict[str, List[str]]:
        """获取科目列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT category, name FROM account_subjects 
            WHERE company_id = ? AND is_active = TRUE
            ORDER BY category, name
        ''', (company_id,))
        
        subjects = {}
        for category, name in cursor.fetchall():
            if category not in subjects:
                subjects[category] = []
            subjects[category].append(name)
        
        conn.close()
        return subjects
    
    def log_ai_processing(self, log_data: Dict) -> str:
        """记录AI处理日志"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        log_id = str(uuid.uuid4())
        
        cursor.execute('''
            INSERT INTO ai_processing_logs 
            (id, company_id, input_text, processing_type, success, confidence, 
             response_time_ms, model_used)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            log_id,
            log_data.get('company_id', 'default'),
            log_data.get('input_text'),
            log_data.get('processing_type'),
            log_data.get('success'),
            log_data.get('confidence'),
            log_data.get('response_time_ms'),
            log_data.get('model_used')
        ))
        
        conn.commit()
        conn.close()
        
        return log_id
    
    def get_ai_stats(self, company_id: str = 'default') -> Dict:
        """获取AI处理统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 总处理数
        cursor.execute('''
            SELECT COUNT(*) FROM ai_processing_logs WHERE company_id = ?
        ''', (company_id,))
        total_processed = cursor.fetchone()[0]
        
        # 成功率
        cursor.execute('''
            SELECT COUNT(*) FROM ai_processing_logs 
            WHERE company_id = ? AND success = TRUE
        ''', (company_id,))
        success_count = cursor.fetchone()[0]
        success_rate = success_count / total_processed if total_processed > 0 else 0
        
        # 平均置信度
        cursor.execute('''
            SELECT AVG(confidence) FROM ai_processing_logs 
            WHERE company_id = ? AND success = TRUE AND confidence IS NOT NULL
        ''', (company_id,))
        avg_confidence = cursor.fetchone()[0] or 0
        
        # 平均响应时间
        cursor.execute('''
            SELECT AVG(response_time_ms) FROM ai_processing_logs 
            WHERE company_id = ? AND response_time_ms IS NOT NULL
        ''', (company_id,))
        avg_response_time = cursor.fetchone()[0] or 0
        
        conn.close()
        
        return {
            'total_processed': total_processed,
            'success_rate': success_rate,
            'avg_confidence': avg_confidence,
            'avg_response_time': avg_response_time
        }
    
    def get_financial_summary(self, company_id: str = 'default') -> Dict:
        """获取财务汇总"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 本月收入 - 更灵活的匹配
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM journal_entries
            WHERE company_id = ? AND (
                credit_account LIKE '%売上%' OR
                credit_account LIKE '%収入%' OR
                credit_account LIKE '%収益%' OR
                debit_account IN (SELECT name FROM account_subjects WHERE category = '収益')
            )
            AND strftime('%Y-%m', entry_date) = strftime('%Y-%m', 'now')
        ''', (company_id,))
        monthly_revenue = cursor.fetchone()[0]

        # 本月支出 - 更灵活的匹配
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0) FROM journal_entries
            WHERE company_id = ? AND (
                debit_account IN (SELECT name FROM account_subjects WHERE category = '費用') OR
                debit_account LIKE '%費%' OR
                debit_account LIKE '%支出%' OR
                debit_account LIKE '%経費%'
            )
            AND strftime('%Y-%m', entry_date) = strftime('%Y-%m', 'now')
        ''', (company_id,))
        monthly_expenses = cursor.fetchone()[0]

        # 总仕訳数
        cursor.execute('''
            SELECT COUNT(*) FROM journal_entries WHERE company_id = ?
        ''', (company_id,))
        total_entries = cursor.fetchone()[0]

        # 本月仕訳数
        cursor.execute('''
            SELECT COUNT(*) FROM journal_entries
            WHERE company_id = ? AND strftime('%Y-%m', entry_date) = strftime('%Y-%m', 'now')
        ''', (company_id,))
        monthly_entries = cursor.fetchone()[0]

        conn.close()

        return {
            'monthly_revenue': monthly_revenue,
            'monthly_expenses': monthly_expenses,
            'total_entries': total_entries,
            'monthly_entries': monthly_entries,
            'monthly_profit': monthly_revenue - monthly_expenses
        }

    def update_journal_entry(self, company_id: str, entry_id: str, entry_data: Dict) -> bool:
        """更新仕訳记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 处理日期时间
            entry_date = entry_data.get('entry_date')
            entry_time = entry_data.get('entry_time')
            entry_datetime = entry_data.get('entry_datetime')

            # 如果提供了完整的datetime，解析出date和time
            if entry_datetime and not entry_date:
                from datetime import datetime
                dt = datetime.fromisoformat(entry_datetime.replace('Z', '+00:00'))
                entry_date = dt.date().isoformat()
                entry_time = dt.time().isoformat()

            cursor.execute('''
                UPDATE journal_entries
                SET entry_date = ?, entry_time = ?, entry_datetime = ?, description = ?, debit_account = ?, credit_account = ?,
                    amount = ?, reference_number = ?, attachment_path = ?, debit_tax_rate = ?, credit_tax_rate = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND company_id = ?
            ''', (
                entry_date,
                entry_time,
                entry_datetime,
                entry_data.get('description'),
                entry_data.get('debit_account'),
                entry_data.get('credit_account'),
                entry_data.get('amount'),
                entry_data.get('reference_number'),
                entry_data.get('attachment_path'),
                entry_data.get('debit_tax_rate', 0.0),
                entry_data.get('credit_tax_rate', 0.0),
                entry_id,
                company_id
            ))

            success = cursor.rowcount > 0
            conn.commit()
            conn.close()

            return success

        except Exception as e:
            print(f"更新仕訳记录失败: {e}")
            return False

    def delete_journal_entry(self, company_id: str, entry_id: str) -> bool:
        """删除仕訳记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM journal_entries
                WHERE id = ? AND company_id = ?
            ''', (entry_id, company_id))

            success = cursor.rowcount > 0
            conn.commit()
            conn.close()

            return success

        except Exception as e:
            print(f"删除仕訳记录失败: {e}")
            return False

# 全局数据库实例
db = DatabaseManager("backend/goldenledger_accounting.db")

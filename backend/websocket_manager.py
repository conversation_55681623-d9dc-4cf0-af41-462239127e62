#!/usr/bin/env python3
"""
WebSocket实时通信管理器
支持实时数据推送、系统状态监控、AI处理进度等
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Set, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MessageType(Enum):
    """消息类型定义"""
    SYSTEM_STATUS = "system_status"
    AI_PROCESSING = "ai_processing"
    JOURNAL_ENTRY = "journal_entry"
    NOTIFICATION = "notification"
    ERROR = "error"
    HEARTBEAT = "heartbeat"
    USER_ACTIVITY = "user_activity"
    REAL_TIME_DATA = "real_time_data"

@dataclass
class WebSocketMessage:
    """WebSocket消息格式"""
    type: MessageType
    data: Dict[str, Any]
    timestamp: str
    client_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": self.type.value,
            "data": self.data,
            "timestamp": self.timestamp,
            "client_id": self.client_id
        }

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 客户端订阅的消息类型
        self.subscriptions: Dict[str, Set[MessageType]] = {}
        
        # 连接元数据
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 消息历史 (最近100条)
        self.message_history: List[WebSocketMessage] = []
        
        # 统计信息
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "start_time": datetime.now()
        }
    
    async def connect(self, websocket: WebSocket, client_id: str, metadata: Dict[str, Any] = None):
        """建立WebSocket连接"""
        await websocket.accept()
        
        self.active_connections[client_id] = websocket
        self.subscriptions[client_id] = set()
        self.connection_metadata[client_id] = metadata or {}
        
        # 更新统计
        self.stats["total_connections"] += 1
        self.stats["active_connections"] = len(self.active_connections)
        
        logger.info(f"客户端 {client_id} 已连接，当前活跃连接数: {self.stats['active_connections']}")
        
        # 发送欢迎消息
        welcome_message = WebSocketMessage(
            type=MessageType.NOTIFICATION,
            data={
                "message": "连接成功",
                "client_id": client_id,
                "server_time": datetime.now().isoformat()
            },
            timestamp=datetime.now().isoformat(),
            client_id=client_id
        )
        
        await self.send_personal_message(welcome_message, client_id)
        
        # 广播系统状态更新
        await self.broadcast_system_status()
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        if client_id in self.subscriptions:
            del self.subscriptions[client_id]
        
        if client_id in self.connection_metadata:
            del self.connection_metadata[client_id]
        
        # 更新统计
        self.stats["active_connections"] = len(self.active_connections)
        
        logger.info(f"客户端 {client_id} 已断开，当前活跃连接数: {self.stats['active_connections']}")
    
    async def send_personal_message(self, message: WebSocketMessage, client_id: str):
        """发送个人消息"""
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(message.to_dict()))
                self.stats["messages_sent"] += 1
                
                # 添加到历史记录
                self._add_to_history(message)
                
            except Exception as e:
                logger.error(f"发送消息给 {client_id} 失败: {str(e)}")
                # 连接可能已断开，清理连接
                self.disconnect(client_id)
    
    async def broadcast(self, message: WebSocketMessage, message_type: MessageType = None):
        """广播消息给所有订阅的客户端"""
        target_type = message_type or message.type
        
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            # 检查客户端是否订阅了此类型的消息
            if target_type in self.subscriptions.get(client_id, set()):
                try:
                    await websocket.send_text(json.dumps(message.to_dict()))
                    self.stats["messages_sent"] += 1
                except Exception as e:
                    logger.error(f"广播消息给 {client_id} 失败: {str(e)}")
                    disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
        
        # 添加到历史记录
        self._add_to_history(message)
    
    async def subscribe(self, client_id: str, message_types: List[MessageType]):
        """订阅消息类型"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].update(message_types)
            
            # 发送订阅确认
            confirm_message = WebSocketMessage(
                type=MessageType.NOTIFICATION,
                data={
                    "message": f"已订阅 {len(message_types)} 种消息类型",
                    "subscribed_types": [mt.value for mt in message_types]
                },
                timestamp=datetime.now().isoformat(),
                client_id=client_id
            )
            
            await self.send_personal_message(confirm_message, client_id)
    
    async def unsubscribe(self, client_id: str, message_types: List[MessageType]):
        """取消订阅消息类型"""
        if client_id in self.subscriptions:
            for message_type in message_types:
                self.subscriptions[client_id].discard(message_type)
    
    async def broadcast_system_status(self):
        """广播系统状态"""
        status_message = WebSocketMessage(
            type=MessageType.SYSTEM_STATUS,
            data={
                "active_connections": self.stats["active_connections"],
                "total_connections": self.stats["total_connections"],
                "messages_sent": self.stats["messages_sent"],
                "uptime_seconds": (datetime.now() - self.stats["start_time"]).total_seconds(),
                "server_time": datetime.now().isoformat()
            },
            timestamp=datetime.now().isoformat()
        )
        
        await self.broadcast(status_message, MessageType.SYSTEM_STATUS)
    
    async def broadcast_ai_processing_update(self, processing_data: Dict[str, Any]):
        """广播AI处理进度更新"""
        ai_message = WebSocketMessage(
            type=MessageType.AI_PROCESSING,
            data=processing_data,
            timestamp=datetime.now().isoformat()
        )
        
        await self.broadcast(ai_message, MessageType.AI_PROCESSING)
    
    async def broadcast_journal_entry(self, journal_entry: Dict[str, Any]):
        """广播新的仕訳记录"""
        journal_message = WebSocketMessage(
            type=MessageType.JOURNAL_ENTRY,
            data=journal_entry,
            timestamp=datetime.now().isoformat()
        )
        
        await self.broadcast(journal_message, MessageType.JOURNAL_ENTRY)
    
    async def send_notification(self, client_id: str, notification: Dict[str, Any]):
        """发送通知"""
        notification_message = WebSocketMessage(
            type=MessageType.NOTIFICATION,
            data=notification,
            timestamp=datetime.now().isoformat(),
            client_id=client_id
        )
        
        await self.send_personal_message(notification_message, client_id)
    
    async def handle_client_message(self, client_id: str, message_data: Dict[str, Any]):
        """处理客户端消息"""
        try:
            message_type = message_data.get("type")
            data = message_data.get("data", {})
            
            self.stats["messages_received"] += 1
            
            if message_type == "subscribe":
                # 处理订阅请求
                types_to_subscribe = data.get("message_types", [])
                message_types = [MessageType(mt) for mt in types_to_subscribe if mt in MessageType.__members__.values()]
                await self.subscribe(client_id, message_types)
                
            elif message_type == "unsubscribe":
                # 处理取消订阅请求
                types_to_unsubscribe = data.get("message_types", [])
                message_types = [MessageType(mt) for mt in types_to_unsubscribe if mt in MessageType.__members__.values()]
                await self.unsubscribe(client_id, message_types)
                
            elif message_type == "heartbeat":
                # 处理心跳
                heartbeat_response = WebSocketMessage(
                    type=MessageType.HEARTBEAT,
                    data={"status": "alive", "server_time": datetime.now().isoformat()},
                    timestamp=datetime.now().isoformat(),
                    client_id=client_id
                )
                await self.send_personal_message(heartbeat_response, client_id)
                
            elif message_type == "get_history":
                # 发送消息历史
                history_data = {
                    "messages": [msg.to_dict() for msg in self.message_history[-50:]],  # 最近50条
                    "total_count": len(self.message_history)
                }
                
                history_message = WebSocketMessage(
                    type=MessageType.NOTIFICATION,
                    data=history_data,
                    timestamp=datetime.now().isoformat(),
                    client_id=client_id
                )
                
                await self.send_personal_message(history_message, client_id)
                
            else:
                logger.warning(f"未知的消息类型: {message_type}")
                
        except Exception as e:
            logger.error(f"处理客户端 {client_id} 消息失败: {str(e)}")
            
            error_message = WebSocketMessage(
                type=MessageType.ERROR,
                data={"error": f"消息处理失败: {str(e)}"},
                timestamp=datetime.now().isoformat(),
                client_id=client_id
            )
            
            await self.send_personal_message(error_message, client_id)
    
    def _add_to_history(self, message: WebSocketMessage):
        """添加消息到历史记录"""
        self.message_history.append(message)
        
        # 保持历史记录在合理大小
        if len(self.message_history) > 100:
            self.message_history = self.message_history[-100:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "uptime_seconds": (datetime.now() - self.stats["start_time"]).total_seconds(),
            "connected_clients": list(self.active_connections.keys()),
            "subscription_summary": {
                client_id: [mt.value for mt in subscriptions]
                for client_id, subscriptions in self.subscriptions.items()
            }
        }

# 全局连接管理器实例
manager = ConnectionManager()

def get_connection_manager() -> ConnectionManager:
    """获取连接管理器实例"""
    return manager

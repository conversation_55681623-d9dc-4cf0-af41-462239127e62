"""
GoldenLedger — Smart AI-Powered Finance System - FastAPI主应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.core.config import settings
from app.core.database import db_manager
from app.api.routes import ai_bookkeeping, llm_management, journal_entries
from .payment_handler import router as payment_router


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    description="基于Microsoft AutoGen的AI全自动记账系统",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(ai_bookkeeping.router, prefix="/api/v1", tags=["AI Bookkeeping"])
app.include_router(llm_management.router, prefix="/api/v1", tags=["LLM Management"])
app.include_router(journal_entries.router, prefix="/api/v1", tags=["Journal Entries"])
# 注册PayPal支付路由，统一前缀
app.include_router(payment_router, prefix="/api/paypal", tags=["PayPal Payment"])

# 信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
)


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print("🚀 启动GoldenLedger — Smart AI-Powered Finance System...")
    
    try:
        # 连接数据库
        await db_manager.connect()
        print("✅ 数据库连接成功")
        
        print("🎉 系统启动完成！")
        print(f"📖 API文档: http://localhost:8000/docs")
        print(f"🔧 管理界面: http://localhost:3000")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print("🛑 正在关闭系统...")
    
    try:
        await db_manager.disconnect()
        print("✅ 数据库连接已关闭")
        print("👋 系统已安全关闭")
        
    except Exception as e:
        print(f"⚠️ 关闭时出现错误: {e}")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    print(f"❌ 未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": str(exc) if settings.DEBUG else "请联系系统管理员",
            "type": "internal_server_error"
        }
    )


@app.get("/")
async def root():
    """根路径 - 系统信息"""
    return {
        "name": settings.APP_NAME,
        "version": settings.VERSION,
        "status": "running",
        "message": "🚀 GoldenLedger — Smart AI-Powered Finance System正在运行",
        "features": [
            "🗣️ 一句话全自动记账",
            "📸 图片/PDF智能识别", 
            "🤖 多智能体协作",
            "🔧 LLM模型管理",
            "📊 日本会计标准"
        ],
        "docs_url": "/docs" if settings.DEBUG else None
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db = await db_manager.get_pg_connection()
        await db_manager.release_pg_connection(db)
        
        # 检查Redis连接
        redis = db_manager.get_redis()
        await redis.ping()
        
        return {
            "status": "healthy",
            "database": "connected",
            "redis": "connected",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "2024-01-01T00:00:00Z"
            }
        )


# 注册API路由
app.include_router(
    ai_bookkeeping.router,
    prefix="/api/v1/ai-bookkeeping",
    tags=["AI全自动记账"]
)

app.include_router(
    llm_management.router,
    prefix="/api/v1/llm",
    tags=["LLM模型管理"]
)

app.include_router(
    journal_entries.router,
    prefix="/api/v1/journal-entries",
    tags=["仕訳帳管理"]
)

#!/usr/bin/env python3
"""
AI智能体协作系统
基于Microsoft AutoGen的多智能体记账处理
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

import google.generativeai as genai
from pydantic import BaseModel

# 尝试导入datetime_parser，如果失败则创建一个简单的替代
try:
    from .datetime_parser import datetime_parser
except ImportError:
    try:
        from datetime_parser import datetime_parser
    except ImportError:
        # 创建一个简单的datetime_parser替代
        class SimpleDateTimeParser:
            def parse_datetime_from_text(self, text):
                from datetime import datetime
                now = datetime.now()
                return {
                    'entry_date': now.strftime('%Y-%m-%d'),
                    'entry_time': now.strftime('%H:%M:%S'),
                    'entry_datetime': now.strftime('%Y-%m-%dT%H:%M:%S'),
                    'original_text': text,
                    'parsed_text': text.replace('今日', f'{now.strftime("%Y年%m月%d日")}')
                }
        datetime_parser = SimpleDateTimeParser()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentRole(Enum):
    """智能体角色定义"""
    ACCOUNTANT = "accountant"          # 会计师 - 主要记账处理
    AUDITOR = "auditor"               # 审计师 - 质量检查
    TRANSLATOR = "translator"         # 翻译师 - 多语言处理
    ANALYZER = "analyzer"             # 分析师 - 数据分析
    VALIDATOR = "validator"           # 验证师 - 数据验证
    COORDINATOR = "coordinator"       # 协调员 - 流程协调

class TaskType(Enum):
    """任务类型定义"""
    NATURAL_LANGUAGE_PROCESSING = "nlp"
    JOURNAL_ENTRY_GENERATION = "journal"
    QUALITY_ASSURANCE = "qa"
    TRANSLATION = "translation"
    FINANCIAL_ANALYSIS = "analysis"
    COMPLIANCE_CHECK = "compliance"

@dataclass
class AgentMessage:
    """智能体消息"""
    sender: str
    receiver: str
    content: str
    task_type: TaskType
    timestamp: datetime
    metadata: Dict[str, Any] = None

@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    data: Dict[str, Any]
    confidence: float
    warnings: List[str] = None
    suggestions: List[str] = None
    processing_time: float = 0.0

    def __post_init__(self):
        """初始化后处理"""
        if self.warnings is None:
            self.warnings = []
        if self.suggestions is None:
            self.suggestions = []

class BaseAgent:
    """基础智能体类"""
    
    def __init__(self, name: str, role: AgentRole, model_config: Dict[str, Any]):
        self.name = name
        self.role = role
        self.model_config = model_config
        self.conversation_history = []
        
        # 初始化Gemini模型
        if model_config.get('provider') == 'google':
            genai.configure(api_key=model_config.get('api_key'))
            self.model = genai.GenerativeModel(model_config.get('model_id', 'gemini-pro'))
    
    async def process_message(self, message: AgentMessage) -> ProcessingResult:
        """处理消息的基础方法"""
        start_time = datetime.now()
        
        try:
            # 记录消息历史
            self.conversation_history.append(message)
            
            # 调用具体的处理逻辑
            result = await self._handle_task(message)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            result.processing_time = processing_time
            
            logger.info(f"{self.name} 处理完成: {message.task_type.value}, 耗时: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"{self.name} 处理失败: {str(e)}")
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=[f"处理错误: {str(e)}"],
                processing_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _handle_task(self, message: AgentMessage) -> ProcessingResult:
        """子类需要实现的具体任务处理方法"""
        raise NotImplementedError

class AccountantAgent(BaseAgent):
    """会计师智能体 - 负责主要的记账处理"""
    
    def __init__(self, model_config: Dict[str, Any]):
        super().__init__("会计师AI", AgentRole.ACCOUNTANT, model_config)
        
        # 会计科目映射
        self.account_mapping = {
            "现金": ["现金", "cash", "現金"],
            "银行存款": ["银行", "bank", "普通預金", "銀行"],
            "应收账款": ["应收", "receivable", "売掛金"],
            "销售收入": ["销售", "收入", "revenue", "売上", "売上高"],
            "办公费用": ["办公", "office", "事務用品", "消耗品"],
            "租金": ["租金", "房租", "rent", "地代家賃"],
            "工资": ["工资", "薪水", "salary", "給料", "給与"]
        }
    
    async def _handle_task(self, message: AgentMessage) -> ProcessingResult:
        """处理记账任务"""
        if message.task_type == TaskType.NATURAL_LANGUAGE_PROCESSING:
            return await self._process_natural_language(message.content)
        elif message.task_type == TaskType.JOURNAL_ENTRY_GENERATION:
            return await self._generate_journal_entry(message.content)
        else:
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=["不支持的任务类型"]
            )
    
    async def _process_natural_language(self, text: str) -> ProcessingResult:
        """处理自然语言输入"""
        # 首先使用日期时间解析器处理时间代词
        datetime_info = datetime_parser.parse_datetime_from_text(text)
        parsed_text = datetime_info['parsed_text']

        prompt = f"""
        作为专业会计师，请分析以下交易描述并提取关键信息：

        原始交易描述: {text}
        处理后描述: {parsed_text}
        解析的日期: {datetime_info['entry_date']}
        解析的时间: {datetime_info['entry_time']}

        请提取以下信息并以JSON格式返回：
        {{
            "transaction_type": "收入/支出/转账",
            "amount": "金额数字",
            "currency": "货币类型",
            "description": "交易描述（使用处理后的描述，不包含时间代词）",
            "date": "使用解析的日期: {datetime_info['entry_date']}",
            "time": "使用解析的时间: {datetime_info['entry_time']}",
            "datetime": "使用解析的完整时间: {datetime_info['entry_datetime']}",
            "payment_method": "支付方式",
            "counterparty": "交易对方",
            "suggested_debit_account": "建议借方科目",
            "suggested_credit_account": "建议贷方科目",
            "confidence": "置信度(0-1)"
        }}

        注意：
        1. 描述字段应该使用处理后的描述，将时间代词替换为具体日期时间
        2. 日期时间字段必须使用解析器提供的准确值
        3. 不要在描述中保留"今天"、"昨天"等代词
        """
        
        try:
            response = self.model.generate_content(prompt)

            # 解析AI响应
            response_text = response.text
            logger.info(f"AI原始响应: {response_text}")

            # 尝试提取JSON
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group()
                logger.info(f"提取的JSON: {json_text}")

                # 清理JSON文本，去除注释
                json_text = re.sub(r'//.*', '', json_text)  # 去除单行注释
                json_text = re.sub(r'/\*.*?\*/', '', json_text, flags=re.DOTALL)  # 去除多行注释
                logger.info(f"清理后的JSON: {json_text}")

                try:
                    parsed_data = json.loads(json_text)

                    return ProcessingResult(
                        success=True,
                        data=parsed_data,
                        confidence=float(parsed_data.get('confidence', 0.8)),
                        suggestions=["建议核实金额和科目分类"]
                    )
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    logger.error(f"问题JSON: {json_text}")
                    return ProcessingResult(
                        success=False,
                        data={},
                        confidence=0.0,
                        warnings=[f"JSON解析错误: {str(e)}"]
                    )
            else:
                return ProcessingResult(
                    success=False,
                    data={},
                    confidence=0.0,
                    warnings=["无法解析AI响应"]
                )

        except Exception as e:
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=[f"AI处理错误: {str(e)}"]
            )
    
    async def _generate_journal_entry(self, transaction_data: str) -> ProcessingResult:
        """生成仕訳分录"""
        try:
            data = json.loads(transaction_data) if isinstance(transaction_data, str) else transaction_data
            
            # 生成仕訳ID
            entry_id = f"J{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # 安全地转换金额
            try:
                amount = float(data.get('amount', 0))
            except (ValueError, TypeError):
                amount = 0.0

            journal_entry = {
                "id": entry_id,
                "date": data.get('date', datetime.now().strftime('%Y-%m-%d')),
                "time": data.get('time', datetime.now().strftime('%H:%M:%S')),
                "datetime": data.get('datetime', datetime.now().isoformat()),
                "description": data.get('description', ''),
                "debit_account": data.get('suggested_debit_account', ''),
                "credit_account": data.get('suggested_credit_account', ''),
                "amount": amount,
                "currency": data.get('currency', 'JPY'),
                "reference": f"REF{datetime.now().strftime('%Y%m%d%H%M%S')}"
            }
            
            # 安全地转换置信度
            try:
                confidence = float(data.get('confidence', 0.8))
            except (ValueError, TypeError):
                confidence = 0.8

            return ProcessingResult(
                success=True,
                data=journal_entry,
                confidence=confidence,
                suggestions=["请核实科目分类是否正确"]
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=[f"仕訳生成错误: {str(e)}"]
            )

class AuditorAgent(BaseAgent):
    """审计师智能体 - 负责质量检查和合规性验证"""
    
    def __init__(self, model_config: Dict[str, Any]):
        super().__init__("审计师AI", AgentRole.AUDITOR, model_config)
    
    async def _handle_task(self, message: AgentMessage) -> ProcessingResult:
        """处理审计任务"""
        if message.task_type == TaskType.QUALITY_ASSURANCE:
            return await self._quality_check(message.content)
        elif message.task_type == TaskType.COMPLIANCE_CHECK:
            return await self._compliance_check(message.content)
        else:
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=["不支持的审计任务类型"]
            )
    
    async def _quality_check(self, journal_entry_data: str) -> ProcessingResult:
        """质量检查"""
        try:
            data = json.loads(journal_entry_data) if isinstance(journal_entry_data, str) else journal_entry_data
            
            issues = []
            warnings = []
            
            # 检查借贷平衡
            if not data.get('debit_account') or not data.get('credit_account'):
                issues.append("缺少借方或贷方科目")
            
            # 检查金额
            amount = data.get('amount', 0)
            try:
                amount_float = float(amount) if amount else 0
                if amount_float <= 0:
                    issues.append("金额必须大于0")
            except (ValueError, TypeError):
                issues.append("金额格式无效")
            
            # 检查描述
            if not data.get('description'):
                warnings.append("建议添加更详细的交易描述")
            
            # 检查日期格式
            date_str = data.get('date')
            if date_str:
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                except ValueError:
                    issues.append("日期格式不正确")
            
            success = len(issues) == 0
            confidence = 0.9 if success else 0.3
            
            return ProcessingResult(
                success=success,
                data={
                    "quality_score": confidence,
                    "issues": issues,
                    "recommendations": warnings
                },
                confidence=confidence,
                warnings=issues + warnings
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=[f"质量检查错误: {str(e)}"]
            )
    
    async def _compliance_check(self, journal_entry_data: str) -> ProcessingResult:
        """合规性检查"""
        try:
            data = json.loads(journal_entry_data) if isinstance(journal_entry_data, str) else journal_entry_data
            
            compliance_issues = []
            
            # 检查会计准则合规性
            debit_account = data.get('debit_account', '')
            credit_account = data.get('credit_account', '')
            
            # 简单的合规性规则
            if debit_account == credit_account:
                compliance_issues.append("借方和贷方科目不能相同")
            
            # 检查科目分类逻辑
            asset_accounts = ['现金', '银行存款', '应收账款', '固定资产']
            liability_accounts = ['应付账款', '短期借款', '长期借款']
            equity_accounts = ['实收资本', '留存收益']
            revenue_accounts = ['销售收入', '其他收入']
            expense_accounts = ['销售费用', '管理费用', '财务费用']
            
            # 更多合规性检查可以在这里添加
            
            success = len(compliance_issues) == 0
            confidence = 0.95 if success else 0.4
            
            return ProcessingResult(
                success=success,
                data={
                    "compliance_score": confidence,
                    "issues": compliance_issues
                },
                confidence=confidence,
                warnings=compliance_issues
            )
            
        except Exception as e:
            return ProcessingResult(
                success=False,
                data={},
                confidence=0.0,
                warnings=[f"合规性检查错误: {str(e)}"]
            )

class MultiAgentCoordinator:
    """多智能体协调器"""
    
    def __init__(self, model_configs: Dict[str, Dict[str, Any]]):
        self.agents = {}
        self.task_queue = asyncio.Queue()
        self.results = {}
        
        # 初始化智能体
        if 'accountant' in model_configs:
            self.agents['accountant'] = AccountantAgent(model_configs['accountant'])
        
        if 'auditor' in model_configs:
            self.agents['auditor'] = AuditorAgent(model_configs['auditor'])
    
    async def process_natural_language_request(self, text: str, company_id: str = 'default') -> Dict[str, Any]:
        """处理自然语言记账请求"""
        try:
            # 预处理: 解析日期时间代词
            datetime_info = datetime_parser.parse_datetime_from_text(text)
            processed_text = datetime_info['parsed_text']

            logger.info(f"日期时间解析结果: {datetime_info}")

            # 第一步: 会计师处理自然语言
            accountant_message = AgentMessage(
                sender="user",
                receiver="accountant",
                content=processed_text,  # 使用处理后的文本
                task_type=TaskType.NATURAL_LANGUAGE_PROCESSING,
                timestamp=datetime.now()
            )
            
            nlp_result = await self.agents['accountant'].process_message(accountant_message)
            
            if not nlp_result.success:
                return {
                    "success": False,
                    "error": "自然语言处理失败",
                    "warnings": nlp_result.warnings
                }
            
            # 第二步: 生成仕訳分录
            journal_message = AgentMessage(
                sender="accountant",
                receiver="accountant",
                content=json.dumps(nlp_result.data),
                task_type=TaskType.JOURNAL_ENTRY_GENERATION,
                timestamp=datetime.now()
            )
            
            journal_result = await self.agents['accountant'].process_message(journal_message)
            
            if not journal_result.success:
                return {
                    "success": False,
                    "error": "仕訳生成失败",
                    "warnings": journal_result.warnings
                }
            
            # 第三步: 审计师质量检查
            if 'auditor' in self.agents:
                audit_message = AgentMessage(
                    sender="accountant",
                    receiver="auditor",
                    content=json.dumps(journal_result.data),
                    task_type=TaskType.QUALITY_ASSURANCE,
                    timestamp=datetime.now()
                )
                
                audit_result = await self.agents['auditor'].process_message(audit_message)
                
                # 合并审计结果
                if audit_result.success:
                    journal_result.warnings.extend(audit_result.warnings or [])
                    journal_result.confidence = min(journal_result.confidence, audit_result.confidence)
            
            # 确保返回的仕訳记录包含解析的日期时间信息
            journal_entry = journal_result.data
            journal_entry.update({
                'date': datetime_info['entry_date'],
                'time': datetime_info['entry_time'],
                'datetime': datetime_info['entry_datetime']
            })

            return {
                "success": True,
                "journal_entry": journal_entry,
                "confidence": journal_result.confidence,
                "warnings": journal_result.warnings or [],
                "suggestions": journal_result.suggestions or [],
                "processing_time": nlp_result.processing_time + journal_result.processing_time,
                "datetime_info": datetime_info  # 包含完整的日期时间解析信息
            }
            
        except Exception as e:
            logger.error(f"多智能体处理错误: {str(e)}")
            return {
                "success": False,
                "error": f"系统错误: {str(e)}"
            }

# 全局协调器实例
coordinator = None

def initialize_coordinator(model_configs: Dict[str, Dict[str, Any]]):
    """初始化协调器"""
    global coordinator
    coordinator = MultiAgentCoordinator(model_configs)
    return coordinator

def get_coordinator() -> MultiAgentCoordinator:
    """获取协调器实例"""
    global coordinator
    if coordinator is None:
        # 从环境变量获取API密钥
        import os
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")

        # 使用环境变量配置初始化
        default_config = {
            'accountant': {
                'provider': 'google',
                'model_id': 'gemini-1.5-flash',
                'api_key': api_key
            },
            'auditor': {
                'provider': 'google',
                'model_id': 'gemini-1.5-flash',
                'api_key': api_key
            }
        }
        coordinator = MultiAgentCoordinator(default_config)

    return coordinator

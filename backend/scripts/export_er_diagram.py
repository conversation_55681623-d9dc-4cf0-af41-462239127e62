#!/usr/bin/env python3
"""
导出ER图为PNG文件
"""

import os
import subprocess
import tempfile
from pathlib import Path

def create_mermaid_file():
    """创建Mermaid ER图文件"""
    mermaid_content = """
erDiagram
    %% 用户和认证相关
    USERS {
        string id PK
        string email UK
        string username
        string password_hash
        string name
        string avatar_url
        boolean email_verified
        boolean is_active
        datetime created_at
        datetime updated_at
        datetime last_login_at
    }
    
    USER_SESSIONS {
        string id PK
        string user_id FK
        string session_token UK
        datetime expires_at
        string ip_address
        string user_agent
        datetime created_at
    }
    
    %% 租户和公司相关
    TENANTS {
        string id PK
        string name
        string domain
        string subscription_plan
        boolean is_active
        datetime trial_ends_at
        integer max_companies
        integer max_users
        datetime created_at
        datetime updated_at
    }
    
    COMPANIES {
        string id PK
        string tenant_id FK
        string name
        string tax_number
        string address
        string industry
        date fiscal_year_start
        date fiscal_year_end
        string currency
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    %% 用户权限相关
    USER_TENANT_ROLES {
        string id PK
        string user_id FK
        string tenant_id FK
        string role
        boolean is_active
        datetime granted_at
        datetime expires_at
        string granted_by FK
    }
    
    USER_COMPANY_PERMISSIONS {
        string id PK
        string user_id FK
        string company_id FK
        string permission_type
        boolean can_read
        boolean can_write
        boolean can_delete
        datetime created_at
    }
    
    %% 会计科目相关
    ACCOUNT_CATEGORIES {
        string id PK
        string tenant_id FK
        string name
        string type
        string code_prefix
        boolean is_system
        integer sort_order
        datetime created_at
    }
    
    ACCOUNT_SUBJECTS {
        string id PK
        string company_id FK
        string category_id FK
        string parent_id FK
        string code
        string name
        string type
        boolean is_active
        boolean is_system
        datetime created_at
        datetime updated_at
    }
    
    %% 记账相关
    JOURNAL_ENTRIES {
        string id PK
        string company_id FK
        string created_by FK
        date entry_date
        string description
        string reference_number
        decimal total_amount
        string status
        boolean ai_generated
        decimal ai_confidence
        text ai_analysis
        datetime created_at
        datetime updated_at
        datetime posted_at
        string posted_by FK
    }
    
    JOURNAL_ENTRY_LINES {
        string id PK
        string journal_entry_id FK
        string account_id FK
        decimal debit_amount
        decimal credit_amount
        string description
        string reference
        datetime created_at
    }
    
    %% 附件和文档
    ATTACHMENTS {
        string id PK
        string company_id FK
        string journal_entry_id FK
        string uploaded_by FK
        string filename
        string file_path
        string file_type
        integer file_size
        string storage_provider
        datetime created_at
    }
    
    %% AI和配置
    LLM_CONFIGURATIONS {
        string id PK
        string tenant_id FK
        string provider
        string model_name
        text api_key_encrypted
        integer max_tokens
        decimal temperature
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    AI_PROCESSING_LOGS {
        string id PK
        string company_id FK
        string user_id FK
        string journal_entry_id FK
        string operation_type
        text input_data
        text output_data
        decimal confidence_score
        integer processing_time_ms
        string status
        text error_message
        datetime created_at
    }
    
    %% 审计日志
    AUDIT_LOGS {
        string id PK
        string tenant_id FK
        string user_id FK
        string company_id FK
        string action
        string resource_type
        string resource_id
        text old_values
        text new_values
        string ip_address
        string user_agent
        datetime created_at
    }
    
    %% 固定资产
    FIXED_ASSETS {
        string id PK
        string company_id FK
        string name
        string category
        decimal original_value
        decimal accumulated_depreciation
        decimal net_value
        date purchase_date
        integer useful_life_years
        string depreciation_method
        datetime created_at
        datetime updated_at
    }
    
    %% 支付记录
    PAYMENT_RECORDS {
        string id PK
        string company_id FK
        string journal_entry_id FK
        string payment_method
        decimal amount
        string currency
        string reference_number
        datetime payment_date
        string status
        datetime created_at
    }
    
    %% 关系定义
    USERS ||--o{ USER_SESSIONS : has
    USERS ||--o{ USER_TENANT_ROLES : has
    USERS ||--o{ USER_COMPANY_PERMISSIONS : has
    USERS ||--o{ JOURNAL_ENTRIES : creates
    USERS ||--o{ ATTACHMENTS : uploads
    USERS ||--o{ AI_PROCESSING_LOGS : triggers
    USERS ||--o{ AUDIT_LOGS : generates
    
    TENANTS ||--o{ COMPANIES : contains
    TENANTS ||--o{ USER_TENANT_ROLES : has
    TENANTS ||--o{ ACCOUNT_CATEGORIES : defines
    TENANTS ||--o{ LLM_CONFIGURATIONS : configures
    TENANTS ||--o{ AUDIT_LOGS : tracks
    
    COMPANIES ||--o{ USER_COMPANY_PERMISSIONS : grants
    COMPANIES ||--o{ ACCOUNT_SUBJECTS : uses
    COMPANIES ||--o{ JOURNAL_ENTRIES : records
    COMPANIES ||--o{ ATTACHMENTS : stores
    COMPANIES ||--o{ AI_PROCESSING_LOGS : processes
    COMPANIES ||--o{ FIXED_ASSETS : owns
    COMPANIES ||--o{ PAYMENT_RECORDS : processes
    
    ACCOUNT_CATEGORIES ||--o{ ACCOUNT_SUBJECTS : categorizes
    ACCOUNT_SUBJECTS ||--o{ ACCOUNT_SUBJECTS : parent_child
    ACCOUNT_SUBJECTS ||--o{ JOURNAL_ENTRY_LINES : debits_credits
    
    JOURNAL_ENTRIES ||--o{ JOURNAL_ENTRY_LINES : contains
    JOURNAL_ENTRIES ||--o{ ATTACHMENTS : supports
    JOURNAL_ENTRIES ||--o{ AI_PROCESSING_LOGS : processes
    JOURNAL_ENTRIES ||--o{ PAYMENT_RECORDS : links
"""
    return mermaid_content.strip()

def main():
    """主函数"""
    print("📊 导出多用户会计系统ER图")
    print("=" * 50)
    
    # 目标路径
    target_dir = Path("/Users/<USER>")
    if not target_dir.exists():
        print(f"❌ 目标目录不存在: {target_dir}")
        return
    
    # 创建临时Mermaid文件
    mermaid_content = create_mermaid_file()
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.mmd', delete=False) as f:
        f.write(mermaid_content)
        temp_mermaid_file = f.name
    
    try:
        # 输出文件路径
        output_file = target_dir / "多用户会计系统ER图.png"
        
        print(f"📄 创建临时Mermaid文件: {temp_mermaid_file}")
        print(f"🎯 输出PNG文件: {output_file}")
        
        # 检查是否安装了mermaid-cli
        try:
            subprocess.run(["mmdc", "--version"], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ 未找到mermaid-cli工具")
            print("💡 请安装: npm install -g @mermaid-js/mermaid-cli")
            
            # 尝试使用在线服务生成
            print("🌐 尝试使用在线服务...")
            create_online_diagram(mermaid_content, output_file)
            return
        
        # 使用mermaid-cli生成PNG
        cmd = [
            "mmdc",
            "-i", temp_mermaid_file,
            "-o", str(output_file),
            "-t", "default",
            "-b", "white",
            "--width", "1920",
            "--height", "1080"
        ]
        
        print("🔄 生成PNG文件...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ ER图已成功导出到: {output_file}")
            print(f"📊 文件大小: {output_file.stat().st_size / 1024:.1f} KB")
        else:
            print(f"❌ 生成失败: {result.stderr}")
            
    finally:
        # 清理临时文件
        os.unlink(temp_mermaid_file)

def create_online_diagram(mermaid_content, output_file):
    """使用在线服务创建图表"""
    import base64
    import urllib.parse
    import requests
    
    try:
        # 编码Mermaid内容
        encoded_content = base64.b64encode(mermaid_content.encode()).decode()
        
        # 使用Mermaid在线服务
        url = f"https://mermaid.ink/img/{encoded_content}"
        
        print(f"🌐 请求在线服务: {url[:100]}...")
        
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            with open(output_file, 'wb') as f:
                f.write(response.content)
            print(f"✅ ER图已通过在线服务导出到: {output_file}")
            print(f"📊 文件大小: {len(response.content) / 1024:.1f} KB")
        else:
            print(f"❌ 在线服务请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 在线服务异常: {str(e)}")
        
        # 创建文本版本作为备选
        text_file = output_file.with_suffix('.txt')
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write("多用户会计系统ER图\n")
            f.write("=" * 50 + "\n\n")
            f.write(mermaid_content)
        
        print(f"📝 已创建文本版本: {text_file}")

if __name__ == "__main__":
    main()

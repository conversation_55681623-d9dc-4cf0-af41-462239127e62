#!/usr/bin/env python3
"""
创建多租户系统所需的数据库表
"""

import requests
import json

class CloudflareD1Manager:
    def __init__(self, account_id: str, database_id: str, api_token: str):
        self.account_id = account_id
        self.database_id = database_id
        self.api_token = api_token
        self.base_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/d1/database/{database_id}"
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }
    
    def execute_sql(self, sql: str) -> dict:
        """执行SQL语句"""
        url = f"{self.base_url}/query"
        data = {"sql": sql}
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"SQL执行失败: {response.status_code} - {response.text}")

def main():
    # 配置信息
    ACCOUNT_ID = "bc6dbb1a5bb06691cdd6a8df6aa768f8"
    DATABASE_ID = "bcff395d-3e68-44c7-b355-13f3014fa240"
    API_TOKEN = "7skRzBNWceiU1UPbEH33mQsE_CeBBpbNtGOY64TC"
    
    db = CloudflareD1Manager(ACCOUNT_ID, DATABASE_ID, API_TOKEN)
    
    print("🚀 创建多租户系统数据库表")
    print("=" * 50)
    
    # 要创建的表
    tables = [
        {
            "name": "user_tenant_roles",
            "sql": """
                CREATE TABLE IF NOT EXISTS user_tenant_roles (
                    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    user_id TEXT NOT NULL,
                    tenant_id TEXT NOT NULL,
                    role TEXT NOT NULL CHECK (role IN ('system_admin', 'tenant_admin', 'accountant', 'cashier', 'viewer')),
                    is_active BOOLEAN DEFAULT TRUE,
                    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    granted_by TEXT,
                    UNIQUE(user_id, tenant_id, role)
                );
            """
        },
        {
            "name": "user_company_permissions",
            "sql": """
                CREATE TABLE IF NOT EXISTS user_company_permissions (
                    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    user_id TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    permission_type TEXT NOT NULL CHECK (permission_type IN (
                        'dashboard', 'journal', 'account', 'report', 'user', 'settings', 'audit'
                    )),
                    can_read BOOLEAN DEFAULT FALSE,
                    can_write BOOLEAN DEFAULT FALSE,
                    can_delete BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, company_id, permission_type)
                );
            """
        },
        {
            "name": "account_categories",
            "sql": """
                CREATE TABLE IF NOT EXISTS account_categories (
                    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    tenant_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL CHECK (type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
                    code_prefix TEXT NOT NULL,
                    is_system BOOLEAN DEFAULT FALSE,
                    sort_order INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(tenant_id, code_prefix)
                );
            """
        },
        {
            "name": "journal_entry_lines",
            "sql": """
                CREATE TABLE IF NOT EXISTS journal_entry_lines (
                    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    journal_entry_id TEXT NOT NULL,
                    account_id TEXT NOT NULL,
                    debit_amount DECIMAL(15,2) DEFAULT 0.00,
                    credit_amount DECIMAL(15,2) DEFAULT 0.00,
                    description TEXT,
                    reference TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            """
        },
        {
            "name": "audit_logs",
            "sql": """
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
                    tenant_id TEXT,
                    user_id TEXT,
                    company_id TEXT,
                    action TEXT NOT NULL,
                    resource_type TEXT NOT NULL,
                    resource_id TEXT,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );
            """
        }
    ]
    
    # 创建表
    for table in tables:
        try:
            print(f"📊 创建表: {table['name']}")
            result = db.execute_sql(table['sql'])
            
            if result.get('success'):
                print(f"✅ 表 {table['name']} 创建成功")
            else:
                print(f"❌ 表 {table['name']} 创建失败: {result.get('errors', [])}")
        
        except Exception as e:
            print(f"❌ 表 {table['name']} 创建异常: {str(e)}")
    
    print("\n🔧 添加缺失的列...")
    
    # 添加缺失的列
    alter_statements = [
        "ALTER TABLE companies ADD COLUMN is_active BOOLEAN DEFAULT TRUE;",
        "ALTER TABLE account_subjects ADD COLUMN category_id TEXT;",
        "ALTER TABLE account_subjects ADD COLUMN parent_id TEXT;",
    ]
    
    for sql in alter_statements:
        try:
            print(f"⚡ 执行: {sql[:50]}...")
            result = db.execute_sql(sql)
            
            if result.get('success'):
                print(f"✅ 执行成功")
            else:
                errors = result.get('errors', [])
                if any('duplicate column name' in str(error) for error in errors):
                    print(f"⏭️ 列已存在，跳过")
                else:
                    print(f"❌ 执行失败: {errors}")
        
        except Exception as e:
            if 'duplicate column name' in str(e):
                print(f"⏭️ 列已存在，跳过")
            else:
                print(f"❌ 执行异常: {str(e)}")
    
    print("\n📋 创建索引...")
    
    # 创建索引
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);",
        "CREATE INDEX IF NOT EXISTS idx_tenants_active ON tenants(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_user_tenant_roles_user ON user_tenant_roles(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_user_tenant_roles_tenant ON user_tenant_roles(tenant_id);",
        "CREATE INDEX IF NOT EXISTS idx_user_company_permissions_user ON user_company_permissions(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_user_company_permissions_company ON user_company_permissions(company_id);",
        "CREATE INDEX IF NOT EXISTS idx_companies_tenant ON companies(tenant_id);",
        "CREATE INDEX IF NOT EXISTS idx_companies_active ON companies(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_account_categories_tenant ON account_categories(tenant_id);",
        "CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_entry ON journal_entry_lines(journal_entry_id);",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant ON audit_logs(tenant_id);",
        "CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);",
    ]
    
    for sql in indexes:
        try:
            print(f"📊 创建索引: {sql.split('ON')[1].split('(')[0].strip()}")
            result = db.execute_sql(sql)
            
            if result.get('success'):
                print(f"✅ 索引创建成功")
            else:
                print(f"❌ 索引创建失败: {result.get('errors', [])}")
        
        except Exception as e:
            print(f"❌ 索引创建异常: {str(e)}")
    
    print("\n🎯 插入初始数据...")
    
    # 插入初始数据
    initial_data = [
        """
        INSERT OR IGNORE INTO tenants (id, name, domain, subscription_plan, is_active) 
        VALUES ('default-tenant-001', '默认租户', 'default.mykuaiji.com', 'professional', TRUE);
        """,
        """
        UPDATE companies 
        SET tenant_id = 'default-tenant-001' 
        WHERE tenant_id IS NULL;
        """,
        """
        INSERT OR IGNORE INTO user_tenant_roles (user_id, tenant_id, role, is_active)
        SELECT 
            id as user_id,
            'default-tenant-001' as tenant_id,
            CASE 
                WHEN role = 'admin' THEN 'tenant_admin'
                WHEN role = 'user' THEN 'accountant'
                ELSE 'viewer'
            END as role,
            is_active
        FROM users;
        """,
    ]
    
    for sql in initial_data:
        try:
            print(f"💾 插入数据...")
            result = db.execute_sql(sql)
            
            if result.get('success'):
                print(f"✅ 数据插入成功")
            else:
                print(f"❌ 数据插入失败: {result.get('errors', [])}")
        
        except Exception as e:
            print(f"❌ 数据插入异常: {str(e)}")
    
    print("\n🎉 多租户系统数据库创建完成!")

if __name__ == "__main__":
    main()

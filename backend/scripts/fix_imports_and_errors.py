#!/usr/bin/env python3
"""
修复导入路径和错误处理的脚本
"""
import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple

# 添加backend目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.import_manager import get_import_manager
from app.core.error_handler import get_error_handler, handle_error, ErrorCategory

import_manager = get_import_manager()
error_handler = get_error_handler()


def find_python_files(directory: Path) -> List[Path]:
    """查找所有Python文件"""
    python_files = []
    for file_path in directory.rglob("*.py"):
        if "venv" not in str(file_path) and "__pycache__" not in str(file_path):
            python_files.append(file_path)
    return python_files


def fix_hardcoded_imports(file_path: Path) -> bool:
    """修复硬编码的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复硬编码的sys.path操作
        patterns_to_fix = [
            # 修复 sys.path.insert(0, str(backend_path))
            (
                r'sys\.path\.insert\(0,\s*str\(backend_path\)\)',
                '# 使用统一的导入管理器\nfrom app.core.import_manager import setup_imports\nsetup_imports()'
            ),
            # 修复 sys.path.append('backend')
            (
                r'sys\.path\.append\([\'"]backend[\'"]\)',
                '# 使用统一的导入管理器\nfrom app.core.import_manager import setup_imports\nsetup_imports()'
            ),
            # 修复 sys.path.append(str(Path(__file__).parent.parent))
            (
                r'sys\.path\.append\(str\(Path\(__file__\)\.parent\.parent\)\)',
                '# 使用统一的导入管理器\nfrom app.core.import_manager import setup_imports\nsetup_imports()'
            ),
            # 修复不一致的backend导入
            (
                r'from backend\.database import',
                'from app.core.import_manager import get_database_manager\nDatabaseManager = get_database_manager()\n# from backend.database import'
            ),
            (
                r'from backend\.config_manager import',
                'from app.core.import_manager import get_config_manager\nConfigManager = get_config_manager()\n# from backend.config_manager import'
            ),
        ]
        
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复导入路径: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        handle_error(e, context={'file': str(file_path)}, reraise=False)
        return False


def fix_empty_exception_handlers(file_path: Path) -> bool:
    """修复空的异常处理"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复空的except:块
        patterns_to_fix = [
            # 修复 except: print("❌ xxx")
            (
                r'except:\s*\n\s*print\("❌([^"]+)"\)\s*\n\s*return',
                r'''except Exception as e:
                    from app.core.error_handler import handle_error, ErrorCategory
                    handle_error(e, context={"operation": "\1"}, reraise=False)
                    print(f"❌\1: {e}")
                    return'''
            ),
            # 修复 except: continue
            (
                r'except:\s*\n\s*continue',
                r'''except (ValueError, KeyError, TypeError) as e:
                    logger.warning(f"处理项目时出错，跳过: {e}")
                    continue'''
            ),
            # 修复 except: pass
            (
                r'except:\s*\n\s*pass',
                r'''except Exception as e:
                    from app.core.error_handler import handle_error
                    handle_error(e, reraise=False)'''
            ),
            # 修复 except: data["field"] = default_value
            (
                r'except:\s*\n\s*data\["([^"]+)"\]\s*=\s*([^\n]+)',
                r'''except (ValueError, TypeError) as e:
                    logger.warning(f"解析\1失败: {e}")
                    data["\1"] = \2'''
            ),
        ]
        
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复异常处理: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        handle_error(e, context={'file': str(file_path)}, reraise=False)
        return False


def add_error_handler_imports(file_path: Path) -> bool:
    """添加错误处理器导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有错误处理导入
        if 'from app.core.error_handler import' in content:
            return False
        
        # 检查是否有异常处理代码
        if 'except' not in content:
            return False
        
        # 在导入部分添加错误处理器导入
        import_section = []
        lines = content.split('\n')
        
        # 找到导入部分的结束位置
        import_end_index = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                import_end_index = i
            elif line.strip() and not line.strip().startswith('#'):
                break
        
        # 在导入部分末尾添加错误处理器导入
        if import_end_index > 0:
            lines.insert(import_end_index + 1, '')
            lines.insert(import_end_index + 2, '# 错误处理')
            lines.insert(import_end_index + 3, 'from app.core.error_handler import handle_error, ErrorCategory')
            
            new_content = '\n'.join(lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 添加错误处理导入: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        handle_error(e, context={'file': str(file_path)}, reraise=False)
        return False


def analyze_file_issues(file_path: Path) -> Dict[str, List[str]]:
    """分析文件中的问题"""
    issues = {
        'hardcoded_imports': [],
        'empty_exceptions': [],
        'missing_error_imports': []
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查硬编码导入
        hardcoded_patterns = [
            r'sys\.path\.insert\(',
            r'sys\.path\.append\(',
            r'from backend\.',
        ]
        
        for pattern in hardcoded_patterns:
            if re.search(pattern, content):
                issues['hardcoded_imports'].append(pattern)
        
        # 检查空异常处理
        empty_exception_patterns = [
            r'except:\s*\n\s*print\(',
            r'except:\s*\n\s*continue',
            r'except:\s*\n\s*pass',
            r'except:\s*\n\s*return',
        ]
        
        for pattern in empty_exception_patterns:
            if re.search(pattern, content, re.MULTILINE):
                issues['empty_exceptions'].append(pattern)
        
        # 检查是否缺少错误处理导入
        if 'except' in content and 'from app.core.error_handler import' not in content:
            issues['missing_error_imports'].append('缺少错误处理器导入')
        
    except Exception as e:
        handle_error(e, context={'file': str(file_path)}, reraise=False)
    
    return issues


def main():
    """主函数"""
    print("🚀 开始修复导入路径和错误处理...")
    
    project_root = import_manager.project_root
    
    # 查找所有Python文件
    python_files = find_python_files(project_root)
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    # 统计信息
    stats = {
        'total_files': len(python_files),
        'fixed_imports': 0,
        'fixed_exceptions': 0,
        'added_error_imports': 0,
        'files_with_issues': 0
    }
    
    # 分析和修复每个文件
    for file_path in python_files:
        print(f"\n🔍 分析文件: {file_path.relative_to(project_root)}")
        
        # 分析问题
        issues = analyze_file_issues(file_path)
        
        has_issues = any(issues.values())
        if has_issues:
            stats['files_with_issues'] += 1
            print(f"  📋 发现问题: {sum(len(v) for v in issues.values())} 个")
            
            # 修复硬编码导入
            if issues['hardcoded_imports']:
                if fix_hardcoded_imports(file_path):
                    stats['fixed_imports'] += 1
            
            # 修复空异常处理
            if issues['empty_exceptions']:
                if fix_empty_exception_handlers(file_path):
                    stats['fixed_exceptions'] += 1
            
            # 添加错误处理导入
            if issues['missing_error_imports']:
                if add_error_handler_imports(file_path):
                    stats['added_error_imports'] += 1
        else:
            print("  ✅ 无问题")
    
    # 输出统计信息
    print(f"\n📊 修复统计:")
    print(f"  总文件数: {stats['total_files']}")
    print(f"  有问题的文件: {stats['files_with_issues']}")
    print(f"  修复导入路径: {stats['fixed_imports']} 个文件")
    print(f"  修复异常处理: {stats['fixed_exceptions']} 个文件")
    print(f"  添加错误处理导入: {stats['added_error_imports']} 个文件")
    
    # 验证导入
    print(f"\n🧪 验证关键模块导入...")
    validation_results = import_manager.validate_imports()
    
    success_count = sum(1 for v in validation_results.values() if v)
    total_count = len(validation_results)
    
    print(f"  成功导入: {success_count}/{total_count} 个模块")
    
    if success_count == total_count:
        print("🎉 所有关键模块导入成功！")
    else:
        print("⚠️ 部分模块导入失败，请检查:")
        for module, success in validation_results.items():
            if not success:
                print(f"    ❌ {module}")
    
    print("\n✅ 修复完成！")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有必要的表结构和初始数据
"""
import asyncio
import asyncpg
from app.core.config import settings
from app.core.database import init_database, db_manager
from app.services.llm_manager import get_model_manager


async def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    # 从DATABASE_URL中提取数据库信息
    import urllib.parse
    parsed = urllib.parse.urlparse(settings.DATABASE_URL)
    
    db_name = parsed.path[1:]  # 移除开头的'/'
    host = parsed.hostname
    port = parsed.port or 5432
    user = parsed.username
    password = parsed.password
    
    # 连接到postgres数据库来创建目标数据库
    postgres_url = f"postgresql://{user}:{password}@{host}:{port}/postgres"
    
    try:
        conn = await asyncpg.connect(postgres_url)
        
        # 检查数据库是否存在
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1", db_name
        )
        
        if not exists:
            await conn.execute(f'CREATE DATABASE "{db_name}"')
            print(f"✅ 数据库 '{db_name}' 已创建")
        else:
            print(f"✅ 数据库 '{db_name}' 已存在")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        raise


async def insert_default_company():
    """插入默认公司数据"""
    db = await db_manager.get_pg_connection()
    
    try:
        # 检查是否已有公司数据
        existing = await db.fetchval("SELECT id FROM companies LIMIT 1")
        
        if not existing:
            company_id = await db.fetchval("""
                INSERT INTO companies (name, corporate_number, tax_id, fiscal_year_end)
                VALUES ($1, $2, $3, $4)
                RETURNING id
            """, "デモ会社", "*************", "T*************", "2024-03-31")
            
            print(f"✅ 默认公司已创建，ID: {company_id}")
            return str(company_id)
        else:
            print("✅ 公司数据已存在")
            return str(existing)
            
    except Exception as e:
        print(f"❌ 公司数据插入失败: {e}")
        raise
    finally:
        await db_manager.release_pg_connection(db)


async def insert_default_account_subjects(company_id: str):
    """插入默认会计科目"""
    db = await db_manager.get_pg_connection()
    
    try:
        # 检查是否已有科目数据
        existing = await db.fetchval(
            "SELECT id FROM account_subjects WHERE company_id = $1 LIMIT 1", 
            company_id
        )
        
        if existing:
            print("✅ 会计科目已存在")
            return
        
        # 标准会计科目数据
        subjects = [
            # 資産科目
            ("101", "現金", "資産", "流動資産"),
            ("102", "普通預金", "資産", "流動資産"),
            ("103", "当座預金", "資産", "流動資産"),
            ("111", "売掛金", "資産", "流動資産"),
            ("121", "商品", "資産", "流動資産"),
            ("151", "建物", "資産", "固定資産"),
            ("152", "機械装置", "資産", "固定資産"),
            
            # 負債科目
            ("201", "買掛金", "負債", "流動負債"),
            ("202", "未払金", "負債", "流動負債"),
            ("211", "借入金", "負債", "固定負債"),
            ("221", "預り金", "負債", "流動負債"),
            
            # 純資産科目
            ("301", "資本金", "純資産", None),
            ("302", "利益剰余金", "純資産", None),
            
            # 収益科目
            ("401", "売上高", "収益", "営業収益"),
            ("411", "受取利息", "収益", "営業外収益"),
            ("421", "雑収入", "収益", "営業外収益"),
            
            # 費用科目
            ("501", "仕入高", "費用", "営業費用"),
            ("511", "給料手当", "費用", "営業費用"),
            ("521", "地代家賃", "費用", "営業費用"),
            ("531", "水道光熱費", "費用", "営業費用"),
            ("541", "通信費", "費用", "営業費用"),
            ("551", "消耗品費", "費用", "営業費用"),
            ("561", "広告宣伝費", "費用", "営業費用"),
            ("571", "旅費交通費", "費用", "営業費用"),
            ("581", "接待交際費", "費用", "営業費用"),
            ("591", "支払利息", "費用", "営業外費用"),
        ]
        
        for code, name, category, subcategory in subjects:
            await db.execute("""
                INSERT INTO account_subjects (company_id, code, name, category, subcategory)
                VALUES ($1, $2, $3, $4, $5)
            """, company_id, code, name, category, subcategory)
        
        print(f"✅ {len(subjects)}個の会計科目を挿入しました")
        
    except Exception as e:
        print(f"❌ 会计科目插入失败: {e}")
        raise
    finally:
        await db_manager.release_pg_connection(db)


async def initialize_default_models(company_id: str):
    """初始化默认LLM模型配置"""
    try:
        model_manager = get_model_manager(company_id)
        await model_manager.initialize_default_models()
        print("✅ 默认LLM模型配置已初始化")
    except Exception as e:
        print(f"❌ LLM模型初始化失败: {e}")


async def main():
    """主初始化函数"""
    print("🚀 开始初始化数据库...")
    
    try:
        # 1. 创建数据库
        await create_database_if_not_exists()
        
        # 2. 连接数据库
        await db_manager.connect()
        
        # 3. 创建表结构
        await init_database()
        
        # 4. 插入默认公司
        company_id = await insert_default_company()
        
        # 5. 插入默认会计科目
        await insert_default_account_subjects(company_id)
        
        # 6. 初始化默认LLM模型
        await initialize_default_models(company_id)
        
        print("🎉 数据库初始化完成！")
        print(f"📋 默认公司ID: {company_id}")
        print("📝 可以开始使用AI记账功能了！")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        raise
    finally:
        await db_manager.disconnect()


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
数据库安全设置脚本
初始化安全目录结构、权限和加密配置
"""
import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加backend目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database_security import init_database_security, get_security_manager
from app.core.database_auth import auth_manager, Role, User

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def setup_secure_directories():
    """设置安全目录结构"""
    logger.info("🔧 设置安全目录结构...")
    
    # 初始化数据库安全管理器
    security_manager = init_database_security()
    
    logger.info("✅ 安全目录结构设置完成")
    return security_manager


async def migrate_existing_database():
    """迁移现有数据库到安全位置"""
    logger.info("🔄 检查并迁移现有数据库...")
    
    security_manager = get_security_manager()
    
    # 检查根目录是否有数据库文件
    old_db_paths = [
        "goldenledger_accounting.db",
        "backend/goldenledger_accounting.db",
    ]
    
    for old_path in old_db_paths:
        old_file = Path(old_path)
        if old_file.exists():
            logger.info(f"📁 发现旧数据库文件: {old_path}")
            
            # 获取安全路径
            new_path = security_manager.get_secure_db_path("goldenledger_accounting")
            
            # 创建备份
            if new_path.exists():
                backup_path = security_manager.backup_database("goldenledger_accounting")
                logger.info(f"📦 已创建备份: {backup_path}")
            
            # 移动文件到安全位置
            import shutil
            shutil.move(str(old_file), str(new_path))
            
            # 设置安全权限
            import stat
            os.chmod(new_path, stat.S_IRUSR | stat.S_IWUSR)  # 600
            
            logger.info(f"✅ 数据库已迁移到安全位置: {new_path}")
            
            # 删除相关的临时文件
            for suffix in ['-shm', '-wal']:
                temp_file = Path(f"{old_path}{suffix}")
                if temp_file.exists():
                    temp_file.unlink()
                    logger.info(f"🗑️ 已删除临时文件: {temp_file}")


async def setup_beta_users():
    """设置内测用户"""
    logger.info("👥 设置内测用户...")
    
    beta_users = [
        {
            "email": "<EMAIL>",
            "role": Role.ADMIN,
            "company_id": None  # 管理员可以访问所有公司
        },
        {
            "email": "admin",
            "role": Role.ADMIN,
            "company_id": None
        }
    ]
    
    for user_info in beta_users:
        user = User(
            user_id=user_info["email"],
            email=user_info["email"],
            role=user_info["role"],
            company_id=user_info["company_id"]
        )
        
        # 创建访问令牌（用于测试）
        token = auth_manager.create_access_token(user)
        logger.info(f"✅ 内测用户已设置: {user.email} (角色: {user.role.value})")
        logger.info(f"🔑 测试令牌: {token[:20]}...")


async def create_security_documentation():
    """创建安全配置文档"""
    logger.info("📝 创建安全配置文档...")
    
    security_manager = get_security_manager()
    docs_dir = security_manager.data_dir / "docs"
    docs_dir.mkdir(exist_ok=True)
    
    # 创建安全配置说明
    security_doc = docs_dir / "database_security.md"
    
    with open(security_doc, 'w', encoding='utf-8') as f:
        f.write("""# 数据库安全配置说明

## 目录结构

```
data/
├── databases/          # 数据库文件（权限：700）
├── backups/           # 数据库备份（权限：700）
├── logs/              # 审计日志（权限：700）
├── docs/              # 文档目录
└── .db_key            # 加密密钥（权限：600）
```

## 安全特性

### 1. 文件权限控制
- 数据库文件：600 (仅所有者可读写)
- 目录权限：700 (仅所有者可访问)
- 加密密钥：600 (仅所有者可读写)

### 2. 数据加密
- 敏感数据使用 Fernet 对称加密
- API密钥等敏感信息加密存储
- 使用 PBKDF2 密钥派生

### 3. 访问控制
- 基于角色的权限控制 (RBAC)
- JWT令牌认证
- 操作审计日志

### 4. 数据库安全配置
- 启用 WAL 模式
- 启用外键约束
- 设置同步模式为 FULL

## 角色权限

### ADMIN
- 完全访问权限
- 可以访问所有公司数据
- 系统管理权限

### ACCOUNTANT
- 会计操作权限
- 仅限指定公司数据
- 不能修改系统配置

### VIEWER
- 只读权限
- 仅限指定公司数据

### BETA_USER
- 内测用户权限
- 基本记账功能
- 仅限指定公司数据

## 内测用户

当前内测用户：
- <EMAIL> (ADMIN)
- admin (ADMIN)

## 备份策略

- 自动备份：每6小时一次
- 备份保留：30天
- 备份文件加密存储

## 审计日志

所有数据库操作都会记录审计日志，包括：
- 用户ID
- 操作类型
- 表名
- 时间戳
- IP地址
- 操作结果

## 安全建议

1. 定期更换加密密钥
2. 监控审计日志
3. 定期清理旧备份
4. 更新用户权限
5. 监控异常访问
""")
    
    # 设置文档权限
    import stat
    os.chmod(security_doc, stat.S_IRUSR | stat.S_IWUSR)  # 600
    
    logger.info(f"✅ 安全文档已创建: {security_doc}")


async def test_security_configuration():
    """测试安全配置"""
    logger.info("🧪 测试安全配置...")
    
    security_manager = get_security_manager()
    
    # 测试加密功能
    test_data = "test_api_key_12345"
    encrypted = security_manager.encrypt_sensitive_data(test_data)
    decrypted = security_manager.decrypt_sensitive_data(encrypted)
    
    if decrypted == test_data:
        logger.info("✅ 数据加密测试通过")
    else:
        logger.error("❌ 数据加密测试失败")
        return False
    
    # 测试数据库连接
    try:
        conn = security_manager.create_secure_connection("test")
        conn.execute("SELECT 1")
        conn.close()
        logger.info("✅ 安全数据库连接测试通过")
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False
    
    # 测试权限系统
    user = User("<EMAIL>", "<EMAIL>", Role.VIEWER)
    
    from app.core.database_auth import Permission
    if not user.has_permission(Permission.WRITE_COMPANIES):
        logger.info("✅ 权限控制测试通过")
    else:
        logger.error("❌ 权限控制测试失败")
        return False
    
    return True


async def main():
    """主函数"""
    logger.info("🚀 开始数据库安全设置...")
    
    try:
        # 1. 设置安全目录结构
        await setup_secure_directories()
        
        # 2. 迁移现有数据库
        await migrate_existing_database()
        
        # 3. 设置内测用户
        await setup_beta_users()
        
        # 4. 创建安全文档
        await create_security_documentation()
        
        # 5. 测试安全配置
        if await test_security_configuration():
            logger.info("🎉 数据库安全设置完成！")
            logger.info("📋 安全特性已启用：")
            logger.info("   - 文件权限控制")
            logger.info("   - 数据加密")
            logger.info("   - 访问控制")
            logger.info("   - 操作审计")
            logger.info("   - 自动备份")
        else:
            logger.error("❌ 安全配置测试失败")
            return 1
        
    except Exception as e:
        logger.error(f"❌ 安全设置失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

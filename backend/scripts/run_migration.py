#!/usr/bin/env python3
"""
数据库迁移执行脚本
用于执行多租户系统的数据库迁移
"""

import os
import sys
import json
import requests
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class CloudflareD1Migrator:
    """Cloudflare D1 数据库迁移器"""
    
    def __init__(self, account_id: str, database_id: str, api_token: str):
        self.account_id = account_id
        self.database_id = database_id
        self.api_token = api_token
        self.base_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/d1/database/{database_id}"
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }
    
    def execute_sql(self, sql: str) -> dict:
        """执行SQL语句"""
        url = f"{self.base_url}/query"
        data = {"sql": sql}
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"SQL执行失败: {response.status_code} - {response.text}")
    
    def execute_migration_file(self, file_path: str) -> bool:
        """执行迁移文件"""
        try:
            print(f"📄 读取迁移文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（简单处理，按分号分割）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            print(f"📊 找到 {len(sql_statements)} 条SQL语句")
            
            success_count = 0
            error_count = 0
            
            for i, sql in enumerate(sql_statements, 1):
                if not sql or sql.startswith('--') or sql.upper().startswith('BEGIN') or sql.upper().startswith('COMMIT'):
                    continue
                
                try:
                    print(f"⚡ 执行语句 {i}/{len(sql_statements)}: {sql[:50]}...")
                    result = self.execute_sql(sql)
                    
                    if result.get('success'):
                        success_count += 1
                        print(f"✅ 语句 {i} 执行成功")
                    else:
                        error_count += 1
                        print(f"❌ 语句 {i} 执行失败: {result.get('errors', [])}")
                
                except Exception as e:
                    error_count += 1
                    print(f"❌ 语句 {i} 执行异常: {str(e)}")
            
            print(f"\n📈 迁移统计:")
            print(f"   ✅ 成功: {success_count}")
            print(f"   ❌ 失败: {error_count}")
            print(f"   📊 总计: {success_count + error_count}")
            
            return error_count == 0
            
        except Exception as e:
            print(f"❌ 迁移文件执行失败: {str(e)}")
            return False
    
    def check_migration_status(self) -> dict:
        """检查迁移状态"""
        try:
            # 检查是否存在迁移记录表
            result = self.execute_sql("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='migration_history'
            """)
            
            if not result.get('result', [{}])[0].get('results', []):
                # 创建迁移记录表
                self.execute_sql("""
                    CREATE TABLE migration_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        migration_name TEXT NOT NULL,
                        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        success BOOLEAN NOT NULL,
                        error_message TEXT
                    )
                """)
                return {"migrations": []}
            
            # 获取已执行的迁移
            result = self.execute_sql("""
                SELECT migration_name, executed_at, success 
                FROM migration_history 
                ORDER BY executed_at DESC
            """)
            
            return {"migrations": result.get('result', [{}])[0].get('results', [])}
            
        except Exception as e:
            print(f"❌ 检查迁移状态失败: {str(e)}")
            return {"migrations": []}
    
    def record_migration(self, migration_name: str, success: bool, error_message: str = None):
        """记录迁移执行结果"""
        try:
            self.execute_sql(f"""
                INSERT INTO migration_history (migration_name, success, error_message)
                VALUES ('{migration_name}', {success}, {f"'{error_message}'" if error_message else 'NULL'})
            """)
        except Exception as e:
            print(f"⚠️ 记录迁移结果失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 多租户数据库迁移工具")
    print("=" * 50)
    
    # 配置信息
    ACCOUNT_ID = "bc6dbb1a5bb06691cdd6a8df6aa768f8"
    DATABASE_ID = "bcff395d-3e68-44c7-b355-13f3014fa240"
    API_TOKEN = "7skRzBNWceiU1UPbEH33mQsE_CeBBpbNtGOY64TC"
    
    # 创建迁移器
    migrator = CloudflareD1Migrator(ACCOUNT_ID, DATABASE_ID, API_TOKEN)
    
    # 检查当前迁移状态
    print("🔍 检查当前迁移状态...")
    status = migrator.check_migration_status()
    executed_migrations = [m['migration_name'] for m in status['migrations'] if m.get('success')]
    
    print(f"📋 已执行的迁移: {len(executed_migrations)}")
    for migration in executed_migrations:
        print(f"   ✅ {migration}")
    
    # 查找待执行的迁移文件
    migrations_dir = Path(__file__).parent.parent / "migrations"
    migration_files = sorted(migrations_dir.glob("*.sql"))
    
    print(f"\n📁 发现 {len(migration_files)} 个迁移文件:")
    for file in migration_files:
        print(f"   📄 {file.name}")
    
    # 执行未执行的迁移
    for migration_file in migration_files:
        migration_name = migration_file.name
        
        if migration_name in executed_migrations:
            print(f"⏭️ 跳过已执行的迁移: {migration_name}")
            continue
        
        print(f"\n🔄 执行迁移: {migration_name}")
        print("-" * 30)
        
        success = migrator.execute_migration_file(str(migration_file))
        
        if success:
            print(f"✅ 迁移 {migration_name} 执行成功")
            migrator.record_migration(migration_name, True)
        else:
            print(f"❌ 迁移 {migration_name} 执行失败")
            migrator.record_migration(migration_name, False, "执行过程中出现错误")
            
            # 询问是否继续
            response = input("是否继续执行后续迁移? (y/N): ")
            if response.lower() != 'y':
                break
    
    print("\n🎉 迁移执行完成!")
    print("=" * 50)
    
    # 最终状态检查
    final_status = migrator.check_migration_status()
    successful_migrations = [m for m in final_status['migrations'] if m.get('success')]
    failed_migrations = [m for m in final_status['migrations'] if not m.get('success')]
    
    print(f"📊 最终统计:")
    print(f"   ✅ 成功迁移: {len(successful_migrations)}")
    print(f"   ❌ 失败迁移: {len(failed_migrations)}")
    
    if failed_migrations:
        print(f"\n⚠️ 失败的迁移:")
        for migration in failed_migrations:
            print(f"   ❌ {migration['migration_name']}")

if __name__ == "__main__":
    main()

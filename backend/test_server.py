#!/usr/bin/env python3
"""
简化的测试服务器
用于验证前后端集成
"""

import json
import asyncio
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import uuid

# 模拟数据存储
class MockDatabase:
    def __init__(self):
        self.users = {}
        self.tenants = {}
        self.companies = {}
        self.journal_entries = {}
        self.account_subjects = {}
        self.init_sample_data()
    
    def init_sample_data(self):
        """初始化示例数据"""
        # 创建示例租户
        tenant_id = str(uuid.uuid4())
        self.tenants[tenant_id] = {
            'id': tenant_id,
            'name': '示例企业集团',
            'domain': 'example.com',
            'subscription_plan': 'premium',
            'is_active': True,
            'max_companies': 10,
            'max_users': 50,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 创建示例用户
        user_id = str(uuid.uuid4())
        self.users[user_id] = {
            'id': user_id,
            'email': '<EMAIL>',
            'username': 'admin',
            'name': '系统管理员',
            'role': 'system_admin',
            'is_active': True,
            'email_verified': True,
            'tenant_roles': [
                {
                    'tenant_id': tenant_id,
                    'tenant_name': '示例企业集团',
                    'role': 'tenant_admin',
                    'granted_at': datetime.now().isoformat()
                }
            ],
            'company_permissions': [],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 创建示例公司
        company_id = str(uuid.uuid4())
        self.companies[company_id] = {
            'id': company_id,
            'tenant_id': tenant_id,
            'name': '示例科技有限公司',
            'tax_number': '91110000123456789X',
            'address': '北京市朝阳区示例大厦',
            'industry': '软件和信息技术服务业',
            'fiscal_year_start': '01-01',
            'fiscal_year_end': '12-31',
            'currency': 'CNY',
            'is_active': True,
            'journal_entries_count': 5,
            'account_subjects_count': 20,
            'users_count': 3,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 创建示例会计科目
        accounts = [
            {'code': '1001', 'name': '库存现金', 'type': 'asset'},
            {'code': '1002', 'name': '银行存款', 'type': 'asset'},
            {'code': '1122', 'name': '应收账款', 'type': 'asset'},
            {'code': '2001', 'name': '短期借款', 'type': 'liability'},
            {'code': '2202', 'name': '应付账款', 'type': 'liability'},
            {'code': '3001', 'name': '实收资本', 'type': 'equity'},
            {'code': '4001', 'name': '主营业务收入', 'type': 'revenue'},
            {'code': '5001', 'name': '主营业务成本', 'type': 'expense'},
            {'code': '6601', 'name': '管理费用', 'type': 'expense'},
        ]
        
        for account in accounts:
            account_id = str(uuid.uuid4())
            self.account_subjects[account_id] = {
                'id': account_id,
                'company_id': company_id,
                'company_name': '示例科技有限公司',
                'code': account['code'],
                'name': account['name'],
                'type': account['type'],
                'parent_id': None,
                'category_id': None,
                'is_active': True,
                'is_system': False,
                'children_count': 0,
                'journal_entries_count': 0,
                'balance': 0.0,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
        
        # 创建示例记账记录
        entry_id = str(uuid.uuid4())
        self.journal_entries[entry_id] = {
            'id': entry_id,
            'company_id': company_id,
            'company_name': '示例科技有限公司',
            'entry_date': date.today().isoformat(),
            'description': '收到投资款',
            'reference_number': 'JE001',
            'total_amount': 100000.00,
            'status': 'posted',
            'ai_generated': False,
            'created_by': user_id,
            'created_by_name': '系统管理员',
            'posted_at': datetime.now().isoformat(),
            'lines': [
                {
                    'id': str(uuid.uuid4()),
                    'account_id': list(self.account_subjects.keys())[1],  # 银行存款
                    'account_code': '1002',
                    'account_name': '银行存款',
                    'debit_amount': 100000.00,
                    'credit_amount': 0.00,
                    'description': '收到投资款'
                },
                {
                    'id': str(uuid.uuid4()),
                    'account_id': list(self.account_subjects.keys())[5],  # 实收资本
                    'account_code': '3001',
                    'account_name': '实收资本',
                    'debit_amount': 0.00,
                    'credit_amount': 100000.00,
                    'description': '投资者投入资本'
                }
            ],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }

# 全局数据库实例
db = MockDatabase()

# 简化的API响应
class APIResponse:
    @staticmethod
    def success(data=None, message="操作成功"):
        return {
            "success": True,
            "data": data,
            "message": message
        }
    
    @staticmethod
    def error(message="操作失败", code=400):
        return {
            "success": False,
            "error": message,
            "code": code
        }

# 模拟API端点
async def handle_login(data):
    """处理登录请求"""
    email = data.get('email')
    password = data.get('password')
    
    # 简单验证
    for user in db.users.values():
        if user['email'] == email:
            return APIResponse.success({
                "access_token": "mock_token_" + user['id'],
                "token_type": "bearer",
                "user": user,
                "expires_in": 3600
            })
    
    return APIResponse.error("用户名或密码错误", 401)

async def handle_get_current_user():
    """获取当前用户信息"""
    # 返回第一个用户作为当前用户
    user = list(db.users.values())[0]
    return APIResponse.success(user)

async def handle_get_companies():
    """获取公司列表"""
    companies = list(db.companies.values())
    return APIResponse.success(companies)

async def handle_get_company_stats(company_id):
    """获取公司统计信息"""
    if company_id not in db.companies:
        return APIResponse.error("公司不存在", 404)
    
    # 模拟统计数据
    stats = {
        "total_entries": 5,
        "total_amount": 150000.00,
        "monthly_entries": 3,
        "monthly_amount": 80000.00,
        "active_accounts": 9,
        "recent_entries": [
            {
                "id": list(db.journal_entries.keys())[0],
                "date": date.today().isoformat(),
                "description": "收到投资款",
                "amount": 100000.00,
                "status": "posted"
            }
        ]
    }
    
    return APIResponse.success(stats)

async def handle_get_journal_entries():
    """获取记账记录列表"""
    entries = list(db.journal_entries.values())
    return APIResponse.success(entries)

async def handle_get_journal_entry(entry_id):
    """获取记账记录详情"""
    if entry_id not in db.journal_entries:
        return APIResponse.error("记账记录不存在", 404)
    
    entry = db.journal_entries[entry_id]
    return APIResponse.success(entry)

async def handle_get_account_subjects():
    """获取会计科目列表"""
    subjects = list(db.account_subjects.values())
    return APIResponse.success(subjects)

# HTTP服务器
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
import threading

class MockAPIHandler(http.server.BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        response_data = None
        
        if path == '/api/auth/me':
            response_data = asyncio.run(handle_get_current_user())
        elif path == '/api/companies/':
            response_data = asyncio.run(handle_get_companies())
        elif path.startswith('/api/companies/') and path.endswith('/stats'):
            company_id = path.split('/')[-2]
            response_data = asyncio.run(handle_get_company_stats(company_id))
        elif path == '/api/journal/':
            response_data = asyncio.run(handle_get_journal_entries())
        elif path.startswith('/api/journal/') and not path.endswith('/'):
            entry_id = path.split('/')[-1]
            response_data = asyncio.run(handle_get_journal_entry(entry_id))
        elif path == '/api/accounts/':
            response_data = asyncio.run(handle_get_account_subjects())
        else:
            response_data = APIResponse.error("接口不存在", 404)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
    
    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
        
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        response_data = None
        
        if path == '/api/auth/login':
            response_data = asyncio.run(handle_login(data))
        else:
            response_data = APIResponse.error("接口不存在", 404)
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_server():
    """启动测试服务器"""
    PORT = 8000
    
    with socketserver.TCPServer(("", PORT), MockAPIHandler) as httpd:
        print(f"🚀 测试服务器启动成功!")
        print(f"📡 服务地址: http://localhost:{PORT}")
        print(f"📚 API文档: http://localhost:{PORT}/docs")
        print(f"🔧 测试用户: <EMAIL> / password123")
        print("按 Ctrl+C 停止服务器")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    start_server()

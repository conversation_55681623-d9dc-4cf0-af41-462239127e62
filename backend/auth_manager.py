#!/usr/bin/env python3
"""
高级用户认证和权限管理系统
支持JWT、RBAC、多因子认证、SSO等
"""
import jwt
import bcrypt
import secrets
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import pyotp
import qrcode
from io import BytesIO
import base64

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserRole(Enum):
    """用户角色定义"""
    SUPER_ADMIN = "super_admin"        # 超级管理员
    ADMIN = "admin"                    # 管理员
    ACCOUNTANT = "accountant"          # 会计师
    AUDITOR = "auditor"               # 审计师
    VIEWER = "viewer"                 # 查看者
    GUEST = "guest"                   # 访客

class Permission(Enum):
    """权限定义"""
    # 系统管理
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    
    # 用户管理
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    
    # 财务数据
    FINANCE_CREATE = "finance:create"
    FINANCE_READ = "finance:read"
    FINANCE_UPDATE = "finance:update"
    FINANCE_DELETE = "finance:delete"
    
    # AI功能
    AI_USE = "ai:use"
    AI_ADMIN = "ai:admin"
    AI_AUDIT = "ai:audit"
    
    # 报表功能
    REPORT_VIEW = "report:view"
    REPORT_EXPORT = "report:export"
    REPORT_CREATE = "report:create"

@dataclass
class User:
    """用户模型"""
    id: str
    username: str
    email: str
    password_hash: str
    role: UserRole
    permissions: Set[Permission]
    is_active: bool = True
    is_verified: bool = False
    mfa_enabled: bool = False
    mfa_secret: Optional[str] = None
    last_login: Optional[str] = None
    created_at: str = ""
    updated_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

@dataclass
class Session:
    """会话模型"""
    session_id: str
    user_id: str
    access_token: str
    refresh_token: str
    expires_at: str
    created_at: str
    last_activity: str
    ip_address: str = ""
    user_agent: str = ""
    is_active: bool = True

@dataclass
class LoginAttempt:
    """登录尝试记录"""
    ip_address: str
    username: str
    success: bool
    timestamp: str
    user_agent: str = ""
    failure_reason: str = ""

class AuthManager:
    """认证管理器"""
    
    def __init__(self, secret_key: str, token_expire_hours: int = 24):
        self.secret_key = secret_key
        self.token_expire_hours = token_expire_hours
        self.algorithm = "HS256"
        
        # 用户存储 (生产环境应使用数据库)
        self.users: Dict[str, User] = {}
        self.sessions: Dict[str, Session] = {}
        self.login_attempts: List[LoginAttempt] = []
        
        # 角色权限映射
        self.role_permissions = {
            UserRole.SUPER_ADMIN: set(Permission),  # 所有权限
            UserRole.ADMIN: {
                Permission.SYSTEM_CONFIG, Permission.SYSTEM_MONITOR,
                Permission.USER_CREATE, Permission.USER_READ, Permission.USER_UPDATE,
                Permission.FINANCE_CREATE, Permission.FINANCE_READ, Permission.FINANCE_UPDATE,
                Permission.AI_USE, Permission.AI_ADMIN,
                Permission.REPORT_VIEW, Permission.REPORT_EXPORT, Permission.REPORT_CREATE
            },
            UserRole.ACCOUNTANT: {
                Permission.FINANCE_CREATE, Permission.FINANCE_READ, Permission.FINANCE_UPDATE,
                Permission.AI_USE, Permission.REPORT_VIEW, Permission.REPORT_EXPORT
            },
            UserRole.AUDITOR: {
                Permission.FINANCE_READ, Permission.AI_AUDIT,
                Permission.REPORT_VIEW, Permission.REPORT_EXPORT
            },
            UserRole.VIEWER: {
                Permission.FINANCE_READ, Permission.REPORT_VIEW
            },
            UserRole.GUEST: {
                Permission.FINANCE_READ
            }
        }
        
        # 创建默认管理员用户
        self._create_default_admin()
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        admin_id = "admin_001"
        if admin_id not in self.users:
            admin_user = User(
                id=admin_id,
                username="admin",
                email="<EMAIL>",
                password_hash=self._hash_password("admin123"),
                role=UserRole.SUPER_ADMIN,
                permissions=self.role_permissions[UserRole.SUPER_ADMIN],
                is_active=True,
                is_verified=True
            )
            self.users[admin_id] = admin_user
            logger.info("默认管理员用户已创建: admin/admin123")
    
    def register_user(self, username: str, email: str, password: str, 
                     role: UserRole = UserRole.VIEWER) -> Dict[str, Any]:
        """注册新用户"""
        try:
            # 检查用户名是否已存在
            if any(user.username == username for user in self.users.values()):
                return {"success": False, "error": "用户名已存在"}
            
            # 检查邮箱是否已存在
            if any(user.email == email for user in self.users.values()):
                return {"success": False, "error": "邮箱已存在"}
            
            # 验证密码强度
            if not self._validate_password(password):
                return {"success": False, "error": "密码强度不足"}
            
            # 创建用户
            user_id = f"user_{secrets.token_hex(8)}"
            user = User(
                id=user_id,
                username=username,
                email=email,
                password_hash=self._hash_password(password),
                role=role,
                permissions=self.role_permissions[role]
            )
            
            self.users[user_id] = user
            
            logger.info(f"新用户注册成功: {username}")
            
            return {
                "success": True,
                "user_id": user_id,
                "message": "用户注册成功"
            }
            
        except Exception as e:
            logger.error(f"用户注册失败: {str(e)}")
            return {"success": False, "error": f"注册失败: {str(e)}"}
    
    def authenticate_user(self, username: str, password: str, 
                         ip_address: str = "", user_agent: str = "",
                         mfa_code: str = "") -> Dict[str, Any]:
        """用户认证"""
        try:
            # 记录登录尝试
            attempt = LoginAttempt(
                ip_address=ip_address,
                username=username,
                success=False,
                timestamp=datetime.now().isoformat(),
                user_agent=user_agent
            )
            
            # 查找用户
            user = None
            for u in self.users.values():
                if u.username == username or u.email == username:
                    user = u
                    break
            
            if not user:
                attempt.failure_reason = "用户不存在"
                self.login_attempts.append(attempt)
                return {"success": False, "error": "用户名或密码错误"}
            
            # 检查用户状态
            if not user.is_active:
                attempt.failure_reason = "用户已禁用"
                self.login_attempts.append(attempt)
                return {"success": False, "error": "用户已被禁用"}
            
            # 验证密码
            if not self._verify_password(password, user.password_hash):
                attempt.failure_reason = "密码错误"
                self.login_attempts.append(attempt)
                return {"success": False, "error": "用户名或密码错误"}
            
            # 验证MFA (如果启用)
            if user.mfa_enabled:
                if not mfa_code:
                    return {
                        "success": False,
                        "error": "需要MFA验证码",
                        "require_mfa": True
                    }
                
                if not self._verify_mfa(user.mfa_secret, mfa_code):
                    attempt.failure_reason = "MFA验证失败"
                    self.login_attempts.append(attempt)
                    return {"success": False, "error": "MFA验证码错误"}
            
            # 生成令牌
            tokens = self._generate_tokens(user)
            
            # 创建会话
            session = Session(
                session_id=secrets.token_hex(16),
                user_id=user.id,
                access_token=tokens["access_token"],
                refresh_token=tokens["refresh_token"],
                expires_at=tokens["expires_at"],
                created_at=datetime.now().isoformat(),
                last_activity=datetime.now().isoformat(),
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.sessions[session.session_id] = session
            
            # 更新用户最后登录时间
            user.last_login = datetime.now().isoformat()
            
            # 记录成功登录
            attempt.success = True
            self.login_attempts.append(attempt)
            
            logger.info(f"用户登录成功: {username}")
            
            return {
                "success": True,
                "access_token": tokens["access_token"],
                "refresh_token": tokens["refresh_token"],
                "expires_at": tokens["expires_at"],
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role.value,
                    "permissions": [p.value for p in user.permissions]
                }
            }
            
        except Exception as e:
            logger.error(f"用户认证失败: {str(e)}")
            return {"success": False, "error": f"认证失败: {str(e)}"}
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证访问令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            user_id = payload.get("user_id")
            if not user_id or user_id not in self.users:
                return {"valid": False, "error": "用户不存在"}
            
            user = self.users[user_id]
            if not user.is_active:
                return {"valid": False, "error": "用户已禁用"}
            
            return {
                "valid": True,
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role.value,
                    "permissions": [p.value for p in user.permissions]
                }
            }
            
        except jwt.ExpiredSignatureError:
            return {"valid": False, "error": "令牌已过期"}
        except jwt.InvalidTokenError:
            return {"valid": False, "error": "无效令牌"}
        except Exception as e:
            return {"valid": False, "error": f"令牌验证失败: {str(e)}"}
    
    def check_permission(self, user_id: str, permission: Permission) -> bool:
        """检查用户权限"""
        if user_id not in self.users:
            return False
        
        user = self.users[user_id]
        return permission in user.permissions
    
    def enable_mfa(self, user_id: str) -> Dict[str, Any]:
        """启用多因子认证"""
        try:
            if user_id not in self.users:
                return {"success": False, "error": "用户不存在"}
            
            user = self.users[user_id]
            
            # 生成MFA密钥
            secret = pyotp.random_base32()
            user.mfa_secret = secret
            
            # 生成QR码
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user.email,
                issuer_name="goldenledger会計AI"
            )
            
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                "success": True,
                "secret": secret,
                "qr_code": f"data:image/png;base64,{qr_code_base64}",
                "manual_entry_key": secret
            }
            
        except Exception as e:
            logger.error(f"启用MFA失败: {str(e)}")
            return {"success": False, "error": f"启用MFA失败: {str(e)}"}
    
    def confirm_mfa(self, user_id: str, mfa_code: str) -> Dict[str, Any]:
        """确认MFA设置"""
        try:
            if user_id not in self.users:
                return {"success": False, "error": "用户不存在"}
            
            user = self.users[user_id]
            
            if not user.mfa_secret:
                return {"success": False, "error": "MFA未初始化"}
            
            if self._verify_mfa(user.mfa_secret, mfa_code):
                user.mfa_enabled = True
                logger.info(f"用户 {user.username} MFA启用成功")
                return {"success": True, "message": "MFA启用成功"}
            else:
                return {"success": False, "error": "MFA验证码错误"}
                
        except Exception as e:
            logger.error(f"确认MFA失败: {str(e)}")
            return {"success": False, "error": f"确认MFA失败: {str(e)}"}
    
    def logout(self, session_id: str) -> bool:
        """用户登出"""
        try:
            if session_id in self.sessions:
                session = self.sessions[session_id]
                session.is_active = False
                logger.info(f"用户登出: session_id={session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
            return False
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户会话列表"""
        sessions = []
        for session in self.sessions.values():
            if session.user_id == user_id and session.is_active:
                sessions.append({
                    "session_id": session.session_id,
                    "created_at": session.created_at,
                    "last_activity": session.last_activity,
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent
                })
        return sessions
    
    def get_login_history(self, username: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """获取登录历史"""
        attempts = self.login_attempts
        
        if username:
            attempts = [a for a in attempts if a.username == username]
        
        # 按时间倒序排列
        attempts.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [asdict(attempt) for attempt in attempts[:limit]]
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def _validate_password(self, password: str) -> bool:
        """验证密码强度"""
        if len(password) < 8:
            return False
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        return sum([has_upper, has_lower, has_digit, has_special]) >= 3
    
    def _generate_tokens(self, user: User) -> Dict[str, str]:
        """生成访问令牌和刷新令牌"""
        now = datetime.utcnow()
        expires_at = now + timedelta(hours=self.token_expire_hours)
        
        payload = {
            "user_id": user.id,
            "username": user.username,
            "role": user.role.value,
            "iat": now,
            "exp": expires_at
        }
        
        access_token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        refresh_token = secrets.token_urlsafe(32)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "expires_at": expires_at.isoformat()
        }
    
    def _verify_mfa(self, secret: str, code: str) -> bool:
        """验证MFA代码"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(code, valid_window=1)
        except Exception:
            return False

# 全局认证管理器实例
auth_manager = None

def get_auth_manager() -> AuthManager:
    """获取认证管理器实例"""
    global auth_manager
    if auth_manager is None:
        # 使用配置管理器获取密钥
        from backend.config_manager import get_config
        secret_key = get_config("system.secret_key", "default_secret_key")
        auth_manager = AuthManager(secret_key)
    return auth_manager

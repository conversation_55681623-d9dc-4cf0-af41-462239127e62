"""
统一的模块导入管理器
解决硬编码路径和导入不一致问题
"""
import os
import sys
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any
from importlib import import_module
from importlib.util import spec_from_file_location, module_from_spec

logger = logging.getLogger(__name__)


class ImportManager:
    """模块导入管理器"""
    
    def __init__(self):
        self.project_root = self._find_project_root()
        self.backend_path = self.project_root / "backend"
        self.initialized = False
        self._setup_paths()
    
    def _find_project_root(self) -> Path:
        """自动查找项目根目录"""
        current = Path(__file__).resolve()
        
        # 向上查找包含特定文件的目录
        markers = [
            "requirements.txt",
            ".git",
            "backend",
            "workers",
            "index.html"
        ]
        
        for parent in current.parents:
            if any((parent / marker).exists() for marker in markers):
                return parent
        
        # 如果找不到，使用当前文件的上级目录
        return current.parent.parent.parent
    
    def _setup_paths(self):
        """设置Python路径"""
        if self.initialized:
            return
        
        paths_to_add = [
            str(self.project_root),
            str(self.backend_path),
            str(self.backend_path / "app"),
        ]
        
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
        
        self.initialized = True
        logger.info(f"✅ Python路径已设置: {self.project_root}")
    
    def safe_import(self, module_name: str, package: Optional[str] = None) -> Optional[Any]:
        """安全导入模块"""
        try:
            if package:
                return import_module(module_name, package)
            else:
                return import_module(module_name)
        except ImportError as e:
            logger.error(f"导入模块失败: {module_name}, 错误: {e}")
            return None
        except Exception as e:
            logger.error(f"导入模块时发生未知错误: {module_name}, 错误: {e}")
            return None
    
    def import_from_file(self, file_path: str, module_name: str) -> Optional[Any]:
        """从文件路径导入模块"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return None
            
            spec = spec_from_file_location(module_name, file_path)
            if spec is None:
                logger.error(f"无法创建模块规范: {file_path}")
                return None
            
            module = module_from_spec(spec)
            if spec.loader is None:
                logger.error(f"无法获取模块加载器: {file_path}")
                return None
            
            spec.loader.exec_module(module)
            return module
            
        except Exception as e:
            logger.error(f"从文件导入模块失败: {file_path}, 错误: {e}")
            return None
    
    def get_backend_module(self, module_path: str) -> Optional[Any]:
        """获取backend模块"""
        try:
            # 标准化模块路径
            if module_path.startswith("backend."):
                module_path = module_path[8:]  # 移除 "backend." 前缀
            
            # 尝试不同的导入方式
            import_attempts = [
                f"app.{module_path}",
                module_path,
                f"backend.app.{module_path}",
                f"backend.{module_path}"
            ]
            
            for attempt in import_attempts:
                module = self.safe_import(attempt)
                if module is not None:
                    logger.debug(f"✅ 成功导入: {attempt}")
                    return module
            
            logger.error(f"所有导入尝试都失败: {module_path}")
            return None
            
        except Exception as e:
            logger.error(f"获取backend模块失败: {module_path}, 错误: {e}")
            return None
    
    def get_database_manager(self):
        """获取数据库管理器"""
        try:
            # 尝试导入数据库模块
            db_module = self.get_backend_module("database")
            if db_module and hasattr(db_module, 'DatabaseManager'):
                return db_module.DatabaseManager
            
            # 备用导入方式
            from database import DatabaseManager
            return DatabaseManager
            
        except Exception as e:
            logger.error(f"获取数据库管理器失败: {e}")
            return None
    
    def get_config_manager(self):
        """获取配置管理器"""
        try:
            config_module = self.get_backend_module("config_manager")
            if config_module and hasattr(config_module, 'ConfigManager'):
                return config_module.ConfigManager
            
            # 备用导入方式
            from config_manager import ConfigManager
            return ConfigManager
            
        except Exception as e:
            logger.error(f"获取配置管理器失败: {e}")
            return None
    
    def validate_imports(self) -> Dict[str, bool]:
        """验证关键模块导入"""
        validation_results = {}
        
        # 关键模块列表
        key_modules = {
            "database": "database",
            "config_manager": "config_manager",
            "ai_bookkeeping": "app.services.ai_bookkeeping",
            "ocr_processor": "app.services.ocr_processor",
            "database_security": "app.core.database_security",
            "database_auth": "app.core.database_auth",
        }
        
        for name, module_path in key_modules.items():
            module = self.get_backend_module(module_path)
            validation_results[name] = module is not None
            
            if module is not None:
                logger.info(f"✅ {name} 模块导入成功")
            else:
                logger.error(f"❌ {name} 模块导入失败")
        
        return validation_results
    
    def get_project_info(self) -> Dict[str, Any]:
        """获取项目信息"""
        return {
            "project_root": str(self.project_root),
            "backend_path": str(self.backend_path),
            "python_path": sys.path[:5],  # 显示前5个路径
            "initialized": self.initialized,
        }


# 全局导入管理器实例
_import_manager: Optional[ImportManager] = None


def get_import_manager() -> ImportManager:
    """获取导入管理器实例"""
    global _import_manager
    
    if _import_manager is None:
        _import_manager = ImportManager()
    
    return _import_manager


def setup_imports():
    """设置导入环境"""
    manager = get_import_manager()
    return manager


def safe_import_backend(module_path: str):
    """安全导入backend模块的便捷函数"""
    manager = get_import_manager()
    return manager.get_backend_module(module_path)


def validate_project_imports():
    """验证项目导入的便捷函数"""
    manager = get_import_manager()
    return manager.validate_imports()


# 便捷的导入函数
def get_database_manager():
    """获取数据库管理器"""
    manager = get_import_manager()
    return manager.get_database_manager()


def get_config_manager():
    """获取配置管理器"""
    manager = get_import_manager()
    return manager.get_config_manager()


# 初始化导入管理器
if __name__ != "__main__":
    setup_imports()

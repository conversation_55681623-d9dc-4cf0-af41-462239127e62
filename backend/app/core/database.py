"""
数据库连接管理模块
提供Cloudflare D1数据库的连接和操作功能
"""

import aiohttp
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class DatabaseConfig:
    """数据库配置"""
    account_id: str
    database_id: str
    api_token: str
    base_url: str = "https://api.cloudflare.com/client/v4"

class DatabaseConnection:
    """数据库连接类"""

    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def execute(self, sql: str, params: List[Any] = None) -> 'QueryResult':
        """执行SQL查询"""
        if not self.session:
            raise RuntimeError("Database connection not initialized")

        url = f"{self.config.base_url}/accounts/{self.config.account_id}/d1/database/{self.config.database_id}/query"

        headers = {
            "Authorization": f"Bearer {self.config.api_token}",
            "Content-Type": "application/json"
        }

        # 处理参数化查询（简化实现）
        if params:
            # 简单的参数替换，实际应该使用更安全的方法
            for param in params:
                if isinstance(param, str):
                    sql = sql.replace('?', f"'{param}'", 1)
                else:
                    sql = sql.replace('?', str(param), 1)

        data = {"sql": sql}

        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                if result.get('success'):
                    return QueryResult(result['result'][0])
                else:
                    raise DatabaseError(f"Query failed: {result.get('errors', [])}")
            else:
                error_text = await response.text()
                raise DatabaseError(f"HTTP {response.status}: {error_text}")

class QueryResult:
    """查询结果类"""

    def __init__(self, result_data: Dict[str, Any]):
        self.data = result_data
        self.results = result_data.get('results', [])
        self.success = result_data.get('success', False)
        self.meta = result_data.get('meta', {})

    def fetchone(self) -> Optional[Dict[str, Any]]:
        """获取一行结果"""
        if self.results:
            return self.results[0]
        return None

    def fetchall(self) -> List[Dict[str, Any]]:
        """获取所有结果"""
        return self.results

    def fetchmany(self, size: int) -> List[Dict[str, Any]]:
        """获取指定数量的结果"""
        return self.results[:size]

class DatabaseError(Exception):
    """数据库异常"""
    pass

# 全局数据库配置
_db_config = DatabaseConfig(
    account_id="bc6dbb1a5bb06691cdd6a8df6aa768f8",
    database_id="bcff395d-3e68-44c7-b355-13f3014fa240",
    api_token="7skRzBNWceiU1UPbEH33mQsE_CeBBpbNtGOY64TC"
)

async def get_database_connection() -> DatabaseConnection:
    """获取数据库连接"""
    return DatabaseConnection(_db_config)


# 数据库操作辅助函数
async def execute_query(sql: str, params: List[Any] = None) -> QueryResult:
    """执行单个查询"""
    async with DatabaseConnection(_db_config) as db:
        return await db.execute(sql, params)

async def execute_transaction(queries: List[tuple]) -> List[QueryResult]:
    """执行事务（多个查询）"""
    results = []
    async with DatabaseConnection(_db_config) as db:
        for sql, params in queries:
            result = await db.execute(sql, params)
            results.append(result)
    return results

# 数据库健康检查
async def check_database_health() -> Dict[str, Any]:
    """检查数据库健康状态"""
    try:
        result = await execute_query("SELECT 1 as health_check")
        return {
            "status": "healthy",
            "connection": "ok",
            "query_time": result.meta.get('duration', 0)
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "connection": "failed",
            "error": str(e)
        }

async def get_database_info() -> Dict[str, Any]:
    """获取数据库信息"""
    try:
        # 获取表数量
        tables_result = await execute_query("""
            SELECT COUNT(*) as table_count
            FROM sqlite_master
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
        """)

        return {
            "table_count": tables_result.fetchone()['table_count'],
            "database_size": 0,  # D1不直接提供大小信息
            "last_query_duration": 0
        }
    except Exception as e:
        return {
            "error": str(e)
        }

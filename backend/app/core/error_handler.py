"""
统一的错误处理管理器
提供结构化的异常处理和日志记录
"""
import logging
import traceback
import functools
from typing import Any, Callable, Dict, Optional, Type, Union, List
from enum import Enum
from datetime import datetime
import json


class ErrorLevel(Enum):
    """错误级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorCategory(Enum):
    """错误分类"""
    DATABASE = "DATABASE"
    IMPORT = "IMPORT"
    VALIDATION = "VALIDATION"
    AUTHENTICATION = "AUTHENTICATION"
    PERMISSION = "PERMISSION"
    NETWORK = "NETWORK"
    FILE_IO = "FILE_IO"
    AI_PROCESSING = "AI_PROCESSING"
    OCR_PROCESSING = "OCR_PROCESSING"
    CONFIGURATION = "CONFIGURATION"
    UNKNOWN = "UNKNOWN"


class GoldenLedgerError(Exception):
    """自定义基础异常类"""
    
    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        level: ErrorLevel = ErrorLevel.ERROR,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.level = level
        self.details = details or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message": self.message,
            "category": self.category.value,
            "level": self.level.value,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class DatabaseError(GoldenLedgerError):
    """数据库相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.DATABASE, **kwargs)


class ImportError(GoldenLedgerError):
    """导入相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.IMPORT, **kwargs)


class ValidationError(GoldenLedgerError):
    """验证相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.VALIDATION, **kwargs)


class AuthenticationError(GoldenLedgerError):
    """认证相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.AUTHENTICATION, **kwargs)


class PermissionError(GoldenLedgerError):
    """权限相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.PERMISSION, **kwargs)


class AIProcessingError(GoldenLedgerError):
    """AI处理相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.AI_PROCESSING, **kwargs)


class OCRProcessingError(GoldenLedgerError):
    """OCR处理相关错误"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.OCR_PROCESSING, **kwargs)


class ErrorHandler:
    """错误处理管理器"""
    
    def __init__(self, logger_name: str = "goldenledger"):
        self.logger = logging.getLogger(logger_name)
        self.error_stats: Dict[str, int] = {}
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志配置"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def handle_error(
        self,
        error: Union[Exception, GoldenLedgerError],
        context: Optional[Dict[str, Any]] = None,
        reraise: bool = False
    ) -> Optional[GoldenLedgerError]:
        """处理错误"""
        
        # 转换为自定义错误类型
        if isinstance(error, GoldenLedgerError):
            gl_error = error
        else:
            gl_error = self._convert_to_gl_error(error)
        
        # 添加上下文信息
        if context:
            gl_error.details.update(context)
        
        # 记录错误统计
        self._update_error_stats(gl_error)
        
        # 记录日志
        self._log_error(gl_error)
        
        # 是否重新抛出异常
        if reraise:
            raise gl_error
        
        return gl_error
    
    def _convert_to_gl_error(self, error: Exception) -> GoldenLedgerError:
        """将标准异常转换为自定义错误"""
        error_type = type(error).__name__
        
        # 根据异常类型确定分类
        category_mapping = {
            'ImportError': ErrorCategory.IMPORT,
            'ModuleNotFoundError': ErrorCategory.IMPORT,
            'sqlite3.Error': ErrorCategory.DATABASE,
            'sqlite3.OperationalError': ErrorCategory.DATABASE,
            'sqlite3.IntegrityError': ErrorCategory.DATABASE,
            'ValueError': ErrorCategory.VALIDATION,
            'TypeError': ErrorCategory.VALIDATION,
            'FileNotFoundError': ErrorCategory.FILE_IO,
            'PermissionError': ErrorCategory.PERMISSION,
            'ConnectionError': ErrorCategory.NETWORK,
            'TimeoutError': ErrorCategory.NETWORK,
        }
        
        category = category_mapping.get(error_type, ErrorCategory.UNKNOWN)
        
        return GoldenLedgerError(
            message=str(error),
            category=category,
            original_exception=error,
            details={
                'error_type': error_type,
                'traceback': traceback.format_exc()
            }
        )
    
    def _update_error_stats(self, error: GoldenLedgerError):
        """更新错误统计"""
        key = f"{error.category.value}_{error.level.value}"
        self.error_stats[key] = self.error_stats.get(key, 0) + 1
    
    def _log_error(self, error: GoldenLedgerError):
        """记录错误日志"""
        log_message = f"[{error.category.value}] {error.message}"
        
        if error.details:
            log_message += f" | Details: {json.dumps(error.details, default=str)}"
        
        # 根据错误级别选择日志方法
        log_method = getattr(self.logger, error.level.value.lower())
        log_method(log_message)
        
        # 如果有原始异常，记录完整的traceback
        if error.original_exception and error.level in [ErrorLevel.ERROR, ErrorLevel.CRITICAL]:
            self.logger.error(f"Original traceback: {traceback.format_exc()}")
    
    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计"""
        return self.error_stats.copy()
    
    def clear_error_stats(self):
        """清除错误统计"""
        self.error_stats.clear()


# 全局错误处理器
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取错误处理器实例"""
    global _error_handler
    
    if _error_handler is None:
        _error_handler = ErrorHandler()
    
    return _error_handler


def handle_error(
    error: Union[Exception, GoldenLedgerError],
    context: Optional[Dict[str, Any]] = None,
    reraise: bool = False
) -> Optional[GoldenLedgerError]:
    """处理错误的便捷函数"""
    handler = get_error_handler()
    return handler.handle_error(error, context, reraise)


def safe_execute(
    func: Callable,
    *args,
    default_return: Any = None,
    context: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Any:
    """安全执行函数"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handle_error(e, context)
        return default_return


def error_handler_decorator(
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    level: ErrorLevel = ErrorLevel.ERROR,
    reraise: bool = True,
    default_return: Any = None
):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                gl_error = GoldenLedgerError(
                    message=f"函数 {func.__name__} 执行失败: {str(e)}",
                    category=category,
                    level=level,
                    original_exception=e,
                    details={
                        'function': func.__name__,
                        'args': str(args)[:200],  # 限制长度
                        'kwargs': str(kwargs)[:200]
                    }
                )
                
                handle_error(gl_error, reraise=reraise)
                
                if not reraise:
                    return default_return
                
        return wrapper
    return decorator


# 便捷的装饰器
def database_error_handler(reraise: bool = True, default_return: Any = None):
    """数据库错误处理装饰器"""
    return error_handler_decorator(
        category=ErrorCategory.DATABASE,
        reraise=reraise,
        default_return=default_return
    )


def import_error_handler(reraise: bool = False, default_return: Any = None):
    """导入错误处理装饰器"""
    return error_handler_decorator(
        category=ErrorCategory.IMPORT,
        reraise=reraise,
        default_return=default_return
    )


def ai_processing_error_handler(reraise: bool = True, default_return: Any = None):
    """AI处理错误处理装饰器"""
    return error_handler_decorator(
        category=ErrorCategory.AI_PROCESSING,
        reraise=reraise,
        default_return=default_return
    )


def ocr_processing_error_handler(reraise: bool = True, default_return: Any = None):
    """OCR处理错误处理装饰器"""
    return error_handler_decorator(
        category=ErrorCategory.OCR_PROCESSING,
        reraise=reraise,
        default_return=default_return
    )

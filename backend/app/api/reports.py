"""
报表生成API路由
处理财务报表生成、统计分析等功能
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal

from ..core.security import UserContext, UserRole
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
reports_router = APIRouter(prefix="/reports", tags=["财务报表"])

# Pydantic模型
class ReportPeriod(BaseModel):
    start_date: date
    end_date: date
    company_id: str

class BalanceSheetItem(BaseModel):
    account_id: str
    account_code: str
    account_name: str
    account_type: str
    balance: Decimal
    parent_id: Optional[str] = None
    level: int = 0

class BalanceSheetResponse(BaseModel):
    company_id: str
    company_name: str
    period_start: str
    period_end: str
    generated_at: str
    
    # 资产
    current_assets: List[BalanceSheetItem] = []
    non_current_assets: List[BalanceSheetItem] = []
    total_assets: Decimal = Decimal('0.00')
    
    # 负债
    current_liabilities: List[BalanceSheetItem] = []
    non_current_liabilities: List[BalanceSheetItem] = []
    total_liabilities: Decimal = Decimal('0.00')
    
    # 所有者权益
    equity: List[BalanceSheetItem] = []
    total_equity: Decimal = Decimal('0.00')

class IncomeStatementItem(BaseModel):
    account_id: str
    account_code: str
    account_name: str
    account_type: str
    amount: Decimal
    parent_id: Optional[str] = None
    level: int = 0

class IncomeStatementResponse(BaseModel):
    company_id: str
    company_name: str
    period_start: str
    period_end: str
    generated_at: str
    
    # 收入
    revenue: List[IncomeStatementItem] = []
    total_revenue: Decimal = Decimal('0.00')
    
    # 成本
    cost_of_sales: List[IncomeStatementItem] = []
    total_cost_of_sales: Decimal = Decimal('0.00')
    
    # 费用
    expenses: List[IncomeStatementItem] = []
    total_expenses: Decimal = Decimal('0.00')
    
    # 利润
    gross_profit: Decimal = Decimal('0.00')
    net_profit: Decimal = Decimal('0.00')

class CashFlowItem(BaseModel):
    description: str
    amount: Decimal
    category: str  # operating, investing, financing

class CashFlowResponse(BaseModel):
    company_id: str
    company_name: str
    period_start: str
    period_end: str
    generated_at: str
    
    # 经营活动现金流
    operating_activities: List[CashFlowItem] = []
    net_operating_cash_flow: Decimal = Decimal('0.00')
    
    # 投资活动现金流
    investing_activities: List[CashFlowItem] = []
    net_investing_cash_flow: Decimal = Decimal('0.00')
    
    # 筹资活动现金流
    financing_activities: List[CashFlowItem] = []
    net_financing_cash_flow: Decimal = Decimal('0.00')
    
    # 现金净变动
    net_cash_change: Decimal = Decimal('0.00')

class TrialBalanceItem(BaseModel):
    account_id: str
    account_code: str
    account_name: str
    account_type: str
    opening_balance: Decimal
    debit_amount: Decimal
    credit_amount: Decimal
    closing_balance: Decimal

class TrialBalanceResponse(BaseModel):
    company_id: str
    company_name: str
    period_start: str
    period_end: str
    generated_at: str
    
    items: List[TrialBalanceItem] = []
    total_debit: Decimal = Decimal('0.00')
    total_credit: Decimal = Decimal('0.00')
    is_balanced: bool = True

class ReportSummary(BaseModel):
    report_type: str
    company_id: str
    company_name: str
    period_start: str
    period_end: str
    generated_at: str
    key_metrics: Dict[str, Any] = {}

# 权限检查函数
def require_report_read(current_user: UserContext = Depends(get_current_user)):
    """需要报表读取权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT, UserRole.VIEWER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要报表读取权限"
        )
    return current_user

# API路由
@reports_router.get("/balance-sheet", response_model=BalanceSheetResponse)
async def generate_balance_sheet(
    company_id: str = Query(...),
    start_date: date = Query(...),
    end_date: date = Query(...),
    current_user: UserContext = Depends(require_report_read)
):
    """生成资产负债表"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 获取公司信息
        company_info = await get_company_info(db, company_id)
        
        # 获取资产科目余额
        assets_query = """
            SELECT as_.id, as_.code, as_.name, as_.type, as_.parent_id,
                   COALESCE(SUM(jel.debit_amount) - SUM(jel.credit_amount), 0) as balance
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE as_.company_id = ? AND as_.type = 'asset' AND as_.is_active = TRUE
            AND (je.entry_date <= ? OR je.entry_date IS NULL)
            AND (je.status = 'posted' OR je.status IS NULL)
            GROUP BY as_.id
            HAVING balance != 0
            ORDER BY as_.code
        """
        
        assets_result = await db.execute(assets_query, [company_id, end_date.isoformat()])
        
        current_assets = []
        non_current_assets = []
        total_assets = Decimal('0.00')
        
        for row in assets_result.fetchall():
            balance = Decimal(str(row['balance']))
            total_assets += balance
            
            item = BalanceSheetItem(
                account_id=row['id'],
                account_code=row['code'],
                account_name=row['name'],
                account_type=row['type'],
                balance=balance,
                parent_id=row['parent_id']
            )
            
            # 简单分类：1001-1499为流动资产，1500以上为非流动资产
            if row['code'].startswith('1') and int(row['code']) < 1500:
                current_assets.append(item)
            else:
                non_current_assets.append(item)
        
        # 获取负债科目余额
        liabilities_query = """
            SELECT as_.id, as_.code, as_.name, as_.type, as_.parent_id,
                   COALESCE(SUM(jel.credit_amount) - SUM(jel.debit_amount), 0) as balance
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE as_.company_id = ? AND as_.type = 'liability' AND as_.is_active = TRUE
            AND (je.entry_date <= ? OR je.entry_date IS NULL)
            AND (je.status = 'posted' OR je.status IS NULL)
            GROUP BY as_.id
            HAVING balance != 0
            ORDER BY as_.code
        """
        
        liabilities_result = await db.execute(liabilities_query, [company_id, end_date.isoformat()])
        
        current_liabilities = []
        non_current_liabilities = []
        total_liabilities = Decimal('0.00')
        
        for row in liabilities_result.fetchall():
            balance = Decimal(str(row['balance']))
            total_liabilities += balance
            
            item = BalanceSheetItem(
                account_id=row['id'],
                account_code=row['code'],
                account_name=row['name'],
                account_type=row['type'],
                balance=balance,
                parent_id=row['parent_id']
            )
            
            # 简单分类：2001-2499为流动负债，2500以上为非流动负债
            if row['code'].startswith('2') and int(row['code']) < 2500:
                current_liabilities.append(item)
            else:
                non_current_liabilities.append(item)
        
        # 获取所有者权益科目余额
        equity_query = """
            SELECT as_.id, as_.code, as_.name, as_.type, as_.parent_id,
                   COALESCE(SUM(jel.credit_amount) - SUM(jel.debit_amount), 0) as balance
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE as_.company_id = ? AND as_.type = 'equity' AND as_.is_active = TRUE
            AND (je.entry_date <= ? OR je.entry_date IS NULL)
            AND (je.status = 'posted' OR je.status IS NULL)
            GROUP BY as_.id
            HAVING balance != 0
            ORDER BY as_.code
        """
        
        equity_result = await db.execute(equity_query, [company_id, end_date.isoformat()])
        
        equity = []
        total_equity = Decimal('0.00')
        
        for row in equity_result.fetchall():
            balance = Decimal(str(row['balance']))
            total_equity += balance
            
            item = BalanceSheetItem(
                account_id=row['id'],
                account_code=row['code'],
                account_name=row['name'],
                account_type=row['type'],
                balance=balance,
                parent_id=row['parent_id']
            )
            equity.append(item)
        
        return BalanceSheetResponse(
            company_id=company_id,
            company_name=company_info['name'],
            period_start=start_date.isoformat(),
            period_end=end_date.isoformat(),
            generated_at=datetime.utcnow().isoformat(),
            current_assets=current_assets,
            non_current_assets=non_current_assets,
            total_assets=total_assets,
            current_liabilities=current_liabilities,
            non_current_liabilities=non_current_liabilities,
            total_liabilities=total_liabilities,
            equity=equity,
            total_equity=total_equity
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成资产负债表失败: {str(e)}"
        )

@reports_router.get("/income-statement", response_model=IncomeStatementResponse)
async def generate_income_statement(
    company_id: str = Query(...),
    start_date: date = Query(...),
    end_date: date = Query(...),
    current_user: UserContext = Depends(require_report_read)
):
    """生成利润表"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 获取公司信息
        company_info = await get_company_info(db, company_id)
        
        # 获取收入科目
        revenue_query = """
            SELECT as_.id, as_.code, as_.name, as_.type, as_.parent_id,
                   COALESCE(SUM(jel.credit_amount) - SUM(jel.debit_amount), 0) as amount
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE as_.company_id = ? AND as_.type = 'revenue' AND as_.is_active = TRUE
            AND je.entry_date BETWEEN ? AND ?
            AND je.status = 'posted'
            GROUP BY as_.id
            HAVING amount != 0
            ORDER BY as_.code
        """
        
        revenue_result = await db.execute(revenue_query, [company_id, start_date.isoformat(), end_date.isoformat()])
        
        revenue = []
        total_revenue = Decimal('0.00')
        
        for row in revenue_result.fetchall():
            amount = Decimal(str(row['amount']))
            total_revenue += amount
            
            item = IncomeStatementItem(
                account_id=row['id'],
                account_code=row['code'],
                account_name=row['name'],
                account_type=row['type'],
                amount=amount,
                parent_id=row['parent_id']
            )
            revenue.append(item)
        
        # 获取成本科目
        cost_query = """
            SELECT as_.id, as_.code, as_.name, as_.type, as_.parent_id,
                   COALESCE(SUM(jel.debit_amount) - SUM(jel.credit_amount), 0) as amount
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE as_.company_id = ? AND as_.type = 'expense' AND as_.code LIKE '5%' AND as_.is_active = TRUE
            AND je.entry_date BETWEEN ? AND ?
            AND je.status = 'posted'
            GROUP BY as_.id
            HAVING amount != 0
            ORDER BY as_.code
        """
        
        cost_result = await db.execute(cost_query, [company_id, start_date.isoformat(), end_date.isoformat()])
        
        cost_of_sales = []
        total_cost_of_sales = Decimal('0.00')
        
        for row in cost_result.fetchall():
            amount = Decimal(str(row['amount']))
            total_cost_of_sales += amount
            
            item = IncomeStatementItem(
                account_id=row['id'],
                account_code=row['code'],
                account_name=row['name'],
                account_type=row['type'],
                amount=amount,
                parent_id=row['parent_id']
            )
            cost_of_sales.append(item)
        
        # 获取费用科目
        expenses_query = """
            SELECT as_.id, as_.code, as_.name, as_.type, as_.parent_id,
                   COALESCE(SUM(jel.debit_amount) - SUM(jel.credit_amount), 0) as amount
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE as_.company_id = ? AND as_.type = 'expense' AND as_.code LIKE '6%' AND as_.is_active = TRUE
            AND je.entry_date BETWEEN ? AND ?
            AND je.status = 'posted'
            GROUP BY as_.id
            HAVING amount != 0
            ORDER BY as_.code
        """
        
        expenses_result = await db.execute(expenses_query, [company_id, start_date.isoformat(), end_date.isoformat()])
        
        expenses = []
        total_expenses = Decimal('0.00')
        
        for row in expenses_result.fetchall():
            amount = Decimal(str(row['amount']))
            total_expenses += amount
            
            item = IncomeStatementItem(
                account_id=row['id'],
                account_code=row['code'],
                account_name=row['name'],
                account_type=row['type'],
                amount=amount,
                parent_id=row['parent_id']
            )
            expenses.append(item)
        
        # 计算利润
        gross_profit = total_revenue - total_cost_of_sales
        net_profit = gross_profit - total_expenses
        
        return IncomeStatementResponse(
            company_id=company_id,
            company_name=company_info['name'],
            period_start=start_date.isoformat(),
            period_end=end_date.isoformat(),
            generated_at=datetime.utcnow().isoformat(),
            revenue=revenue,
            total_revenue=total_revenue,
            cost_of_sales=cost_of_sales,
            total_cost_of_sales=total_cost_of_sales,
            expenses=expenses,
            total_expenses=total_expenses,
            gross_profit=gross_profit,
            net_profit=net_profit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成利润表失败: {str(e)}"
        )

@reports_router.get("/trial-balance", response_model=TrialBalanceResponse)
async def generate_trial_balance(
    company_id: str = Query(...),
    start_date: date = Query(...),
    end_date: date = Query(...),
    current_user: UserContext = Depends(require_report_read)
):
    """生成试算平衡表"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 获取公司信息
        company_info = await get_company_info(db, company_id)
        
        # 获取所有科目的余额
        query = """
            SELECT as_.id, as_.code, as_.name, as_.type,
                   COALESCE(SUM(CASE WHEN je.entry_date < ? THEN jel.debit_amount - jel.credit_amount ELSE 0 END), 0) as opening_balance,
                   COALESCE(SUM(CASE WHEN je.entry_date BETWEEN ? AND ? THEN jel.debit_amount ELSE 0 END), 0) as debit_amount,
                   COALESCE(SUM(CASE WHEN je.entry_date BETWEEN ? AND ? THEN jel.credit_amount ELSE 0 END), 0) as credit_amount
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id AND je.status = 'posted'
            WHERE as_.company_id = ? AND as_.is_active = TRUE
            GROUP BY as_.id
            ORDER BY as_.code
        """
        
        result = await db.execute(query, [
            start_date.isoformat(),
            start_date.isoformat(), end_date.isoformat(),
            start_date.isoformat(), end_date.isoformat(),
            company_id
        ])
        
        items = []
        total_debit = Decimal('0.00')
        total_credit = Decimal('0.00')
        
        for row in result.fetchall():
            opening_balance = Decimal(str(row['opening_balance']))
            debit_amount = Decimal(str(row['debit_amount']))
            credit_amount = Decimal(str(row['credit_amount']))
            
            # 计算期末余额
            if row['type'] in ['asset', 'expense']:
                closing_balance = opening_balance + debit_amount - credit_amount
            else:  # liability, equity, revenue
                closing_balance = opening_balance + credit_amount - debit_amount
            
            # 只显示有余额的科目
            if opening_balance != 0 or debit_amount != 0 or credit_amount != 0 or closing_balance != 0:
                item = TrialBalanceItem(
                    account_id=row['id'],
                    account_code=row['code'],
                    account_name=row['name'],
                    account_type=row['type'],
                    opening_balance=opening_balance,
                    debit_amount=debit_amount,
                    credit_amount=credit_amount,
                    closing_balance=closing_balance
                )
                items.append(item)
                
                total_debit += debit_amount
                total_credit += credit_amount
        
        # 检查借贷平衡
        is_balanced = abs(total_debit - total_credit) < Decimal('0.01')
        
        return TrialBalanceResponse(
            company_id=company_id,
            company_name=company_info['name'],
            period_start=start_date.isoformat(),
            period_end=end_date.isoformat(),
            generated_at=datetime.utcnow().isoformat(),
            items=items,
            total_debit=total_debit,
            total_credit=total_credit,
            is_balanced=is_balanced
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成试算平衡表失败: {str(e)}"
        )

# 辅助函数
async def check_company_access(db, current_user: UserContext, company_id: str) -> bool:
    """检查用户是否有权限访问公司"""
    if current_user.role == UserRole.SYSTEM_ADMIN:
        return True
    
    # 检查公司是否属于用户的租户
    if current_user.tenant_id:
        company_check = await db.execute(
            "SELECT id FROM companies WHERE id = ? AND tenant_id = ?",
            [company_id, current_user.tenant_id]
        )
        return company_check.fetchone() is not None
    
    return False

async def get_company_info(db, company_id: str) -> dict:
    """获取公司信息"""
    result = await db.execute(
        "SELECT id, name FROM companies WHERE id = ?",
        [company_id]
    )
    company = result.fetchone()
    
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="公司不存在"
        )
    
    return company

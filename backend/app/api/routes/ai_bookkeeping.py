"""
AI全自动记账API路由
支持自然语言输入和图片/PDF处理
"""
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import Optional, List
from pydantic import BaseModel
import asyncio

from app.services.ai_bookkeeping import get_ai_bookkeeping_engine, JournalEntry
from app.services.ocr_processor import get_ocr_processor
from app.core.config import settings


router = APIRouter()


class NaturalLanguageRequest(BaseModel):
    """自然语言记账请求"""
    text: str
    company_id: str = "default"


class JournalEntryResponse(BaseModel):
    """仕訳响应"""
    success: bool
    journal_entry: Optional[dict] = None
    confidence: float = 0.0
    warnings: List[str] = []
    suggestions: List[str] = []
    error: Optional[str] = None


@router.post("/natural-language", response_model=JournalEntryResponse)
async def process_natural_language(request: NaturalLanguageRequest):
    """
    🗣️ 一句话全自动记账
    
    用户只需要说一句话，AI就能自动生成完整的仕訳分录
    
    示例输入：
    - "今天收到田中先生的设计费50万日元"
    - "在便利店买了1200日元的办公用品"
    - "支付了这个月的房租15万日元"
    """
    try:
        # 获取AI记账引擎
        ai_engine = get_ai_bookkeeping_engine(request.company_id)
        
        # 处理自然语言输入
        journal_entry = await ai_engine.process_natural_language(request.text)
        
        return JournalEntryResponse(
            success=True,
            journal_entry=journal_entry.dict(),
            confidence=journal_entry.confidence,
            warnings=journal_entry.warnings,
            suggestions=journal_entry.suggestions
        )
        
    except Exception as e:
        print(f"❌ 自然语言处理失败: {e}")
        return JournalEntryResponse(
            success=False,
            error=str(e)
        )


@router.post("/upload-image", response_model=JournalEntryResponse)
async def process_image_upload(
    file: UploadFile = File(...),
    company_id: str = Form("default")
):
    """
    📸 图片智能记账
    
    上传发票、收据图片，AI自动识别并生成仕訳分录
    
    支持格式：JPG, PNG, PDF
    最大文件大小：10MB
    """
    try:
        # 验证文件类型
        if not file.content_type.startswith(('image/', 'application/pdf')):
            raise HTTPException(
                status_code=400,
                detail="不支持的文件类型。请上传图片（JPG, PNG）或PDF文件。"
            )
        
        # 验证文件大小
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"文件过大。最大支持{settings.MAX_FILE_SIZE // 1024 // 1024}MB。"
            )
        
        # 获取OCR处理器
        ocr_processor = get_ocr_processor(company_id)
        
        # 处理图片或PDF
        if file.content_type == 'application/pdf':
            results = await ocr_processor.process_pdf(file_content, file.filename)
            # 返回第一页的结果
            result = results[0] if results else {"success": False, "error": "PDF处理失败"}
        else:
            result = await ocr_processor.process_image(file_content, file.filename)
        
        if result["success"]:
            return JournalEntryResponse(
                success=True,
                journal_entry=result["journal_entry"],
                confidence=result["confidence"],
                warnings=result["journal_entry"]["warnings"] if result["journal_entry"] else [],
                suggestions=result["journal_entry"]["suggestions"] if result["journal_entry"] else []
            )
        else:
            return JournalEntryResponse(
                success=False,
                error=result["error"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 图片处理失败: {e}")
        return JournalEntryResponse(
            success=False,
            error=str(e)
        )


@router.post("/batch-upload", response_model=List[JournalEntryResponse])
async def process_batch_upload(
    files: List[UploadFile] = File(...),
    company_id: str = Form("default")
):
    """
    📚 批量文件处理
    
    一次上传多个发票/收据文件，批量生成仕訳分录
    """
    try:
        if len(files) > 10:
            raise HTTPException(
                status_code=400,
                detail="一次最多只能上传10个文件"
            )
        
        # 获取OCR处理器
        ocr_processor = get_ocr_processor(company_id)
        
        # 并行处理所有文件
        tasks = []
        for file in files:
            file_content = await file.read()
            
            if file.content_type == 'application/pdf':
                task = ocr_processor.process_pdf(file_content, file.filename)
            else:
                task = ocr_processor.process_image(file_content, file.filename)
            
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                responses.append(JournalEntryResponse(
                    success=False,
                    error=str(result)
                ))
            elif isinstance(result, list):  # PDF结果
                for page_result in result:
                    if page_result["success"]:
                        responses.append(JournalEntryResponse(
                            success=True,
                            journal_entry=page_result["journal_entry"],
                            confidence=page_result["confidence"]
                        ))
                    else:
                        responses.append(JournalEntryResponse(
                            success=False,
                            error=page_result["error"]
                        ))
            else:  # 图片结果
                if result["success"]:
                    responses.append(JournalEntryResponse(
                        success=True,
                        journal_entry=result["journal_entry"],
                        confidence=result["confidence"]
                    ))
                else:
                    responses.append(JournalEntryResponse(
                        success=False,
                        error=result["error"]
                    ))
        
        return responses
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 批量处理失败: {e}")
        return [JournalEntryResponse(
            success=False,
            error=str(e)
        )]


@router.get("/demo")
async def demo_examples():
    """
    📝 演示示例
    
    返回一些演示用的输入示例，帮助用户了解如何使用AI记账功能
    """
    return {
        "natural_language_examples": [
            "今天收到ABC公司的咨询费30万日元，银行转账",
            "在便利店买了办公用品1200日元，现金支付",
            "支付了这个月的房租15万日元",
            "收到客户的货款50万日元",
            "购买了新的电脑设备20万日元",
            "支付员工工资总计80万日元",
            "收到银行利息收入5000日元",
            "支付广告费用8万日元"
        ],
        "tips": [
            "💡 说话越自然越好，AI能理解日常用语",
            "💡 包含金额、对方、支付方式信息效果更好",
            "💡 可以上传发票图片让AI自动识别",
            "💡 AI会自动选择合适的会计科目",
            "💡 生成的仕訳可以编辑和确认"
        ],
        "supported_formats": [
            "📄 自然语言文字输入",
            "📸 JPG/PNG图片上传",
            "📋 PDF文件上传",
            "📚 批量文件处理"
        ]
    }


@router.get("/stats/{company_id}")
async def get_ai_stats(company_id: str):
    """
    📊 AI记账统计
    
    获取AI记账功能的使用统计信息
    """
    try:
        # 这里可以添加统计逻辑
        # 暂时返回模拟数据
        return {
            "total_processed": 156,
            "success_rate": 0.94,
            "avg_confidence": 0.87,
            "most_common_accounts": [
                {"account": "消耗品費", "count": 45},
                {"account": "売上高", "count": 38},
                {"account": "地代家賃", "count": 23}
            ],
            "recent_activity": [
                {"date": "2024-01-01", "count": 12},
                {"date": "2024-01-02", "count": 8},
                {"date": "2024-01-03", "count": 15}
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"统计信息获取失败: {str(e)}"
        )

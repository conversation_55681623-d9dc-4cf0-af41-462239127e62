"""
仕訳帳管理API路由
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel
from datetime import date, datetime
from uuid import UUID

from app.core.database import get_database


router = APIRouter()


class JournalEntryCreate(BaseModel):
    """创建仕訳请求"""
    entry_date: date
    description: str
    debit_account: str
    credit_account: str
    amount: float
    reference_number: Optional[str] = None


class JournalEntryResponse(BaseModel):
    """仕訳响应"""
    id: str
    entry_date: date
    description: str
    debit_account: str
    credit_account: str
    amount: float
    reference_number: Optional[str]
    status: str
    created_at: datetime


@router.post("/{company_id}", response_model=JournalEntryResponse)
async def create_journal_entry(company_id: str, entry: JournalEntryCreate):
    """
    ➕ 创建新的仕訳分录
    """
    try:
        db = await get_database()
        
        # 创建仕訳主记录
        journal_id = await db.fetchval("""
            INSERT INTO journal_entries 
            (company_id, entry_date, description, reference_number, total_amount, status)
            VALUES ($1, $2, $3, $4, $5, 'confirmed')
            RETURNING id
        """, company_id, entry.entry_date, entry.description, 
             entry.reference_number, entry.amount)
        
        # 获取会计科目ID
        debit_subject = await db.fetchrow("""
            SELECT id FROM account_subjects 
            WHERE company_id = $1 AND name = $2
        """, company_id, entry.debit_account)
        
        credit_subject = await db.fetchrow("""
            SELECT id FROM account_subjects 
            WHERE company_id = $1 AND name = $2
        """, company_id, entry.credit_account)
        
        if not debit_subject or not credit_subject:
            raise HTTPException(
                status_code=400,
                detail="指定的会计科目不存在"
            )
        
        # 创建借方明细
        await db.execute("""
            INSERT INTO journal_details 
            (journal_entry_id, account_subject_id, debit_amount, description)
            VALUES ($1, $2, $3, $4)
        """, journal_id, debit_subject["id"], entry.amount, entry.description)
        
        # 创建贷方明细
        await db.execute("""
            INSERT INTO journal_details 
            (journal_entry_id, account_subject_id, credit_amount, description)
            VALUES ($1, $2, $3, $4)
        """, journal_id, credit_subject["id"], entry.amount, entry.description)
        
        # 返回创建的记录
        created_entry = await db.fetchrow("""
            SELECT * FROM journal_entries WHERE id = $1
        """, journal_id)
        
        return JournalEntryResponse(
            id=str(created_entry["id"]),
            entry_date=created_entry["entry_date"],
            description=created_entry["description"],
            debit_account=entry.debit_account,
            credit_account=entry.credit_account,
            amount=float(created_entry["total_amount"]),
            reference_number=created_entry["reference_number"],
            status=created_entry["status"],
            created_at=created_entry["created_at"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"仕訳作成失败: {str(e)}"
        )


@router.get("/{company_id}", response_model=List[JournalEntryResponse])
async def get_journal_entries(
    company_id: str,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    account: Optional[str] = Query(None),
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0)
):
    """
    📋 获取仕訳分录列表
    """
    try:
        db = await get_database()
        
        # 构建查询条件
        conditions = ["je.company_id = $1"]
        params = [company_id]
        param_count = 1
        
        if start_date:
            param_count += 1
            conditions.append(f"je.entry_date >= ${param_count}")
            params.append(start_date)
        
        if end_date:
            param_count += 1
            conditions.append(f"je.entry_date <= ${param_count}")
            params.append(end_date)
        
        where_clause = " AND ".join(conditions)
        
        # 查询仕訳记录
        query = f"""
            SELECT DISTINCT je.*, 
                   d_subj.name as debit_account,
                   c_subj.name as credit_account
            FROM journal_entries je
            LEFT JOIN journal_details jd_debit ON je.id = jd_debit.journal_entry_id AND jd_debit.debit_amount > 0
            LEFT JOIN journal_details jd_credit ON je.id = jd_credit.journal_entry_id AND jd_credit.credit_amount > 0
            LEFT JOIN account_subjects d_subj ON jd_debit.account_subject_id = d_subj.id
            LEFT JOIN account_subjects c_subj ON jd_credit.account_subject_id = c_subj.id
            WHERE {where_clause}
            ORDER BY je.entry_date DESC, je.created_at DESC
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        
        params.extend([limit, offset])
        entries = await db.fetch(query, *params)
        
        return [
            JournalEntryResponse(
                id=str(entry["id"]),
                entry_date=entry["entry_date"],
                description=entry["description"],
                debit_account=entry["debit_account"] or "不明",
                credit_account=entry["credit_account"] or "不明",
                amount=float(entry["total_amount"]),
                reference_number=entry["reference_number"],
                status=entry["status"],
                created_at=entry["created_at"]
            )
            for entry in entries
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"仕訳取得失败: {str(e)}"
        )


@router.get("/{company_id}/{entry_id}", response_model=JournalEntryResponse)
async def get_journal_entry(company_id: str, entry_id: str):
    """
    📄 获取单个仕訳分录详情
    """
    try:
        db = await get_database()
        
        entry = await db.fetchrow("""
            SELECT je.*, 
                   d_subj.name as debit_account,
                   c_subj.name as credit_account
            FROM journal_entries je
            LEFT JOIN journal_details jd_debit ON je.id = jd_debit.journal_entry_id AND jd_debit.debit_amount > 0
            LEFT JOIN journal_details jd_credit ON je.id = jd_credit.journal_entry_id AND jd_credit.credit_amount > 0
            LEFT JOIN account_subjects d_subj ON jd_debit.account_subject_id = d_subj.id
            LEFT JOIN account_subjects c_subj ON jd_credit.account_subject_id = c_subj.id
            WHERE je.id = $1 AND je.company_id = $2
        """, entry_id, company_id)
        
        if not entry:
            raise HTTPException(
                status_code=404,
                detail="仕訳が見つかりません"
            )
        
        return JournalEntryResponse(
            id=str(entry["id"]),
            entry_date=entry["entry_date"],
            description=entry["description"],
            debit_account=entry["debit_account"] or "不明",
            credit_account=entry["credit_account"] or "不明",
            amount=float(entry["total_amount"]),
            reference_number=entry["reference_number"],
            status=entry["status"],
            created_at=entry["created_at"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"仕訳取得失败: {str(e)}"
        )


@router.put("/{company_id}/{entry_id}")
async def update_journal_entry(
    company_id: str, 
    entry_id: str, 
    entry: JournalEntryCreate
):
    """
    ✏️ 更新仕訳分录
    """
    try:
        db = await get_database()
        
        # 更新主记录
        await db.execute("""
            UPDATE journal_entries 
            SET entry_date = $1, description = $2, reference_number = $3, total_amount = $4
            WHERE id = $5 AND company_id = $6
        """, entry.entry_date, entry.description, entry.reference_number, 
             entry.amount, entry_id, company_id)
        
        return {"success": True, "message": "仕訳が更新されました"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"仕訳更新失败: {str(e)}"
        )


@router.delete("/{company_id}/{entry_id}")
async def delete_journal_entry(company_id: str, entry_id: str):
    """
    🗑️ 删除仕訳分录
    """
    try:
        db = await get_database()
        
        # 删除明细记录
        await db.execute("""
            DELETE FROM journal_details 
            WHERE journal_entry_id = $1
        """, entry_id)
        
        # 删除主记录
        await db.execute("""
            DELETE FROM journal_entries 
            WHERE id = $1 AND company_id = $2
        """, entry_id, company_id)
        
        return {"success": True, "message": "仕訳が削除されました"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"仕訳削除失败: {str(e)}"
        )


@router.get("/{company_id}/accounts")
async def get_account_subjects(company_id: str):
    """
    📊 获取会计科目列表
    """
    try:
        db = await get_database()
        
        subjects = await db.fetch("""
            SELECT code, name, category, subcategory
            FROM account_subjects
            WHERE company_id = $1 AND is_active = TRUE
            ORDER BY code
        """, company_id)
        
        # 按分类组织
        grouped = {}
        for subject in subjects:
            category = subject["category"]
            if category not in grouped:
                grouped[category] = []
            grouped[category].append({
                "code": subject["code"],
                "name": subject["name"],
                "subcategory": subject["subcategory"]
            })
        
        return grouped
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"会计科目取得失败: {str(e)}"
        )

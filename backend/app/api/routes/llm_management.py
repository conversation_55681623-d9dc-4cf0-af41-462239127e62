"""
LLM模型管理API路由
支持多供应商模型配置和管理
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime

from app.services.llm_manager import get_model_manager, get_llm_client, ModelProvider, TaskType
from app.core.database import get_database


router = APIRouter()


class ModelConfigRequest(BaseModel):
    """模型配置请求"""
    provider: ModelProvider
    model_name: str
    model_id: str
    api_key: str
    task_types: List[TaskType]
    priority: int = 1
    temperature: float = 0.1
    max_tokens: int = 2048


class ModelConfigResponse(BaseModel):
    """模型配置响应"""
    id: str
    provider: str
    model_name: str
    model_id: str
    task_types: List[str]
    priority: int
    temperature: float
    max_tokens: int
    is_active: bool
    created_at: datetime


class UsageStatsResponse(BaseModel):
    """使用统计响应"""
    provider: str
    total_requests: int
    total_tokens: int
    total_cost: float
    avg_response_time: float


class TestModelRequest(BaseModel):
    """模型测试请求"""
    provider: ModelProvider
    model_id: str
    api_key: str
    test_prompt: str = "こんにちは。これはテストメッセージです。"


@router.get("/models/{company_id}", response_model=List[ModelConfigResponse])
async def get_model_configurations(company_id: str):
    """
    📋 获取公司的所有模型配置
    """
    try:
        db = await get_database()
        
        configs = await db.fetch("""
            SELECT mc.*, lm.name as model_name, lm.model_id, lm.provider
            FROM model_configurations mc
            JOIN llm_models lm ON mc.llm_model_id = lm.id
            WHERE mc.company_id = $1
            ORDER BY mc.priority ASC
        """, company_id)
        
        # 按任务类型分组
        grouped_configs = {}
        for config in configs:
            key = (config["llm_model_id"], config["provider"], config["model_name"])
            if key not in grouped_configs:
                grouped_configs[key] = {
                    "id": str(config["id"]),
                    "provider": config["provider"],
                    "model_name": config["model_name"],
                    "model_id": config["model_id"],
                    "task_types": [],
                    "priority": config["priority"],
                    "temperature": float(config["temperature"]),
                    "max_tokens": config["max_tokens"],
                    "is_active": config["is_active"],
                    "created_at": config["created_at"]
                }
            grouped_configs[key]["task_types"].append(config["task_type"])
        
        return list(grouped_configs.values())
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"模型配置获取失败: {str(e)}"
        )


@router.post("/models/{company_id}")
async def add_model_configuration(company_id: str, config: ModelConfigRequest):
    """
    ➕ 添加新的模型配置
    """
    try:
        model_manager = get_model_manager(company_id)
        
        # 添加模型配置
        await model_manager._add_model_configuration(await get_database(), {
            "provider": config.provider,
            "model_name": config.model_name,
            "model_id": config.model_id,
            "api_key": config.api_key,
            "task_types": config.task_types,
            "priority": config.priority,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens
        })
        
        return {"success": True, "message": "模型配置已添加"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"模型配置添加失败: {str(e)}"
        )


@router.put("/models/{company_id}/{config_id}")
async def update_model_configuration(
    company_id: str, 
    config_id: str, 
    config: ModelConfigRequest
):
    """
    ✏️ 更新模型配置
    """
    try:
        db = await get_database()
        
        # 更新模型配置
        await db.execute("""
            UPDATE model_configurations 
            SET priority = $1, temperature = $2, max_tokens = $3, is_active = $4
            WHERE id = $5 AND company_id = $6
        """, config.priority, config.temperature, config.max_tokens, 
             True, config_id, company_id)
        
        return {"success": True, "message": "模型配置已更新"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"模型配置更新失败: {str(e)}"
        )


@router.delete("/models/{company_id}/{config_id}")
async def delete_model_configuration(company_id: str, config_id: str):
    """
    🗑️ 删除模型配置
    """
    try:
        db = await get_database()
        
        # 软删除（设置为非活跃状态）
        await db.execute("""
            UPDATE model_configurations 
            SET is_active = FALSE
            WHERE id = $1 AND company_id = $2
        """, config_id, company_id)
        
        return {"success": True, "message": "模型配置已删除"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"模型配置删除失败: {str(e)}"
        )


@router.post("/test")
async def test_model_connection(request: TestModelRequest):
    """
    🧪 测试模型连接
    """
    try:
        # 创建临时LLM客户端进行测试
        from app.services.llm_manager import UnifiedLLMClient
        
        # 模拟配置
        test_config = {
            "provider": request.provider,
            "model_id": request.model_id,
            "api_key": request.api_key,
            "temperature": 0.1,
            "max_tokens": 100
        }
        
        # 创建临时客户端
        client = UnifiedLLMClient("test")
        
        # 根据供应商调用对应的方法
        if request.provider == ModelProvider.GOOGLE:
            response = await client._call_gemini(test_config, request.test_prompt)
        elif request.provider == ModelProvider.OPENAI:
            response = await client._call_openai(test_config, request.test_prompt)
        elif request.provider == ModelProvider.ANTHROPIC:
            response = await client._call_anthropic(test_config, request.test_prompt)
        else:
            raise ValueError(f"不支持的供应商: {request.provider}")
        
        return {
            "success": True,
            "response": response["text"][:100] + "..." if len(response["text"]) > 100 else response["text"],
            "model": response["model"],
            "provider": response["provider"]
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/usage/{company_id}", response_model=List[UsageStatsResponse])
async def get_usage_statistics(company_id: str, year_month: str = "2024-01"):
    """
    📊 获取模型使用统计
    """
    try:
        model_manager = get_model_manager(company_id)
        stats = await model_manager.get_usage_statistics(year_month)
        
        return [
            UsageStatsResponse(
                provider=stat["provider"],
                total_requests=stat["total_requests"],
                total_tokens=stat["total_tokens"],
                total_cost=float(stat["total_cost"]),
                avg_response_time=float(stat["avg_response_time"])
            )
            for stat in stats
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"使用统计获取失败: {str(e)}"
        )


@router.get("/providers")
async def get_supported_providers():
    """
    🏢 获取支持的模型供应商列表
    """
    return {
        "providers": [
            {
                "id": "Google",
                "name": "Google Gemini",
                "icon": "🔍",
                "models": [
                    {"id": "models/gemini-2.5-pro", "name": "Gemini-2.5-Pro"},
                    {"id": "models/gemini-2.5-flash", "name": "Gemini-2.5-Flash"},
                    {"id": "models/gemini-1.5-pro", "name": "Gemini-1.5-Pro"}
                ]
            },
            {
                "id": "OpenAI",
                "name": "OpenAI GPT",
                "icon": "🤖",
                "models": [
                    {"id": "gpt-4", "name": "GPT-4"},
                    {"id": "gpt-3.5-turbo", "name": "GPT-3.5-Turbo"}
                ]
            },
            {
                "id": "Anthropic",
                "name": "Anthropic Claude",
                "icon": "🧠",
                "models": [
                    {"id": "claude-3-opus-********", "name": "Claude-3-Opus"},
                    {"id": "claude-3-sonnet-********", "name": "Claude-3-Sonnet"}
                ]
            }
        ],
        "task_types": [
            {"id": "complex_accounting", "name": "複雑な会計処理"},
            {"id": "simple_classification", "name": "簡単な分類"},
            {"id": "ocr_processing", "name": "OCR処理"},
            {"id": "natural_language", "name": "自然言語理解"},
            {"id": "analysis", "name": "データ分析"}
        ]
    }


@router.get("/default-config/{company_id}")
async def get_default_configuration(company_id: str):
    """
    ⚙️ 获取默认配置信息
    """
    return {
        "default_provider": "Google",
        "default_model": "models/gemini-2.5-pro",
        "gemini_configured": True,
        "api_key_status": "configured",
        "recommended_settings": {
            "temperature": 0.1,
            "max_tokens": 2048,
            "priority": 1
        }
    }

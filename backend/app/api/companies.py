"""
公司管理API路由
处理公司的创建、查询、更新等功能
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date

from ..core.security import UserContext, UserRole
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
companies_router = APIRouter(prefix="/companies", tags=["公司管理"])

# Pydantic模型
class CompanyBase(BaseModel):
    name: str
    tax_number: Optional[str] = None
    address: Optional[str] = None
    industry: Optional[str] = None
    fiscal_year_start: Optional[date] = None
    fiscal_year_end: Optional[date] = None
    currency: str = "CNY"

class CompanyCreate(CompanyBase):
    tenant_id: Optional[str] = None  # 如果不提供，使用当前用户的租户

class CompanyUpdate(BaseModel):
    name: Optional[str] = None
    tax_number: Optional[str] = None
    address: Optional[str] = None
    industry: Optional[str] = None
    fiscal_year_start: Optional[date] = None
    fiscal_year_end: Optional[date] = None
    currency: Optional[str] = None
    is_active: Optional[bool] = None

class CompanyResponse(CompanyBase):
    id: str
    tenant_id: str
    is_active: bool
    created_at: str
    updated_at: str
    
    # 统计信息
    journal_entries_count: int = 0
    account_subjects_count: int = 0
    users_count: int = 0

class CompanyStats(BaseModel):
    total_entries: int
    total_amount: float
    monthly_entries: int
    monthly_amount: float
    active_accounts: int
    recent_entries: List[dict] = []

# 权限检查函数
def require_company_access(current_user: UserContext = Depends(get_current_user)):
    """需要公司访问权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要公司访问权限"
        )
    return current_user

def require_company_write(current_user: UserContext = Depends(get_current_user)):
    """需要公司写入权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要公司管理权限"
        )
    return current_user

# API路由
@companies_router.get("/", response_model=List[CompanyResponse])
async def get_companies(current_user: UserContext = Depends(require_company_access)):
    """获取用户可访问的公司列表"""
    try:
        db = await get_database_connection()
        
        if current_user.role == UserRole.SYSTEM_ADMIN:
            # 系统管理员可以看到所有公司
            query = """
                SELECT c.*, 
                       COUNT(DISTINCT je.id) as journal_entries_count,
                       COUNT(DISTINCT as_.id) as account_subjects_count,
                       COUNT(DISTINCT ucp.user_id) as users_count
                FROM companies c
                LEFT JOIN journal_entries je ON c.id = je.company_id
                LEFT JOIN account_subjects as_ ON c.id = as_.company_id AND as_.is_active = TRUE
                LEFT JOIN user_company_permissions ucp ON c.id = ucp.company_id
                WHERE c.is_active = TRUE
                GROUP BY c.id
                ORDER BY c.created_at DESC
            """
            result = await db.execute(query)
        else:
            # 根据用户租户获取公司
            tenant_condition = ""
            params = []
            
            if current_user.tenant_id:
                tenant_condition = "AND c.tenant_id = ?"
                params.append(current_user.tenant_id)
            
            query = f"""
                SELECT c.*, 
                       COUNT(DISTINCT je.id) as journal_entries_count,
                       COUNT(DISTINCT as_.id) as account_subjects_count,
                       COUNT(DISTINCT ucp.user_id) as users_count
                FROM companies c
                LEFT JOIN journal_entries je ON c.id = je.company_id
                LEFT JOIN account_subjects as_ ON c.id = as_.company_id AND as_.is_active = TRUE
                LEFT JOIN user_company_permissions ucp ON c.id = ucp.company_id
                WHERE c.is_active = TRUE {tenant_condition}
                GROUP BY c.id
                ORDER BY c.created_at DESC
            """
            result = await db.execute(query, params)
        
        companies = []
        for row in result.fetchall():
            companies.append(CompanyResponse(
                id=row['id'],
                tenant_id=row['tenant_id'],
                name=row['name'],
                tax_number=row['tax_number'],
                address=row['address'],
                industry=row['industry'],
                fiscal_year_start=row['fiscal_year_start'],
                fiscal_year_end=row['fiscal_year_end'],
                currency=row['currency'],
                is_active=row['is_active'],
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                journal_entries_count=row['journal_entries_count'] or 0,
                account_subjects_count=row['account_subjects_count'] or 0,
                users_count=row['users_count'] or 0
            ))
        
        return companies
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公司列表失败: {str(e)}"
        )

@companies_router.post("/", response_model=CompanyResponse)
async def create_company(
    company_data: CompanyCreate,
    current_user: UserContext = Depends(require_company_write)
):
    """创建新公司"""
    try:
        db = await get_database_connection()
        
        # 确定租户ID
        tenant_id = company_data.tenant_id or current_user.tenant_id
        
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定租户ID"
            )
        
        # 检查租户是否存在且用户有权限
        if current_user.role != UserRole.SYSTEM_ADMIN:
            tenant_check = await db.execute("""
                SELECT t.id, t.max_companies,
                       COUNT(c.id) as current_companies
                FROM tenants t
                LEFT JOIN companies c ON t.id = c.tenant_id AND c.is_active = TRUE
                JOIN user_tenant_roles utr ON t.id = utr.tenant_id
                WHERE t.id = ? AND utr.user_id = ? AND utr.is_active = TRUE
                GROUP BY t.id
            """, [tenant_id, current_user.user_id])
            
            tenant_info = tenant_check.fetchone()
            if not tenant_info:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权在该租户下创建公司"
                )
            
            # 检查公司数量限制
            if tenant_info['current_companies'] >= tenant_info['max_companies']:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"已达到最大公司数量限制 ({tenant_info['max_companies']})"
                )
        
        # 生成公司ID
        company_id = f"company_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建公司
        await db.execute("""
            INSERT INTO companies (
                id, tenant_id, name, tax_number, address, industry,
                fiscal_year_start, fiscal_year_end, currency, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE)
        """, [
            company_id,
            tenant_id,
            company_data.name,
            company_data.tax_number,
            company_data.address,
            company_data.industry,
            company_data.fiscal_year_start,
            company_data.fiscal_year_end,
            company_data.currency
        ])
        
        # 为公司创建默认会计科目
        await create_default_accounts(db, company_id, tenant_id)
        
        # 给创建者分配公司权限
        await assign_company_permissions(db, current_user.user_id, company_id)
        
        # 获取创建的公司信息
        result = await db.execute(
            "SELECT * FROM companies WHERE id = ?",
            [company_id]
        )
        company = result.fetchone()
        
        return CompanyResponse(
            id=company['id'],
            tenant_id=company['tenant_id'],
            name=company['name'],
            tax_number=company['tax_number'],
            address=company['address'],
            industry=company['industry'],
            fiscal_year_start=company['fiscal_year_start'],
            fiscal_year_end=company['fiscal_year_end'],
            currency=company['currency'],
            is_active=company['is_active'],
            created_at=company['created_at'],
            updated_at=company['updated_at'],
            journal_entries_count=0,
            account_subjects_count=0,
            users_count=1
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建公司失败: {str(e)}"
        )

@companies_router.get("/{company_id}", response_model=CompanyResponse)
async def get_company(
    company_id: str,
    current_user: UserContext = Depends(require_company_access)
):
    """获取公司详情"""
    try:
        db = await get_database_connection()
        
        # 检查访问权限
        if not await check_company_access(db, current_user, company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 获取公司信息
        query = """
            SELECT c.*, 
                   COUNT(DISTINCT je.id) as journal_entries_count,
                   COUNT(DISTINCT as_.id) as account_subjects_count,
                   COUNT(DISTINCT ucp.user_id) as users_count
            FROM companies c
            LEFT JOIN journal_entries je ON c.id = je.company_id
            LEFT JOIN account_subjects as_ ON c.id = as_.company_id AND as_.is_active = TRUE
            LEFT JOIN user_company_permissions ucp ON c.id = ucp.company_id
            WHERE c.id = ?
            GROUP BY c.id
        """
        
        result = await db.execute(query, [company_id])
        company = result.fetchone()
        
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="公司不存在"
            )
        
        return CompanyResponse(
            id=company['id'],
            tenant_id=company['tenant_id'],
            name=company['name'],
            tax_number=company['tax_number'],
            address=company['address'],
            industry=company['industry'],
            fiscal_year_start=company['fiscal_year_start'],
            fiscal_year_end=company['fiscal_year_end'],
            currency=company['currency'],
            is_active=company['is_active'],
            created_at=company['created_at'],
            updated_at=company['updated_at'],
            journal_entries_count=company['journal_entries_count'] or 0,
            account_subjects_count=company['account_subjects_count'] or 0,
            users_count=company['users_count'] or 0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公司信息失败: {str(e)}"
        )

@companies_router.get("/{company_id}/stats", response_model=CompanyStats)
async def get_company_stats(
    company_id: str,
    current_user: UserContext = Depends(require_company_access)
):
    """获取公司统计信息"""
    try:
        db = await get_database_connection()
        
        # 检查访问权限
        if not await check_company_access(db, current_user, company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 获取总体统计
        total_stats = await db.execute("""
            SELECT 
                COUNT(*) as total_entries,
                COALESCE(SUM(total_amount), 0) as total_amount
            FROM journal_entries 
            WHERE company_id = ? AND status = 'posted'
        """, [company_id])
        
        total_result = total_stats.fetchone()
        
        # 获取本月统计
        monthly_stats = await db.execute("""
            SELECT 
                COUNT(*) as monthly_entries,
                COALESCE(SUM(total_amount), 0) as monthly_amount
            FROM journal_entries 
            WHERE company_id = ? AND status = 'posted'
            AND strftime('%Y-%m', entry_date) = strftime('%Y-%m', 'now')
        """, [company_id])
        
        monthly_result = monthly_stats.fetchone()
        
        # 获取活跃科目数
        active_accounts = await db.execute("""
            SELECT COUNT(*) as active_accounts
            FROM account_subjects 
            WHERE company_id = ? AND is_active = TRUE
        """, [company_id])
        
        accounts_result = active_accounts.fetchone()
        
        # 获取最近记录
        recent_entries = await db.execute("""
            SELECT id, entry_date, description, total_amount, status
            FROM journal_entries 
            WHERE company_id = ?
            ORDER BY created_at DESC
            LIMIT 5
        """, [company_id])
        
        recent_list = []
        for entry in recent_entries.fetchall():
            recent_list.append({
                'id': entry['id'],
                'date': entry['entry_date'],
                'description': entry['description'],
                'amount': float(entry['total_amount']),
                'status': entry['status']
            })
        
        return CompanyStats(
            total_entries=total_result['total_entries'],
            total_amount=float(total_result['total_amount']),
            monthly_entries=monthly_result['monthly_entries'],
            monthly_amount=float(monthly_result['monthly_amount']),
            active_accounts=accounts_result['active_accounts'],
            recent_entries=recent_list
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公司统计失败: {str(e)}"
        )

# 辅助函数
async def check_company_access(db, current_user: UserContext, company_id: str) -> bool:
    """检查用户是否有权限访问公司"""
    if current_user.role == UserRole.SYSTEM_ADMIN:
        return True
    
    # 检查公司是否属于用户的租户
    if current_user.tenant_id:
        company_check = await db.execute(
            "SELECT id FROM companies WHERE id = ? AND tenant_id = ?",
            [company_id, current_user.tenant_id]
        )
        return company_check.fetchone() is not None
    
    return False

async def create_default_accounts(db, company_id: str, tenant_id: str):
    """为新公司创建默认会计科目"""
    # 获取租户的会计科目分类
    categories = await db.execute(
        "SELECT * FROM account_categories WHERE tenant_id = ? ORDER BY sort_order",
        [tenant_id]
    )
    
    # 默认会计科目
    default_accounts = [
        ('1001001', '库存现金', 'asset'),
        ('1001002', '银行存款', 'asset'),
        ('1101001', '应收账款', 'asset'),
        ('1501001', '固定资产', 'asset'),
        ('2001001', '应付账款', 'liability'),
        ('2001002', '应付工资', 'liability'),
        ('3001001', '实收资本', 'equity'),
        ('3001002', '未分配利润', 'equity'),
        ('4001001', '主营业务收入', 'revenue'),
        ('5001001', '主营业务成本', 'expense'),
        ('6001001', '销售费用', 'expense'),
        ('6001002', '管理费用', 'expense'),
    ]
    
    category_map = {}
    for category in categories.fetchall():
        category_map[category['type']] = category['id']
    
    for code, name, account_type in default_accounts:
        category_id = category_map.get(account_type)
        if category_id:
            await db.execute("""
                INSERT INTO account_subjects (
                    company_id, category_id, code, name, type, is_active, is_system
                ) VALUES (?, ?, ?, ?, ?, TRUE, TRUE)
            """, [company_id, category_id, code, name, account_type])

async def assign_company_permissions(db, user_id: str, company_id: str):
    """为用户分配公司权限"""
    permissions = [
        ('dashboard', True, False, False),
        ('journal', True, True, True),
        ('account', True, True, False),
        ('report', True, False, False),
    ]
    
    for perm_type, can_read, can_write, can_delete in permissions:
        await db.execute("""
            INSERT INTO user_company_permissions (
                user_id, company_id, permission_type, can_read, can_write, can_delete
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, [user_id, company_id, perm_type, can_read, can_write, can_delete])

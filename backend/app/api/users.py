"""
用户管理API路由
处理用户的邀请、权限管理等功能
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime, timedelta

from ..core.security import UserContext, UserRole
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
users_router = APIRouter(prefix="/users", tags=["用户管理"])

# Pydantic模型
class UserBase(BaseModel):
    email: EmailStr
    username: str
    name: Optional[str] = None

class UserInvite(BaseModel):
    email: EmailStr
    tenant_id: str
    role: str
    company_ids: Optional[List[str]] = []
    expires_days: int = 7

class UserRoleUpdate(BaseModel):
    role: str
    is_active: bool = True
    expires_at: Optional[str] = None

class UserPermissionUpdate(BaseModel):
    company_id: str
    permission_type: str
    can_read: bool = False
    can_write: bool = False
    can_delete: bool = False

class UserResponse(UserBase):
    id: str
    role: str
    is_active: bool
    email_verified: bool
    created_at: str
    last_login_at: Optional[str] = None
    
    # 租户角色信息
    tenant_roles: List[dict] = []
    company_permissions: List[dict] = []

class InviteResponse(BaseModel):
    id: str
    email: str
    tenant_id: str
    role: str
    status: str
    expires_at: str
    created_at: str
    invited_by: str

# 权限检查函数
def require_user_management(current_user: UserContext = Depends(get_current_user)):
    """需要用户管理权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要用户管理权限"
        )
    return current_user

# API路由
@users_router.get("/", response_model=List[UserResponse])
async def get_users(
    tenant_id: Optional[str] = None,
    current_user: UserContext = Depends(require_user_management)
):
    """获取用户列表"""
    try:
        db = await get_database_connection()
        
        # 构建查询条件
        if current_user.role == UserRole.SYSTEM_ADMIN:
            # 系统管理员可以查看所有用户
            if tenant_id:
                query = """
                    SELECT DISTINCT u.*, utr.role as current_role
                    FROM users u
                    LEFT JOIN user_tenant_roles utr ON u.id = utr.user_id AND utr.tenant_id = ? AND utr.is_active = TRUE
                    ORDER BY u.created_at DESC
                """
                params = [tenant_id]
            else:
                query = """
                    SELECT u.*, 'system' as current_role
                    FROM users u
                    ORDER BY u.created_at DESC
                """
                params = []
        else:
            # 租户管理员只能查看自己租户的用户
            tenant_id = current_user.tenant_id
            query = """
                SELECT u.*, utr.role as current_role
                FROM users u
                JOIN user_tenant_roles utr ON u.id = utr.user_id
                WHERE utr.tenant_id = ? AND utr.is_active = TRUE
                ORDER BY u.created_at DESC
            """
            params = [tenant_id]
        
        result = await db.execute(query, params)
        users = []
        
        for row in result.fetchall():
            # 获取用户的租户角色
            tenant_roles_query = """
                SELECT utr.*, t.name as tenant_name
                FROM user_tenant_roles utr
                JOIN tenants t ON utr.tenant_id = t.id
                WHERE utr.user_id = ? AND utr.is_active = TRUE
            """
            tenant_roles_result = await db.execute(tenant_roles_query, [row['id']])
            tenant_roles = []
            for role_row in tenant_roles_result.fetchall():
                tenant_roles.append({
                    'tenant_id': role_row['tenant_id'],
                    'tenant_name': role_row['tenant_name'],
                    'role': role_row['role'],
                    'granted_at': role_row['granted_at'],
                    'expires_at': role_row['expires_at']
                })
            
            # 获取用户的公司权限
            company_perms_query = """
                SELECT ucp.*, c.name as company_name
                FROM user_company_permissions ucp
                JOIN companies c ON ucp.company_id = c.id
                WHERE ucp.user_id = ?
            """
            company_perms_result = await db.execute(company_perms_query, [row['id']])
            company_permissions = []
            for perm_row in company_perms_result.fetchall():
                company_permissions.append({
                    'company_id': perm_row['company_id'],
                    'company_name': perm_row['company_name'],
                    'permission_type': perm_row['permission_type'],
                    'can_read': perm_row['can_read'],
                    'can_write': perm_row['can_write'],
                    'can_delete': perm_row['can_delete']
                })
            
            users.append(UserResponse(
                id=row['id'],
                email=row['email'],
                username=row['username'],
                name=row['name'],
                role=row['current_role'] or 'viewer',
                is_active=row['is_active'],
                email_verified=row['email_verified'],
                created_at=row['created_at'],
                last_login_at=row['last_login_at'],
                tenant_roles=tenant_roles,
                company_permissions=company_permissions
            ))
        
        return users
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {str(e)}"
        )

@users_router.post("/invite", response_model=InviteResponse)
async def invite_user(
    invite_data: UserInvite,
    current_user: UserContext = Depends(require_user_management)
):
    """邀请用户加入租户"""
    try:
        db = await get_database_connection()
        
        # 检查权限
        if current_user.role != UserRole.SYSTEM_ADMIN:
            if invite_data.tenant_id != current_user.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能邀请用户到自己的租户"
                )
        
        # 检查租户是否存在
        tenant_check = await db.execute(
            "SELECT id, max_users FROM tenants WHERE id = ? AND is_active = TRUE",
            [invite_data.tenant_id]
        )
        tenant = tenant_check.fetchone()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在或已停用"
            )
        
        # 检查用户数量限制
        current_users = await db.execute("""
            SELECT COUNT(*) as user_count
            FROM user_tenant_roles
            WHERE tenant_id = ? AND is_active = TRUE
        """, [invite_data.tenant_id])
        
        user_count = current_users.fetchone()['user_count']
        if user_count >= tenant['max_users']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"已达到最大用户数量限制 ({tenant['max_users']})"
            )
        
        # 检查用户是否已存在
        existing_user = await db.execute(
            "SELECT id FROM users WHERE email = ?",
            [invite_data.email]
        )
        user_exists = existing_user.fetchone()
        
        # 检查是否已有邀请
        existing_invite = await db.execute("""
            SELECT id FROM user_invitations 
            WHERE email = ? AND tenant_id = ? AND status = 'pending'
        """, [invite_data.email, invite_data.tenant_id])
        
        if existing_invite.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该用户已有待处理的邀请"
            )
        
        # 生成邀请ID
        invite_id = f"invite_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        expires_at = datetime.utcnow() + timedelta(days=invite_data.expires_days)
        
        # 创建邀请记录
        await db.execute("""
            INSERT INTO user_invitations (
                id, email, tenant_id, role, company_ids, status, 
                expires_at, invited_by, created_at
            ) VALUES (?, ?, ?, ?, ?, 'pending', ?, ?, CURRENT_TIMESTAMP)
        """, [
            invite_id,
            invite_data.email,
            invite_data.tenant_id,
            invite_data.role,
            ','.join(invite_data.company_ids) if invite_data.company_ids else '',
            expires_at.isoformat(),
            current_user.user_id
        ])
        
        # 如果用户已存在，直接分配角色
        if user_exists:
            await assign_user_role(
                db, 
                user_exists['id'], 
                invite_data.tenant_id, 
                invite_data.role,
                invite_data.company_ids
            )
            
            # 更新邀请状态
            await db.execute(
                "UPDATE user_invitations SET status = 'accepted' WHERE id = ?",
                [invite_id]
            )
        
        # TODO: 发送邀请邮件
        
        return InviteResponse(
            id=invite_id,
            email=invite_data.email,
            tenant_id=invite_data.tenant_id,
            role=invite_data.role,
            status='accepted' if user_exists else 'pending',
            expires_at=expires_at.isoformat(),
            created_at=datetime.utcnow().isoformat(),
            invited_by=current_user.user_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"邀请用户失败: {str(e)}"
        )

@users_router.put("/{user_id}/role", response_model=dict)
async def update_user_role(
    user_id: str,
    role_data: UserRoleUpdate,
    current_user: UserContext = Depends(require_user_management)
):
    """更新用户角色"""
    try:
        db = await get_database_connection()
        
        # 检查目标用户是否存在
        user_check = await db.execute(
            "SELECT id FROM users WHERE id = ?",
            [user_id]
        )
        
        if not user_check.fetchone():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查权限（非系统管理员只能管理自己租户的用户）
        tenant_id = current_user.tenant_id
        if current_user.role != UserRole.SYSTEM_ADMIN:
            # 验证用户是否属于当前租户
            tenant_user_check = await db.execute("""
                SELECT id FROM user_tenant_roles 
                WHERE user_id = ? AND tenant_id = ?
            """, [user_id, tenant_id])
            
            if not tenant_user_check.fetchone():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权管理该用户"
                )
        
        # 更新用户租户角色
        await db.execute("""
            UPDATE user_tenant_roles 
            SET role = ?, is_active = ?, expires_at = ?, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND tenant_id = ?
        """, [
            role_data.role,
            role_data.is_active,
            role_data.expires_at,
            user_id,
            tenant_id
        ])
        
        return {"message": "用户角色更新成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户角色失败: {str(e)}"
        )

@users_router.put("/{user_id}/permissions", response_model=dict)
async def update_user_permissions(
    user_id: str,
    permission_data: UserPermissionUpdate,
    current_user: UserContext = Depends(require_user_management)
):
    """更新用户公司权限"""
    try:
        db = await get_database_connection()
        
        # 检查权限
        if current_user.role != UserRole.SYSTEM_ADMIN:
            # 检查公司是否属于当前租户
            company_check = await db.execute(
                "SELECT id FROM companies WHERE id = ? AND tenant_id = ?",
                [permission_data.company_id, current_user.tenant_id]
            )
            
            if not company_check.fetchone():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权管理该公司的用户权限"
                )
        
        # 更新或插入权限
        await db.execute("""
            INSERT OR REPLACE INTO user_company_permissions (
                user_id, company_id, permission_type, can_read, can_write, can_delete
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, [
            user_id,
            permission_data.company_id,
            permission_data.permission_type,
            permission_data.can_read,
            permission_data.can_write,
            permission_data.can_delete
        ])
        
        return {"message": "用户权限更新成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户权限失败: {str(e)}"
        )

@users_router.delete("/{user_id}")
async def remove_user(
    user_id: str,
    current_user: UserContext = Depends(require_user_management)
):
    """从租户中移除用户"""
    try:
        db = await get_database_connection()
        
        # 检查权限
        tenant_id = current_user.tenant_id
        if current_user.role != UserRole.SYSTEM_ADMIN:
            # 验证用户是否属于当前租户
            tenant_user_check = await db.execute("""
                SELECT id FROM user_tenant_roles 
                WHERE user_id = ? AND tenant_id = ?
            """, [user_id, tenant_id])
            
            if not tenant_user_check.fetchone():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权移除该用户"
                )
        
        # 禁用用户在租户中的角色
        await db.execute("""
            UPDATE user_tenant_roles 
            SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND tenant_id = ?
        """, [user_id, tenant_id])
        
        # 删除用户的公司权限
        await db.execute("""
            DELETE FROM user_company_permissions 
            WHERE user_id = ? AND company_id IN (
                SELECT id FROM companies WHERE tenant_id = ?
            )
        """, [user_id, tenant_id])
        
        return {"message": "用户已从租户中移除"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移除用户失败: {str(e)}"
        )

# 辅助函数
async def assign_user_role(db, user_id: str, tenant_id: str, role: str, company_ids: List[str]):
    """为用户分配租户角色和公司权限"""
    # 分配租户角色
    await db.execute("""
        INSERT OR REPLACE INTO user_tenant_roles (
            user_id, tenant_id, role, is_active, granted_at
        ) VALUES (?, ?, ?, TRUE, CURRENT_TIMESTAMP)
    """, [user_id, tenant_id, role])
    
    # 分配公司权限
    if company_ids:
        for company_id in company_ids:
            # 根据角色分配默认权限
            permissions = get_default_permissions_for_role(role)
            
            for perm_type, can_read, can_write, can_delete in permissions:
                await db.execute("""
                    INSERT OR REPLACE INTO user_company_permissions (
                        user_id, company_id, permission_type, can_read, can_write, can_delete
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, [user_id, company_id, perm_type, can_read, can_write, can_delete])

def get_default_permissions_for_role(role: str) -> List[tuple]:
    """根据角色获取默认权限"""
    if role == 'tenant_admin':
        return [
            ('dashboard', True, True, False),
            ('journal', True, True, True),
            ('account', True, True, True),
            ('report', True, True, False),
            ('user', True, True, True),
            ('settings', True, True, False),
        ]
    elif role == 'accountant':
        return [
            ('dashboard', True, False, False),
            ('journal', True, True, True),
            ('account', True, True, False),
            ('report', True, False, False),
        ]
    elif role == 'cashier':
        return [
            ('dashboard', True, False, False),
            ('journal', True, True, False),
            ('account', True, False, False),
            ('report', True, False, False),
        ]
    else:  # viewer
        return [
            ('dashboard', True, False, False),
            ('journal', True, False, False),
            ('account', True, False, False),
            ('report', True, False, False),
        ]

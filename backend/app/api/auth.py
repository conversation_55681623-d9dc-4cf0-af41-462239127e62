"""
认证API路由
处理用户登录、注册、令牌验证等认证相关功能
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional
import bcrypt
import jwt
from datetime import datetime, timedelta

from ..core.security import SecurityManager, UserContext, UserRole
from ..core.database import get_database_connection
from ..models.user import User, UserCreate, UserLogin, UserResponse

# 创建路由器
auth_router = APIRouter(prefix="/auth", tags=["认证"])

# 安全管理器
security_manager = SecurityManager(secret_key="your-jwt-secret-key")
security = HTTPBearer()

# Pydantic模型
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class RegisterRequest(BaseModel):
    email: EmailStr
    username: str
    password: str
    name: Optional[str] = None

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: UserResponse
    expires_in: int = 86400  # 24小时

class RefreshTokenRequest(BaseModel):
    refresh_token: str

# 依赖函数
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserContext:
    """获取当前用户"""
    try:
        token = credentials.credentials
        user_context = security_manager.verify_token(token)
        
        if not user_context:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user_context
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="访问令牌已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

# API路由
@auth_router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    try:
        db = await get_database_connection()
        
        # 查询用户
        user_query = """
            SELECT id, email, username, password_hash, name, avatar_url, 
                   role, is_active, email_verified, created_at, last_login_at
            FROM users 
            WHERE email = ? AND is_active = TRUE
        """
        
        result = await db.execute(user_query, [request.email])
        user_data = result.fetchone()
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 验证密码
        if not security_manager.verify_password(request.password, user_data['password_hash']):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查邮箱验证状态
        if not user_data['email_verified']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="请先验证您的邮箱地址"
            )
        
        # 获取用户的租户信息
        tenant_query = """
            SELECT t.id, t.name, utr.role
            FROM tenants t
            JOIN user_tenant_roles utr ON t.id = utr.tenant_id
            WHERE utr.user_id = ? AND utr.is_active = TRUE
            ORDER BY utr.granted_at DESC
            LIMIT 1
        """
        
        tenant_result = await db.execute(tenant_query, [user_data['id']])
        tenant_data = tenant_result.fetchone()
        
        # 创建用户上下文
        user_context = UserContext(
            user_id=user_data['id'],
            email=user_data['email'],
            username=user_data['username'],
            role=UserRole(tenant_data['role'] if tenant_data else 'viewer'),
            tenant_id=tenant_data['id'] if tenant_data else None
        )
        
        # 生成访问令牌
        access_token = security_manager.generate_token(user_context)
        
        # 更新最后登录时间
        await db.execute(
            "UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?",
            [user_data['id']]
        )
        
        # 创建用户会话记录
        session_token = security_manager.generate_session_token()
        await db.execute("""
            INSERT INTO user_sessions (user_id, session_token, expires_at, ip_address)
            VALUES (?, ?, datetime('now', '+24 hours'), ?)
        """, [user_data['id'], session_token, "127.0.0.1"])  # TODO: 获取真实IP
        
        # 构建响应
        user_response = UserResponse(
            id=user_data['id'],
            email=user_data['email'],
            username=user_data['username'],
            name=user_data['name'],
            avatar_url=user_data['avatar_url'],
            role=user_context.role.value,
            is_active=user_data['is_active'],
            email_verified=user_data['email_verified'],
            created_at=user_data['created_at'],
            last_login_at=datetime.utcnow().isoformat()
        )
        
        return LoginResponse(
            access_token=access_token,
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@auth_router.post("/register", response_model=LoginResponse)
async def register(request: RegisterRequest):
    """用户注册"""
    try:
        db = await get_database_connection()
        
        # 检查邮箱是否已存在
        existing_user = await db.execute(
            "SELECT id FROM users WHERE email = ?", 
            [request.email]
        )
        
        if existing_user.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱地址已被注册"
            )
        
        # 检查用户名是否已存在
        existing_username = await db.execute(
            "SELECT id FROM users WHERE username = ?", 
            [request.username]
        )
        
        if existing_username.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该用户名已被使用"
            )
        
        # 创建新用户
        user_id = f"user_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        password_hash = security_manager.hash_password(request.password)
        
        await db.execute("""
            INSERT INTO users (id, email, username, password_hash, name, role, is_active, email_verified)
            VALUES (?, ?, ?, ?, ?, 'user', TRUE, FALSE)
        """, [user_id, request.email, request.username, password_hash, request.name])
        
        # 为新用户分配到默认租户
        await db.execute("""
            INSERT INTO user_tenant_roles (user_id, tenant_id, role, is_active)
            VALUES (?, 'default-tenant-001', 'viewer', TRUE)
        """, [user_id])
        
        # 创建用户上下文并生成令牌
        user_context = UserContext(
            user_id=user_id,
            email=request.email,
            username=request.username,
            role=UserRole.VIEWER,
            tenant_id='default-tenant-001'
        )
        
        access_token = security_manager.generate_token(user_context)
        
        # 构建响应
        user_response = UserResponse(
            id=user_id,
            email=request.email,
            username=request.username,
            name=request.name,
            role='viewer',
            is_active=True,
            email_verified=False,
            created_at=datetime.utcnow().isoformat()
        )
        
        return LoginResponse(
            access_token=access_token,
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败: {str(e)}"
        )

@auth_router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: UserContext = Depends(get_current_user)):
    """获取当前用户信息"""
    try:
        db = await get_database_connection()
        
        user_query = """
            SELECT id, email, username, name, avatar_url, role, 
                   is_active, email_verified, created_at, last_login_at
            FROM users 
            WHERE id = ?
        """
        
        result = await db.execute(user_query, [current_user.user_id])
        user_data = result.fetchone()
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return UserResponse(
            id=user_data['id'],
            email=user_data['email'],
            username=user_data['username'],
            name=user_data['name'],
            avatar_url=user_data['avatar_url'],
            role=current_user.role.value,
            is_active=user_data['is_active'],
            email_verified=user_data['email_verified'],
            created_at=user_data['created_at'],
            last_login_at=user_data['last_login_at']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户信息失败: {str(e)}"
        )

@auth_router.post("/logout")
async def logout(current_user: UserContext = Depends(get_current_user)):
    """用户退出登录"""
    try:
        db = await get_database_connection()
        
        # 删除用户会话
        await db.execute(
            "DELETE FROM user_sessions WHERE user_id = ?",
            [current_user.user_id]
        )
        
        return {"message": "退出登录成功"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"退出登录失败: {str(e)}"
        )

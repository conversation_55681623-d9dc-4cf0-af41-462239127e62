"""
记账管理API路由
处理记账录入、查询、修改等核心业务功能
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, validator
from typing import List, Optional
from datetime import datetime, date
from decimal import Decimal

from ..core.security import UserContext, UserRole
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
journal_router = APIRouter(prefix="/journal", tags=["记账管理"])

# Pydantic模型
class JournalEntryLineCreate(BaseModel):
    account_id: str
    debit_amount: Optional[Decimal] = Decimal('0.00')
    credit_amount: Optional[Decimal] = Decimal('0.00')
    description: Optional[str] = None
    reference: Optional[str] = None

class JournalEntryCreate(BaseModel):
    company_id: str
    entry_date: date
    description: str
    reference_number: Optional[str] = None
    lines: List[JournalEntryLineCreate]
    
    @validator('lines')
    def validate_lines(cls, v):
        if len(v) < 2:
            raise ValueError('记账分录至少需要两行')
        
        total_debit = sum(line.debit_amount for line in v)
        total_credit = sum(line.credit_amount for line in v)
        
        if abs(total_debit - total_credit) > Decimal('0.01'):
            raise ValueError('借贷金额必须平衡')
        
        return v

class JournalEntryUpdate(BaseModel):
    entry_date: Optional[date] = None
    description: Optional[str] = None
    reference_number: Optional[str] = None
    lines: Optional[List[JournalEntryLineCreate]] = None
    status: Optional[str] = None

class JournalEntryLineResponse(BaseModel):
    id: str
    account_id: str
    account_code: str
    account_name: str
    debit_amount: Decimal
    credit_amount: Decimal
    description: Optional[str] = None
    reference: Optional[str] = None

class JournalEntryResponse(BaseModel):
    id: str
    company_id: str
    company_name: str
    entry_date: str
    description: str
    reference_number: Optional[str] = None
    total_amount: Decimal
    status: str
    ai_generated: bool = False
    ai_confidence: Optional[Decimal] = None
    ai_analysis: Optional[str] = None
    created_by: str
    created_by_name: Optional[str] = None
    created_at: str
    updated_at: str
    posted_at: Optional[str] = None
    posted_by: Optional[str] = None
    
    lines: List[JournalEntryLineResponse] = []

class JournalEntryFilter(BaseModel):
    company_id: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    status: Optional[str] = None
    account_id: Optional[str] = None
    created_by: Optional[str] = None
    ai_generated: Optional[bool] = None
    search: Optional[str] = None

class AIJournalRequest(BaseModel):
    company_id: str
    description: str
    amount: Decimal
    transaction_type: str  # income, expense, transfer
    attachment_data: Optional[str] = None  # base64 encoded image

# 权限检查函数
def require_journal_read(current_user: UserContext = Depends(get_current_user)):
    """需要记账读取权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT, UserRole.CASHIER, UserRole.VIEWER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要记账读取权限"
        )
    return current_user

def require_journal_write(current_user: UserContext = Depends(get_current_user)):
    """需要记账写入权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT, UserRole.CASHIER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要记账写入权限"
        )
    return current_user

def require_journal_post(current_user: UserContext = Depends(get_current_user)):
    """需要记账过账权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要记账过账权限"
        )
    return current_user

# API路由
@journal_router.get("/", response_model=List[JournalEntryResponse])
async def get_journal_entries(
    company_id: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    status: Optional[str] = Query(None),
    account_id: Optional[str] = Query(None),
    created_by: Optional[str] = Query(None),
    ai_generated: Optional[bool] = Query(None),
    search: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: UserContext = Depends(require_journal_read)
):
    """获取记账记录列表"""
    try:
        db = await get_database_connection()
        
        # 构建查询条件
        conditions = []
        params = []
        
        # 权限过滤
        if current_user.role != UserRole.SYSTEM_ADMIN:
            if current_user.tenant_id:
                conditions.append("c.tenant_id = ?")
                params.append(current_user.tenant_id)
        
        if company_id:
            conditions.append("je.company_id = ?")
            params.append(company_id)
        
        if start_date:
            conditions.append("je.entry_date >= ?")
            params.append(start_date.isoformat())
        
        if end_date:
            conditions.append("je.entry_date <= ?")
            params.append(end_date.isoformat())
        
        if status:
            conditions.append("je.status = ?")
            params.append(status)
        
        if created_by:
            conditions.append("je.created_by = ?")
            params.append(created_by)
        
        if ai_generated is not None:
            conditions.append("je.ai_generated = ?")
            params.append(ai_generated)
        
        if search:
            conditions.append("(je.description LIKE ? OR je.reference_number LIKE ?)")
            search_term = f"%{search}%"
            params.extend([search_term, search_term])
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询记账记录
        query = f"""
            SELECT je.*, c.name as company_name, u.name as created_by_name
            FROM journal_entries je
            JOIN companies c ON je.company_id = c.id
            LEFT JOIN users u ON je.created_by = u.id
            {where_clause}
            ORDER BY je.entry_date DESC, je.created_at DESC
            LIMIT ? OFFSET ?
        """
        
        params.extend([page_size, offset])
        result = await db.execute(query, params)
        
        entries = []
        for row in result.fetchall():
            # 获取记账分录明细
            lines_query = """
                SELECT jel.*, as_.code as account_code, as_.name as account_name
                FROM journal_entry_lines jel
                JOIN account_subjects as_ ON jel.account_id = as_.id
                WHERE jel.journal_entry_id = ?
                ORDER BY jel.created_at
            """
            
            lines_result = await db.execute(lines_query, [row['id']])
            lines = []
            
            for line_row in lines_result.fetchall():
                lines.append(JournalEntryLineResponse(
                    id=line_row['id'],
                    account_id=line_row['account_id'],
                    account_code=line_row['account_code'],
                    account_name=line_row['account_name'],
                    debit_amount=Decimal(str(line_row['debit_amount'])),
                    credit_amount=Decimal(str(line_row['credit_amount'])),
                    description=line_row['description'],
                    reference=line_row['reference']
                ))
            
            entries.append(JournalEntryResponse(
                id=row['id'],
                company_id=row['company_id'],
                company_name=row['company_name'],
                entry_date=row['entry_date'],
                description=row['description'],
                reference_number=row['reference_number'],
                total_amount=Decimal(str(row['total_amount'])),
                status=row['status'],
                ai_generated=bool(row['ai_generated']),
                ai_confidence=Decimal(str(row['ai_confidence'])) if row['ai_confidence'] else None,
                ai_analysis=row['ai_analysis'],
                created_by=row['created_by'],
                created_by_name=row['created_by_name'],
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                posted_at=row['posted_at'],
                posted_by=row['posted_by'],
                lines=lines
            ))
        
        return entries
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取记账记录失败: {str(e)}"
        )

@journal_router.post("/", response_model=JournalEntryResponse)
async def create_journal_entry(
    entry_data: JournalEntryCreate,
    current_user: UserContext = Depends(require_journal_write)
):
    """创建记账记录"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, entry_data.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 验证会计科目
        account_ids = [line.account_id for line in entry_data.lines]
        accounts_query = f"""
            SELECT id, code, name FROM account_subjects 
            WHERE id IN ({','.join(['?' for _ in account_ids])}) 
            AND company_id = ? AND is_active = TRUE
        """
        
        accounts_result = await db.execute(accounts_query, account_ids + [entry_data.company_id])
        valid_accounts = {row['id']: row for row in accounts_result.fetchall()}
        
        for line in entry_data.lines:
            if line.account_id not in valid_accounts:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的会计科目: {line.account_id}"
                )
        
        # 计算总金额
        total_amount = sum(line.debit_amount for line in entry_data.lines)
        
        # 生成记账记录ID
        entry_id = f"je_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建记账记录
        await db.execute("""
            INSERT INTO journal_entries (
                id, company_id, entry_date, description, reference_number,
                total_amount, status, created_by, ai_generated
            ) VALUES (?, ?, ?, ?, ?, ?, 'draft', ?, FALSE)
        """, [
            entry_id,
            entry_data.company_id,
            entry_data.entry_date.isoformat(),
            entry_data.description,
            entry_data.reference_number,
            str(total_amount),
            current_user.user_id
        ])
        
        # 创建记账分录明细
        for line in entry_data.lines:
            line_id = f"jel_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
            await db.execute("""
                INSERT INTO journal_entry_lines (
                    id, journal_entry_id, account_id, debit_amount, credit_amount,
                    description, reference
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, [
                line_id,
                entry_id,
                line.account_id,
                str(line.debit_amount),
                str(line.credit_amount),
                line.description,
                line.reference
            ])
        
        # 返回创建的记账记录
        return await get_journal_entry(entry_id, current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建记账记录失败: {str(e)}"
        )

@journal_router.get("/{entry_id}", response_model=JournalEntryResponse)
async def get_journal_entry(
    entry_id: str,
    current_user: UserContext = Depends(require_journal_read)
):
    """获取记账记录详情"""
    try:
        db = await get_database_connection()
        
        # 查询记账记录
        query = """
            SELECT je.*, c.name as company_name, u.name as created_by_name
            FROM journal_entries je
            JOIN companies c ON je.company_id = c.id
            LEFT JOIN users u ON je.created_by = u.id
            WHERE je.id = ?
        """
        
        result = await db.execute(query, [entry_id])
        entry = result.fetchone()
        
        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记账记录不存在"
            )
        
        # 检查访问权限
        if not await check_company_access(db, current_user, entry['company_id']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该记账记录"
            )
        
        # 获取记账分录明细
        lines_query = """
            SELECT jel.*, as_.code as account_code, as_.name as account_name
            FROM journal_entry_lines jel
            JOIN account_subjects as_ ON jel.account_id = as_.id
            WHERE jel.journal_entry_id = ?
            ORDER BY jel.created_at
        """
        
        lines_result = await db.execute(lines_query, [entry_id])
        lines = []
        
        for line_row in lines_result.fetchall():
            lines.append(JournalEntryLineResponse(
                id=line_row['id'],
                account_id=line_row['account_id'],
                account_code=line_row['account_code'],
                account_name=line_row['account_name'],
                debit_amount=Decimal(str(line_row['debit_amount'])),
                credit_amount=Decimal(str(line_row['credit_amount'])),
                description=line_row['description'],
                reference=line_row['reference']
            ))
        
        return JournalEntryResponse(
            id=entry['id'],
            company_id=entry['company_id'],
            company_name=entry['company_name'],
            entry_date=entry['entry_date'],
            description=entry['description'],
            reference_number=entry['reference_number'],
            total_amount=Decimal(str(entry['total_amount'])),
            status=entry['status'],
            ai_generated=bool(entry['ai_generated']),
            ai_confidence=Decimal(str(entry['ai_confidence'])) if entry['ai_confidence'] else None,
            ai_analysis=entry['ai_analysis'],
            created_by=entry['created_by'],
            created_by_name=entry['created_by_name'],
            created_at=entry['created_at'],
            updated_at=entry['updated_at'],
            posted_at=entry['posted_at'],
            posted_by=entry['posted_by'],
            lines=lines
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取记账记录失败: {str(e)}"
        )

@journal_router.post("/{entry_id}/post")
async def post_journal_entry(
    entry_id: str,
    current_user: UserContext = Depends(require_journal_post)
):
    """过账记账记录"""
    try:
        db = await get_database_connection()
        
        # 检查记账记录是否存在
        entry_check = await db.execute(
            "SELECT id, company_id, status FROM journal_entries WHERE id = ?",
            [entry_id]
        )
        entry = entry_check.fetchone()
        
        if not entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记账记录不存在"
            )
        
        # 检查访问权限
        if not await check_company_access(db, current_user, entry['company_id']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权操作该记账记录"
            )
        
        # 检查状态
        if entry['status'] != 'draft':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能过账草稿状态的记账记录"
            )
        
        # 更新状态为已过账
        await db.execute("""
            UPDATE journal_entries 
            SET status = 'posted', posted_at = CURRENT_TIMESTAMP, posted_by = ?
            WHERE id = ?
        """, [current_user.user_id, entry_id])
        
        return {"message": "记账记录已过账"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"过账失败: {str(e)}"
        )

# 辅助函数
async def check_company_access(db, current_user: UserContext, company_id: str) -> bool:
    """检查用户是否有权限访问公司"""
    if current_user.role == UserRole.SYSTEM_ADMIN:
        return True
    
    # 检查公司是否属于用户的租户
    if current_user.tenant_id:
        company_check = await db.execute(
            "SELECT id FROM companies WHERE id = ? AND tenant_id = ?",
            [company_id, current_user.tenant_id]
        )
        return company_check.fetchone() is not None
    
    return False

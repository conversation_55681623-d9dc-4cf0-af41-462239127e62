"""
会计科目管理API路由
处理会计科目的创建、查询、更新等功能
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, validator
from typing import List, Optional
from datetime import datetime

from ..core.security import UserContext, UserRole
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
accounts_router = APIRouter(prefix="/accounts", tags=["会计科目"])

# Pydantic模型
class AccountCategoryResponse(BaseModel):
    id: str
    tenant_id: str
    name: str
    type: str
    code_prefix: str
    is_system: bool
    sort_order: int
    created_at: str

class AccountSubjectBase(BaseModel):
    code: str
    name: str
    type: str
    parent_id: Optional[str] = None
    category_id: Optional[str] = None
    is_active: bool = True

class AccountSubjectCreate(AccountSubjectBase):
    company_id: str
    
    @validator('code')
    def validate_code(cls, v):
        if not v or len(v) < 4:
            raise ValueError('科目代码至少需要4位')
        if not v.isdigit():
            raise ValueError('科目代码必须是数字')
        return v

class AccountSubjectUpdate(BaseModel):
    name: Optional[str] = None
    parent_id: Optional[str] = None
    is_active: Optional[bool] = None

class AccountSubjectResponse(AccountSubjectBase):
    id: str
    company_id: str
    company_name: str
    category_name: Optional[str] = None
    parent_code: Optional[str] = None
    parent_name: Optional[str] = None
    is_system: bool
    created_at: str
    updated_at: str
    
    # 统计信息
    children_count: int = 0
    journal_entries_count: int = 0
    balance: float = 0.0

class AccountTreeNode(BaseModel):
    id: str
    code: str
    name: str
    type: str
    is_active: bool
    is_system: bool
    balance: float = 0.0
    children: List['AccountTreeNode'] = []

# 更新前向引用
AccountTreeNode.model_rebuild()

class AccountBalance(BaseModel):
    account_id: str
    account_code: str
    account_name: str
    account_type: str
    debit_total: float
    credit_total: float
    balance: float
    period_start: str
    period_end: str

# 权限检查函数
def require_account_read(current_user: UserContext = Depends(get_current_user)):
    """需要科目读取权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT, UserRole.CASHIER, UserRole.VIEWER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要科目读取权限"
        )
    return current_user

def require_account_write(current_user: UserContext = Depends(get_current_user)):
    """需要科目写入权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN, UserRole.ACCOUNTANT]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要科目写入权限"
        )
    return current_user

# API路由
@accounts_router.get("/categories", response_model=List[AccountCategoryResponse])
async def get_account_categories(
    tenant_id: Optional[str] = Query(None),
    current_user: UserContext = Depends(require_account_read)
):
    """获取会计科目分类"""
    try:
        db = await get_database_connection()
        
        # 确定租户ID
        target_tenant_id = tenant_id or current_user.tenant_id
        
        if not target_tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定租户ID"
            )
        
        # 检查权限
        if current_user.role != UserRole.SYSTEM_ADMIN and target_tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该租户的科目分类"
            )
        
        query = """
            SELECT * FROM account_categories 
            WHERE tenant_id = ? 
            ORDER BY sort_order, name
        """
        
        result = await db.execute(query, [target_tenant_id])
        categories = []
        
        for row in result.fetchall():
            categories.append(AccountCategoryResponse(
                id=row['id'],
                tenant_id=row['tenant_id'],
                name=row['name'],
                type=row['type'],
                code_prefix=row['code_prefix'],
                is_system=bool(row['is_system']),
                sort_order=row['sort_order'],
                created_at=row['created_at']
            ))
        
        return categories
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取科目分类失败: {str(e)}"
        )

@accounts_router.get("/", response_model=List[AccountSubjectResponse])
async def get_account_subjects(
    company_id: Optional[str] = Query(None),
    type: Optional[str] = Query(None),
    parent_id: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    search: Optional[str] = Query(None),
    current_user: UserContext = Depends(require_account_read)
):
    """获取会计科目列表"""
    try:
        db = await get_database_connection()
        
        # 构建查询条件
        conditions = []
        params = []
        
        # 权限过滤
        if current_user.role != UserRole.SYSTEM_ADMIN:
            if current_user.tenant_id:
                conditions.append("c.tenant_id = ?")
                params.append(current_user.tenant_id)
        
        if company_id:
            conditions.append("as_.company_id = ?")
            params.append(company_id)
        
        if type:
            conditions.append("as_.type = ?")
            params.append(type)
        
        if parent_id:
            conditions.append("as_.parent_id = ?")
            params.append(parent_id)
        
        if is_active is not None:
            conditions.append("as_.is_active = ?")
            params.append(is_active)
        
        if search:
            conditions.append("(as_.code LIKE ? OR as_.name LIKE ?)")
            search_term = f"%{search}%"
            params.extend([search_term, search_term])
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        query = f"""
            SELECT as_.*, c.name as company_name, ac.name as category_name,
                   p.code as parent_code, p.name as parent_name,
                   COUNT(DISTINCT child.id) as children_count,
                   COUNT(DISTINCT jel.id) as journal_entries_count,
                   COALESCE(SUM(jel.debit_amount) - SUM(jel.credit_amount), 0) as balance
            FROM account_subjects as_
            JOIN companies c ON as_.company_id = c.id
            LEFT JOIN account_categories ac ON as_.category_id = ac.id
            LEFT JOIN account_subjects p ON as_.parent_id = p.id
            LEFT JOIN account_subjects child ON as_.id = child.parent_id
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            {where_clause}
            GROUP BY as_.id
            ORDER BY as_.code
        """
        
        result = await db.execute(query, params)
        subjects = []
        
        for row in result.fetchall():
            subjects.append(AccountSubjectResponse(
                id=row['id'],
                company_id=row['company_id'],
                company_name=row['company_name'],
                code=row['code'],
                name=row['name'],
                type=row['type'],
                parent_id=row['parent_id'],
                category_id=row['category_id'],
                category_name=row['category_name'],
                parent_code=row['parent_code'],
                parent_name=row['parent_name'],
                is_active=bool(row['is_active']),
                is_system=bool(row['is_system']),
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                children_count=row['children_count'] or 0,
                journal_entries_count=row['journal_entries_count'] or 0,
                balance=float(row['balance'] or 0)
            ))
        
        return subjects
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会计科目失败: {str(e)}"
        )

@accounts_router.get("/tree", response_model=List[AccountTreeNode])
async def get_account_tree(
    company_id: str = Query(...),
    type: Optional[str] = Query(None),
    current_user: UserContext = Depends(require_account_read)
):
    """获取会计科目树形结构"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该公司"
            )
        
        # 构建查询条件
        conditions = ["as_.company_id = ?"]
        params = [company_id]
        
        if type:
            conditions.append("as_.type = ?")
            params.append(type)
        
        where_clause = "WHERE " + " AND ".join(conditions)
        
        # 获取所有科目
        query = f"""
            SELECT as_.*, 
                   COALESCE(SUM(jel.debit_amount) - SUM(jel.credit_amount), 0) as balance
            FROM account_subjects as_
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            {where_clause}
            GROUP BY as_.id
            ORDER BY as_.code
        """
        
        result = await db.execute(query, params)
        all_subjects = {}
        root_subjects = []
        
        # 构建科目字典
        for row in result.fetchall():
            subject = AccountTreeNode(
                id=row['id'],
                code=row['code'],
                name=row['name'],
                type=row['type'],
                is_active=bool(row['is_active']),
                is_system=bool(row['is_system']),
                balance=float(row['balance'] or 0),
                children=[]
            )
            
            all_subjects[row['id']] = subject
            
            if not row['parent_id']:
                root_subjects.append(subject)
        
        # 构建树形结构
        for row in result.fetchall():
            if row['parent_id'] and row['parent_id'] in all_subjects:
                parent = all_subjects[row['parent_id']]
                child = all_subjects[row['id']]
                parent.children.append(child)
        
        return root_subjects
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取科目树失败: {str(e)}"
        )

@accounts_router.post("/", response_model=AccountSubjectResponse)
async def create_account_subject(
    subject_data: AccountSubjectCreate,
    current_user: UserContext = Depends(require_account_write)
):
    """创建会计科目"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, subject_data.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权在该公司创建科目"
            )
        
        # 检查科目代码是否已存在
        existing_code = await db.execute(
            "SELECT id FROM account_subjects WHERE company_id = ? AND code = ?",
            [subject_data.company_id, subject_data.code]
        )
        
        if existing_code.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="科目代码已存在"
            )
        
        # 验证父科目
        if subject_data.parent_id:
            parent_check = await db.execute(
                "SELECT id, type FROM account_subjects WHERE id = ? AND company_id = ?",
                [subject_data.parent_id, subject_data.company_id]
            )
            parent = parent_check.fetchone()
            
            if not parent:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="父科目不存在"
                )
            
            if parent['type'] != subject_data.type:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="子科目类型必须与父科目一致"
                )
        
        # 验证科目分类
        if subject_data.category_id:
            category_check = await db.execute("""
                SELECT id, type FROM account_categories ac
                JOIN companies c ON ac.tenant_id = c.tenant_id
                WHERE ac.id = ? AND c.id = ?
            """, [subject_data.category_id, subject_data.company_id])
            
            category = category_check.fetchone()
            if not category:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="科目分类不存在"
                )
            
            if category['type'] != subject_data.type:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="科目类型必须与分类一致"
                )
        
        # 生成科目ID
        subject_id = f"account_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建科目
        await db.execute("""
            INSERT INTO account_subjects (
                id, company_id, category_id, parent_id, code, name, type, is_active, is_system
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, FALSE)
        """, [
            subject_id,
            subject_data.company_id,
            subject_data.category_id,
            subject_data.parent_id,
            subject_data.code,
            subject_data.name,
            subject_data.type,
            subject_data.is_active
        ])
        
        # 返回创建的科目
        return await get_account_subject(subject_id, current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建会计科目失败: {str(e)}"
        )

@accounts_router.get("/{subject_id}", response_model=AccountSubjectResponse)
async def get_account_subject(
    subject_id: str,
    current_user: UserContext = Depends(require_account_read)
):
    """获取会计科目详情"""
    try:
        db = await get_database_connection()
        
        query = """
            SELECT as_.*, c.name as company_name, ac.name as category_name,
                   p.code as parent_code, p.name as parent_name,
                   COUNT(DISTINCT child.id) as children_count,
                   COUNT(DISTINCT jel.id) as journal_entries_count,
                   COALESCE(SUM(jel.debit_amount) - SUM(jel.credit_amount), 0) as balance
            FROM account_subjects as_
            JOIN companies c ON as_.company_id = c.id
            LEFT JOIN account_categories ac ON as_.category_id = ac.id
            LEFT JOIN account_subjects p ON as_.parent_id = p.id
            LEFT JOIN account_subjects child ON as_.id = child.parent_id
            LEFT JOIN journal_entry_lines jel ON as_.id = jel.account_id
            WHERE as_.id = ?
            GROUP BY as_.id
        """
        
        result = await db.execute(query, [subject_id])
        subject = result.fetchone()
        
        if not subject:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会计科目不存在"
            )
        
        # 检查访问权限
        if not await check_company_access(db, current_user, subject['company_id']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该会计科目"
            )
        
        return AccountSubjectResponse(
            id=subject['id'],
            company_id=subject['company_id'],
            company_name=subject['company_name'],
            code=subject['code'],
            name=subject['name'],
            type=subject['type'],
            parent_id=subject['parent_id'],
            category_id=subject['category_id'],
            category_name=subject['category_name'],
            parent_code=subject['parent_code'],
            parent_name=subject['parent_name'],
            is_active=bool(subject['is_active']),
            is_system=bool(subject['is_system']),
            created_at=subject['created_at'],
            updated_at=subject['updated_at'],
            children_count=subject['children_count'] or 0,
            journal_entries_count=subject['journal_entries_count'] or 0,
            balance=float(subject['balance'] or 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会计科目失败: {str(e)}"
        )

@accounts_router.get("/{subject_id}/balance", response_model=AccountBalance)
async def get_account_balance(
    subject_id: str,
    start_date: str = Query(...),
    end_date: str = Query(...),
    current_user: UserContext = Depends(require_account_read)
):
    """获取会计科目余额"""
    try:
        db = await get_database_connection()
        
        # 检查科目是否存在
        subject_check = await db.execute(
            "SELECT id, company_id, code, name, type FROM account_subjects WHERE id = ?",
            [subject_id]
        )
        subject = subject_check.fetchone()
        
        if not subject:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会计科目不存在"
            )
        
        # 检查访问权限
        if not await check_company_access(db, current_user, subject['company_id']):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该会计科目"
            )
        
        # 计算余额
        balance_query = """
            SELECT 
                COALESCE(SUM(jel.debit_amount), 0) as debit_total,
                COALESCE(SUM(jel.credit_amount), 0) as credit_total
            FROM journal_entry_lines jel
            JOIN journal_entries je ON jel.journal_entry_id = je.id
            WHERE jel.account_id = ? 
            AND je.entry_date BETWEEN ? AND ?
            AND je.status = 'posted'
        """
        
        balance_result = await db.execute(balance_query, [subject_id, start_date, end_date])
        balance_data = balance_result.fetchone()
        
        debit_total = float(balance_data['debit_total'] or 0)
        credit_total = float(balance_data['credit_total'] or 0)
        
        # 根据科目类型计算余额
        if subject['type'] in ['asset', 'expense']:
            balance = debit_total - credit_total
        else:  # liability, equity, revenue
            balance = credit_total - debit_total
        
        return AccountBalance(
            account_id=subject['id'],
            account_code=subject['code'],
            account_name=subject['name'],
            account_type=subject['type'],
            debit_total=debit_total,
            credit_total=credit_total,
            balance=balance,
            period_start=start_date,
            period_end=end_date
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取科目余额失败: {str(e)}"
        )

# 辅助函数
async def check_company_access(db, current_user: UserContext, company_id: str) -> bool:
    """检查用户是否有权限访问公司"""
    if current_user.role == UserRole.SYSTEM_ADMIN:
        return True
    
    # 检查公司是否属于用户的租户
    if current_user.tenant_id:
        company_check = await db.execute(
            "SELECT id FROM companies WHERE id = ? AND tenant_id = ?",
            [company_id, current_user.tenant_id]
        )
        return company_check.fetchone() is not None
    
    return False

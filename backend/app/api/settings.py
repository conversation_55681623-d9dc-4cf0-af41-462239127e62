"""
系统设置API路由
处理AI配置、系统参数等设置功能
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

from ..core.security import UserContext, UserRole
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
settings_router = APIRouter(prefix="/settings", tags=["系统设置"])

# Pydantic模型
class LLMConfigurationBase(BaseModel):
    provider: str
    model_name: str
    max_tokens: int = 1000
    temperature: Decimal = Decimal('0.7')
    is_active: bool = True

class LLMConfigurationCreate(LLMConfigurationBase):
    tenant_id: Optional[str] = None
    api_key: str
    
    @validator('provider')
    def validate_provider(cls, v):
        allowed_providers = ['openai', 'anthropic', 'google']
        if v not in allowed_providers:
            raise ValueError(f'Provider must be one of: {", ".join(allowed_providers)}')
        return v
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if v < 0 or v > 2:
            raise ValueError('Temperature must be between 0 and 2')
        return v

class LLMConfigurationUpdate(BaseModel):
    model_name: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[Decimal] = None
    is_active: Optional[bool] = None
    api_key: Optional[str] = None

class LLMConfigurationResponse(LLMConfigurationBase):
    id: str
    tenant_id: str
    tenant_name: str
    api_key_masked: str
    created_at: str
    updated_at: str

class SystemParameterBase(BaseModel):
    key: str
    value: str
    description: Optional[str] = None
    is_system: bool = False

class SystemParameterCreate(SystemParameterBase):
    tenant_id: Optional[str] = None

class SystemParameterUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None

class SystemParameterResponse(SystemParameterBase):
    id: str
    tenant_id: Optional[str] = None
    tenant_name: Optional[str] = None
    created_at: str
    updated_at: str

class AuditLogFilter(BaseModel):
    tenant_id: Optional[str] = None
    user_id: Optional[str] = None
    company_id: Optional[str] = None
    action: Optional[str] = None
    resource_type: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None

class AuditLogResponse(BaseModel):
    id: str
    tenant_id: Optional[str] = None
    tenant_name: Optional[str] = None
    user_id: Optional[str] = None
    user_name: Optional[str] = None
    company_id: Optional[str] = None
    company_name: Optional[str] = None
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: str

class BackupRequest(BaseModel):
    company_id: str
    backup_type: str = "full"  # full, incremental
    description: Optional[str] = None

class BackupResponse(BaseModel):
    id: str
    company_id: str
    company_name: str
    backup_type: str
    file_path: str
    file_size: int
    description: Optional[str] = None
    status: str
    created_at: str

# 权限检查函数
def require_settings_read(current_user: UserContext = Depends(get_current_user)):
    """需要设置读取权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要设置读取权限"
        )
    return current_user

def require_settings_write(current_user: UserContext = Depends(get_current_user)):
    """需要设置写入权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要设置写入权限"
        )
    return current_user

def require_system_admin(current_user: UserContext = Depends(get_current_user)):
    """需要系统管理员权限"""
    if current_user.role != UserRole.SYSTEM_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要系统管理员权限"
        )
    return current_user

# LLM配置API
@settings_router.get("/llm", response_model=List[LLMConfigurationResponse])
async def get_llm_configurations(
    tenant_id: Optional[str] = Query(None),
    current_user: UserContext = Depends(require_settings_read)
):
    """获取LLM配置列表"""
    try:
        db = await get_database_connection()
        
        # 构建查询条件
        conditions = []
        params = []
        
        if current_user.role == UserRole.SYSTEM_ADMIN:
            if tenant_id:
                conditions.append("llm.tenant_id = ?")
                params.append(tenant_id)
        else:
            # 租户管理员只能查看自己租户的配置
            conditions.append("llm.tenant_id = ?")
            params.append(current_user.tenant_id)
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        query = f"""
            SELECT llm.*, t.name as tenant_name
            FROM llm_configurations llm
            LEFT JOIN tenants t ON llm.tenant_id = t.id
            {where_clause}
            ORDER BY llm.created_at DESC
        """
        
        result = await db.execute(query, params)
        configurations = []
        
        for row in result.fetchall():
            # 掩码API密钥
            api_key_masked = mask_api_key(row['api_key_encrypted']) if row['api_key_encrypted'] else ""
            
            configurations.append(LLMConfigurationResponse(
                id=row['id'],
                tenant_id=row['tenant_id'],
                tenant_name=row['tenant_name'],
                provider=row['provider'],
                model_name=row['model_name'],
                api_key_masked=api_key_masked,
                max_tokens=row['max_tokens'],
                temperature=Decimal(str(row['temperature'])),
                is_active=bool(row['is_active']),
                created_at=row['created_at'],
                updated_at=row['updated_at']
            ))
        
        return configurations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取LLM配置失败: {str(e)}"
        )

@settings_router.post("/llm", response_model=LLMConfigurationResponse)
async def create_llm_configuration(
    config_data: LLMConfigurationCreate,
    current_user: UserContext = Depends(require_settings_write)
):
    """创建LLM配置"""
    try:
        db = await get_database_connection()
        
        # 确定租户ID
        tenant_id = config_data.tenant_id or current_user.tenant_id
        
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定租户ID"
            )
        
        # 检查权限
        if current_user.role != UserRole.SYSTEM_ADMIN and tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权为该租户创建配置"
            )
        
        # 检查是否已存在相同提供商的配置
        existing_config = await db.execute(
            "SELECT id FROM llm_configurations WHERE tenant_id = ? AND provider = ?",
            [tenant_id, config_data.provider]
        )
        
        if existing_config.fetchone():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"租户已存在{config_data.provider}的配置"
            )
        
        # 生成配置ID
        config_id = f"llm_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # 加密API密钥（简化处理，实际应使用专门的加密库）
        encrypted_api_key = encrypt_api_key(config_data.api_key)
        
        # 创建配置
        await db.execute("""
            INSERT INTO llm_configurations (
                id, tenant_id, provider, model_name, api_key_encrypted,
                max_tokens, temperature, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, [
            config_id,
            tenant_id,
            config_data.provider,
            config_data.model_name,
            encrypted_api_key,
            config_data.max_tokens,
            str(config_data.temperature),
            config_data.is_active
        ])
        
        # 返回创建的配置
        return await get_llm_configuration(config_id, current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建LLM配置失败: {str(e)}"
        )

@settings_router.get("/llm/{config_id}", response_model=LLMConfigurationResponse)
async def get_llm_configuration(
    config_id: str,
    current_user: UserContext = Depends(require_settings_read)
):
    """获取LLM配置详情"""
    try:
        db = await get_database_connection()
        
        query = """
            SELECT llm.*, t.name as tenant_name
            FROM llm_configurations llm
            LEFT JOIN tenants t ON llm.tenant_id = t.id
            WHERE llm.id = ?
        """
        
        result = await db.execute(query, [config_id])
        config = result.fetchone()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="LLM配置不存在"
            )
        
        # 检查权限
        if current_user.role != UserRole.SYSTEM_ADMIN and config['tenant_id'] != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该配置"
            )
        
        # 掩码API密钥
        api_key_masked = mask_api_key(config['api_key_encrypted']) if config['api_key_encrypted'] else ""
        
        return LLMConfigurationResponse(
            id=config['id'],
            tenant_id=config['tenant_id'],
            tenant_name=config['tenant_name'],
            provider=config['provider'],
            model_name=config['model_name'],
            api_key_masked=api_key_masked,
            max_tokens=config['max_tokens'],
            temperature=Decimal(str(config['temperature'])),
            is_active=bool(config['is_active']),
            created_at=config['created_at'],
            updated_at=config['updated_at']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取LLM配置失败: {str(e)}"
        )

@settings_router.get("/audit-logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    tenant_id: Optional[str] = Query(None),
    user_id: Optional[str] = Query(None),
    company_id: Optional[str] = Query(None),
    action: Optional[str] = Query(None),
    resource_type: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    current_user: UserContext = Depends(require_settings_read)
):
    """获取审计日志"""
    try:
        db = await get_database_connection()
        
        # 构建查询条件
        conditions = []
        params = []
        
        # 权限过滤
        if current_user.role != UserRole.SYSTEM_ADMIN:
            conditions.append("al.tenant_id = ?")
            params.append(current_user.tenant_id)
        elif tenant_id:
            conditions.append("al.tenant_id = ?")
            params.append(tenant_id)
        
        if user_id:
            conditions.append("al.user_id = ?")
            params.append(user_id)
        
        if company_id:
            conditions.append("al.company_id = ?")
            params.append(company_id)
        
        if action:
            conditions.append("al.action = ?")
            params.append(action)
        
        if resource_type:
            conditions.append("al.resource_type = ?")
            params.append(resource_type)
        
        if start_date:
            conditions.append("al.created_at >= ?")
            params.append(start_date)
        
        if end_date:
            conditions.append("al.created_at <= ?")
            params.append(end_date)
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        query = f"""
            SELECT al.*, t.name as tenant_name, u.name as user_name, c.name as company_name
            FROM audit_logs al
            LEFT JOIN tenants t ON al.tenant_id = t.id
            LEFT JOIN users u ON al.user_id = u.id
            LEFT JOIN companies c ON al.company_id = c.id
            {where_clause}
            ORDER BY al.created_at DESC
            LIMIT ? OFFSET ?
        """
        
        params.extend([page_size, offset])
        result = await db.execute(query, params)
        
        logs = []
        for row in result.fetchall():
            # 解析JSON字段
            old_values = None
            new_values = None
            
            if row['old_values']:
                try:
                    import json
                    old_values = json.loads(row['old_values'])
                except:
                    pass
            
            if row['new_values']:
                try:
                    import json
                    new_values = json.loads(row['new_values'])
                except:
                    pass
            
            logs.append(AuditLogResponse(
                id=row['id'],
                tenant_id=row['tenant_id'],
                tenant_name=row['tenant_name'],
                user_id=row['user_id'],
                user_name=row['user_name'],
                company_id=row['company_id'],
                company_name=row['company_name'],
                action=row['action'],
                resource_type=row['resource_type'],
                resource_id=row['resource_id'],
                old_values=old_values,
                new_values=new_values,
                ip_address=row['ip_address'],
                user_agent=row['user_agent'],
                created_at=row['created_at']
            ))
        
        return logs
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取审计日志失败: {str(e)}"
        )

@settings_router.post("/backup")
async def create_backup(
    backup_request: BackupRequest,
    current_user: UserContext = Depends(require_settings_write)
):
    """创建数据备份"""
    try:
        db = await get_database_connection()
        
        # 检查公司访问权限
        if not await check_company_access(db, current_user, backup_request.company_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权备份该公司数据"
            )
        
        # 生成备份ID和文件路径
        backup_id = f"backup_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        file_path = f"/backups/{backup_request.company_id}/{backup_id}.sql"
        
        # 创建备份记录
        await db.execute("""
            INSERT INTO data_backups (
                id, company_id, backup_type, file_path, description, 
                status, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, 'processing', ?, CURRENT_TIMESTAMP)
        """, [
            backup_id,
            backup_request.company_id,
            backup_request.backup_type,
            file_path,
            backup_request.description,
            current_user.user_id
        ])
        
        # TODO: 实际的备份逻辑
        # 这里应该调用备份服务来创建实际的备份文件
        
        # 更新备份状态
        await db.execute(
            "UPDATE data_backups SET status = 'completed', file_size = ? WHERE id = ?",
            [1024, backup_id]  # 示例文件大小
        )
        
        return {"message": "备份创建成功", "backup_id": backup_id}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建备份失败: {str(e)}"
        )

# 辅助函数
def mask_api_key(api_key: str) -> str:
    """掩码API密钥"""
    if not api_key or len(api_key) < 8:
        return "****"
    return api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:]

def encrypt_api_key(api_key: str) -> str:
    """加密API密钥（简化实现）"""
    # 实际应使用专门的加密库如cryptography
    import base64
    return base64.b64encode(api_key.encode()).decode()

def decrypt_api_key(encrypted_key: str) -> str:
    """解密API密钥（简化实现）"""
    import base64
    return base64.b64decode(encrypted_key.encode()).decode()

async def check_company_access(db, current_user: UserContext, company_id: str) -> bool:
    """检查用户是否有权限访问公司"""
    if current_user.role == UserRole.SYSTEM_ADMIN:
        return True
    
    # 检查公司是否属于用户的租户
    if current_user.tenant_id:
        company_check = await db.execute(
            "SELECT id FROM companies WHERE id = ? AND tenant_id = ?",
            [company_id, current_user.tenant_id]
        )
        return company_check.fetchone() is not None
    
    return False

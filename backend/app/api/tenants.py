"""
租户管理API路由
处理租户的创建、查询、更新等功能
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from ..core.security import UserContext, UserRole, PermissionType
from ..core.database import get_database_connection
from .auth import get_current_user

# 创建路由器
tenants_router = APIRouter(prefix="/tenants", tags=["租户管理"])

# Pydantic模型
class TenantBase(BaseModel):
    name: str
    domain: Optional[str] = None
    subscription_plan: str = "trial"
    max_companies: int = 1
    max_users: int = 5

class TenantCreate(TenantBase):
    pass

class TenantUpdate(BaseModel):
    name: Optional[str] = None
    domain: Optional[str] = None
    subscription_plan: Optional[str] = None
    max_companies: Optional[int] = None
    max_users: Optional[int] = None
    is_active: Optional[bool] = None

class TenantResponse(TenantBase):
    id: str
    is_active: bool
    trial_ends_at: Optional[str] = None
    created_at: str
    updated_at: str
    
    # 统计信息
    companies_count: int = 0
    users_count: int = 0

class UserTenantRole(BaseModel):
    user_id: str
    user_email: str
    user_name: Optional[str] = None
    role: str
    is_active: bool
    granted_at: str
    expires_at: Optional[str] = None

# 权限检查函数
def require_system_admin(current_user: UserContext = Depends(get_current_user)):
    """需要系统管理员权限"""
    if current_user.role != UserRole.SYSTEM_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要系统管理员权限"
        )
    return current_user

def require_tenant_admin(current_user: UserContext = Depends(get_current_user)):
    """需要租户管理员权限"""
    if current_user.role not in [UserRole.SYSTEM_ADMIN, UserRole.TENANT_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要租户管理员权限"
        )
    return current_user

# API路由
@tenants_router.get("/", response_model=List[TenantResponse])
async def get_tenants(current_user: UserContext = Depends(get_current_user)):
    """获取用户可访问的租户列表"""
    try:
        db = await get_database_connection()
        
        if current_user.role == UserRole.SYSTEM_ADMIN:
            # 系统管理员可以看到所有租户
            query = """
                SELECT t.*, 
                       COUNT(DISTINCT c.id) as companies_count,
                       COUNT(DISTINCT utr.user_id) as users_count
                FROM tenants t
                LEFT JOIN companies c ON t.id = c.tenant_id AND c.is_active = TRUE
                LEFT JOIN user_tenant_roles utr ON t.id = utr.tenant_id AND utr.is_active = TRUE
                GROUP BY t.id
                ORDER BY t.created_at DESC
            """
            result = await db.execute(query)
        else:
            # 普通用户只能看到自己所属的租户
            query = """
                SELECT t.*, 
                       COUNT(DISTINCT c.id) as companies_count,
                       COUNT(DISTINCT utr2.user_id) as users_count
                FROM tenants t
                JOIN user_tenant_roles utr ON t.id = utr.tenant_id
                LEFT JOIN companies c ON t.id = c.tenant_id AND c.is_active = TRUE
                LEFT JOIN user_tenant_roles utr2 ON t.id = utr2.tenant_id AND utr2.is_active = TRUE
                WHERE utr.user_id = ? AND utr.is_active = TRUE
                GROUP BY t.id
                ORDER BY t.created_at DESC
            """
            result = await db.execute(query, [current_user.user_id])
        
        tenants = []
        for row in result.fetchall():
            tenants.append(TenantResponse(
                id=row['id'],
                name=row['name'],
                domain=row['domain'],
                subscription_plan=row['subscription_plan'],
                is_active=row['is_active'],
                trial_ends_at=row['trial_ends_at'],
                max_companies=row['max_companies'],
                max_users=row['max_users'],
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                companies_count=row['companies_count'] or 0,
                users_count=row['users_count'] or 0
            ))
        
        return tenants
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户列表失败: {str(e)}"
        )

@tenants_router.post("/", response_model=TenantResponse)
async def create_tenant(
    tenant_data: TenantCreate,
    current_user: UserContext = Depends(require_system_admin)
):
    """创建新租户（仅系统管理员）"""
    try:
        db = await get_database_connection()
        
        # 检查域名是否已存在
        if tenant_data.domain:
            existing_domain = await db.execute(
                "SELECT id FROM tenants WHERE domain = ?",
                [tenant_data.domain]
            )
            if existing_domain.fetchone():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该域名已被使用"
                )
        
        # 生成租户ID
        tenant_id = f"tenant_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建租户
        await db.execute("""
            INSERT INTO tenants (
                id, name, domain, subscription_plan, is_active,
                trial_ends_at, max_companies, max_users
            ) VALUES (?, ?, ?, ?, TRUE, datetime('now', '+30 days'), ?, ?)
        """, [
            tenant_id,
            tenant_data.name,
            tenant_data.domain,
            tenant_data.subscription_plan,
            tenant_data.max_companies,
            tenant_data.max_users
        ])
        
        # 创建默认会计科目分类
        default_categories = [
            ('流动资产', 'asset', '1001', 1),
            ('非流动资产', 'asset', '1501', 2),
            ('流动负债', 'liability', '2001', 3),
            ('非流动负债', 'liability', '2501', 4),
            ('所有者权益', 'equity', '3001', 5),
            ('营业收入', 'revenue', '4001', 6),
            ('营业成本', 'expense', '5001', 7),
            ('期间费用', 'expense', '6001', 8),
        ]
        
        for name, type_, code_prefix, sort_order in default_categories:
            await db.execute("""
                INSERT INTO account_categories (tenant_id, name, type, code_prefix, is_system, sort_order)
                VALUES (?, ?, ?, ?, TRUE, ?)
            """, [tenant_id, name, type_, code_prefix, sort_order])
        
        # 获取创建的租户信息
        result = await db.execute(
            "SELECT * FROM tenants WHERE id = ?",
            [tenant_id]
        )
        tenant = result.fetchone()
        
        return TenantResponse(
            id=tenant['id'],
            name=tenant['name'],
            domain=tenant['domain'],
            subscription_plan=tenant['subscription_plan'],
            is_active=tenant['is_active'],
            trial_ends_at=tenant['trial_ends_at'],
            max_companies=tenant['max_companies'],
            max_users=tenant['max_users'],
            created_at=tenant['created_at'],
            updated_at=tenant['updated_at'],
            companies_count=0,
            users_count=0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建租户失败: {str(e)}"
        )

@tenants_router.get("/{tenant_id}", response_model=TenantResponse)
async def get_tenant(
    tenant_id: str,
    current_user: UserContext = Depends(get_current_user)
):
    """获取租户详情"""
    try:
        db = await get_database_connection()
        
        # 检查访问权限
        if current_user.role != UserRole.SYSTEM_ADMIN:
            # 检查用户是否属于该租户
            access_check = await db.execute("""
                SELECT id FROM user_tenant_roles 
                WHERE user_id = ? AND tenant_id = ? AND is_active = TRUE
            """, [current_user.user_id, tenant_id])
            
            if not access_check.fetchone():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问该租户"
                )
        
        # 获取租户信息
        query = """
            SELECT t.*, 
                   COUNT(DISTINCT c.id) as companies_count,
                   COUNT(DISTINCT utr.user_id) as users_count
            FROM tenants t
            LEFT JOIN companies c ON t.id = c.tenant_id AND c.is_active = TRUE
            LEFT JOIN user_tenant_roles utr ON t.id = utr.tenant_id AND utr.is_active = TRUE
            WHERE t.id = ?
            GROUP BY t.id
        """
        
        result = await db.execute(query, [tenant_id])
        tenant = result.fetchone()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在"
            )
        
        return TenantResponse(
            id=tenant['id'],
            name=tenant['name'],
            domain=tenant['domain'],
            subscription_plan=tenant['subscription_plan'],
            is_active=tenant['is_active'],
            trial_ends_at=tenant['trial_ends_at'],
            max_companies=tenant['max_companies'],
            max_users=tenant['max_users'],
            created_at=tenant['created_at'],
            updated_at=tenant['updated_at'],
            companies_count=tenant['companies_count'] or 0,
            users_count=tenant['users_count'] or 0
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户信息失败: {str(e)}"
        )

@tenants_router.put("/{tenant_id}", response_model=TenantResponse)
async def update_tenant(
    tenant_id: str,
    tenant_data: TenantUpdate,
    current_user: UserContext = Depends(require_tenant_admin)
):
    """更新租户信息"""
    try:
        db = await get_database_connection()
        
        # 检查租户是否存在
        existing_tenant = await db.execute(
            "SELECT * FROM tenants WHERE id = ?",
            [tenant_id]
        )
        tenant = existing_tenant.fetchone()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在"
            )
        
        # 检查权限（非系统管理员只能修改自己的租户）
        if current_user.role != UserRole.SYSTEM_ADMIN and current_user.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改该租户"
            )
        
        # 构建更新语句
        update_fields = []
        update_values = []
        
        if tenant_data.name is not None:
            update_fields.append("name = ?")
            update_values.append(tenant_data.name)
        
        if tenant_data.domain is not None:
            # 检查域名是否已被其他租户使用
            domain_check = await db.execute(
                "SELECT id FROM tenants WHERE domain = ? AND id != ?",
                [tenant_data.domain, tenant_id]
            )
            if domain_check.fetchone():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该域名已被使用"
                )
            update_fields.append("domain = ?")
            update_values.append(tenant_data.domain)
        
        if tenant_data.subscription_plan is not None:
            update_fields.append("subscription_plan = ?")
            update_values.append(tenant_data.subscription_plan)
        
        if tenant_data.max_companies is not None:
            update_fields.append("max_companies = ?")
            update_values.append(tenant_data.max_companies)
        
        if tenant_data.max_users is not None:
            update_fields.append("max_users = ?")
            update_values.append(tenant_data.max_users)
        
        if tenant_data.is_active is not None:
            update_fields.append("is_active = ?")
            update_values.append(tenant_data.is_active)
        
        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            update_values.append(tenant_id)
            
            update_query = f"""
                UPDATE tenants 
                SET {', '.join(update_fields)}
                WHERE id = ?
            """
            
            await db.execute(update_query, update_values)
        
        # 返回更新后的租户信息
        return await get_tenant(tenant_id, current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新租户失败: {str(e)}"
        )

@tenants_router.get("/{tenant_id}/users", response_model=List[UserTenantRole])
async def get_tenant_users(
    tenant_id: str,
    current_user: UserContext = Depends(require_tenant_admin)
):
    """获取租户用户列表"""
    try:
        db = await get_database_connection()
        
        # 检查权限
        if current_user.role != UserRole.SYSTEM_ADMIN and current_user.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该租户用户"
            )
        
        query = """
            SELECT utr.*, u.email, u.name
            FROM user_tenant_roles utr
            JOIN users u ON utr.user_id = u.id
            WHERE utr.tenant_id = ?
            ORDER BY utr.granted_at DESC
        """
        
        result = await db.execute(query, [tenant_id])
        users = []
        
        for row in result.fetchall():
            users.append(UserTenantRole(
                user_id=row['user_id'],
                user_email=row['email'],
                user_name=row['name'],
                role=row['role'],
                is_active=row['is_active'],
                granted_at=row['granted_at'],
                expires_at=row['expires_at']
            ))
        
        return users
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户用户失败: {str(e)}"
        )

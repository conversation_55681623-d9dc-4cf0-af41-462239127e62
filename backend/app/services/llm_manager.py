"""
LLM Model Manager - 统一管理多个LLM供应商
支持Google Gemini, OpenAI, Anthropic等多种模型
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from enum import Enum
import google.generativeai as genai
from openai import AsyncOpenAI
import anthropic

from app.core.config import settings
from app.core.database import get_database
from app.core.encryption import EncryptionService


class ModelProvider(str, Enum):
    GOOGLE = "Google"
    OPENAI = "OpenAI"
    ANTHROPIC = "Anthropic"
    LOCAL = "Local"


class TaskType(str, Enum):
    COMPLEX_ACCOUNTING = "complex_accounting"
    SIMPLE_CLASSIFICATION = "simple_classification"
    OCR_PROCESSING = "ocr_processing"
    NATURAL_LANGUAGE = "natural_language"
    ANALYSIS = "analysis"


class LLMModelManager:
    """统一LLM模型管理器"""
    
    def __init__(self, company_id: str):
        self.company_id = company_id
        self.encryption_service = EncryptionService()
        
        # 默认Gemini配置
        self.default_gemini_key = settings.GOOGLE_API_KEY
        
        # 支持的模型配置
        self.model_configs = {
            ModelProvider.GOOGLE: {
                "models/gemini-2.5-pro": {
                    "name": "Gemini-2.5-Pro",
                    "max_tokens": 8192,
                    "supports": [TaskType.COMPLEX_ACCOUNTING, TaskType.NATURAL_LANGUAGE]
                },
                "models/gemini-2.5-flash": {
                    "name": "Gemini-2.5-Flash", 
                    "max_tokens": 8192,
                    "supports": [TaskType.SIMPLE_CLASSIFICATION, TaskType.OCR_PROCESSING]
                },
                "models/gemini-1.5-pro": {
                    "name": "Gemini-1.5-Pro",
                    "max_tokens": 2048,
                    "supports": [TaskType.OCR_PROCESSING, TaskType.ANALYSIS]
                }
            },
            ModelProvider.OPENAI: {
                "gpt-4": {
                    "name": "GPT-4",
                    "max_tokens": 8192,
                    "supports": [TaskType.COMPLEX_ACCOUNTING, TaskType.ANALYSIS]
                },
                "gpt-3.5-turbo": {
                    "name": "GPT-3.5-Turbo",
                    "max_tokens": 4096,
                    "supports": [TaskType.SIMPLE_CLASSIFICATION, TaskType.NATURAL_LANGUAGE]
                }
            },
            ModelProvider.ANTHROPIC: {
                "claude-3-opus-********": {
                    "name": "Claude-3-Opus",
                    "max_tokens": 4096,
                    "supports": [TaskType.COMPLEX_ACCOUNTING, TaskType.ANALYSIS]
                }
            }
        }
    
    async def initialize_default_models(self):
        """初始化默认模型配置"""
        db = await get_database()
        
        try:
            # 添加默认Gemini模型配置
            await self._add_model_configuration(db, {
                "provider": ModelProvider.GOOGLE,
                "model_name": "Gemini-2.5-Pro",
                "model_id": "models/gemini-2.5-pro",
                "api_key": self.default_gemini_key,
                "task_types": [TaskType.COMPLEX_ACCOUNTING, TaskType.NATURAL_LANGUAGE],
                "priority": 1,
                "temperature": 0.1,
                "max_tokens": 8192
            })
            
            await self._add_model_configuration(db, {
                "provider": ModelProvider.GOOGLE,
                "model_name": "Gemini-2.5-Flash",
                "model_id": "models/gemini-2.5-flash", 
                "api_key": self.default_gemini_key,
                "task_types": [TaskType.SIMPLE_CLASSIFICATION, TaskType.OCR_PROCESSING],
                "priority": 2,
                "temperature": 0.1,
                "max_tokens": 8192
            })
            
            print("✅ 默认Gemini模型配置已初始化")
            
        except Exception as e:
            print(f"❌ 初始化默认模型失败: {e}")
    
    async def _add_model_configuration(self, db, config: Dict[str, Any]):
        """添加模型配置到数据库"""
        # 加密存储API密钥
        encrypted_key = self.encryption_service.encrypt(config["api_key"])
        
        # 保存API密钥
        api_key_result = await db.fetchrow("""
            INSERT INTO api_keys (company_id, provider, key_name, encrypted_key)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (company_id, provider, key_name) 
            DO UPDATE SET encrypted_key = $4, last_used_at = NOW()
            RETURNING id
        """, self.company_id, config["provider"], 
             f"{config['model_name']}_key", encrypted_key)
        
        api_key_id = api_key_result["id"]
        
        # 获取或创建LLM模型记录
        model_result = await db.fetchrow("""
            INSERT INTO llm_models (name, provider, model_id, max_tokens, capabilities)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (model_id) DO UPDATE SET 
                name = $1, max_tokens = $4, capabilities = $5
            RETURNING id
        """, config["model_name"], config["provider"], config["model_id"],
             config["max_tokens"], json.dumps(config["task_types"]))
        
        model_id = model_result["id"]
        
        # 保存模型配置
        for task_type in config["task_types"]:
            await db.execute("""
                INSERT INTO model_configurations 
                (company_id, llm_model_id, api_key_id, task_type, priority, temperature, max_tokens)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (company_id, llm_model_id, task_type)
                DO UPDATE SET priority = $5, temperature = $6, max_tokens = $7
            """, self.company_id, model_id, api_key_id, task_type,
                 config["priority"], config["temperature"], config["max_tokens"])
    
    async def get_best_model(self, task_type: TaskType) -> Dict[str, Any]:
        """根据任务类型获取最佳模型配置"""
        db = await get_database()
        
        config = await db.fetchrow("""
            SELECT mc.*, lm.model_id, lm.provider, lm.name as model_name, ak.encrypted_key
            FROM model_configurations mc
            JOIN llm_models lm ON mc.llm_model_id = lm.id
            JOIN api_keys ak ON mc.api_key_id = ak.id
            WHERE mc.company_id = $1 AND mc.task_type = $2 
            AND mc.is_active = TRUE AND ak.is_active = TRUE
            ORDER BY mc.priority ASC
            LIMIT 1
        """, self.company_id, task_type.value)
        
        if not config:
            # 使用默认Gemini配置
            return self._get_default_gemini_config(task_type)
        
        # 解密API密钥
        api_key = self.encryption_service.decrypt(config["encrypted_key"])
        
        return {
            "id": config["id"],
            "provider": config["provider"],
            "model_id": config["model_id"],
            "model_name": config["model_name"],
            "api_key": api_key,
            "temperature": float(config["temperature"]),
            "max_tokens": config["max_tokens"]
        }
    
    def _get_default_gemini_config(self, task_type: TaskType) -> Dict[str, Any]:
        """获取默认Gemini配置"""
        if task_type in [TaskType.COMPLEX_ACCOUNTING, TaskType.NATURAL_LANGUAGE]:
            model_id = "models/gemini-2.5-pro"
        else:
            model_id = "models/gemini-2.5-flash"
        
        return {
            "id": "default",
            "provider": ModelProvider.GOOGLE,
            "model_id": model_id,
            "model_name": model_id.split("/")[-1],
            "api_key": self.default_gemini_key,
            "temperature": 0.1,
            "max_tokens": 8192
        }
    
    async def log_usage(self, model_config_id: str, usage_data: Dict[str, Any]):
        """记录模型使用情况"""
        if model_config_id == "default":
            return  # 跳过默认配置的日志记录
        
        db = await get_database()
        
        await db.execute("""
            INSERT INTO model_usage_logs 
            (company_id, model_configuration_id, task_type, input_tokens, 
             output_tokens, cost, response_time_ms, success, error_message)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        """, self.company_id, model_config_id, usage_data["task_type"],
             usage_data.get("input_tokens", 0), usage_data.get("output_tokens", 0),
             usage_data.get("cost", 0.0), usage_data.get("response_time", 0),
             usage_data.get("success", True), usage_data.get("error_message"))


class UnifiedLLMClient:
    """统一LLM客户端 - 支持多供应商模型调用"""

    def __init__(self, company_id: str):
        self.company_id = company_id
        self.model_manager = get_model_manager(company_id)

    async def generate_content(self, task_type: TaskType, prompt: str, **kwargs) -> Dict[str, Any]:
        """统一的内容生成接口"""
        start_time = time.time()

        try:
            # 获取最佳模型配置
            model_config = await self.model_manager.get_best_model(task_type)

            # 根据供应商调用对应的客户端
            if model_config["provider"] == ModelProvider.GOOGLE:
                response = await self._call_gemini(model_config, prompt, **kwargs)
            elif model_config["provider"] == ModelProvider.OPENAI:
                response = await self._call_openai(model_config, prompt, **kwargs)
            elif model_config["provider"] == ModelProvider.ANTHROPIC:
                response = await self._call_anthropic(model_config, prompt, **kwargs)
            else:
                raise ValueError(f"Unsupported provider: {model_config['provider']}")

            # 记录使用情况
            response_time = int((time.time() - start_time) * 1000)
            await self.model_manager.log_usage(model_config["id"], {
                "task_type": task_type.value,
                "input_tokens": self._count_tokens(prompt),
                "output_tokens": self._count_tokens(response["text"]),
                "cost": self._calculate_cost(model_config, response),
                "response_time": response_time,
                "success": True
            })

            return response

        except Exception as e:
            # 尝试降级到备用模型
            return await self._handle_fallback(task_type, prompt, e, **kwargs)

    async def _call_gemini(self, config: Dict[str, Any], prompt: str, **kwargs) -> Dict[str, Any]:
        """调用Google Gemini模型"""
        genai.configure(api_key=config["api_key"])
        model = genai.GenerativeModel(config["model_id"])

        generation_config = genai.types.GenerationConfig(
            temperature=config["temperature"],
            max_output_tokens=config["max_tokens"]
        )

        response = await model.generate_content_async(
            prompt,
            generation_config=generation_config
        )

        return {
            "text": response.text,
            "model": config["model_name"],
            "provider": config["provider"],
            "usage": {
                "prompt_tokens": response.usage_metadata.prompt_token_count if response.usage_metadata else 0,
                "completion_tokens": response.usage_metadata.candidates_token_count if response.usage_metadata else 0,
                "total_tokens": response.usage_metadata.total_token_count if response.usage_metadata else 0
            }
        }

    async def _call_openai(self, config: Dict[str, Any], prompt: str, **kwargs) -> Dict[str, Any]:
        """调用OpenAI模型"""
        client = AsyncOpenAI(api_key=config["api_key"])

        response = await client.chat.completions.create(
            model=config["model_id"],
            messages=[{"role": "user", "content": prompt}],
            temperature=config["temperature"],
            max_tokens=config["max_tokens"]
        )

        return {
            "text": response.choices[0].message.content,
            "model": config["model_name"],
            "provider": config["provider"],
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }

    async def _call_anthropic(self, config: Dict[str, Any], prompt: str, **kwargs) -> Dict[str, Any]:
        """调用Anthropic Claude模型"""
        client = anthropic.AsyncAnthropic(api_key=config["api_key"])

        response = await client.messages.create(
            model=config["model_id"],
            max_tokens=config["max_tokens"],
            temperature=config["temperature"],
            messages=[{"role": "user", "content": prompt}]
        )

        return {
            "text": response.content[0].text,
            "model": config["model_name"],
            "provider": config["provider"],
            "usage": {
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens
            }
        }

    async def _handle_fallback(self, task_type: TaskType, prompt: str, error: Exception, **kwargs):
        """处理模型调用失败的降级策略"""
        print(f"⚠️ 模型调用失败，尝试降级: {error}")

        # 使用默认Gemini配置作为降级
        try:
            default_config = self.model_manager._get_default_gemini_config(task_type)
            response = await self._call_gemini(default_config, prompt, **kwargs)

            # 记录降级使用
            await self.model_manager.log_usage("fallback", {
                "task_type": task_type.value,
                "success": True,
                "error_message": f"Fallback after error: {str(error)}"
            })

            return response

        except Exception as fallback_error:
            # 记录失败日志
            await self.model_manager.log_usage("fallback", {
                "task_type": task_type.value,
                "success": False,
                "error_message": f"Fallback failed: {str(fallback_error)}"
            })
            raise fallback_error

    def _count_tokens(self, text: str) -> int:
        """简单的token计数估算"""
        return len(text.split()) * 1.3  # 粗略估算

    def _calculate_cost(self, config: Dict[str, Any], response: Dict[str, Any]) -> float:
        """计算API调用成本"""
        # 简化的成本计算，实际应根据各供应商的定价
        usage = response.get("usage", {})
        total_tokens = usage.get("total_tokens", 0)

        # 基础成本估算 (每1000 tokens的成本)
        cost_per_1k_tokens = {
            ModelProvider.GOOGLE: 0.001,  # Gemini定价
            ModelProvider.OPENAI: 0.002,  # GPT定价
            ModelProvider.ANTHROPIC: 0.003  # Claude定价
        }

        provider = config["provider"]
        return (total_tokens / 1000) * cost_per_1k_tokens.get(provider, 0.001)


# 全局模型管理器实例
_model_managers: Dict[str, LLMModelManager] = {}
_llm_clients: Dict[str, UnifiedLLMClient] = {}


def get_model_manager(company_id: str) -> LLMModelManager:
    """获取公司的模型管理器实例"""
    if company_id not in _model_managers:
        _model_managers[company_id] = LLMModelManager(company_id)
    return _model_managers[company_id]


def get_llm_client(company_id: str) -> UnifiedLLMClient:
    """获取公司的LLM客户端实例"""
    if company_id not in _llm_clients:
        _llm_clients[company_id] = UnifiedLLMClient(company_id)
    return _llm_clients[company_id]

"""
智能仕訳生成器
基于日本会计准则的自动分录生成系统
"""
import json
from datetime import date
from typing import Dict, List, Optional, Any, Tuple
from pydantic import BaseModel
from enum import Enum

from app.services.llm_manager import get_llm_client, TaskType
from app.core.database import get_database


class AccountCategory(str, Enum):
    """会计科目分类"""
    ASSETS = "資産"           # 资产
    LIABILITIES = "負債"      # 负债
    EQUITY = "純資産"         # 净资产
    REVENUE = "収益"          # 收益
    EXPENSES = "費用"         # 费用


class AccountSubcategory(str, Enum):
    """会计科目子分类"""
    # 资产子分类
    CURRENT_ASSETS = "流動資産"
    FIXED_ASSETS = "固定資産"
    
    # 负债子分类
    CURRENT_LIABILITIES = "流動負債"
    LONG_TERM_LIABILITIES = "固定負債"
    
    # 收益子分类
    OPERATING_REVENUE = "営業収益"
    NON_OPERATING_REVENUE = "営業外収益"
    
    # 费用子分类
    OPERATING_EXPENSES = "営業費用"
    NON_OPERATING_EXPENSES = "営業外費用"


class StandardAccount(BaseModel):
    """标准会计科目"""
    code: str
    name: str
    category: AccountCategory
    subcategory: Optional[AccountSubcategory] = None
    description: str
    is_debit_normal: bool  # 借方余额为正常余额


class JournalRule(BaseModel):
    """仕訳规则"""
    pattern: str
    debit_account: str
    credit_account: str
    description: str
    confidence: float


class JournalGenerator:
    """智能仕訳生成器"""
    
    def __init__(self, company_id: str):
        self.company_id = company_id
        self.llm_client = get_llm_client(company_id)
        
        # 日本标准会计科目
        self.standard_accounts = self._init_standard_accounts()
        
        # 常用仕訳规则
        self.journal_rules = self._init_journal_rules()
    
    def _init_standard_accounts(self) -> Dict[str, StandardAccount]:
        """初始化日本标准会计科目"""
        accounts = {
            # 資産科目
            "101": StandardAccount(
                code="101", name="現金", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.CURRENT_ASSETS,
                description="手元現金", is_debit_normal=True
            ),
            "102": StandardAccount(
                code="102", name="普通預金", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.CURRENT_ASSETS,
                description="銀行普通預金", is_debit_normal=True
            ),
            "103": StandardAccount(
                code="103", name="当座預金", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.CURRENT_ASSETS,
                description="銀行当座預金", is_debit_normal=True
            ),
            "111": StandardAccount(
                code="111", name="売掛金", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.CURRENT_ASSETS,
                description="売上債権", is_debit_normal=True
            ),
            "121": StandardAccount(
                code="121", name="商品", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.CURRENT_ASSETS,
                description="商品在庫", is_debit_normal=True
            ),
            "151": StandardAccount(
                code="151", name="建物", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.FIXED_ASSETS,
                description="建物・構築物", is_debit_normal=True
            ),
            "152": StandardAccount(
                code="152", name="機械装置", category=AccountCategory.ASSETS,
                subcategory=AccountSubcategory.FIXED_ASSETS,
                description="機械装置", is_debit_normal=True
            ),
            
            # 負債科目
            "201": StandardAccount(
                code="201", name="買掛金", category=AccountCategory.LIABILITIES,
                subcategory=AccountSubcategory.CURRENT_LIABILITIES,
                description="仕入債務", is_debit_normal=False
            ),
            "202": StandardAccount(
                code="202", name="未払金", category=AccountCategory.LIABILITIES,
                subcategory=AccountSubcategory.CURRENT_LIABILITIES,
                description="未払債務", is_debit_normal=False
            ),
            "211": StandardAccount(
                code="211", name="借入金", category=AccountCategory.LIABILITIES,
                subcategory=AccountSubcategory.LONG_TERM_LIABILITIES,
                description="銀行借入金", is_debit_normal=False
            ),
            "221": StandardAccount(
                code="221", name="預り金", category=AccountCategory.LIABILITIES,
                subcategory=AccountSubcategory.CURRENT_LIABILITIES,
                description="預り金", is_debit_normal=False
            ),
            
            # 純資産科目
            "301": StandardAccount(
                code="301", name="資本金", category=AccountCategory.EQUITY,
                description="資本金", is_debit_normal=False
            ),
            "302": StandardAccount(
                code="302", name="利益剰余金", category=AccountCategory.EQUITY,
                description="利益剰余金", is_debit_normal=False
            ),
            
            # 収益科目
            "401": StandardAccount(
                code="401", name="売上高", category=AccountCategory.REVENUE,
                subcategory=AccountSubcategory.OPERATING_REVENUE,
                description="売上収入", is_debit_normal=False
            ),
            "411": StandardAccount(
                code="411", name="受取利息", category=AccountCategory.REVENUE,
                subcategory=AccountSubcategory.NON_OPERATING_REVENUE,
                description="受取利息", is_debit_normal=False
            ),
            "421": StandardAccount(
                code="421", name="雑収入", category=AccountCategory.REVENUE,
                subcategory=AccountSubcategory.NON_OPERATING_REVENUE,
                description="その他収入", is_debit_normal=False
            ),
            
            # 費用科目
            "501": StandardAccount(
                code="501", name="仕入高", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="商品仕入", is_debit_normal=True
            ),
            "511": StandardAccount(
                code="511", name="給料手当", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="従業員給与", is_debit_normal=True
            ),
            "521": StandardAccount(
                code="521", name="地代家賃", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="賃借料", is_debit_normal=True
            ),
            "531": StandardAccount(
                code="531", name="水道光熱費", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="水道光熱費", is_debit_normal=True
            ),
            "541": StandardAccount(
                code="541", name="通信費", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="通信費", is_debit_normal=True
            ),
            "551": StandardAccount(
                code="551", name="消耗品費", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="消耗品費", is_debit_normal=True
            ),
            "561": StandardAccount(
                code="561", name="広告宣伝費", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="広告宣伝費", is_debit_normal=True
            ),
            "571": StandardAccount(
                code="571", name="旅費交通費", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="旅費交通費", is_debit_normal=True
            ),
            "581": StandardAccount(
                code="581", name="接待交際費", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.OPERATING_EXPENSES,
                description="接待交際費", is_debit_normal=True
            ),
            "591": StandardAccount(
                code="591", name="支払利息", category=AccountCategory.EXPENSES,
                subcategory=AccountSubcategory.NON_OPERATING_EXPENSES,
                description="支払利息", is_debit_normal=True
            )
        }
        
        return accounts
    
    def _init_journal_rules(self) -> List[JournalRule]:
        """初始化常用仕訳规则"""
        return [
            # 売上関連
            JournalRule(
                pattern="売上|収入|入金|代金回収",
                debit_account="普通預金",
                credit_account="売上高",
                description="売上収入",
                confidence=0.9
            ),
            JournalRule(
                pattern="現金売上|現金収入",
                debit_account="現金",
                credit_account="売上高",
                description="現金売上",
                confidence=0.95
            ),
            
            # 仕入関連
            JournalRule(
                pattern="仕入|商品購入|在庫",
                debit_account="仕入高",
                credit_account="買掛金",
                description="商品仕入",
                confidence=0.9
            ),
            
            # 経費関連
            JournalRule(
                pattern="給与|給料|賃金|ボーナス",
                debit_account="給料手当",
                credit_account="普通預金",
                description="給与支払",
                confidence=0.95
            ),
            JournalRule(
                pattern="家賃|賃料|地代",
                debit_account="地代家賃",
                credit_account="普通預金",
                description="家賃支払",
                confidence=0.95
            ),
            JournalRule(
                pattern="電気|ガス|水道|光熱",
                debit_account="水道光熱費",
                credit_account="普通預金",
                description="光熱費支払",
                confidence=0.9
            ),
            JournalRule(
                pattern="電話|インターネット|通信",
                debit_account="通信費",
                credit_account="普通預金",
                description="通信費支払",
                confidence=0.9
            ),
            JournalRule(
                pattern="文房具|事務用品|消耗品",
                debit_account="消耗品費",
                credit_account="現金",
                description="消耗品購入",
                confidence=0.85
            ),
            JournalRule(
                pattern="広告|宣伝|マーケティング",
                debit_account="広告宣伝費",
                credit_account="普通預金",
                description="広告宣伝費",
                confidence=0.9
            ),
            JournalRule(
                pattern="交通費|旅費|出張",
                debit_account="旅費交通費",
                credit_account="現金",
                description="交通費",
                confidence=0.9
            ),
            JournalRule(
                pattern="接待|懇親|会食",
                debit_account="接待交際費",
                credit_account="現金",
                description="接待交際費",
                confidence=0.85
            )
        ]
    
    async def suggest_accounts(self, description: str, amount: float) -> Tuple[str, str, float]:
        """基于描述和金额建议会计科目"""
        
        # 1. 首先尝试规则匹配
        for rule in self.journal_rules:
            if any(keyword in description for keyword in rule.pattern.split("|")):
                return rule.debit_account, rule.credit_account, rule.confidence
        
        # 2. 使用LLM进行智能分类
        accounts_list = [f"{acc.name}（{acc.category.value}）" for acc in self.standard_accounts.values()]
        
        prompt = f"""
        以下の取引内容に基づいて、最適な勘定科目を選択してください：
        
        取引内容: {description}
        金額: {amount:,.0f}円
        
        利用可能な勘定科目:
        {', '.join(accounts_list)}
        
        日本の会計基準と複式簿記の原則に従って、借方と貸方の勘定科目を選択してください。
        
        以下のJSON形式で回答してください：
        {{
            "debit_account": "借方勘定科目名",
            "credit_account": "貸方勘定科目名",
            "confidence": "0.0から1.0の信頼度",
            "reasoning": "選択理由"
        }}
        """
        
        try:
            response = await self.llm_client.generate_content(
                TaskType.COMPLEX_ACCOUNTING, prompt
            )
            
            # JSONレスポンスをパース
            import re
            json_match = re.search(r'\{.*\}', response["text"], re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(0))
                return (
                    data.get("debit_account", "消耗品費"),
                    data.get("credit_account", "現金"),
                    float(data.get("confidence", 0.7))
                )
        
        except Exception as e:
            print(f"❌ LLM科目分类失败: {e}")
        
        # 3. 默认分类
        if amount > 0:
            return "消耗品費", "現金", 0.5
        else:
            return "現金", "雑収入", 0.5
    
    async def validate_journal_entry(self, debit_account: str, credit_account: str, amount: float) -> Dict[str, Any]:
        """验证仕訳分录的正确性"""
        
        warnings = []
        suggestions = []
        
        # 检查科目是否存在
        debit_exists = any(acc.name == debit_account for acc in self.standard_accounts.values())
        credit_exists = any(acc.name == credit_account for acc in self.standard_accounts.values())
        
        if not debit_exists:
            warnings.append(f"借方科目「{debit_account}」が標準科目にありません")
        
        if not credit_exists:
            warnings.append(f"貸方科目「{credit_account}」が標準科目にありません")
        
        # 检查金额
        if amount <= 0:
            warnings.append("金額が0以下です")
        elif amount > ********:  # 1000万円以上
            warnings.append("金額が非常に大きいです。確認してください")
        
        # 检查科目组合的合理性
        if debit_account == credit_account:
            warnings.append("借方と貸方が同じ科目です")
        
        # 提供改善建议
        if len(warnings) == 0 and amount < 1000:
            suggestions.append("少額取引です。消耗品費として処理することも可能です")
        
        return {
            "is_valid": len(warnings) == 0,
            "warnings": warnings,
            "suggestions": suggestions
        }
    
    async def get_account_suggestions(self, partial_name: str) -> List[Dict[str, str]]:
        """获取科目名称建议（用于自动补全）"""
        
        suggestions = []
        partial_lower = partial_name.lower()
        
        for account in self.standard_accounts.values():
            if partial_lower in account.name.lower() or partial_lower in account.description.lower():
                suggestions.append({
                    "code": account.code,
                    "name": account.name,
                    "category": account.category.value,
                    "description": account.description
                })
        
        return suggestions[:10]  # 返回前10个建议


# 全局仕訳生成器实例
_journal_generators: Dict[str, JournalGenerator] = {}


def get_journal_generator(company_id: str) -> JournalGenerator:
    """获取公司的仕訳生成器实例"""
    if company_id not in _journal_generators:
        _journal_generators[company_id] = JournalGenerator(company_id)
    return _journal_generators[company_id]

"""
OCR图像识别处理器
支持发票、收据的自动识别和信息提取
"""
import io
import json
import re
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from PIL import Image
import pdf2image

# Google Cloud Vision API
from google.cloud import vision
from google.oauth2 import service_account

from app.services.llm_manager import get_llm_client, TaskType
from app.services.ai_bookkeeping import JournalEntry, get_ai_bookkeeping_engine
from app.core.config import settings


class InvoiceItem(BaseModel):
    """发票项目"""
    description: str = Field(..., description="商品/服务描述")
    quantity: Optional[float] = Field(None, description="数量")
    unit_price: Optional[float] = Field(None, description="单价")
    amount: float = Field(..., description="金额")


class InvoiceData(BaseModel):
    """发票结构化数据"""
    invoice_number: Optional[str] = Field(None, description="发票号码")
    date: Optional[date] = Field(None, description="发票日期")
    supplier: Optional[str] = Field(None, description="供应商名称")
    customer: Optional[str] = Field(None, description="客户名称")
    items: List[InvoiceItem] = Field(default_factory=list, description="发票项目")
    subtotal: Optional[float] = Field(None, description="小计")
    tax: Optional[float] = Field(None, description="消费税")
    total: float = Field(..., description="总金额")
    payment_terms: Optional[str] = Field(None, description="支付条件")
    confidence: float = Field(default=0.0, description="识别置信度")


class OCRProcessor:
    """OCR图像处理器"""
    
    def __init__(self, company_id: str):
        self.company_id = company_id
        self.llm_client = get_llm_client(company_id)
        self.ai_engine = get_ai_bookkeeping_engine(company_id)
        
        # 初始化Google Cloud Vision客户端
        self.vision_client = self._init_vision_client()
    
    def _init_vision_client(self) -> Optional[vision.ImageAnnotatorClient]:
        """初始化Google Cloud Vision客户端"""
        try:
            if settings.GOOGLE_APPLICATION_CREDENTIALS:
                credentials = service_account.Credentials.from_service_account_file(
                    settings.GOOGLE_APPLICATION_CREDENTIALS
                )
                return vision.ImageAnnotatorClient(credentials=credentials)
            else:
                # 使用默认凭据
                return vision.ImageAnnotatorClient()
        except Exception as e:
            print(f"⚠️ Google Cloud Vision初始化失败: {e}")
            return None
    
    async def process_image(self, image_data: bytes, filename: str = "") -> Dict[str, Any]:
        """处理图像并提取发票信息"""
        
        try:
            # 1. OCR文字识别
            ocr_text = await self._extract_text_from_image(image_data)
            
            if not ocr_text:
                return {
                    "success": False,
                    "error": "无法从图像中提取文字",
                    "ocr_text": "",
                    "structured_data": None,
                    "journal_entry": None
                }
            
            # 2. 结构化数据提取
            structured_data = await self._extract_structured_data(ocr_text)
            
            # 3. 生成仕訳
            journal_entry = await self._generate_journal_from_invoice(structured_data)
            
            return {
                "success": True,
                "ocr_text": ocr_text,
                "structured_data": structured_data.dict(),
                "journal_entry": journal_entry.dict(),
                "confidence": structured_data.confidence
            }
            
        except Exception as e:
            print(f"❌ 图像处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "ocr_text": "",
                "structured_data": None,
                "journal_entry": None
            }
    
    async def process_pdf(self, pdf_data: bytes, filename: str = "") -> List[Dict[str, Any]]:
        """处理PDF文件并提取所有页面的发票信息"""
        
        try:
            # 将PDF转换为图像
            images = pdf2image.convert_from_bytes(pdf_data)
            
            results = []
            for i, image in enumerate(images):
                # 将PIL图像转换为字节
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format='PNG')
                image_data = img_byte_arr.getvalue()
                
                # 处理每一页
                result = await self.process_image(image_data, f"{filename}_page_{i+1}")
                result["page_number"] = i + 1
                results.append(result)
            
            return results
            
        except Exception as e:
            print(f"❌ PDF处理失败: {e}")
            return [{
                "success": False,
                "error": str(e),
                "page_number": 1
            }]
    
    async def _extract_text_from_image(self, image_data: bytes) -> str:
        """使用Google Cloud Vision API提取图像中的文字"""
        
        if not self.vision_client:
            # 降级到简单的文字提取
            return await self._fallback_text_extraction(image_data)
        
        try:
            image = vision.Image(content=image_data)
            
            # 执行文字检测
            response = self.vision_client.text_detection(image=image)
            texts = response.text_annotations
            
            if texts:
                # 返回完整的文字内容
                return texts[0].description
            else:
                return ""
                
        except Exception as e:
            print(f"⚠️ Google Cloud Vision API调用失败: {e}")
            return await self._fallback_text_extraction(image_data)
    
    async def _fallback_text_extraction(self, image_data: bytes) -> str:
        """降级文字提取方法"""
        try:
            # 使用Tesseract OCR作为降级方案
            import pytesseract
            
            image = Image.open(io.BytesIO(image_data))
            text = pytesseract.image_to_string(image, lang='jpn+eng')
            return text
            
        except Exception as e:
            print(f"⚠️ 降级OCR也失败: {e}")
            return ""
    
    async def _extract_structured_data(self, ocr_text: str) -> InvoiceData:
        """从OCR文字中提取结构化数据"""
        
        prompt = f"""
        以下のOCRテキストから請求書/領収書の情報を抽出してください：
        
        {ocr_text}
        
        以下のJSON形式で回答してください：
        {{
            "invoice_number": "請求書番号（見つからない場合はnull）",
            "date": "日付（YYYY-MM-DD形式、見つからない場合はnull）",
            "supplier": "供給者名/店舗名",
            "customer": "顧客名（見つからない場合はnull）",
            "items": [
                {{
                    "description": "商品・サービス名",
                    "quantity": "数量（数値、見つからない場合はnull）",
                    "unit_price": "単価（数値、見つからない場合はnull）",
                    "amount": "金額（数値）"
                }}
            ],
            "subtotal": "小計（数値、見つからない場合はnull）",
            "tax": "消費税額（数値、見つからない場合はnull）",
            "total": "合計金額（数値）",
            "payment_terms": "支払条件（見つからない場合はnull）",
            "confidence": "0.0から1.0の信頼度"
        }}
        
        注意事項：
        - 金額は必ず数値のみで返してください（カンマや円マークは除く）
        - 日付は必ずYYYY-MM-DD形式で返してください
        - 不明な項目はnullを返してください
        - 複数の商品がある場合はitemsに配列で含めてください
        """
        
        try:
            response = await self.llm_client.generate_content(
                TaskType.OCR_PROCESSING, prompt
            )
            
            json_text = self._extract_json_from_response(response["text"])
            data = json.loads(json_text)
            
            # 日付処理
            if data.get("date"):
                try:
                    data["date"] = datetime.strptime(data["date"], "%Y-%m-%d").date()
                except (ValueError, TypeError) as e:
                    logger.warning(f"日期解析失败: {e}")
                    data["date"] = None
            
            # 項目処理
            items = []
            for item_data in data.get("items", []):
                try:
                    items.append(InvoiceItem(**item_data))
                except:
                    continue
            data["items"] = items
            
            return InvoiceData(**data)
            
        except Exception as e:
            print(f"❌ 结构化数据提取失败: {e}")
            
            # 使用正则表达式作为降级方案
            return self._extract_data_with_regex(ocr_text)
    
    def _extract_data_with_regex(self, text: str) -> InvoiceData:
        """使用正则表达式提取基本信息"""
        
        # 提取金额
        total_amount = self._extract_amount_regex(text)
        
        # 提取日期
        date_match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', text)
        invoice_date = None
        if date_match:
            try:
                year, month, day = date_match.groups()
                invoice_date = date(int(year), int(month), int(day))
            except:
                pass
        
        # 提取供应商（通常在文档顶部）
        lines = text.split('\n')
        supplier = lines[0].strip() if lines else "不明"
        
        return InvoiceData(
            date=invoice_date,
            supplier=supplier,
            total=total_amount or 0.0,
            confidence=0.3  # 正则表达式提取的置信度较低
        )
    
    async def _generate_journal_from_invoice(self, invoice_data: InvoiceData) -> JournalEntry:
        """从发票数据生成仕訳"""
        
        # 构建描述文本
        description_parts = []
        if invoice_data.supplier:
            description_parts.append(f"供給者: {invoice_data.supplier}")
        if invoice_data.items:
            items_desc = ", ".join([item.description for item in invoice_data.items[:3]])
            description_parts.append(f"内容: {items_desc}")
        if invoice_data.invoice_number:
            description_parts.append(f"請求書番号: {invoice_data.invoice_number}")
        
        description = " / ".join(description_parts) or "請求書処理"
        
        # 使用AI引擎生成仕訳
        journal_entry = await self.ai_engine.process_natural_language(
            f"請求書の支払い {invoice_data.total}円 {description}"
        )
        
        # 更新日期和金额
        if invoice_data.date:
            journal_entry.date = invoice_data.date
        journal_entry.amount = invoice_data.total
        journal_entry.description = description
        
        # 调整置信度
        journal_entry.confidence = min(journal_entry.confidence, invoice_data.confidence)
        
        return journal_entry
    
    def _extract_json_from_response(self, response_text: str) -> str:
        """从LLM响应中提取JSON"""
        # 查找JSON代码块
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            return json_match.group(1)
        
        # 查找花括号包围的JSON
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            return json_match.group(0)
        
        return response_text.strip()
    
    def _extract_amount_regex(self, text: str) -> Optional[float]:
        """使用正则表达式提取金额"""
        patterns = [
            r'合計[：:\s]*(\d{1,3}(?:,\d{3})*(?:\.\d+)?)円',
            r'総額[：:\s]*(\d{1,3}(?:,\d{3})*(?:\.\d+)?)円',
            r'(\d{1,3}(?:,\d{3})*(?:\.\d+)?)円',
            r'¥(\d{1,3}(?:,\d{3})*(?:\.\d+)?)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    # 取最大的金额作为总额
                    amounts = [float(match.replace(',', '')) for match in matches]
                    return max(amounts)
                except:
                    continue
        
        return None


# 全局OCR处理器实例
_ocr_processors: Dict[str, OCRProcessor] = {}


def get_ocr_processor(company_id: str) -> OCRProcessor:
    """获取公司的OCR处理器实例"""
    if company_id not in _ocr_processors:
        _ocr_processors[company_id] = OCRProcessor(company_id)
    return _ocr_processors[company_id]

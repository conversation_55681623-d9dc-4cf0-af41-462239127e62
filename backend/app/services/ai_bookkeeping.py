"""
AI全自动记账核心引擎
支持自然语言理解和自动仕訳生成
"""
import json
import re
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

from app.services.llm_manager import get_llm_client, TaskType
from app.core.database import get_database


class TransactionInfo(BaseModel):
    """交易信息模型"""
    date: Optional[date] = Field(None, description="交易日期")
    amount: Optional[float] = Field(None, description="金额")
    description: str = Field(..., description="交易描述")
    counterparty: Optional[str] = Field(None, description="交易对方")
    payment_method: Optional[str] = Field(None, description="支付方式")
    transaction_type: Optional[str] = Field(None, description="收入/支出")
    category: Optional[str] = Field(None, description="交易类别")


class AccountClassification(BaseModel):
    """会计科目分类结果"""
    debit_account: str = Field(..., description="借方科目")
    credit_account: str = Field(..., description="贷方科目")
    reasoning: str = Field(..., description="分类理由")
    confidence: float = Field(..., description="置信度")


class JournalEntry(BaseModel):
    """仕訳分录"""
    date: date
    description: str
    debit_account: str
    credit_account: str
    amount: float
    reference_number: Optional[str] = None
    confidence: float = 1.0
    warnings: List[str] = []
    suggestions: List[str] = []


class AIBookkeepingEngine:
    """AI记账核心引擎"""
    
    def __init__(self, company_id: str):
        self.company_id = company_id
        self.llm_client = get_llm_client(company_id)
        
        # 日本标准会计科目
        self.standard_accounts = {
            "資産": ["現金", "普通預金", "当座預金", "売掛金", "商品", "建物", "機械装置"],
            "負債": ["買掛金", "借入金", "未払金", "預り金"],
            "純資産": ["資本金", "利益剰余金"],
            "収益": ["売上高", "受取利息", "雑収入"],
            "費用": ["仕入高", "給料手当", "地代家賃", "水道光熱費", "通信費", "消耗品費", "広告宣伝費"]
        }
    
    async def process_natural_language(self, user_input: str) -> JournalEntry:
        """处理自然语言输入的核心方法"""
        
        # 1. 语义理解和信息提取
        transaction_info = await self._extract_transaction_info(user_input)
        
        # 2. 智能科目分类
        account_classification = await self._classify_accounts(transaction_info)
        
        # 3. 生成仕訳
        journal_entry = await self._generate_journal_entry(
            transaction_info, account_classification
        )
        
        # 4. 质量检查
        validated_entry = await self._validate_entry(journal_entry)
        
        return validated_entry
    
    async def _extract_transaction_info(self, user_input: str) -> TransactionInfo:
        """使用LLM提取交易信息"""
        
        prompt = f"""
        以下の日本語文章から取引情報を抽出してください：
        "{user_input}"
        
        以下のJSON形式で回答してください：
        {{
            "date": "取引日（YYYY-MM-DD形式、明記されていない場合は今日の日付）",
            "amount": "金額（数値のみ、カンマや円マークは除く）",
            "description": "取引内容の要約",
            "counterparty": "取引相手（会社名や個人名）",
            "payment_method": "支払方法（現金、銀行振込、クレジットカードなど）",
            "transaction_type": "収入または支出",
            "category": "推定される取引カテゴリ（売上、仕入、経費など）"
        }}
        
        注意事項：
        - 金額は必ず数値のみで返してください
        - 日付が明記されていない場合は今日の日付を使用
        - 不明な項目はnullを返してください
        """
        
        try:
            response = await self.llm_client.generate_content(
                TaskType.NATURAL_LANGUAGE, prompt
            )
            
            # JSONレスポンスをパース
            json_text = self._extract_json_from_response(response["text"])
            data = json.loads(json_text)
            
            # 日付処理
            if data.get("date"):
                try:
                    data["date"] = datetime.strptime(data["date"], "%Y-%m-%d").date()
                except (ValueError, TypeError) as e:
                    logger.warning(f"日期解析失败，使用今天日期: {e}")
                    data["date"] = date.today()
            else:
                data["date"] = date.today()
            
            return TransactionInfo(**data)
            
        except Exception as e:
            print(f"❌ 交易信息提取失败: {e}")
            # 返回基础信息
            return TransactionInfo(
                date=date.today(),
                description=user_input,
                amount=self._extract_amount_regex(user_input)
            )
    
    async def _classify_accounts(self, transaction_info: TransactionInfo) -> AccountClassification:
        """智能会计科目分类"""
        
        # 构建科目列表
        accounts_list = []
        for category, accounts in self.standard_accounts.items():
            accounts_list.extend([f"{account}（{category}）" for account in accounts])
        
        prompt = f"""
        以下の取引情報に基づいて、適切な勘定科目を選択してください：
        
        取引情報：
        - 日付: {transaction_info.date}
        - 金額: {transaction_info.amount}円
        - 内容: {transaction_info.description}
        - 種類: {transaction_info.transaction_type}
        - カテゴリ: {transaction_info.category}
        - 取引相手: {transaction_info.counterparty}
        - 支払方法: {transaction_info.payment_method}
        
        利用可能な勘定科目：
        {', '.join(accounts_list)}
        
        日本の会計基準に従って、借方と貸方の勘定科目を選択してください。
        複式簿記の原則に従い、借方と貸方の金額は必ず一致させてください。
        
        以下のJSON形式で回答してください：
        {{
            "debit_account": "借方勘定科目名（カテゴリ表記なし）",
            "credit_account": "貸方勘定科目名（カテゴリ表記なし）",
            "reasoning": "この科目を選択した理由",
            "confidence": "0.0から1.0の信頼度"
        }}
        """
        
        try:
            response = await self.llm_client.generate_content(
                TaskType.COMPLEX_ACCOUNTING, prompt
            )
            
            json_text = self._extract_json_from_response(response["text"])
            data = json.loads(json_text)
            
            return AccountClassification(**data)
            
        except Exception as e:
            print(f"❌ 科目分类失败: {e}")
            # 返回默认分类
            if transaction_info.transaction_type == "収入":
                return AccountClassification(
                    debit_account="普通預金",
                    credit_account="売上高",
                    reasoning="デフォルト分類",
                    confidence=0.5
                )
            else:
                return AccountClassification(
                    debit_account="消耗品費",
                    credit_account="現金",
                    reasoning="デフォルト分類",
                    confidence=0.5
                )
    
    async def _generate_journal_entry(
        self, 
        transaction_info: TransactionInfo, 
        classification: AccountClassification
    ) -> JournalEntry:
        """生成仕訳分录"""
        
        return JournalEntry(
            date=transaction_info.date or date.today(),
            description=transaction_info.description,
            debit_account=classification.debit_account,
            credit_account=classification.credit_account,
            amount=transaction_info.amount or 0.0,
            confidence=classification.confidence
        )
    
    async def _validate_entry(self, entry: JournalEntry) -> JournalEntry:
        """验证和质量检查仕訳"""
        warnings = []
        suggestions = []
        
        # 基本验证
        if entry.amount <= 0:
            warnings.append("金額が0以下です。正しい金額を確認してください。")
        
        if entry.amount > 1000000:  # 100万円以上
            warnings.append("金額が通常より大きいです。確認してください。")
        
        # 科目验证
        all_accounts = []
        for accounts in self.standard_accounts.values():
            all_accounts.extend(accounts)
        
        if entry.debit_account not in all_accounts:
            warnings.append(f"借方科目「{entry.debit_account}」が標準科目にありません。")
        
        if entry.credit_account not in all_accounts:
            warnings.append(f"貸方科目「{entry.credit_account}」が標準科目にありません。")
        
        # 信頼度チェック
        if entry.confidence < 0.7:
            suggestions.append("AI分類の信頼度が低いです。内容を確認してください。")
        
        # 日付チェック
        if entry.date > date.today():
            warnings.append("未来の日付が設定されています。")
        
        entry.warnings = warnings
        entry.suggestions = suggestions
        
        return entry
    
    def _extract_json_from_response(self, response_text: str) -> str:
        """从LLM响应中提取JSON"""
        # 查找JSON代码块
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            return json_match.group(1)
        
        # 查找花括号包围的JSON
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            return json_match.group(0)
        
        return response_text.strip()
    
    def _extract_amount_regex(self, text: str) -> Optional[float]:
        """使用正则表达式提取金额"""
        # 匹配日文金额格式
        patterns = [
            r'(\d{1,3}(?:,\d{3})*(?:\.\d+)?)円',
            r'(\d{1,3}(?:,\d{3})*(?:\.\d+)?)万円',
            r'(\d+(?:\.\d+)?)円',
            r'(\d+(?:,\d{3})*)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                amount_str = match.group(1).replace(',', '')
                try:
                    amount = float(amount_str)
                    if '万円' in match.group(0):
                        amount *= 10000
                    return amount
                except (ValueError, KeyError, TypeError) as e:
                    logger.warning(f"处理项目时出错，跳过: {e}")
                    continue
        
        return None


# 全局AI记账引擎实例
_ai_engines: Dict[str, AIBookkeepingEngine] = {}


def get_ai_bookkeeping_engine(company_id: str) -> AIBookkeepingEngine:
    """获取公司的AI记账引擎实例"""
    if company_id not in _ai_engines:
        _ai_engines[company_id] = AIBookkeepingEngine(company_id)
    return _ai_engines[company_id]

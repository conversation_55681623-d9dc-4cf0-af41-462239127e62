#!/usr/bin/env python3
"""
日期时间解析模块
将自然语言中的时间代词转换为具体的日期时间
"""
import re
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import jieba
import jieba.posseg as pseg

class DateTimeParser:
    """日期时间解析器"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
        # 时间代词映射
        self.time_keywords = {
            # 中文时间代词
            '今天': 0,
            '今日': 0,
            '当天': 0,
            '本日': 0,
            '昨天': -1,
            '昨日': -1,
            '前天': -2,
            '前日': -2,
            '大前天': -3,
            '明天': 1,
            '明日': 1,
            '后天': 2,
            '后日': 2,
            '大后天': 3,
            
            # 日语时间代词
            '今日': 0,
            '昨日': -1,
            '明日': 1,
            '一昨日': -2,
            '明後日': 2,
            
            # 英语时间代词
            'today': 0,
            'yesterday': -1,
            'tomorrow': 1,
            
            # 相对时间
            '上周': -7,
            '上週': -7,
            '先週': -7,
            '下周': 7,
            '下週': 7,
            '来週': 7,
            '下个月': 30,
            '来月': 30,
            '上个月': -30,
            '先月': -30,
        }
        
        # 时间点关键词
        self.time_points = {
            '早上': '08:00:00',
            '上午': '10:00:00',
            '中午': '12:00:00',
            '下午': '14:00:00',
            '傍晚': '18:00:00',
            '晚上': '20:00:00',
            '深夜': '23:00:00',
            '凌晨': '02:00:00',
            
            # 日语时间点
            '朝': '08:00:00',
            '午前': '10:00:00',
            '正午': '12:00:00',
            '午後': '14:00:00',
            '夕方': '18:00:00',
            '夜': '20:00:00',
            '深夜': '23:00:00',
            '明け方': '05:00:00',
        }
        
        # 数字映射
        self.number_map = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '零': 0, '〇': 0,
            '１': 1, '２': 2, '３': 3, '４': 4, '５': 5,
            '６': 6, '７': 7, '８': 8, '９': 9, '０': 0,
        }
    
    def parse_datetime_from_text(self, text: str) -> Dict[str, Optional[str]]:
        """
        从文本中解析日期时间信息
        
        Args:
            text: 输入文本
            
        Returns:
            包含解析结果的字典：
            {
                'entry_date': '2025-07-09',
                'entry_time': '14:30:00',
                'entry_datetime': '2025-07-09T14:30:00',
                'original_text': '原始文本',
                'parsed_text': '解析后的文本'
            }
        """
        now = datetime.now()
        result = {
            'entry_date': now.strftime('%Y-%m-%d'),
            'entry_time': now.strftime('%H:%M:%S'),
            'entry_datetime': now.isoformat(),
            'original_text': text,
            'parsed_text': text
        }
        
        # 解析相对日期
        parsed_date = self._parse_relative_date(text, now)
        if parsed_date:
            result['entry_date'] = parsed_date.strftime('%Y-%m-%d')
            result['entry_datetime'] = f"{result['entry_date']}T{result['entry_time']}"
        
        # 解析时间点
        parsed_time = self._parse_time_point(text)
        if parsed_time:
            result['entry_time'] = parsed_time
            result['entry_datetime'] = f"{result['entry_date']}T{parsed_time}"
        
        # 解析具体时间
        specific_time = self._parse_specific_time(text)
        if specific_time:
            result['entry_time'] = specific_time
            result['entry_datetime'] = f"{result['entry_date']}T{specific_time}"
        
        # 解析具体日期
        specific_date = self._parse_specific_date(text)
        if specific_date:
            result['entry_date'] = specific_date.strftime('%Y-%m-%d')
            result['entry_datetime'] = f"{result['entry_date']}T{result['entry_time']}"
        
        # 生成解析后的文本（替换时间代词）
        result['parsed_text'] = self._replace_time_keywords(text, result)
        
        return result
    
    def _parse_relative_date(self, text: str, base_date: datetime) -> Optional[datetime]:
        """解析相对日期"""
        for keyword, days_offset in self.time_keywords.items():
            if keyword in text:
                if abs(days_offset) <= 7:  # 只处理一周内的相对日期
                    return base_date + timedelta(days=days_offset)
        return None
    
    def _parse_time_point(self, text: str) -> Optional[str]:
        """解析时间点"""
        for keyword, time_str in self.time_points.items():
            if keyword in text:
                return time_str
        return None
    
    def _parse_specific_time(self, text: str) -> Optional[str]:
        """解析具体时间（如：3点、15:30、下午2点）"""
        # 匹配 HH:MM 格式
        time_pattern = r'(\d{1,2}):(\d{2})'
        match = re.search(time_pattern, text)
        if match:
            hour, minute = match.groups()
            return f"{int(hour):02d}:{minute}:00"
        
        # 匹配中文时间（如：3点、15点30分）
        chinese_time_patterns = [
            r'(\d{1,2})点(\d{1,2})分',
            r'(\d{1,2})時(\d{1,2})分',
            r'(\d{1,2})点',
            r'(\d{1,2})時',
        ]
        
        for pattern in chinese_time_patterns:
            match = re.search(pattern, text)
            if match:
                groups = match.groups()
                hour = int(groups[0])
                minute = int(groups[1]) if len(groups) > 1 else 0
                
                # 处理12小时制
                if '下午' in text or '午後' in text:
                    if hour < 12:
                        hour += 12
                elif '上午' in text or '午前' in text:
                    if hour == 12:
                        hour = 0
                
                return f"{hour:02d}:{minute:02d}:00"
        
        return None
    
    def _parse_specific_date(self, text: str) -> Optional[datetime]:
        """解析具体日期"""
        # 匹配 YYYY-MM-DD 格式
        date_pattern = r'(\d{4})-(\d{1,2})-(\d{1,2})'
        match = re.search(date_pattern, text)
        if match:
            year, month, day = match.groups()
            try:
                return datetime(int(year), int(month), int(day))
            except ValueError:
                pass
        
        # 匹配 MM月DD日 格式
        chinese_date_pattern = r'(\d{1,2})月(\d{1,2})日'
        match = re.search(chinese_date_pattern, text)
        if match:
            month, day = match.groups()
            try:
                current_year = datetime.now().year
                return datetime(current_year, int(month), int(day))
            except ValueError:
                pass
        
        return None
    
    def _replace_time_keywords(self, text: str, parsed_result: Dict) -> str:
        """替换文本中的时间代词为具体日期时间"""
        result_text = text
        
        # 替换日期代词
        for keyword in self.time_keywords.keys():
            if keyword in result_text:
                # 获取对应的具体日期
                date_str = parsed_result['entry_date']
                # 将日期转换为更友好的格式
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                friendly_date = date_obj.strftime('%Y年%m月%d日')
                result_text = result_text.replace(keyword, friendly_date)
        
        # 替换时间点代词
        for keyword in self.time_points.keys():
            if keyword in result_text:
                time_str = parsed_result['entry_time']
                # 将时间转换为更友好的格式
                time_obj = datetime.strptime(time_str, '%H:%M:%S')
                friendly_time = time_obj.strftime('%H:%M')
                result_text = result_text.replace(keyword, friendly_time)
        
        return result_text
    
    def extract_amount_and_description(self, text: str) -> Tuple[Optional[float], str]:
        """提取金额和描述"""
        # 金额模式
        amount_patterns = [
            r'(\d+(?:\.\d+)?)円',
            r'(\d+(?:\.\d+)?)元',
            r'(\d+(?:\.\d+)?)块',
            r'(\d+(?:\.\d+)?)¥',
            r'¥(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)万円',
            r'(\d+(?:\.\d+)?)万元',
        ]
        
        amount = None
        description = text
        
        for pattern in amount_patterns:
            match = re.search(pattern, text)
            if match:
                amount_str = match.group(1)
                amount = float(amount_str)
                
                # 处理万的单位
                if '万' in match.group(0):
                    amount *= 10000
                
                # 从描述中移除金额部分
                description = re.sub(pattern, '', text).strip()
                break
        
        return amount, description

# 全局解析器实例
datetime_parser = DateTimeParser()

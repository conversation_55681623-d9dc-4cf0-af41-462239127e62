import os
import httpx  # 使用httpx进行异步请求
import jwt
import asyncio
from fastapi import APIRouter, Depends, HTTPException, Header
from pydantic import BaseModel
from typing import Optional

# --- Models ---
class PaymentVerification(BaseModel):
    orderID: str
    planId: str

# --- Router ---
router = APIRouter()

# --- Cloudflare D1 数据库配置 ---
CLOUDFLARE_ACCOUNT_ID = os.environ.get("CF_ACCOUNT_ID")
CLOUDFLARE_API_TOKEN = os.environ.get("CF_API_TOKEN")
CLOUDFLARE_D1_DATABASE_ID = os.environ.get("CF_D1_DATABASE_ID")
JWT_SECRET = os.environ.get("JWT_SECRET", "your-super-secret-key-for-testing")

async def get_current_user_id(authorization: Optional[str] = Header(None)) -> int:
    """从Authorization头中解析并验证JWT，返回用户ID"""
    if authorization is None:
        raise HTTPException(status_code=401, detail="Authorization header is missing")
    
    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        raise HTTPException(status_code=401, detail="Invalid authorization format")
        
    token = parts[1]
    try:
        decoded_token = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        user_id = decoded_token.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token: user_id missing")
        return user_id
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

async def update_user_plan_in_d1(user_id: int, new_plan_id: str):
    """通过 Cloudflare API 异步更新 D1 数据库中的用户套餐"""
    if not all([CLOUDFLARE_ACCOUNT_ID, CLOUDFLARE_API_TOKEN, CLOUDFLARE_D1_DATABASE_ID]):
        print("Error: Cloudflare API credentials are not set.")
        raise HTTPException(status_code=500, detail="Server configuration error")

    api_url = f"https://api.cloudflare.com/client/v4/accounts/{CLOUDFLARE_ACCOUNT_ID}/d1/database/{CLOUDFLARE_D1_DATABASE_ID}/query"
    headers = {"Authorization": f"Bearer {CLOUDFLARE_API_TOKEN}", "Content-Type": "application/json"}
    query = {"sql": "UPDATE users SET plan_id = ? WHERE id = ?", "params": [new_plan_id, user_id]}

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(api_url, headers=headers, json=query)
            response.raise_for_status()
        
        result = response.json()
        print("Cloudflare D1 API Response:", result)

        if not result.get("success") or not result.get("result"):
            error_message = result.get("errors", [{}])[0].get("message", "Unknown D1 error")
            raise HTTPException(status_code=500, detail=f"D1 query failed: {error_message}")

        if result["result"][0].get("meta", {}).get("changes", 0) == 0:
            raise HTTPException(status_code=404, detail="User not found or plan already set")
            
        return {"message": f"Successfully updated plan for user {user_id} to {new_plan_id}."}

    except httpx.HTTPStatusError as e:
        print(f"Error calling Cloudflare API: {e.response.text}")
        raise HTTPException(status_code=e.response.status_code, detail="Failed to communicate with database service.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected server error occurred: {e}")


@router.post("/verify-payment", tags=["Payment"])
async def verify_payment(
    payment: PaymentVerification,
    user_id: int = Depends(get_current_user_id)
):
    """
    接收PayPal支付凭证，验证并更新用户套餐。
    """
    print(f"Received payment verification for user {user_id}:")
    print(f"  Order ID: {payment.orderID}")
    print(f"  Plan ID: {payment.planId}")
    
    # TODO: 在此实现真实的PayPal API验证逻辑
    print("Simulating successful PayPal API verification.")
    
    # 更新数据库
    update_result = await update_user_plan_in_d1(user_id, payment.planId)
    
    return {"success": True, **update_result}


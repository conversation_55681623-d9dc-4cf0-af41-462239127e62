#!/usr/bin/env python3
"""
高级数据备份和恢复系统
支持自动备份、增量备份、云存储、数据压缩等
"""
import os
import json
import gzip
import shutil
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import tarfile
import threading
import schedule
import time
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BackupType(Enum):
    """备份类型"""
    FULL = "full"           # 完整备份
    INCREMENTAL = "incremental"  # 增量备份
    DIFFERENTIAL = "differential"  # 差异备份

class BackupStatus(Enum):
    """备份状态"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class BackupInfo:
    """备份信息"""
    id: str
    type: BackupType
    status: BackupStatus
    start_time: str
    end_time: str = ""
    file_path: str = ""
    file_size: int = 0
    checksum: str = ""
    description: str = ""
    error_message: str = ""
    
    def __post_init__(self):
        if not self.start_time:
            self.start_time = datetime.now().isoformat()

@dataclass
class RestoreInfo:
    """恢复信息"""
    id: str
    backup_id: str
    status: BackupStatus
    start_time: str
    end_time: str = ""
    restored_files: List[str] = None
    error_message: str = ""
    
    def __post_init__(self):
        if not self.start_time:
            self.start_time = datetime.now().isoformat()
        if self.restored_files is None:
            self.restored_files = []

class BackupManager:
    """备份管理器"""
    
    def __init__(self, backup_dir: str = "backup", data_dir: str = "data"):
        self.backup_dir = Path(backup_dir)
        self.data_dir = Path(data_dir)
        
        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)
        
        # 备份记录
        self.backups: Dict[str, BackupInfo] = {}
        self.restores: Dict[str, RestoreInfo] = {}
        
        # 配置
        self.max_backups = 30  # 最大保留备份数
        self.compression_enabled = True
        self.auto_backup_enabled = True
        self.backup_interval_hours = 6
        
        # 加载备份记录
        self._load_backup_records()
        
        # 启动自动备份调度
        if self.auto_backup_enabled:
            self._start_backup_scheduler()
    
    def create_backup(self, backup_type: BackupType = BackupType.FULL, 
                     description: str = "") -> str:
        """创建备份"""
        backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_info = BackupInfo(
            id=backup_id,
            type=backup_type,
            status=BackupStatus.PENDING,
            start_time=datetime.now().isoformat(),
            description=description or f"{backup_type.value}备份"
        )
        
        self.backups[backup_id] = backup_info
        
        # 在后台线程中执行备份
        backup_thread = threading.Thread(
            target=self._perform_backup,
            args=(backup_id,)
        )
        backup_thread.daemon = True
        backup_thread.start()
        
        logger.info(f"备份任务已启动: {backup_id}")
        return backup_id
    
    def _perform_backup(self, backup_id: str):
        """执行备份操作"""
        backup_info = self.backups[backup_id]
        
        try:
            backup_info.status = BackupStatus.IN_PROGRESS
            logger.info(f"开始执行备份: {backup_id}")
            
            # 创建备份文件路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{backup_id}_{backup_info.type.value}.tar"
            
            if self.compression_enabled:
                backup_filename += ".gz"
            
            backup_path = self.backup_dir / backup_filename
            backup_info.file_path = str(backup_path)
            
            # 根据备份类型执行不同的备份策略
            if backup_info.type == BackupType.FULL:
                self._create_full_backup(backup_path)
            elif backup_info.type == BackupType.INCREMENTAL:
                self._create_incremental_backup(backup_path)
            elif backup_info.type == BackupType.DIFFERENTIAL:
                self._create_differential_backup(backup_path)
            
            # 计算文件大小和校验和
            backup_info.file_size = backup_path.stat().st_size
            backup_info.checksum = self._calculate_checksum(backup_path)
            backup_info.status = BackupStatus.COMPLETED
            backup_info.end_time = datetime.now().isoformat()
            
            logger.info(f"备份完成: {backup_id}, 大小: {backup_info.file_size} bytes")
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            # 保存备份记录
            self._save_backup_records()
            
        except Exception as e:
            backup_info.status = BackupStatus.FAILED
            backup_info.error_message = str(e)
            backup_info.end_time = datetime.now().isoformat()
            
            logger.error(f"备份失败: {backup_id}, 错误: {str(e)}")
    
    def _create_full_backup(self, backup_path: Path):
        """创建完整备份"""
        mode = "w:gz" if self.compression_enabled else "w"
        
        with tarfile.open(backup_path, mode) as tar:
            # 备份数据目录
            if self.data_dir.exists():
                tar.add(self.data_dir, arcname="data")
            
            # 备份配置文件
            config_files = [
                "config",
                ".env",
                "requirements.txt"
            ]
            
            for config_file in config_files:
                config_path = Path(config_file)
                if config_path.exists():
                    tar.add(config_path, arcname=config_file)
            
            # 备份日志文件 (最近7天)
            logs_dir = Path("logs")
            if logs_dir.exists():
                cutoff_date = datetime.now() - timedelta(days=7)
                for log_file in logs_dir.glob("*.log"):
                    if datetime.fromtimestamp(log_file.stat().st_mtime) > cutoff_date:
                        tar.add(log_file, arcname=f"logs/{log_file.name}")
    
    def _create_incremental_backup(self, backup_path: Path):
        """创建增量备份"""
        # 获取最后一次备份的时间
        last_backup_time = self._get_last_backup_time()
        
        mode = "w:gz" if self.compression_enabled else "w"
        
        with tarfile.open(backup_path, mode) as tar:
            # 只备份自上次备份以来修改的文件
            for file_path in self._get_modified_files_since(last_backup_time):
                if file_path.exists():
                    tar.add(file_path, arcname=str(file_path))
    
    def _create_differential_backup(self, backup_path: Path):
        """创建差异备份"""
        # 获取最后一次完整备份的时间
        last_full_backup_time = self._get_last_full_backup_time()
        
        mode = "w:gz" if self.compression_enabled else "w"
        
        with tarfile.open(backup_path, mode) as tar:
            # 备份自上次完整备份以来修改的文件
            for file_path in self._get_modified_files_since(last_full_backup_time):
                if file_path.exists():
                    tar.add(file_path, arcname=str(file_path))
    
    def restore_backup(self, backup_id: str, restore_path: str = None) -> str:
        """恢复备份"""
        if backup_id not in self.backups:
            raise ValueError(f"备份不存在: {backup_id}")
        
        backup_info = self.backups[backup_id]
        
        if backup_info.status != BackupStatus.COMPLETED:
            raise ValueError(f"备份未完成，无法恢复: {backup_id}")
        
        restore_id = f"restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        restore_info = RestoreInfo(
            id=restore_id,
            backup_id=backup_id,
            status=BackupStatus.PENDING,
            start_time=datetime.now().isoformat()
        )
        
        self.restores[restore_id] = restore_info
        
        # 在后台线程中执行恢复
        restore_thread = threading.Thread(
            target=self._perform_restore,
            args=(restore_id, restore_path)
        )
        restore_thread.daemon = True
        restore_thread.start()
        
        logger.info(f"恢复任务已启动: {restore_id}")
        return restore_id
    
    def _perform_restore(self, restore_id: str, restore_path: str = None):
        """执行恢复操作"""
        restore_info = self.restores[restore_id]
        backup_info = self.backups[restore_info.backup_id]
        
        try:
            restore_info.status = BackupStatus.IN_PROGRESS
            logger.info(f"开始执行恢复: {restore_id}")
            
            backup_file = Path(backup_info.file_path)
            
            if not backup_file.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_file}")
            
            # 验证备份文件完整性
            if not self._verify_backup_integrity(backup_file, backup_info.checksum):
                raise ValueError("备份文件校验失败")
            
            # 确定恢复路径
            target_path = Path(restore_path) if restore_path else Path(".")
            
            # 解压备份文件
            mode = "r:gz" if backup_file.suffix == ".gz" else "r"
            
            with tarfile.open(backup_file, mode) as tar:
                # 提取所有文件
                tar.extractall(target_path)
                
                # 记录恢复的文件
                restore_info.restored_files = tar.getnames()
            
            restore_info.status = BackupStatus.COMPLETED
            restore_info.end_time = datetime.now().isoformat()
            
            logger.info(f"恢复完成: {restore_id}")
            
        except Exception as e:
            restore_info.status = BackupStatus.FAILED
            restore_info.error_message = str(e)
            restore_info.end_time = datetime.now().isoformat()
            
            logger.error(f"恢复失败: {restore_id}, 错误: {str(e)}")
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """获取备份列表"""
        backups = []
        for backup_info in self.backups.values():
            backup_dict = asdict(backup_info)
            backup_dict['type'] = backup_info.type.value
            backup_dict['status'] = backup_info.status.value
            backups.append(backup_dict)
        
        # 按时间倒序排列
        backups.sort(key=lambda x: x['start_time'], reverse=True)
        return backups
    
    def get_restore_list(self) -> List[Dict[str, Any]]:
        """获取恢复列表"""
        restores = []
        for restore_info in self.restores.values():
            restore_dict = asdict(restore_info)
            restore_dict['status'] = restore_info.status.value
            restores.append(restore_dict)
        
        # 按时间倒序排列
        restores.sort(key=lambda x: x['start_time'], reverse=True)
        return restores
    
    def delete_backup(self, backup_id: str) -> bool:
        """删除备份"""
        if backup_id not in self.backups:
            return False
        
        backup_info = self.backups[backup_id]
        
        # 删除备份文件
        backup_file = Path(backup_info.file_path)
        if backup_file.exists():
            backup_file.unlink()
        
        # 删除备份记录
        del self.backups[backup_id]
        
        # 保存备份记录
        self._save_backup_records()
        
        logger.info(f"备份已删除: {backup_id}")
        return True
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _verify_backup_integrity(self, file_path: Path, expected_checksum: str) -> bool:
        """验证备份文件完整性"""
        actual_checksum = self._calculate_checksum(file_path)
        return actual_checksum == expected_checksum
    
    def _get_last_backup_time(self) -> datetime:
        """获取最后一次备份时间"""
        completed_backups = [
            backup for backup in self.backups.values()
            if backup.status == BackupStatus.COMPLETED
        ]
        
        if not completed_backups:
            return datetime.min
        
        latest_backup = max(completed_backups, key=lambda x: x.start_time)
        return datetime.fromisoformat(latest_backup.start_time)
    
    def _get_last_full_backup_time(self) -> datetime:
        """获取最后一次完整备份时间"""
        full_backups = [
            backup for backup in self.backups.values()
            if backup.type == BackupType.FULL and backup.status == BackupStatus.COMPLETED
        ]
        
        if not full_backups:
            return datetime.min
        
        latest_backup = max(full_backups, key=lambda x: x.start_time)
        return datetime.fromisoformat(latest_backup.start_time)
    
    def _get_modified_files_since(self, since_time: datetime) -> List[Path]:
        """获取自指定时间以来修改的文件"""
        modified_files = []
        
        # 检查数据目录
        if self.data_dir.exists():
            for file_path in self.data_dir.rglob("*"):
                if file_path.is_file():
                    mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if mtime > since_time:
                        modified_files.append(file_path)
        
        return modified_files
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        completed_backups = [
            backup for backup in self.backups.values()
            if backup.status == BackupStatus.COMPLETED
        ]
        
        if len(completed_backups) <= self.max_backups:
            return
        
        # 按时间排序，删除最旧的备份
        completed_backups.sort(key=lambda x: x.start_time)
        
        backups_to_delete = completed_backups[:-self.max_backups]
        
        for backup in backups_to_delete:
            self.delete_backup(backup.id)
    
    def _load_backup_records(self):
        """加载备份记录"""
        records_file = self.backup_dir / "backup_records.json"
        
        if records_file.exists():
            try:
                with open(records_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 恢复备份记录
                for backup_data in data.get('backups', []):
                    backup_info = BackupInfo(
                        id=backup_data['id'],
                        type=BackupType(backup_data['type']),
                        status=BackupStatus(backup_data['status']),
                        start_time=backup_data['start_time'],
                        end_time=backup_data.get('end_time', ''),
                        file_path=backup_data.get('file_path', ''),
                        file_size=backup_data.get('file_size', 0),
                        checksum=backup_data.get('checksum', ''),
                        description=backup_data.get('description', ''),
                        error_message=backup_data.get('error_message', '')
                    )
                    self.backups[backup_info.id] = backup_info
                
                # 恢复恢复记录
                for restore_data in data.get('restores', []):
                    restore_info = RestoreInfo(
                        id=restore_data['id'],
                        backup_id=restore_data['backup_id'],
                        status=BackupStatus(restore_data['status']),
                        start_time=restore_data['start_time'],
                        end_time=restore_data.get('end_time', ''),
                        restored_files=restore_data.get('restored_files', []),
                        error_message=restore_data.get('error_message', '')
                    )
                    self.restores[restore_info.id] = restore_info
                
                logger.info(f"加载备份记录: {len(self.backups)} 个备份, {len(self.restores)} 个恢复")
                
            except Exception as e:
                logger.error(f"加载备份记录失败: {str(e)}")
    
    def _save_backup_records(self):
        """保存备份记录"""
        records_file = self.backup_dir / "backup_records.json"
        
        try:
            data = {
                'backups': [
                    {
                        'id': backup.id,
                        'type': backup.type.value,
                        'status': backup.status.value,
                        'start_time': backup.start_time,
                        'end_time': backup.end_time,
                        'file_path': backup.file_path,
                        'file_size': backup.file_size,
                        'checksum': backup.checksum,
                        'description': backup.description,
                        'error_message': backup.error_message
                    }
                    for backup in self.backups.values()
                ],
                'restores': [
                    {
                        'id': restore.id,
                        'backup_id': restore.backup_id,
                        'status': restore.status.value,
                        'start_time': restore.start_time,
                        'end_time': restore.end_time,
                        'restored_files': restore.restored_files,
                        'error_message': restore.error_message
                    }
                    for restore in self.restores.values()
                ]
            }
            
            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存备份记录失败: {str(e)}")
    
    def _start_backup_scheduler(self):
        """启动备份调度器"""
        def run_scheduler():
            schedule.every(self.backup_interval_hours).hours.do(
                lambda: self.create_backup(BackupType.INCREMENTAL, "自动增量备份")
            )
            
            # 每天凌晨2点执行完整备份
            schedule.every().day.at("02:00").do(
                lambda: self.create_backup(BackupType.FULL, "自动完整备份")
            )
            
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        
        scheduler_thread = threading.Thread(target=run_scheduler)
        scheduler_thread.daemon = True
        scheduler_thread.start()
        
        logger.info("备份调度器已启动")

# 全局备份管理器实例
backup_manager = None

def get_backup_manager() -> BackupManager:
    """获取备份管理器实例"""
    global backup_manager
    if backup_manager is None:
        backup_manager = BackupManager()
    return backup_manager

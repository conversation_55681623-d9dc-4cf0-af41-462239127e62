#!/usr/bin/env python3
"""
全局替换 goldenledger 为 goldenledger
保持品牌一致性
"""
import os
import re
from pathlib import Path
import shutil

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup"
    shutil.copy2(file_path, backup_path)
    return backup_path

def replace_in_file(file_path, replacements):
    """在文件中进行替换"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        for old_text, new_text in replacements.items():
            if old_text in content:
                content = content.replace(old_text, new_text)
                changes_made.append(f"{old_text} -> {new_text}")
        
        if content != original_content:
            # 备份原文件
            backup_file(file_path)
            
            # 写入新内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return changes_made
        
        return []
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return []

def get_replacements():
    """获取替换规则"""
    return {
        # 数据库文件名
        'goldenledger_accounting.db': 'goldenledger_accounting.db',
        
        # 文本替换
        'goldenledger': 'goldenledger',
        'GoldenLedger': 'GoldenLedger',
        'GOLDENLEDGER': 'GOLDENLEDGER',
        
        # 特定的品牌引用
        'goldenledger会计': 'GoldenLedger会计',
        'goldenledger.co.jp': 'goldenledger.com',
        
        # 网络相关
        'goldenledger-network': 'goldenledger-network',
        
        # 目录和路径
        'goldenledger_kuaiji': 'goldenledger_kuaiji',
    }

def should_process_file(file_path):
    """判断是否应该处理该文件"""
    # 要处理的文件扩展名
    process_extensions = {'.html', '.py', '.md', '.txt', '.js', '.ts', '.json', '.yml', '.yaml', '.sh', '.sql'}
    
    # 要排除的目录
    exclude_dirs = {'node_modules', '.git', '__pycache__', '.pytest_cache', 'venv', 'env', 'backup'}
    
    # 要排除的文件
    exclude_files = {'replace_goldenledger_to_goldenledger.py'}
    
    file_path = Path(file_path)
    
    # 检查是否在排除目录中
    for part in file_path.parts:
        if part in exclude_dirs:
            return False
    
    # 检查是否是排除文件
    if file_path.name in exclude_files:
        return False
    
    # 检查文件扩展名或特殊文件
    return (file_path.suffix.lower() in process_extensions or 
            file_path.name in ['Dockerfile', 'deploy.sh', '_redirects', 'wrangler.toml'])

def rename_files_and_directories():
    """重命名包含goldenledger的文件和目录"""
    renamed_items = []
    
    # 遍历所有文件和目录
    for root, dirs, files in os.walk('.', topdown=False):
        # 重命名文件
        for file in files:
            if 'goldenledger' in file.lower():
                old_path = Path(root) / file
                new_name = file.replace('goldenledger', 'goldenledger').replace('GoldenLedger', 'GoldenLedger')
                new_path = Path(root) / new_name
                
                if old_path != new_path:
                    try:
                        old_path.rename(new_path)
                        renamed_items.append(f"文件: {old_path} -> {new_path}")
                    except Exception as e:
                        print(f"重命名文件失败 {old_path}: {e}")
        
        # 重命名目录
        for dir_name in dirs:
            if 'goldenledger' in dir_name.lower():
                old_path = Path(root) / dir_name
                new_name = dir_name.replace('goldenledger', 'goldenledger').replace('GoldenLedger', 'GoldenLedger')
                new_path = Path(root) / new_name
                
                if old_path != new_path:
                    try:
                        old_path.rename(new_path)
                        renamed_items.append(f"目录: {old_path} -> {new_path}")
                    except Exception as e:
                        print(f"重命名目录失败 {old_path}: {e}")
    
    return renamed_items

def main():
    """主函数"""
    print("🔄 开始全局替换 goldenledger -> goldenledger")
    print("=" * 60)
    
    replacements = get_replacements()
    
    # 1. 重命名文件和目录
    print("1️⃣ 重命名包含goldenledger的文件和目录...")
    renamed_items = rename_files_and_directories()
    
    if renamed_items:
        print(f"✅ 重命名了 {len(renamed_items)} 个项目:")
        for item in renamed_items[:5]:  # 只显示前5个
            print(f"  {item}")
        if len(renamed_items) > 5:
            print(f"  ... 还有 {len(renamed_items) - 5} 个项目")
    else:
        print("  ℹ️  没有需要重命名的文件或目录")
    
    # 2. 替换文件内容
    print("\n2️⃣ 替换文件内容...")
    
    total_files = 0
    modified_files = 0
    total_changes = 0
    
    for root, dirs, files in os.walk('.'):
        # 排除特定目录
        dirs[:] = [d for d in dirs if d not in {'node_modules', '.git', '__pycache__', '.pytest_cache', 'venv', 'env', 'backup'}]
        
        for file in files:
            file_path = Path(root) / file
            
            if should_process_file(file_path):
                total_files += 1
                changes = replace_in_file(file_path, replacements)
                
                if changes:
                    modified_files += 1
                    total_changes += len(changes)
                    print(f"  ✅ {file_path}: {len(changes)} 处修改")
                    for change in changes[:3]:  # 只显示前3个变更
                        print(f"    - {change}")
                    if len(changes) > 3:
                        print(f"    - ... 还有 {len(changes) - 3} 处修改")
    
    print(f"\n📊 替换统计:")
    print(f"  - 扫描文件数: {total_files}")
    print(f"  - 修改文件数: {modified_files}")
    print(f"  - 总修改次数: {total_changes}")
    
    # 3. 特殊处理数据库文件
    print("\n3️⃣ 处理数据库文件...")
    old_db_paths = [
        'backend/goldenledger_accounting.db',
        'goldenledger_accounting.db'
    ]
    
    for old_db_path in old_db_paths:
        if Path(old_db_path).exists():
            new_db_path = old_db_path.replace('goldenledger_accounting.db', 'goldenledger_accounting.db')
            try:
                shutil.copy2(old_db_path, new_db_path)
                print(f"  ✅ 复制数据库: {old_db_path} -> {new_db_path}")
                print(f"  ⚠️  请手动删除旧数据库文件: {old_db_path}")
            except Exception as e:
                print(f"  ❌ 复制数据库失败: {e}")
    
    print(f"\n🎉 替换完成！")
    print(f"📝 建议接下来:")
    print(f"  1. 检查修改结果")
    print(f"  2. 测试应用功能")
    print(f"  3. 更新配置文件中的数据库路径")
    print(f"  4. 删除备份文件（.backup）")

if __name__ == "__main__":
    main()

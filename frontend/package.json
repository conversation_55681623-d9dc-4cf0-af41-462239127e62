{"name": "GoldenLedger-accounting-ai-frontend", "private": true, "version": "4.0.6", "type": "module", "description": "GoldenLedger — Smart AI-Powered Finance System前端", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.81.5", "axios": "^1.6.2", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^10.18.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-pdf": "^7.5.1", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "tailwind-merge": "^3.3.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.0"}}
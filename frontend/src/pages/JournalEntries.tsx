import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Plus, Search, Filter, Download, Edit, Trash2 } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { useQuery } from '@tanstack/react-query'
import { journalEntriesAPI, type JournalEntry } from '@/lib/api'
import { formatCurrency, formatDate } from '@/lib/utils'

const JournalEntries: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEntries, setSelectedEntries] = useState<string[]>([])

  // 获取仕訳列表
  const { data: entries = [], isLoading } = useQuery({
    queryKey: ['journal-entries'],
    queryFn: () => journalEntriesAPI.getEntries('default'),
  })

  // 获取会计科目
  const { data: accountSubjects = {} } = useQuery({
    queryKey: ['account-subjects'],
    queryFn: () => journalEntriesAPI.getAccountSubjects('default'),
  })

  const filteredEntries = entries.filter((entry: JournalEntry) =>
    entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entry.debit_account.toLowerCase().includes(searchTerm.toLowerCase()) ||
    entry.credit_account.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSelectEntry = (entryId: string) => {
    setSelectedEntries(prev =>
      prev.includes(entryId)
        ? prev.filter(id => id !== entryId)
        : [...prev, entryId]
    )
  }

  const handleSelectAll = () => {
    if (selectedEntries.length === filteredEntries.length) {
      setSelectedEntries([])
    } else {
      setSelectedEntries(filteredEntries.map((entry: JournalEntry) => entry.id))
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">仕訳帳</h1>
          <p className="text-gray-600">会計仕訳の管理と確認</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            エクスポート
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            新規仕訳
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="仕訳を検索..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 raycast-input"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              フィルター
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{entries.length}</p>
              <p className="text-sm text-gray-600">総仕訳数</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(
                  entries
                    .filter((e: JournalEntry) => e.debit_account.includes('売上'))
                    .reduce((sum: number, e: JournalEntry) => sum + e.amount, 0)
                )}
              </p>
              <p className="text-sm text-gray-600">今月の売上</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(
                  entries
                    .filter((e: JournalEntry) => e.debit_account.includes('費用') || e.debit_account.includes('消耗品'))
                    .reduce((sum: number, e: JournalEntry) => sum + e.amount, 0)
                )}
              </p>
              <p className="text-sm text-gray-600">今月の費用</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Journal Entries Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>仕訳一覧</CardTitle>
            {selectedEntries.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedEntries.length}件選択中
                </span>
                <Button variant="outline" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  削除
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-gray-600 mt-2">読み込み中...</p>
            </div>
          ) : filteredEntries.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-600">仕訳が見つかりません</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedEntries.length === filteredEntries.length}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300"
                      />
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      日付
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      摘要
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      借方
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      貸方
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      金額
                    </th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEntries.map((entry: JournalEntry, index: number) => (
                    <motion.tr
                      key={entry.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-4 py-4">
                        <input
                          type="checkbox"
                          checked={selectedEntries.includes(entry.id)}
                          onChange={() => handleSelectEntry(entry.id)}
                          className="rounded border-gray-300"
                        />
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {formatDate(entry.entry_date)}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {entry.description}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {entry.debit_account}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {entry.credit_account}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900 text-right font-medium">
                        {formatCurrency(entry.amount)}
                      </td>
                      <td className="px-4 py-4 text-center">
                        <div className="flex items-center justify-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default JournalEntries

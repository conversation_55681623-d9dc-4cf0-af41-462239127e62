import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Plus, 
  Settings, 
  TestTube, 
  Activity,
  DollarSign,
  Clock,
  Brain,
  Edit,
  Trash2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { useQuery } from '@tanstack/react-query'
import { llmManagementAPI, type ModelConfiguration, type UsageStats } from '@/lib/api'

const LLMManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'models' | 'usage'>('models')

  // 获取模型配置
  const { data: modelConfigs = [], isLoading: modelsLoading } = useQuery({
    queryKey: ['model-configurations'],
    queryFn: () => llmManagementAPI.getModelConfigurations('default'),
  })

  // 获取使用统计
  const { data: usageStats = [], isLoading: statsLoading } = useQuery({
    queryKey: ['usage-statistics'],
    queryFn: () => llmManagementAPI.getUsageStatistics('default'),
  })

  // 获取支持的供应商
  const { data: providersData } = useQuery({
    queryKey: ['supported-providers'],
    queryFn: llmManagementAPI.getSupportedProviders,
  })

  const tabs = [
    { id: 'models', label: 'モデル設定', icon: Brain },
    { id: 'usage', label: '使用統計', icon: Activity },
  ]

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'Google':
        return '🔍'
      case 'OpenAI':
        return '🤖'
      case 'Anthropic':
        return '🧠'
      default:
        return '💻'
    }
  }

  const getTaskTypeLabel = (taskType: string) => {
    const labels: Record<string, string> = {
      'complex_accounting': '複雑な会計処理',
      'simple_classification': '簡単な分類',
      'ocr_processing': 'OCR処理',
      'natural_language': '自然言語理解',
      'analysis': 'データ分析',
    }
    return labels[taskType] || taskType
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">LLMモデル管理</h1>
          <p className="text-gray-600">AI機能で使用するモデルを設定・管理します</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          新しいモデルを追加
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">設定済みモデル</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  {modelConfigs.length}
                </p>
              </div>
              <Brain className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">今月のリクエスト</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  {usageStats.reduce((sum, stat) => sum + stat.total_requests, 0).toLocaleString()}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">今月のコスト</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  ¥{usageStats.reduce((sum, stat) => sum + stat.total_cost, 0).toFixed(2)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均応答時間</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">
                  {usageStats.length > 0 
                    ? (usageStats.reduce((sum, stat) => sum + stat.avg_response_time, 0) / usageStats.length).toFixed(0)
                    : 0
                  }ms
                </p>
              </div>
              <Clock className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'models' && (
          <div className="space-y-6">
            {/* Model Configurations */}
            <Card>
              <CardHeader>
                <CardTitle>設定済みモデル</CardTitle>
              </CardHeader>
              <CardContent>
                {modelsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="text-gray-600 mt-2">読み込み中...</p>
                  </div>
                ) : modelConfigs.length === 0 ? (
                  <div className="text-center py-8">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">設定済みモデルがありません</p>
                    <Button className="mt-4">
                      <Plus className="h-4 w-4 mr-2" />
                      最初のモデルを追加
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {modelConfigs.map((config: ModelConfiguration, index: number) => (
                      <motion.div
                        key={config.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="text-2xl">
                              {getProviderIcon(config.provider)}
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">
                                {config.model_name}
                              </h3>
                              <p className="text-sm text-gray-600">
                                {config.provider} • 優先度: {config.priority}
                              </p>
                              <div className="flex flex-wrap gap-1 mt-2">
                                {config.task_types.map((taskType) => (
                                  <span
                                    key={taskType}
                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                  >
                                    {getTaskTypeLabel(taskType)}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <TestTube className="h-4 w-4 mr-2" />
                              テスト
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'usage' && (
          <div className="space-y-6">
            {/* Usage Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>プロバイダー別使用統計</CardTitle>
              </CardHeader>
              <CardContent>
                {statsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="text-gray-600 mt-2">読み込み中...</p>
                  </div>
                ) : usageStats.length === 0 ? (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">使用統計がありません</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {usageStats.map((stat: UsageStats, index: number) => (
                      <motion.div
                        key={stat.provider}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="border border-gray-200 rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">
                              {getProviderIcon(stat.provider)}
                            </span>
                            <h3 className="font-semibold text-gray-900">
                              {stat.provider}
                            </h3>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">リクエスト数</p>
                            <p className="text-lg font-semibold text-gray-900">
                              {stat.total_requests.toLocaleString()}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">トークン数</p>
                            <p className="text-lg font-semibold text-gray-900">
                              {stat.total_tokens.toLocaleString()}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">コスト</p>
                            <p className="text-lg font-semibold text-gray-900">
                              ¥{stat.total_cost.toFixed(2)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">平均応答時間</p>
                            <p className="text-lg font-semibold text-gray-900">
                              {stat.avg_response_time.toFixed(0)}ms
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </motion.div>
    </div>
  )
}

export default LLMManagement

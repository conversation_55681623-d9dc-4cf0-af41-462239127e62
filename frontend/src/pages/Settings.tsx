import React from 'react'
import { motion } from 'framer-motion'
import { 
  Building, 
  User, 
  Bell, 
  Shield, 
  Database,
  Palette,
  Globe,
  HelpCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const Settings: React.FC = () => {
  const settingsSections = [
    {
      title: '会社情報',
      description: '会社の基本情報と設定',
      icon: Building,
      color: 'from-blue-500 to-cyan-500',
      items: [
        { label: '会社名', value: 'デモ会社' },
        { label: '法人番号', value: '1234567890123' },
        { label: '決算期', value: '3月' },
        { label: '住所', value: '東京都渋谷区...' },
      ]
    },
    {
      title: 'ユーザー設定',
      description: 'アカウントとプロファイル設定',
      icon: User,
      color: 'from-green-500 to-emerald-500',
      items: [
        { label: 'ユーザー名', value: 'admin' },
        { label: 'メールアドレス', value: '<EMAIL>' },
        { label: '権限', value: '管理者' },
        { label: '最終ログイン', value: '2024-01-01 10:00' },
      ]
    },
    {
      title: '通知設定',
      description: 'アラートと通知の設定',
      icon: Bell,
      color: 'from-yellow-500 to-orange-500',
      items: [
        { label: 'メール通知', value: '有効', toggle: true },
        { label: 'AI処理完了通知', value: '有効', toggle: true },
        { label: 'エラー通知', value: '有効', toggle: true },
        { label: '月次レポート', value: '無効', toggle: true },
      ]
    },
    {
      title: 'セキュリティ',
      description: 'セキュリティとプライバシー設定',
      icon: Shield,
      color: 'from-red-500 to-pink-500',
      items: [
        { label: '二段階認証', value: '無効', toggle: true },
        { label: 'セッション有効期限', value: '30分' },
        { label: 'パスワード変更', value: '30日前' },
        { label: 'ログイン履歴', value: '表示' },
      ]
    },
    {
      title: 'データベース',
      description: 'データベースとバックアップ設定',
      icon: Database,
      color: 'from-purple-500 to-violet-500',
      items: [
        { label: 'データベース容量', value: '2.3 GB / 10 GB' },
        { label: '自動バックアップ', value: '有効', toggle: true },
        { label: 'バックアップ頻度', value: '毎日' },
        { label: '最終バックアップ', value: '2024-01-01 02:00' },
      ]
    },
    {
      title: '表示設定',
      description: 'テーマと表示オプション',
      icon: Palette,
      color: 'from-indigo-500 to-blue-500',
      items: [
        { label: 'テーマ', value: 'ライト' },
        { label: '言語', value: '日本語' },
        { label: '通貨表示', value: '日本円 (¥)' },
        { label: '日付形式', value: 'YYYY/MM/DD' },
      ]
    },
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">設定</h1>
        <p className="text-gray-600">システムの設定と環境設定を管理します</p>
      </div>

      {/* Settings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {settingsSections.map((section, index) => {
          const Icon = section.icon
          return (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 bg-gradient-to-r ${section.color} rounded-lg flex items-center justify-center`}>
                      <Icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                      <p className="text-sm text-gray-600">{section.description}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {section.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700">{item.label}</span>
                        {item.toggle ? (
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              className="sr-only peer"
                              defaultChecked={item.value === '有効'}
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        ) : (
                          <span className="text-sm font-medium text-gray-900">{item.value}</span>
                        )}
                      </div>
                    ))}
                    <div className="pt-4 border-t">
                      <Button variant="outline" size="sm" className="w-full">
                        設定を編集
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Globe className="h-5 w-5 mr-2 text-primary" />
            システム情報
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <p className="text-sm text-gray-600">バージョン</p>
              <p className="font-semibold text-gray-900">v1.0.0</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">ビルド</p>
              <p className="font-semibold text-gray-900">2024.01.01</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">環境</p>
              <p className="font-semibold text-gray-900">Development</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">サーバー</p>
              <p className="font-semibold text-gray-900">localhost:8000</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <HelpCircle className="h-5 w-5 mr-2 text-primary" />
            ヘルプとサポート
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <div className="text-2xl">📚</div>
              <div className="text-center">
                <p className="font-medium">ドキュメント</p>
                <p className="text-xs text-gray-600">使い方ガイド</p>
              </div>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <div className="text-2xl">💬</div>
              <div className="text-center">
                <p className="font-medium">サポート</p>
                <p className="text-xs text-gray-600">お問い合わせ</p>
              </div>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <div className="text-2xl">🔄</div>
              <div className="text-center">
                <p className="font-medium">アップデート</p>
                <p className="text-xs text-gray-600">最新版を確認</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default Settings

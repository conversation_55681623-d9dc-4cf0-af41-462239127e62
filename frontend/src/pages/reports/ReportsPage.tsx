/**
 * 报表页面
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { BarChart3, PieChart, TrendingUp } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';

const ReportsPage: React.FC = () => {
  const reports = [
    {
      name: '资产负债表',
      description: '显示企业在特定日期的财务状况',
      href: '/reports/balance-sheet',
      icon: BarChart3,
      color: 'bg-blue-500',
    },
    {
      name: '利润表',
      description: '显示企业在特定期间的经营成果',
      href: '/reports/income-statement',
      icon: TrendingUp,
      color: 'bg-green-500',
    },
    {
      name: '试算平衡表',
      description: '验证账户借贷平衡情况',
      href: '/reports/trial-balance',
      icon: PieChart,
      color: 'bg-purple-500',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          财务报表
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          查看和生成各种财务报表
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reports.map((report) => (
          <Card key={report.name} className="hover:shadow-lg transition-shadow">
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 ${report.color} rounded-lg flex items-center justify-center`}>
                <report.icon className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {report.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {report.description}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <Link to={report.href}>
                <Button variant="outline" fullWidth>
                  查看报表
                </Button>
              </Link>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ReportsPage;

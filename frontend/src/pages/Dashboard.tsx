import React from 'react'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  DollarSign, 
  FileText, 
  Brain,
  MessageSquare,
  Camera,
  Zap,
  ArrowRight
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { formatCurrency } from '@/lib/utils'

const Dashboard: React.FC = () => {
  // 模拟数据
  const kpiData = [
    {
      title: '今月の売上',
      value: 2450000,
      change: '+12.5%',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: '今月の費用',
      value: 890000,
      change: '-3.2%',
      icon: DollarSign,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: '仕訳数',
      value: 156,
      change: '+8.1%',
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'AI処理成功率',
      value: '94%',
      change: '+2.1%',
      icon: Brain,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'ai_booking',
      description: 'AI記帳: コンビニで事務用品購入',
      amount: 1200,
      time: '2分前',
      status: 'success',
    },
    {
      id: 2,
      type: 'manual_entry',
      description: '手動入力: 家賃支払い',
      amount: 150000,
      time: '15分前',
      status: 'success',
    },
    {
      id: 3,
      type: 'ocr_processing',
      description: 'OCR処理: 請求書スキャン',
      amount: 45000,
      time: '1時間前',
      status: 'pending',
    },
  ]

  const quickActions = [
    {
      title: '一句話記帳',
      description: '自然言語でAI記帳',
      icon: MessageSquare,
      color: 'from-green-500 to-blue-500',
      action: () => {
        // AI聊天窗口会自动打开
      },
    },
    {
      title: '写真アップロード',
      description: '領収書を撮影して記帳',
      icon: Camera,
      color: 'from-purple-500 to-pink-500',
      action: () => {
        // 文件上传功能
      },
    },
    {
      title: 'クイック仕訳',
      description: '手動で仕訳を作成',
      icon: Zap,
      color: 'from-orange-500 to-red-500',
      action: () => {
        // 快速仕訳功能
      },
    },
  ]

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary to-secondary rounded-xl p-8 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              おかえりなさい！
            </h1>
            <p className="text-lg opacity-90">
              今日も効率的な記帳をサポートします
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <Brain className="h-12 w-12" />
            </div>
          </div>
        </div>
      </motion.div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi, index) => {
          const Icon = kpi.icon
          return (
            <motion.div
              key={kpi.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="kpi-card">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        {kpi.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900 mt-2">
                        {typeof kpi.value === 'number' ? formatCurrency(kpi.value) : kpi.value}
                      </p>
                      <p className={`text-sm mt-1 ${kpi.color}`}>
                        {kpi.change}
                      </p>
                    </div>
                    <div className={`p-3 rounded-full ${kpi.bgColor}`}>
                      <Icon className={`h-6 w-6 ${kpi.color}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          クイックアクション
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon
            return (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card 
                  className="cursor-pointer group hover:shadow-lg transition-all duration-200"
                  onClick={action.action}
                >
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center mb-4`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {action.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      {action.description}
                    </p>
                    <div className="flex items-center text-primary group-hover:text-primary/80">
                      <span className="text-sm font-medium">開始する</span>
                      <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            最近のアクティビティ
          </h2>
          <Card>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-200">
                {recentActivities.map((activity) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {activity.description}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {activity.time}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {formatCurrency(activity.amount)}
                        </p>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          activity.status === 'success' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {activity.status === 'success' ? '完了' : '処理中'}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AI Statistics */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            AI記帳統計
          </h2>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="h-5 w-5 mr-2 text-primary" />
                今月のAI処理状況
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">総処理数</span>
                  <span className="font-semibold">156件</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">成功率</span>
                  <span className="font-semibold text-green-600">94%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">平均処理時間</span>
                  <span className="font-semibold">2.3秒</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">コスト削減</span>
                  <span className="font-semibold text-blue-600">78%</span>
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t">
                <Button className="w-full" variant="outline">
                  詳細レポートを見る
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default Dashboard

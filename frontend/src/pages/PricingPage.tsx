import React from 'react';
import { motion } from 'framer-motion';
import { Check, X } from 'lucide-react';
import Button from '@/components/ui/Button';

// --- TypeScript Interfaces ---
interface Plan {
  id: string;
  name: string;
  price: number;
  features: string[];
  excluded: string[];
  color: string;
  popular?: boolean;
}

const PricingPage: React.FC = () => {

  const renderPayPalButtons = (plan: Plan) => {
    if (window.paypal && plan.price > 0) {
      const buttonContainer = document.querySelector(`#paypal-${plan.id}`);
      if (buttonContainer) {
        buttonContainer.innerHTML = ''; // 清空容器
        window.paypal.Buttons({
          style: { layout: 'vertical', color: plan.color as any, shape: 'rect', label: 'paypal' },
          createOrder: (data, actions) => actions.order.create({
            purchase_units: [{
              description: `${plan.name} 月額`,
              amount: { currency_code: 'JPY', value: plan.price.toString() }
            }]
          }),
          onApprove: (data, actions) => actions.order.capture().then(details => {
            alert(`支付成功！交易ID: ${details.id}`);
            // 在这里调用后端验证API
          }),
          onError: (err) => console.error(`PayPal Error for ${plan.name}:`, err),
        }).render(`#paypal-${plan.id}`);
      }
    }
  };

  React.useEffect(() => {
    plans.forEach(plan => {
      renderPayPalButtons(plan);
    });
  }, []);

  const plans: Plan[] = [
    { id: 'free', name: 'フリープラン', price: 0, features: ['AI記帳：月10回まで', 'データ保存：30日間', '基本的な記帳機能', '簡単なレポート'], excluded: ['PDFエクスポート', 'OCR機能'], color: 'gray' },
    { id: 'basic', name: 'ベーシックプラン', price: 980, features: ['AI記帳：月100回まで', 'データ保存：1年間', 'PDFエクスポート', '基本AI分析', 'メールサポート'], excluded: ['OCR機能'], color: 'blue' },
    { id: 'pro', name: 'プロプラン', price: 2980, features: ['AI記帳：無制限', 'データ保存：無制限', 'OCR領収書認識', '複数会社管理', '高度な分析機能', '優先サポート'], excluded: [], color: 'gold', popular: true },
    { id: 'enterprise', name: 'エンタープライズ', price: 9800, features: ['プロプランの全機能', 'マルチユーザー対応', 'API アクセス', 'カスタム統合', '専任カスタマーマネージャー', 'SLA保証'], excluded: [], color: 'black' },
  ];

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
      <header className="text-center py-16 bg-gray-100 dark:bg-gray-800">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">料金プラン</h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mt-4">あなたのビジネスに最適なプランを選択してください。</p>
      </header>
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {plans.map(plan => (
              <div key={plan.id} className={`relative rounded-2xl shadow-lg p-8 border-2 ${plan.popular ? 'border-purple-500' : 'border-gray-200 dark:border-gray-700'} bg-white dark:bg-gray-800`}>
                {plan.popular && <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-bold">人気No.1</div>}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">{plan.name}</h3>
                  <div className={`text-4xl font-bold text-${plan.color}-600 dark:text-${plan.color}-400 mb-2`}>¥{plan.price.toLocaleString()}</div>
                  <div className="text-gray-500 dark:text-gray-400">{plan.price > 0 ? '月額（税込）' : '永続無料'}</div>
                </div>
                <ul className="space-y-4 mb-8">
                  {plan.features.map(feature => <li key={feature} className="flex items-center"><Check className="text-green-500 mr-3" /><span>{feature}</span></li>)}
                  {plan.excluded.map(feature => <li key={feature} className="flex items-center text-gray-400"><X className="mr-3" /><span>{feature}</span></li>)}
                </ul>
                {plan.price > 0 ? (
                  <>
                    <Button className="w-full mb-4">7日間無料トライアル</Button>
                    <div id={`paypal-${plan.id}`} className="mt-2"></div>
                  </>
                ) : (
                  <Button className="w-full" disabled>現在のプラン</Button>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>
    </motion.div>
  );
};

export default PricingPage;

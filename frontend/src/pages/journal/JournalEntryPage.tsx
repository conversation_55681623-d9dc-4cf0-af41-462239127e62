/**
 * 记账详情页面
 */

import React from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Edit, CheckCircle, Calendar, User, DollarSign } from 'lucide-react';
import { useJournalEntry, usePostJournalEntry } from '../../lib/react-query';
import { formatCurrency, formatDate } from '../../lib/utils';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

const JournalEntryPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const { data: entry, isLoading } = useJournalEntry(id!);
  const postEntryMutation = usePostJournalEntry();

  const handlePost = async () => {
    if (!entry) return;
    
    try {
      await postEntryMutation.mutateAsync(entry.id);
    } catch (error) {
      // 错误已在mutation中处理
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!entry) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            记账记录不存在
          </p>
          <Button onClick={() => navigate('/journal')}>
            返回列表
          </Button>
        </div>
      </div>
    );
  }

  // 状态标签组件
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const statusConfig = {
      draft: {
        label: '草稿',
        className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      },
      posted: {
        label: '已过账',
        className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      },
      cancelled: {
        label: '已取消',
        className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
        {config.label}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/journal')}
            icon={<ArrowLeft className="h-4 w-4" />}
          >
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              记账详情
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {entry.company_name} - 记账记录详情
            </p>
          </div>
        </div>

        <div className="flex space-x-3">
          {entry.status === 'draft' && (
            <>
              <Link to={`/journal/${entry.id}/edit`}>
                <Button
                  variant="outline"
                  icon={<Edit className="h-4 w-4" />}
                >
                  编辑
                </Button>
              </Link>
              <Button
                onClick={handlePost}
                loading={postEntryMutation.isPending}
                icon={<CheckCircle className="h-4 w-4" />}
              >
                过账
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 基本信息 */}
      <Card>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          基本信息
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-1">
              <Calendar className="h-4 w-4 mr-1" />
              记账日期
            </div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatDate(entry.entry_date)}
            </div>
          </div>

          <div>
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-1">
              <DollarSign className="h-4 w-4 mr-1" />
              总金额
            </div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatCurrency(entry.total_amount)}
            </div>
          </div>

          <div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
              状态
            </div>
            <StatusBadge status={entry.status} />
          </div>

          <div>
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-1">
              <User className="h-4 w-4 mr-1" />
              创建者
            </div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {entry.created_by_name || '未知'}
            </div>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
              记账描述
            </div>
            <div className="text-sm text-gray-900 dark:text-white">
              {entry.description}
            </div>
          </div>

          {entry.reference_number && (
            <div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                凭证号
              </div>
              <div className="text-sm text-gray-900 dark:text-white">
                {entry.reference_number}
              </div>
            </div>
          )}
        </div>

        {/* AI信息 */}
        {entry.ai_generated && (
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-center mb-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-blue-900 dark:text-blue-200">
                AI智能生成
              </span>
              {entry.ai_confidence && (
                <span className="ml-2 text-xs text-blue-700 dark:text-blue-300">
                  置信度: {(entry.ai_confidence * 100).toFixed(1)}%
                </span>
              )}
            </div>
            {entry.ai_analysis && (
              <p className="text-sm text-blue-800 dark:text-blue-200">
                {entry.ai_analysis}
              </p>
            )}
          </div>
        )}
      </Card>

      {/* 记账分录 */}
      <Card>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          记账分录
        </h3>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  会计科目
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  借方金额
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  贷方金额
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  备注
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {entry.lines.map((line, index) => (
                <tr key={line.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {line.account_code} - {line.account_name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {line.debit_amount > 0 ? formatCurrency(line.debit_amount) : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {line.credit_amount > 0 ? formatCurrency(line.credit_amount) : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {line.description || '-'}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <td className="px-6 py-3 text-sm font-medium text-gray-900 dark:text-white">
                  合计
                </td>
                <td className="px-6 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(entry.lines.reduce((sum, line) => sum + line.debit_amount, 0))}
                </td>
                <td className="px-6 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(entry.lines.reduce((sum, line) => sum + line.credit_amount, 0))}
                </td>
                <td className="px-6 py-3"></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </Card>

      {/* 操作历史 */}
      <Card>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          操作历史
        </h3>

        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <div className="flex-1">
              <div className="text-sm text-gray-900 dark:text-white">
                记账记录已创建
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {formatDate(entry.created_at, 'yyyy-MM-dd HH:mm:ss')} · {entry.created_by_name}
              </div>
            </div>
          </div>

          {entry.posted_at && (
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <div className="text-sm text-gray-900 dark:text-white">
                  记账记录已过账
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDate(entry.posted_at, 'yyyy-MM-dd HH:mm:ss')} · {entry.posted_by || '系统'}
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default JournalEntryPage;

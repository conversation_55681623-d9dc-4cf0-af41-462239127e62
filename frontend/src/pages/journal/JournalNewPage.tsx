/**
 * 新建记账页面
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Trash2, Save, ArrowLeft } from 'lucide-react';
import { useAppStore } from '../../stores/app-store';
import { useCreateJournalEntry, useAccountSubjects } from '../../lib/react-query';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import { formatCurrency } from '../../lib/utils';

// 表单验证schema
const journalEntrySchema = z.object({
  entry_date: z.string().min(1, '请选择记账日期'),
  description: z.string().min(1, '请输入记账描述'),
  reference_number: z.string().optional(),
  lines: z.array(z.object({
    account_id: z.string().min(1, '请选择会计科目'),
    debit_amount: z.number().min(0, '借方金额不能为负数'),
    credit_amount: z.number().min(0, '贷方金额不能为负数'),
    description: z.string().optional(),
  })).min(2, '至少需要两行记账分录'),
}).refine((data) => {
  const totalDebit = data.lines.reduce((sum, line) => sum + line.debit_amount, 0);
  const totalCredit = data.lines.reduce((sum, line) => sum + line.credit_amount, 0);
  return Math.abs(totalDebit - totalCredit) < 0.01;
}, {
  message: '借贷金额必须平衡',
  path: ['lines'],
});

type JournalEntryFormData = z.infer<typeof journalEntrySchema>;

const JournalNewPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentCompany } = useAppStore();
  const createJournalEntryMutation = useCreateJournalEntry();

  const { data: accounts = [] } = useAccountSubjects({
    company_id: currentCompany?.id,
    is_active: true,
  });

  const {
    register,
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<JournalEntryFormData>({
    resolver: zodResolver(journalEntrySchema),
    defaultValues: {
      entry_date: new Date().toISOString().split('T')[0],
      lines: [
        { account_id: '', debit_amount: 0, credit_amount: 0, description: '' },
        { account_id: '', debit_amount: 0, credit_amount: 0, description: '' },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'lines',
  });

  const watchedLines = watch('lines');

  // 计算总计
  const totalDebit = watchedLines.reduce((sum, line) => sum + (line.debit_amount || 0), 0);
  const totalCredit = watchedLines.reduce((sum, line) => sum + (line.credit_amount || 0), 0);
  const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;

  // 会计科目选项
  const accountOptions = accounts.map(account => ({
    value: account.id,
    label: `${account.code} - ${account.name}`,
  }));

  const onSubmit = async (data: JournalEntryFormData) => {
    if (!currentCompany) {
      return;
    }

    try {
      await createJournalEntryMutation.mutateAsync({
        company_id: currentCompany.id,
        entry_date: data.entry_date,
        description: data.description,
        reference_number: data.reference_number,
        lines: data.lines.map(line => ({
          account_id: line.account_id,
          debit_amount: line.debit_amount,
          credit_amount: line.credit_amount,
          description: line.description,
        })),
      });

      navigate('/journal');
    } catch (error) {
      // 错误已在mutation中处理
    }
  };

  const addLine = () => {
    append({ account_id: '', debit_amount: 0, credit_amount: 0, description: '' });
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            请先选择一个公司
          </p>
          <Button onClick={() => navigate('/companies')}>
            选择公司
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/journal')}
            icon={<ArrowLeft className="h-4 w-4" />}
          >
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              新建记账
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {currentCompany.name} - 创建新的记账记录
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 基本信息 */}
        <Card>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            基本信息
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              {...register('entry_date')}
              type="date"
              label="记账日期"
              error={errors.entry_date?.message}
              fullWidth
            />
            
            <Input
              {...register('reference_number')}
              type="text"
              label="凭证号（可选）"
              placeholder="请输入凭证号"
              fullWidth
            />
            
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                借贷平衡状态
              </label>
              <div className={`px-3 py-2 rounded-md text-sm font-medium ${
                isBalanced 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {isBalanced ? '已平衡' : '未平衡'}
              </div>
            </div>
          </div>
          
          <div className="mt-4">
            <Input
              {...register('description')}
              type="text"
              label="记账描述"
              placeholder="请输入记账描述"
              error={errors.description?.message}
              fullWidth
            />
          </div>
        </Card>

        {/* 记账分录 */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              记账分录
            </h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addLine}
              icon={<Plus className="h-4 w-4" />}
            >
              添加分录
            </Button>
          </div>

          <div className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="grid grid-cols-12 gap-4 items-end">
                <div className="col-span-4">
                  <Select
                    {...register(`lines.${index}.account_id`)}
                    label={index === 0 ? '会计科目' : ''}
                    options={accountOptions}
                    placeholder="请选择会计科目"
                    error={errors.lines?.[index]?.account_id?.message}
                    fullWidth
                  />
                </div>
                
                <div className="col-span-2">
                  <Input
                    {...register(`lines.${index}.debit_amount`, { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    min="0"
                    label={index === 0 ? '借方金额' : ''}
                    placeholder="0.00"
                    error={errors.lines?.[index]?.debit_amount?.message}
                    fullWidth
                  />
                </div>
                
                <div className="col-span-2">
                  <Input
                    {...register(`lines.${index}.credit_amount`, { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    min="0"
                    label={index === 0 ? '贷方金额' : ''}
                    placeholder="0.00"
                    error={errors.lines?.[index]?.credit_amount?.message}
                    fullWidth
                  />
                </div>
                
                <div className="col-span-3">
                  <Input
                    {...register(`lines.${index}.description`)}
                    type="text"
                    label={index === 0 ? '备注' : ''}
                    placeholder="备注信息"
                    fullWidth
                  />
                </div>
                
                <div className="col-span-1">
                  {fields.length > 2 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => remove(index)}
                      icon={<Trash2 className="h-4 w-4" />}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* 总计 */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-12 gap-4">
              <div className="col-span-4">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  总计
                </span>
              </div>
              <div className="col-span-2">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(totalDebit)}
                </span>
              </div>
              <div className="col-span-2">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(totalCredit)}
                </span>
              </div>
              <div className="col-span-4">
                <span className={`text-sm font-medium ${
                  isBalanced 
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  差额: {formatCurrency(Math.abs(totalDebit - totalCredit))}
                </span>
              </div>
            </div>
          </div>

          {errors.lines && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400">
              {errors.lines.message}
            </p>
          )}
        </Card>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/journal')}
          >
            取消
          </Button>
          <Button
            type="submit"
            loading={createJournalEntryMutation.isPending}
            disabled={!isBalanced}
            icon={<Save className="h-4 w-4" />}
          >
            保存记账
          </Button>
        </div>
      </form>
    </div>
  );
};

export default JournalNewPage;

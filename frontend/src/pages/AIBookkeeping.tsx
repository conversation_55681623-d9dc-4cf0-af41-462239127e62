import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  MessageSquare, 
  Camera, 
  FileText, 
  Mic,
  Sparkles,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { useQuery } from '@tanstack/react-query'
import { aiBookkeepingAPI } from '@/lib/api'

const AIBookkeeping: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'demo' | 'stats'>('demo')

  // 获取演示示例
  const { data: demoData } = useQuery({
    queryKey: ['ai-demo-examples'],
    queryFn: aiBookkeepingAPI.getDemoExamples,
  })

  // 获取统计信息
  const { data: statsData } = useQuery({
    queryKey: ['ai-stats'],
    queryFn: () => aiBookkeepingAPI.getStats('default'),
  })

  const features = [
    {
      icon: MessageSquare,
      title: '自然言語記帳',
      description: '日常会話のように話すだけで、AIが自動的に仕訳を生成します',
      example: '「今日コンビニで事務用品を1200円で購入」',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: Camera,
      title: '写真アップロード',
      description: 'レシートや請求書を撮影するだけで、OCRが自動的に情報を抽出',
      example: 'JPG, PNG, PDF対応',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: Mic,
      title: '音声入力',
      description: '音声認識で手を使わずに記帳。移動中でも簡単に入力可能',
      example: 'マイクボタンを押して話すだけ',
      color: 'from-purple-500 to-violet-500',
    },
    {
      icon: FileText,
      title: 'バッチ処理',
      description: '複数のファイルを一度にアップロードして、まとめて処理',
      example: '最大10ファイルまで同時処理',
      color: 'from-orange-500 to-red-500',
    },
  ]

  const tabs = [
    { id: 'demo', label: 'デモ・使い方', icon: Sparkles },
    { id: 'stats', label: '統計情報', icon: TrendingUp },
  ]

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <div className="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
          <Sparkles className="h-4 w-4 mr-2" />
          革命的なAI記帳システム
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          一句話全自動記帳
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          話すだけ、撮るだけ、アップロードするだけ。<br />
          AIが自動的に適切な仕訳を生成し、記帳作業を革新します。
        </p>
      </motion.div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {features.map((feature, index) => {
          const Icon = feature.icon
          return (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center mb-4`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 mb-3">
                    {feature.description}
                  </p>
                  <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                    💡 {feature.example}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'demo' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Demo Examples */}
            <Card>
              <CardHeader>
                <CardTitle>入力例</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {demoData?.natural_language_examples?.map((example: string, index: number) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                    >
                      <p className="text-sm text-gray-700">"{example}"</p>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle>使い方のコツ</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {demoData?.tips?.map((tip: string, index: number) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start space-x-3"
                    >
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-gray-700">{tip}</p>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'stats' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">総処理数</p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">
                      {statsData?.total_processed || 0}
                    </p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">成功率</p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">
                      {((statsData?.success_rate || 0) * 100).toFixed(1)}%
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">平均信頼度</p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">
                      {((statsData?.avg_confidence || 0) * 100).toFixed(1)}%
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">処理時間</p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">
                      2.3秒
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </motion.div>

      {/* CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary to-secondary rounded-xl p-8 text-white text-center"
      >
        <h2 className="text-2xl font-bold mb-4">
          今すぐAI記帳を始めましょう
        </h2>
        <p className="text-lg opacity-90 mb-6">
          右下のチャットボタンをクリックして、AI記帳を体験してください
        </p>
        <Button 
          variant="outline" 
          className="bg-white text-primary hover:bg-gray-100"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          AIチャットを開く
        </Button>
      </motion.div>
    </div>
  )
}

export default AIBookkeeping

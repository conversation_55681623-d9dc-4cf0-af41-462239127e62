/**
 * 记账记录列表组件
 * 显示和管理记账记录
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  CheckCircle, 
  Clock,
  Calendar,
  User,
  DollarSign
} from 'lucide-react';
import { useAppStore } from '../../stores/app-store';
import { useJournalEntries, usePostJournalEntry } from '../../lib/react-query';
import { JournalEntryFilter } from '../../types/api';
import { formatCurrency, formatDate } from '../../lib/utils';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { clsx } from 'clsx';

const JournalEntryList: React.FC = () => {
  const { currentCompany } = useAppStore();
  const [filter, setFilter] = useState<JournalEntryFilter>({
    company_id: currentCompany?.id,
    page: 1,
    page_size: 20,
  });
  const [searchTerm, setSearchTerm] = useState('');

  const { data: entries, isLoading, refetch } = useJournalEntries(filter);
  const postEntryMutation = usePostJournalEntry();

  // 处理搜索
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setFilter(prev => ({
      ...prev,
      search: term || undefined,
      page: 1,
    }));
  };

  // 处理过账
  const handlePost = async (entryId: string) => {
    try {
      await postEntryMutation.mutateAsync(entryId);
      refetch();
    } catch (error) {
      // 错误已在mutation中处理
    }
  };

  // 状态标签组件
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const statusConfig = {
      draft: {
        label: '草稿',
        className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      },
      posted: {
        label: '已过账',
        className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      },
      cancelled: {
        label: '已取消',
        className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <span className={clsx('inline-flex px-2 py-1 text-xs font-medium rounded-full', config.className)}>
        {config.label}
      </span>
    );
  };

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            请先选择一个公司
          </p>
          <Link to="/companies">
            <Button>选择公司</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            记账管理
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {currentCompany.name} - 记账记录管理
          </p>
        </div>
        <Link to="/journal/new">
          <Button icon={<Plus className="h-4 w-4" />}>
            新建记账
          </Button>
        </Link>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索框 */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索记账记录..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* 状态过滤 */}
          <div className="flex gap-2">
            <select
              value={filter.status || ''}
              onChange={(e) => setFilter(prev => ({
                ...prev,
                status: e.target.value || undefined,
                page: 1,
              }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部状态</option>
              <option value="draft">草稿</option>
              <option value="posted">已过账</option>
              <option value="cancelled">已取消</option>
            </select>

            <Button variant="outline" icon={<Filter className="h-4 w-4" />}>
              更多筛选
            </Button>
          </div>
        </div>
      </Card>

      {/* 记账记录列表 */}
      <Card>
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : entries?.length ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    日期
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    描述
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    金额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    创建者
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {entries.map((entry) => (
                  <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-900 dark:text-white">
                          {formatDate(entry.entry_date)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {entry.description}
                      </div>
                      {entry.reference_number && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          凭证号: {entry.reference_number}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatCurrency(entry.total_amount)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={entry.status} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-900 dark:text-white">
                          {entry.created_by_name || '未知'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link to={`/journal/${entry.id}`}>
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={<Eye className="h-4 w-4" />}
                          >
                            查看
                          </Button>
                        </Link>
                        
                        {entry.status === 'draft' && (
                          <>
                            <Link to={`/journal/${entry.id}/edit`}>
                              <Button
                                variant="ghost"
                                size="sm"
                                icon={<Edit className="h-4 w-4" />}
                              >
                                编辑
                              </Button>
                            </Link>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              icon={<CheckCircle className="h-4 w-4" />}
                              loading={postEntryMutation.isPending}
                              onClick={() => handlePost(entry.id)}
                            >
                              过账
                            </Button>
                          </>
                        )}
                        
                        {entry.status === 'posted' && (
                          <div className="flex items-center text-green-600 dark:text-green-400">
                            <CheckCircle className="h-4 w-4 mr-1" />
                            <span className="text-xs">已过账</span>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              暂无记账记录
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              开始创建您的第一条记账记录
            </p>
            <Link to="/journal/new">
              <Button icon={<Plus className="h-4 w-4" />}>
                新建记账
              </Button>
            </Link>
          </div>
        )}
      </Card>

      {/* 分页 */}
      {entries && entries.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            显示第 {((filter.page || 1) - 1) * (filter.page_size || 20) + 1} 到{' '}
            {Math.min((filter.page || 1) * (filter.page_size || 20), entries.length)} 条记录
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={(filter.page || 1) <= 1}
              onClick={() => setFilter(prev => ({ ...prev, page: (prev.page || 1) - 1 }))}
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={entries.length < (filter.page_size || 20)}
              onClick={() => setFilter(prev => ({ ...prev, page: (prev.page || 1) + 1 }))}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default JournalEntryList;

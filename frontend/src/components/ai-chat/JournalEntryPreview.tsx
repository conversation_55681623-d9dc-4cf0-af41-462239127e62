import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Check, Edit, AlertTriangle, Lightbulb, TrendingUp } from 'lucide-react'
import <PERSON>ton from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import JournalEntryEditModal from './JournalEntryEditModal'
import { formatCurrency, formatDate } from '@/lib/utils'
import type { JournalEntry } from '@/lib/api'

interface JournalEntryPreviewProps {
  entry: JournalEntry
  confidence: number
  warnings: string[]
  suggestions: string[]
  onConfirm: (entry?: JournalEntry) => void
  onEdit?: (entry: JournalEntry) => void
}

const JournalEntryPreview: React.FC<JournalEntryPreviewProps> = ({
  entry,
  confidence,
  warnings,
  suggestions,
  onConfirm,
  onEdit,
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [currentEntry, setCurrentEntry] = useState(entry)
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100'
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.8) return <TrendingUp className="h-4 w-4" />
    if (confidence >= 0.6) return <AlertTriangle className="h-4 w-4" />
    return <AlertTriangle className="h-4 w-4" />
  }

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(currentEntry)
    } else {
      setIsEditModalOpen(true)
    }
  }

  const handleEditSave = (editedEntry: JournalEntry) => {
    setCurrentEntry(editedEntry)
    setIsEditModalOpen(false)
  }

  const handleConfirmClick = () => {
    onConfirm(currentEntry)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
      className="w-full"
    >
      <Card className="journal-preview">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-gray-900">生成された仕訳</h3>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(confidence)}`}>
            {getConfidenceIcon(confidence)}
            <span>信頼度: {(confidence * 100).toFixed(0)}%</span>
          </div>
        </div>

        {/* 仕訳内容 */}
        <div className="space-y-4">
          {/* 日期和金额 */}
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">日付: {formatDate(currentEntry.entry_date)}</span>
            <span className="font-semibold text-lg">{formatCurrency(currentEntry.amount)}</span>
          </div>

          {/* 借方贷方 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">借方 (Debit)</label>
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="font-medium text-blue-900">{currentEntry.debit_account}</div>
                <div className="text-sm text-blue-700">{formatCurrency(currentEntry.amount)}</div>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">貸方 (Credit)</label>
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="font-medium text-green-900">{currentEntry.credit_account}</div>
                <div className="text-sm text-green-700">{formatCurrency(currentEntry.amount)}</div>
              </div>
            </div>
          </div>

          {/* 摘要 */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">摘要</label>
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="text-sm text-gray-900">{currentEntry.description}</div>
            </div>
          </div>

          {/* 参考号码 */}
          {currentEntry.reference_number && (
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">伝票番号</label>
              <div className="text-sm text-gray-600">{currentEntry.reference_number}</div>
            </div>
          )}
        </div>

        {/* 警告信息 */}
        {warnings.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
          >
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div>
                <div className="text-sm font-medium text-yellow-800">注意事項</div>
                <ul className="mt-1 text-sm text-yellow-700 space-y-1">
                  {warnings.map((warning, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-1">•</span>
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
        )}

        {/* 建议信息 */}
        {suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <div className="flex items-start space-x-2">
              <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <div className="text-sm font-medium text-blue-800">提案</div>
                <ul className="mt-1 text-sm text-blue-700 space-y-1">
                  {suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-1">•</span>
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2 mt-6">
          <Button
            onClick={handleConfirmClick}
            className="flex-1"
            size="sm"
          >
            <Check className="h-4 w-4 mr-2" />
            確認して記録
          </Button>
          <Button
            variant="outline"
            onClick={handleEditClick}
            size="sm"
          >
            <Edit className="h-4 w-4 mr-2" />
            編集
          </Button>
        </div>
      </Card>

      {/* 编辑模态框 */}
      <JournalEntryEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleEditSave}
        entry={currentEntry}
      />
    </motion.div>
  )
}

export default JournalEntryPreview

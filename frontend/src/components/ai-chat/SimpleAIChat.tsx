import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, X, Send, Sparkles } from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string | React.ReactNode
  timestamp: Date
}

const SimpleAIChat: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'こんにちは！AI会計アシスタントです。取引内容を教えてください。',
      timestamp: new Date(),
    }
  ])
  const [inputText, setInputText] = useState('')

  const handleSubmit = () => {
    if (!inputText.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInputText('')

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: (
          <div className="space-y-3">
            <p>承知いたしました。以下の仕訳を生成しました：</p>
            <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold mb-3 text-gray-800">生成された仕訳</h4>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="text-xs text-gray-500 uppercase">借方</label>
                  <div className="bg-blue-50 border border-blue-200 rounded p-2">
                    <div className="font-medium text-blue-900">消耗品費</div>
                    <div className="text-sm text-blue-700">¥1,200</div>
                  </div>
                </div>
                <div>
                  <label className="text-xs text-gray-500 uppercase">貸方</label>
                  <div className="bg-green-50 border border-green-200 rounded p-2">
                    <div className="font-medium text-green-900">現金</div>
                    <div className="text-sm text-green-700">¥1,200</div>
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-600 mb-3">
                <strong>摘要:</strong> {inputText}
              </div>
              <div className="flex space-x-2">
                <button className="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600 transition-colors">
                  ✓ 確認して記録
                </button>
                <button className="border border-gray-300 px-4 py-2 rounded text-sm hover:bg-gray-50 transition-colors">
                  ✏️ 編集
                </button>
              </div>
            </div>
          </div>
        ),
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, aiResponse])
    }, 1000)
  }

  return (
    <>
      {/* 聊天按钮 */}
      <motion.div
        className="fixed bottom-6 right-6 z-50"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: 'spring', stiffness: 260, damping: 20 }}
      >
        <Button
          onClick={() => setIsOpen(true)}
          className="w-14 h-14 rounded-full shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:shadow-xl transition-shadow"
          size="lg"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </motion.div>

      {/* 聊天窗口 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed bottom-6 right-6 z-50 w-96 h-[600px] max-h-[80vh]"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="h-full flex flex-col overflow-hidden shadow-2xl">
              {/* 聊天头部 */}
              <div className="p-4 border-b bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-5 w-5" />
                    <div>
                      <h3 className="font-semibold">AI会計アシスタント</h3>
                      <p className="text-sm opacity-90">何でも話しかけてください</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* 聊天内容 */}
              <div className="flex-1 p-4 overflow-y-auto space-y-4 bg-gray-50">
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-white text-gray-800 shadow-sm border'
                      }`}
                    >
                      {message.content}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* 输入区域 */}
              <div className="p-4 border-t bg-white">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
                    placeholder="取引内容を入力..."
                    className="flex-1 border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <Button
                    onClick={handleSubmit}
                    disabled={!inputText.trim()}
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  例: "今日コンビニで事務用品を1200円で購入"
                </p>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default SimpleAIChat

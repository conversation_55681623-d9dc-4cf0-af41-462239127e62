import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, X, Send, Camera, Paperclip, Mic } from 'lucide-react'
import { useDropzone } from 'react-dropzone'
import toast from 'react-hot-toast'

import But<PERSON> from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import ChatMessage from './ChatMessage'
import JournalEntryPreview from './JournalEntryPreview'
import FileUploadZone from './FileUploadZone'
import VoiceInput from './VoiceInput'
import SmartSuggestions from './SmartSuggestions'
import { aiBookkeepingAPI, type JournalEntry } from '@/lib/api'

interface Message {
  id: string
  type: 'user' | 'ai' | 'system'
  content: string | React.ReactNode
  timestamp: Date
  isLoading?: boolean
}

interface AIChatProps {
  companyId?: string
}

const AIChat: React.FC<AIChatProps> = ({ companyId = 'default' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'こんにちは！AI会計アシスタントです。取引内容を教えてください。写真をアップロードするか、直接お話しください。',
      timestamp: new Date(),
    }
  ])
  const [inputText, setInputText] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 处理文字输入
  const handleTextSubmit = async (text: string) => {
    if (!text.trim() || isProcessing) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: text,
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInputText('')
    setIsProcessing(true)

    try {
      const response = await aiBookkeepingAPI.processNaturalLanguage({
        text,
        company_id: companyId,
      })

      if (response.success && response.journal_entry) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: (
            <JournalEntryPreview
              entry={response.journal_entry}
              confidence={response.confidence}
              warnings={response.warnings}
              suggestions={response.suggestions}
              onConfirm={() => handleConfirmEntry(response.journal_entry!)}
              onEdit={() => handleEditEntry(response.journal_entry!)}
            />
          ),
          timestamp: new Date(),
        }
        setMessages(prev => [...prev, aiMessage])
      } else {
        throw new Error(response.error || '処理に失敗しました')
      }
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `申し訳ございません。エラーが発生しました: ${error instanceof Error ? error.message : '不明なエラー'}`,
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, errorMessage])
      toast.error('処理に失敗しました')
    } finally {
      setIsProcessing(false)
    }
  }

  // 处理文件上传
  const handleFileUpload = async (files: File[]) => {
    if (files.length === 0 || isProcessing) return

    setIsProcessing(true)

    try {
      const file = files[0]
      
      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: `📎 ${file.name} をアップロードしました`,
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, userMessage])

      const response = await aiBookkeepingAPI.processImageUpload(file, companyId)

      if (response.success && response.journal_entry) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: (
            <JournalEntryPreview
              entry={response.journal_entry}
              confidence={response.confidence}
              warnings={response.warnings}
              suggestions={response.suggestions}
              onConfirm={() => handleConfirmEntry(response.journal_entry!)}
              onEdit={() => handleEditEntry(response.journal_entry!)}
            />
          ),
          timestamp: new Date(),
        }
        setMessages(prev => [...prev, aiMessage])
      } else {
        throw new Error(response.error || 'ファイル処理に失敗しました')
      }
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `ファイル処理中にエラーが発生しました: ${error instanceof Error ? error.message : '不明なエラー'}`,
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, errorMessage])
      toast.error('ファイル処理に失敗しました')
    } finally {
      setIsProcessing(false)
    }
  }

  // 确认仕訳
  const handleConfirmEntry = async (entry: JournalEntry) => {
    try {
      // 这里可以调用API保存仕訳
      toast.success('仕訳が記録されました！')
      
      const confirmMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: '✅ 仕訳が正常に記録されました。他にも記録したい取引はありますか？',
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, confirmMessage])
    } catch (error) {
      toast.error('仕訳の保存に失敗しました')
    }
  }

  // 编辑仕訳
  const handleEditEntry = (entry: JournalEntry) => {
    // 这里可以打开编辑模态框
    toast.info('編集機能は開発中です')
  }

  // 语音输入处理
  const handleVoiceTranscript = (transcript: string) => {
    setInputText(transcript)
    // 自动提交语音输入
    setTimeout(() => {
      handleTextSubmit(transcript)
    }, 500)
  }

  // 智能建议点击
  const handleSuggestionClick = (suggestionText: string) => {
    setInputText(suggestionText)
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  // 输入框焦点处理
  const handleInputFocus = () => {
    setShowSuggestions(true)
  }

  const handleInputBlur = () => {
    // 延迟隐藏建议，允许点击建议
    setTimeout(() => setShowSuggestions(false), 200)
  }

  return (
    <>
      {/* 聊天按钮 */}
      <motion.div
        className="fixed bottom-6 right-6 z-50"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: 'spring', stiffness: 260, damping: 20 }}
      >
        <Button
          onClick={() => setIsOpen(true)}
          className="w-14 h-14 rounded-full shadow-lg bg-gradient-to-r from-primary to-secondary"
          size="lg"
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </motion.div>

      {/* 聊天窗口 */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed bottom-6 right-6 z-50 w-96 h-[600px] max-h-[80vh]"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="h-full flex flex-col overflow-hidden">
              {/* 聊天头部 */}
              <div className="p-4 border-b bg-gradient-to-r from-primary to-secondary text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">AI会計アシスタント</h3>
                    <p className="text-sm opacity-90">何でも話しかけてください</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* 聊天内容 */}
              <div className="flex-1 p-4 overflow-y-auto space-y-4">
                {messages.map((message) => (
                  <ChatMessage key={message.id} message={message} />
                ))}
                {isProcessing && (
                  <ChatMessage
                    message={{
                      id: 'loading',
                      type: 'ai',
                      content: '処理中...',
                      timestamp: new Date(),
                      isLoading: true,
                    }}
                  />
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* 输入区域 */}
              <div className="p-4 border-t bg-gray-50 relative">
                <FileUploadZone onUpload={handleFileUpload} />

                {/* 智能建议 */}
                <SmartSuggestions
                  onSuggestionClick={handleSuggestionClick}
                  inputText={inputText}
                  isVisible={showSuggestions && !isProcessing}
                />

                <div className="flex items-center space-x-2 mt-3">
                  <VoiceInput
                    onTranscript={handleVoiceTranscript}
                    disabled={isProcessing}
                  />

                  <div className="flex-1 relative">
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleTextSubmit(inputText)}
                      onFocus={handleInputFocus}
                      onBlur={handleInputBlur}
                      placeholder="取引内容を入力..."
                      className="w-full raycast-input"
                      disabled={isProcessing}
                    />
                  </div>

                  <Button
                    onClick={() => handleTextSubmit(inputText)}
                    disabled={!inputText.trim() || isProcessing}
                    size="sm"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default AIChat

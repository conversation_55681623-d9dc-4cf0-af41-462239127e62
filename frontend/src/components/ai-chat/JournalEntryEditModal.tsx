import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Save, Calendar, DollarSign, FileText } from 'lucide-react'
import Button from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { formatCurrency } from '@/lib/utils'
import type { JournalEntry } from '@/lib/api'

interface JournalEntryEditModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (entry: JournalEntry) => void
  entry: JournalEntry
  accountSubjects?: Record<string, string[]>
}

const JournalEntryEditModal: React.FC<JournalEntryEditModalProps> = ({
  isOpen,
  onClose,
  onSave,
  entry,
  accountSubjects = {},
}) => {
  const [editedEntry, setEditedEntry] = useState<JournalEntry>(entry)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    setEditedEntry(entry)
    setErrors({})
  }, [entry])

  const validateEntry = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!editedEntry.entry_date) {
      newErrors.entry_date = '日付は必須です'
    }

    if (!editedEntry.description.trim()) {
      newErrors.description = '摘要は必須です'
    }

    if (!editedEntry.debit_account.trim()) {
      newErrors.debit_account = '借方科目は必須です'
    }

    if (!editedEntry.credit_account.trim()) {
      newErrors.credit_account = '貸方科目は必須です'
    }

    if (editedEntry.amount <= 0) {
      newErrors.amount = '金額は0より大きい値を入力してください'
    }

    if (editedEntry.debit_account === editedEntry.credit_account) {
      newErrors.accounts = '借方と貸方に同じ科目は設定できません'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (validateEntry()) {
      onSave(editedEntry)
      onClose()
    }
  }

  const handleFieldChange = (field: keyof JournalEntry, value: any) => {
    setEditedEntry(prev => ({
      ...prev,
      [field]: value,
    }))
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }))
    }
  }

  const getAllAccounts = () => {
    const allAccounts: string[] = []
    Object.values(accountSubjects).forEach(accounts => {
      allAccounts.push(...accounts)
    })
    return allAccounts
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <Card className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">仕訳編集</h2>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Form */}
            <div className="space-y-6">
              {/* 日期和金额 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="h-4 w-4 inline mr-1" />
                    日付
                  </label>
                  <input
                    type="date"
                    value={editedEntry.entry_date}
                    onChange={(e) => handleFieldChange('entry_date', e.target.value)}
                    className={`raycast-input ${errors.entry_date ? 'border-red-300' : ''}`}
                  />
                  {errors.entry_date && (
                    <p className="text-red-500 text-xs mt-1">{errors.entry_date}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <DollarSign className="h-4 w-4 inline mr-1" />
                    金額
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1"
                    value={editedEntry.amount}
                    onChange={(e) => handleFieldChange('amount', parseFloat(e.target.value) || 0)}
                    className={`raycast-input ${errors.amount ? 'border-red-300' : ''}`}
                    placeholder="0"
                  />
                  {errors.amount && (
                    <p className="text-red-500 text-xs mt-1">{errors.amount}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    {formatCurrency(editedEntry.amount)}
                  </p>
                </div>
              </div>

              {/* 借方贷方 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    借方科目
                  </label>
                  <select
                    value={editedEntry.debit_account}
                    onChange={(e) => handleFieldChange('debit_account', e.target.value)}
                    className={`raycast-input ${errors.debit_account ? 'border-red-300' : ''}`}
                  >
                    <option value="">科目を選択してください</option>
                    {getAllAccounts().map(account => (
                      <option key={account} value={account}>
                        {account}
                      </option>
                    ))}
                  </select>
                  {errors.debit_account && (
                    <p className="text-red-500 text-xs mt-1">{errors.debit_account}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    貸方科目
                  </label>
                  <select
                    value={editedEntry.credit_account}
                    onChange={(e) => handleFieldChange('credit_account', e.target.value)}
                    className={`raycast-input ${errors.credit_account ? 'border-red-300' : ''}`}
                  >
                    <option value="">科目を選択してください</option>
                    {getAllAccounts().map(account => (
                      <option key={account} value={account}>
                        {account}
                      </option>
                    ))}
                  </select>
                  {errors.credit_account && (
                    <p className="text-red-500 text-xs mt-1">{errors.credit_account}</p>
                  )}
                </div>
              </div>

              {/* 科目错误 */}
              {errors.accounts && (
                <p className="text-red-500 text-sm">{errors.accounts}</p>
              )}

              {/* 摘要 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FileText className="h-4 w-4 inline mr-1" />
                  摘要
                </label>
                <textarea
                  value={editedEntry.description}
                  onChange={(e) => handleFieldChange('description', e.target.value)}
                  className={`raycast-input min-h-[80px] resize-none ${errors.description ? 'border-red-300' : ''}`}
                  placeholder="取引内容を入力してください"
                  rows={3}
                />
                {errors.description && (
                  <p className="text-red-500 text-xs mt-1">{errors.description}</p>
                )}
              </div>

              {/* 参考号码 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  伝票番号（任意）
                </label>
                <input
                  type="text"
                  value={editedEntry.reference_number || ''}
                  onChange={(e) => handleFieldChange('reference_number', e.target.value)}
                  className="raycast-input"
                  placeholder="伝票番号を入力してください"
                />
              </div>

              {/* 预览 */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-3">仕訳プレビュー</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="space-y-1">
                    <div className="text-gray-600">借方</div>
                    <div className="font-medium">{editedEntry.debit_account || '未選択'}</div>
                    <div className="text-gray-600">{formatCurrency(editedEntry.amount)}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-gray-600">貸方</div>
                    <div className="font-medium">{editedEntry.credit_account || '未選択'}</div>
                    <div className="text-gray-600">{formatCurrency(editedEntry.amount)}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
              <Button variant="outline" onClick={onClose}>
                キャンセル
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                保存
              </Button>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default JournalEntryEditModal

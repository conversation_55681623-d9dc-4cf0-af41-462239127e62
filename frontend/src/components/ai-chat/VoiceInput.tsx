import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, MicOff, Volume2 } from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import toast from 'react-hot-toast'

interface VoiceInputProps {
  onTranscript: (text: string) => void
  disabled?: boolean
}

const VoiceInput: React.FC<VoiceInputProps> = ({ onTranscript, disabled = false }) => {
  const [isListening, setIsListening] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [isSupported, setIsSupported] = useState(false)
  
  const recognitionRef = useRef<any>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    // 检查浏览器是否支持语音识别
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    
    if (SpeechRecognition) {
      setIsSupported(true)
      
      const recognition = new SpeechRecognition()
      recognition.continuous = true
      recognition.interimResults = true
      recognition.lang = 'ja-JP' // 日语识别
      
      recognition.onstart = () => {
        setIsListening(true)
        toast.success('音声認識を開始しました')
      }
      
      recognition.onresult = (event: any) => {
        let finalTranscript = ''
        let interimTranscript = ''
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }
        
        setTranscript(finalTranscript + interimTranscript)
        
        if (finalTranscript) {
          onTranscript(finalTranscript)
          stopListening()
        }
      }
      
      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error)
        setIsListening(false)
        
        switch (event.error) {
          case 'no-speech':
            toast.error('音声が検出されませんでした')
            break
          case 'audio-capture':
            toast.error('マイクにアクセスできません')
            break
          case 'not-allowed':
            toast.error('マイクの使用が許可されていません')
            break
          default:
            toast.error('音声認識エラーが発生しました')
        }
      }
      
      recognition.onend = () => {
        setIsListening(false)
      }
      
      recognitionRef.current = recognition
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [onTranscript])

  const startListening = async () => {
    if (!isSupported) {
      toast.error('お使いのブラウザは音声認識に対応していません')
      return
    }

    if (!recognitionRef.current) return

    try {
      // マイクの許可を要求
      await navigator.mediaDevices.getUserMedia({ audio: true })
      
      setTranscript('')
      recognitionRef.current.start()
      
      // 10秒後に自動停止
      timeoutRef.current = setTimeout(() => {
        stopListening()
        toast.info('音声認識を自動停止しました')
      }, 10000)
      
    } catch (error) {
      toast.error('マイクにアクセスできません')
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
    }
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    setIsListening(false)
  }

  const toggleListening = () => {
    if (isListening) {
      stopListening()
    } else {
      startListening()
    }
  }

  if (!isSupported) {
    return (
      <Button
        variant="outline"
        size="sm"
        disabled
        className="opacity-50"
      >
        <MicOff className="h-4 w-4" />
      </Button>
    )
  }

  return (
    <div className="flex items-center space-x-2">
      <motion.div
        animate={isListening ? { scale: [1, 1.1, 1] } : { scale: 1 }}
        transition={{ repeat: isListening ? Infinity : 0, duration: 1 }}
      >
        <Button
          variant={isListening ? "destructive" : "outline"}
          size="sm"
          onClick={toggleListening}
          disabled={disabled}
          className={isListening ? 'bg-red-500 hover:bg-red-600' : ''}
        >
          {isListening ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Volume2 className="h-4 w-4" />
            </motion.div>
          ) : (
            <Mic className="h-4 w-4" />
          )}
        </Button>
      </motion.div>
      
      {/* 实时转录显示 */}
      {isListening && transcript && (
        <motion.div
          initial={{ opacity: 0, width: 0 }}
          animate={{ opacity: 1, width: 'auto' }}
          exit={{ opacity: 0, width: 0 }}
          className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded max-w-32 truncate"
        >
          {transcript}
        </motion.div>
      )}
    </div>
  )
}

export default VoiceInput

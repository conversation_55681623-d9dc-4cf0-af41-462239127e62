import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Lightbulb, TrendingUp, Clock, DollarSign } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

interface Suggestion {
  id: string
  type: 'quick' | 'template' | 'recent' | 'smart'
  text: string
  description?: string
  icon?: React.ReactNode
}

interface SmartSuggestionsProps {
  onSuggestionClick: (text: string) => void
  inputText: string
  isVisible: boolean
}

const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  onSuggestionClick,
  inputText,
  isVisible,
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([])

  // 基础建议模板
  const baseSuggestions: Suggestion[] = [
    {
      id: '1',
      type: 'quick',
      text: '今日コンビニで事務用品を1200円で購入',
      description: '消耗品費の記録',
      icon: <DollarSign className="h-4 w-4" />,
    },
    {
      id: '2',
      type: 'quick',
      text: 'ABC会社から売上50万円を銀行振込で受取',
      description: '売上の記録',
      icon: <TrendingUp className="h-4 w-4" />,
    },
    {
      id: '3',
      type: 'quick',
      text: '今月の家賃15万円を支払い',
      description: '地代家賃の記録',
      icon: <DollarSign className="h-4 w-4" />,
    },
    {
      id: '4',
      type: 'template',
      text: '電気代8500円を口座振替で支払い',
      description: '水道光熱費の記録',
      icon: <Lightbulb className="h-4 w-4" />,
    },
    {
      id: '5',
      type: 'template',
      text: '従業員給与80万円を支払い',
      description: '給料手当の記録',
      icon: <DollarSign className="h-4 w-4" />,
    },
    {
      id: '6',
      type: 'recent',
      text: '広告費5万円をクレジットカードで支払い',
      description: '広告宣伝費の記録',
      icon: <Clock className="h-4 w-4" />,
    },
  ]

  // 智能建议生成
  const generateSmartSuggestions = (input: string): Suggestion[] => {
    const smartSuggestions: Suggestion[] = []
    
    // 基于输入内容生成智能建议
    if (input.includes('コンビニ') || input.includes('購入')) {
      smartSuggestions.push({
        id: 'smart-1',
        type: 'smart',
        text: `${input}した商品の消耗品費として記録`,
        description: 'AI提案: 消耗品費での記録',
        icon: <Lightbulb className="h-4 w-4 text-blue-500" />,
      })
    }
    
    if (input.includes('売上') || input.includes('収入')) {
      smartSuggestions.push({
        id: 'smart-2',
        type: 'smart',
        text: `${input}を売上高として記録`,
        description: 'AI提案: 売上高での記録',
        icon: <Lightbulb className="h-4 w-4 text-green-500" />,
      })
    }
    
    if (input.includes('支払') || input.includes('費用')) {
      smartSuggestions.push({
        id: 'smart-3',
        type: 'smart',
        text: `${input}を適切な費用科目で記録`,
        description: 'AI提案: 費用科目の自動選択',
        icon: <Lightbulb className="h-4 w-4 text-orange-500" />,
      })
    }
    
    return smartSuggestions
  }

  useEffect(() => {
    let filteredSuggestions = [...baseSuggestions]
    
    if (inputText.trim()) {
      // 输入内容匹配
      filteredSuggestions = baseSuggestions.filter(suggestion =>
        suggestion.text.toLowerCase().includes(inputText.toLowerCase()) ||
        suggestion.description?.toLowerCase().includes(inputText.toLowerCase())
      )
      
      // 添加智能建议
      const smartSuggestions = generateSmartSuggestions(inputText)
      filteredSuggestions = [...smartSuggestions, ...filteredSuggestions]
    }
    
    // 限制显示数量
    setSuggestions(filteredSuggestions.slice(0, 6))
  }, [inputText])

  const getSuggestionStyle = (type: string) => {
    switch (type) {
      case 'smart':
        return 'border-blue-200 bg-blue-50 hover:bg-blue-100'
      case 'recent':
        return 'border-green-200 bg-green-50 hover:bg-green-100'
      case 'template':
        return 'border-purple-200 bg-purple-50 hover:bg-purple-100'
      default:
        return 'border-gray-200 bg-gray-50 hover:bg-gray-100'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'smart':
        return 'AI提案'
      case 'recent':
        return '最近の取引'
      case 'template':
        return 'テンプレート'
      default:
        return 'クイック入力'
    }
  }

  if (!isVisible || suggestions.length === 0) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
        className="absolute bottom-full left-0 right-0 mb-2 z-10"
      >
        <Card className="max-h-64 overflow-y-auto">
          <div className="p-2 space-y-1">
            <div className="text-xs font-medium text-gray-500 px-2 py-1">
              💡 入力候補
            </div>
            
            {suggestions.map((suggestion, index) => (
              <motion.button
                key={suggestion.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => onSuggestionClick(suggestion.text)}
                className={`w-full text-left p-3 rounded-lg border transition-colors ${getSuggestionStyle(suggestion.type)}`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {suggestion.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {suggestion.text}
                    </div>
                    {suggestion.description && (
                      <div className="text-xs text-gray-600 mt-1">
                        {suggestion.description}
                      </div>
                    )}
                    <div className="text-xs text-gray-500 mt-1">
                      {getTypeLabel(suggestion.type)}
                    </div>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
}

export default SmartSuggestions

/**
 * 仪表板组件
 * 显示公司财务概览和关键指标
 */

import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  FileText, 
  Users, 
  Calendar,
  BarChart3,
  PieChart
} from 'lucide-react';
import { useAppStore } from '../../stores/app-store';
import { useCompanyStats } from '../../lib/react-query';
import { formatCurrency, formatNumber } from '../../lib/utils';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { Link } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const { currentCompany } = useAppStore();
  const { data: stats, isLoading } = useCompanyStats(currentCompany?.id || '');

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            请先选择一个公司
          </p>
          <Link to="/companies">
            <Button>选择公司</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* 加载骨架屏 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(2)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            财务仪表板
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {currentCompany.name} - 财务概览
          </p>
        </div>
        <div className="flex space-x-3">
          <Link to="/journal/new">
            <Button icon={<FileText className="h-4 w-4" />}>
              新建记账
            </Button>
          </Link>
          <Link to="/reports">
            <Button variant="outline" icon={<BarChart3 className="h-4 w-4" />}>
              查看报表
            </Button>
          </Link>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 总记账记录数 */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                总记账记录
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatNumber(stats?.total_entries || 0)}
              </p>
            </div>
          </div>
        </Card>

        {/* 总金额 */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                总金额
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatCurrency(stats?.total_amount || 0)}
              </p>
            </div>
          </div>
        </Card>

        {/* 本月记录数 */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                本月记录
              </p>
              <div className="flex items-center">
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {formatNumber(stats?.monthly_entries || 0)}
                </p>
                {stats?.monthly_entries && stats?.total_entries && (
                  <div className="ml-2 flex items-center">
                    {stats.monthly_entries > stats.total_entries * 0.1 ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* 活跃科目数 */}
        <Card>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                <PieChart className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                活跃科目
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {formatNumber(stats?.active_accounts || 0)}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* 最近记录和快速操作 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最近记录 */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              最近记录
            </h3>
            <Link to="/journal">
              <Button variant="ghost" size="sm">
                查看全部
              </Button>
            </Link>
          </div>
          
          <div className="space-y-3">
            {stats?.recent_entries?.length ? (
              stats.recent_entries.map((entry) => (
                <div
                  key={entry.id}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {entry.description}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {entry.date}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(entry.amount)}
                    </p>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        entry.status === 'posted'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }`}
                    >
                      {entry.status === 'posted' ? '已过账' : '草稿'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  暂无记账记录
                </p>
                <Link to="/journal/new" className="mt-2 inline-block">
                  <Button size="sm">创建第一条记录</Button>
                </Link>
              </div>
            )}
          </div>
        </Card>

        {/* 快速操作 */}
        <Card>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            快速操作
          </h3>
          
          <div className="grid grid-cols-2 gap-3">
            <Link to="/journal/new">
              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400 mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  新建记账
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  创建新的记账记录
                </p>
              </div>
            </Link>

            <Link to="/accounts">
              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <PieChart className="h-8 w-8 text-green-600 dark:text-green-400 mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  会计科目
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  管理会计科目
                </p>
              </div>
            </Link>

            <Link to="/reports/balance-sheet">
              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <BarChart3 className="h-8 w-8 text-purple-600 dark:text-purple-400 mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  资产负债表
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  查看财务状况
                </p>
              </div>
            </Link>

            <Link to="/reports/income-statement">
              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                <TrendingUp className="h-8 w-8 text-orange-600 dark:text-orange-400 mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  利润表
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  查看经营成果
                </p>
              </div>
            </Link>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;

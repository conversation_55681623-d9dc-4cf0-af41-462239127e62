import React, { useState } from 'react';
import {
  UnstyledButton,
  Group,
  Text,
  Box,
  Collapse,
  ThemeIcon,
  rem,
} from '@mantine/core';
import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react';
import { useStyles } from './NavigationItem.styles';

interface NavigationItemProps {
  item: {
    icon: React.ComponentType<any>;
    label: string;
    path: string;
    children?: Array<{
      label: string;
      path: string;
    }>;
  };
  currentPath: string;
  onNavigate: (path: string) => void;
}

export const NavigationItem: React.FC<NavigationItemProps> = ({
  item,
  currentPath,
  onNavigate,
}) => {
  const { classes, theme } = useStyles();
  const [opened, setOpened] = useState(
    item.children?.some(child => currentPath.startsWith(child.path)) || false
  );
  
  const hasChildren = item.children && item.children.length > 0;
  const isActive = currentPath === item.path || 
    (item.children?.some(child => currentPath.startsWith(child.path)));

  const handleClick = () => {
    if (hasChildren) {
      setOpened(!opened);
    } else {
      onNavigate(item.path);
    }
  };

  const ChevronIcon = theme.dir === 'ltr' ? IconChevronRight : IconChevronLeft;

  return (
    <>
      <UnstyledButton
        onClick={handleClick}
        className={`${classes.control} ${isActive ? classes.active : ''}`}
      >
        <Group position="apart" spacing={0}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ThemeIcon variant="light" size={30}>
              <item.icon size={rem(18)} />
            </ThemeIcon>
            <Box ml="md">
              <Text size="sm" weight={500}>
                {item.label}
              </Text>
            </Box>
          </Box>
          {hasChildren && (
            <ChevronIcon
              className={classes.chevron}
              size={rem(16)}
              stroke={1.5}
              style={{
                transform: opened ? `rotate(${theme.dir === 'rtl' ? -90 : 90}deg)` : 'none',
              }}
            />
          )}
        </Group>
      </UnstyledButton>

      {hasChildren && (
        <Collapse in={opened}>
          <Box className={classes.childrenContainer}>
            {item.children?.map((child) => (
              <UnstyledButton
                key={child.path}
                onClick={() => onNavigate(child.path)}
                className={`${classes.childControl} ${
                  currentPath === child.path ? classes.childActive : ''
                }`}
              >
                <Text size="sm" pl={rem(31)}>
                  {child.label}
                </Text>
              </UnstyledButton>
            ))}
          </Box>
        </Collapse>
      )}
    </>
  );
};

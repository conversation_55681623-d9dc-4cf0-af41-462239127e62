import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  AppShell,
  Navbar,
  Header,
  Text,
  MediaQuery,
  Burger,
  useMantineTheme,
  Group,
  Avatar,
  Menu,
  Select,
  Badge,
  ActionIcon,
  Tooltip,
  Divider,
  Stack,
  Box,
} from '@mantine/core';
import {
  IconDashboard,
  IconReceipt,
  IconBuilding,
  IconUsers,
  IconSettings,
  IconLogout,
  IconChevronDown,
  IconBell,
  IconSwitchHorizontal,
  IconPlus,
} from '@tabler/icons-react';
import { useAuth } from '../../hooks/useAuth';
import { useTenant } from '../../hooks/useTenant';
import { NavigationItem } from './NavigationItem';

interface MultiTenantLayoutProps {
  children?: React.ReactNode;
}

export const MultiTenantLayout: React.FC<MultiTenantLayoutProps> = () => {
  const theme = useMantineTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [opened, setOpened] = useState(false);
  
  const { user, logout, hasPermission } = useAuth();
  const { 
    currentTenant, 
    currentCompany, 
    tenants, 
    companies, 
    switchTenant, 
    switchCompany,
    createCompany 
  } = useTenant();

  // 导航菜单项
  const navigationItems = [
    {
      icon: IconDashboard,
      label: '仪表板',
      path: '/dashboard',
      permission: 'dashboard.read',
    },
    {
      icon: IconReceipt,
      label: '记账管理',
      path: '/journal',
      permission: 'journal.read',
      children: [
        { label: '记账录入', path: '/journal/entry', permission: 'journal.write' },
        { label: '记账查询', path: '/journal/list', permission: 'journal.read' },
        { label: 'AI智能记账', path: '/journal/ai', permission: 'journal.ai' },
      ],
    },
    {
      icon: IconBuilding,
      label: '公司管理',
      path: '/companies',
      permission: 'company.read',
      children: [
        { label: '公司信息', path: '/companies/info', permission: 'company.read' },
        { label: '会计科目', path: '/companies/accounts', permission: 'account.read' },
        { label: '财务报表', path: '/companies/reports', permission: 'report.read' },
      ],
    },
    {
      icon: IconUsers,
      label: '用户管理',
      path: '/users',
      permission: 'user.read',
      children: [
        { label: '用户列表', path: '/users/list', permission: 'user.read' },
        { label: '权限管理', path: '/users/permissions', permission: 'user.manage' },
        { label: '邀请用户', path: '/users/invite', permission: 'user.invite' },
      ],
    },
    {
      icon: IconSettings,
      label: '系统设置',
      path: '/settings',
      permission: 'settings.read',
      children: [
        { label: 'AI配置', path: '/settings/ai', permission: 'settings.ai' },
        { label: '系统配置', path: '/settings/system', permission: 'settings.system' },
        { label: '审计日志', path: '/settings/audit', permission: 'audit.read' },
      ],
    },
  ];

  // 过滤用户有权限的菜单项
  const filteredNavigation = navigationItems.filter(item => 
    hasPermission(item.permission)
  ).map(item => ({
    ...item,
    children: item.children?.filter(child => hasPermission(child.permission))
  }));

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleTenantSwitch = (tenantId: string) => {
    switchTenant(tenantId);
  };

  const handleCompanySwitch = (companyId: string) => {
    switchCompany(companyId);
  };

  return (
    <AppShell
      styles={{
        main: {
          background: theme.colorScheme === 'dark' ? theme.colors.dark[8] : theme.colors.gray[0],
        },
      }}
      navbar={
        <Navbar p="md" hiddenBreakpoint="sm" hidden={!opened} width={{ sm: 200, lg: 300 }}>
          {/* 租户和公司选择器 */}
          <Navbar.Section mb="md">
            <Stack spacing="xs">
              <Select
                label="当前租户"
                placeholder="选择租户"
                value={currentTenant?.id}
                onChange={handleTenantSwitch}
                data={tenants.map(t => ({ value: t.id, label: t.name }))}
                icon={<IconBuilding size={16} />}
                rightSection={<IconChevronDown size={16} />}
              />
              
              {currentTenant && (
                <Group position="apart">
                  <Select
                    label="当前公司"
                    placeholder="选择公司"
                    value={currentCompany?.id}
                    onChange={handleCompanySwitch}
                    data={companies.map(c => ({ value: c.id, label: c.name }))}
                    icon={<IconSwitchHorizontal size={16} />}
                    style={{ flex: 1 }}
                  />
                  <Tooltip label="创建新公司">
                    <ActionIcon 
                      variant="light" 
                      color="blue" 
                      mt={25}
                      onClick={() => navigate('/companies/create')}
                    >
                      <IconPlus size={16} />
                    </ActionIcon>
                  </Tooltip>
                </Group>
              )}
            </Stack>
          </Navbar.Section>

          <Divider mb="md" />

          {/* 导航菜单 */}
          <Navbar.Section grow>
            <Stack spacing="xs">
              {filteredNavigation.map((item) => (
                <NavigationItem
                  key={item.path}
                  item={item}
                  currentPath={location.pathname}
                  onNavigate={(path) => {
                    navigate(path);
                    setOpened(false);
                  }}
                />
              ))}
            </Stack>
          </Navbar.Section>

          {/* 用户信息 */}
          <Navbar.Section>
            <Box
              sx={(theme) => ({
                paddingTop: theme.spacing.sm,
                borderTop: `1px solid ${
                  theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2]
                }`,
              })}
            >
              <Group>
                <Avatar src={user?.avatar_url} radius="xl" />
                <Box sx={{ flex: 1 }}>
                  <Text size="sm" weight={500}>
                    {user?.name || user?.username}
                  </Text>
                  <Text color="dimmed" size="xs">
                    {user?.email}
                  </Text>
                </Box>
              </Group>
            </Box>
          </Navbar.Section>
        </Navbar>
      }
      header={
        <Header height={{ base: 50, md: 70 }} p="md">
          <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
            <MediaQuery largerThan="sm" styles={{ display: 'none' }}>
              <Burger
                opened={opened}
                onClick={() => setOpened((o) => !o)}
                size="sm"
                color={theme.colors.gray[6]}
                mr="xl"
              />
            </MediaQuery>

            <Group sx={{ height: '100%' }} px={20} position="apart" style={{ width: '100%' }}>
              <Group>
                <Text size="xl" weight={700} color="blue">
                  我的会计
                </Text>
                {currentCompany && (
                  <Badge variant="light" color="blue">
                    {currentCompany.name}
                  </Badge>
                )}
              </Group>

              <Group>
                <Tooltip label="通知">
                  <ActionIcon variant="light">
                    <IconBell size={18} />
                  </ActionIcon>
                </Tooltip>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <Group style={{ cursor: 'pointer' }}>
                      <Avatar src={user?.avatar_url} size={30} radius="xl" />
                      <IconChevronDown size={16} />
                    </Group>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Label>账户</Menu.Label>
                    <Menu.Item onClick={() => navigate('/profile')}>
                      个人资料
                    </Menu.Item>
                    <Menu.Item onClick={() => navigate('/settings')}>
                      设置
                    </Menu.Item>
                    
                    <Menu.Divider />
                    
                    <Menu.Item 
                      color="red" 
                      icon={<IconLogout size={14} />}
                      onClick={handleLogout}
                    >
                      退出登录
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              </Group>
            </Group>
          </div>
        </Header>
      }
    >
      <Outlet />
    </AppShell>
  );
};

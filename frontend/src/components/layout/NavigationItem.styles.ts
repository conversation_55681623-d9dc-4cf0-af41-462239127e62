import { createStyles, rem } from '@mantine/core';

export const useStyles = createStyles((theme) => ({
  control: {
    fontWeight: 500,
    display: 'block',
    width: '100%',
    padding: `${theme.spacing.xs} ${theme.spacing.md}`,
    color: theme.colorScheme === 'dark' ? theme.colors.dark[0] : theme.black,
    fontSize: theme.fontSizes.sm,
    borderRadius: theme.radius.sm,
    transition: 'background-color 100ms ease',

    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : theme.colors.gray[0],
    },
  },

  active: {
    backgroundColor: theme.colorScheme === 'dark' 
      ? theme.fn.rgba(theme.colors[theme.primaryColor][7], 0.25)
      : theme.colors[theme.primaryColor][0],
    color: theme.colorScheme === 'dark' 
      ? theme.colors[theme.primaryColor][4] 
      : theme.colors[theme.primaryColor][7],

    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark'
        ? theme.fn.rgba(theme.colors[theme.primaryColor][7], 0.35)
        : theme.colors[theme.primaryColor][1],
    },
  },

  chevron: {
    transition: 'transform 200ms ease',
  },

  childrenContainer: {
    paddingLeft: theme.spacing.md,
    paddingTop: theme.spacing.xs,
    borderLeft: `${rem(1)} solid ${
      theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[3]
    }`,
    marginLeft: rem(15),
  },

  childControl: {
    fontWeight: 400,
    display: 'block',
    width: '100%',
    padding: `${rem(8)} ${theme.spacing.md}`,
    color: theme.colorScheme === 'dark' ? theme.colors.dark[2] : theme.colors.gray[6],
    fontSize: theme.fontSizes.sm,
    borderRadius: theme.radius.sm,
    transition: 'background-color 100ms ease',

    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : theme.colors.gray[0],
      color: theme.colorScheme === 'dark' ? theme.colors.dark[0] : theme.black,
    },
  },

  childActive: {
    backgroundColor: theme.colorScheme === 'dark' 
      ? theme.fn.rgba(theme.colors[theme.primaryColor][7], 0.15)
      : theme.colors[theme.primaryColor][0],
    color: theme.colorScheme === 'dark' 
      ? theme.colors[theme.primaryColor][4] 
      : theme.colors[theme.primaryColor][7],
    fontWeight: 500,

    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark'
        ? theme.fn.rgba(theme.colors[theme.primaryColor][7], 0.25)
        : theme.colors[theme.primaryColor][1],
    },
  },
}));

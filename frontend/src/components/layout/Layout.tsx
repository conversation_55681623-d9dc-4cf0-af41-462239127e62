import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  LayoutDashboard, 
  MessageSquare, 
  BookOpen, 
  Settings, 
  Brain,
  Menu,
  X,
  Spark<PERSON>
} from 'lucide-react'
import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'
import SimpleAIChat from '@/components/ai-chat/SimpleAIChat'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    {
      name: 'ダッシュボード',
      href: '/',
      icon: LayoutDashboard,
      description: '概要とKPI'
    },
    {
      name: 'AI記帳',
      href: '/ai-bookkeeping',
      icon: MessageSquare,
      description: '一句話全自動記帳',
      highlight: true
    },
    {
      name: '仕訳帳',
      href: '/journal-entries',
      icon: BookOpen,
      description: '仕訳の管理'
    },
    {
      name: 'LLM管理',
      href: '/llm-management',
      icon: Brain,
      description: 'AIモデル設定'
    },
    {
      name: '設定',
      href: '/settings',
      icon: Settings,
      description: 'システム設定'
    },
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(href)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: sidebarOpen ? 0 : '-100%',
        }}
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 lg:translate-x-0 lg:static lg:inset-0',
          'lg:block'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold gradient-text">GoldenLedger AI</h1>
                <p className="text-xs text-gray-500">会計智能体</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href)
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    'sidebar-nav-item group relative',
                    active && 'active',
                    item.highlight && 'bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20'
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className={cn(
                    'h-5 w-5',
                    active ? 'text-primary' : 'text-gray-500 group-hover:text-gray-700'
                  )} />
                  <div className="flex-1">
                    <div className={cn(
                      'font-medium',
                      active ? 'text-primary' : 'text-gray-700'
                    )}>
                      {item.name}
                      {item.highlight && (
                        <span className="ml-2 text-xs bg-primary text-white px-1.5 py-0.5 rounded-full">
                          NEW
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {item.description}
                    </div>
                  </div>
                  
                  {active && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full"
                    />
                  )}
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>GoldenLedger — Smart AI-Powered Finance System</p>
              <p className="mt-1">v1.0.0</p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-30 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1 items-center">
              <h2 className="text-lg font-semibold text-gray-900">
                {navigation.find(item => isActive(item.href))?.name || 'ダッシュボード'}
              </h2>
            </div>
            
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* 可以添加用户菜单等 */}
              <div className="text-sm text-gray-600">
                デモ会社
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-8">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>

      {/* AI Chat Component */}
      <SimpleAIChat />
    </div>
  )
}

export default Layout

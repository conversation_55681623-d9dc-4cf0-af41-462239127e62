/**
 * API相关类型定义
 * 定义所有API接口的请求和响应类型
 */

// 基础类型
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// 用户相关类型
export interface User extends BaseEntity {
  email: string;
  username: string;
  name?: string;
  avatar_url?: string;
  role: string;
  is_active: boolean;
  email_verified: boolean;
  last_login_at?: string;
  tenant_roles: UserTenantRole[];
  company_permissions: UserCompanyPermission[];
}

export interface UserTenantRole {
  tenant_id: string;
  tenant_name: string;
  role: string;
  granted_at: string;
  expires_at?: string;
}

export interface UserCompanyPermission {
  company_id: string;
  company_name: string;
  permission_type: string;
  can_read: boolean;
  can_write: boolean;
  can_delete: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  name?: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
  expires_in: number;
}

// 租户相关类型
export interface Tenant extends BaseEntity {
  name: string;
  domain?: string;
  subscription_plan: string;
  is_active: boolean;
  trial_ends_at?: string;
  max_companies: number;
  max_users: number;
  companies_count: number;
  users_count: number;
}

export interface TenantCreate {
  name: string;
  domain?: string;
  subscription_plan?: string;
  max_companies?: number;
  max_users?: number;
}

export interface TenantUpdate {
  name?: string;
  domain?: string;
  subscription_plan?: string;
  max_companies?: number;
  max_users?: number;
  is_active?: boolean;
}

// 公司相关类型
export interface Company extends BaseEntity {
  tenant_id: string;
  name: string;
  tax_number?: string;
  address?: string;
  industry?: string;
  fiscal_year_start?: string;
  fiscal_year_end?: string;
  currency: string;
  is_active: boolean;
  journal_entries_count: number;
  account_subjects_count: number;
  users_count: number;
}

export interface CompanyCreate {
  tenant_id?: string;
  name: string;
  tax_number?: string;
  address?: string;
  industry?: string;
  fiscal_year_start?: string;
  fiscal_year_end?: string;
  currency?: string;
}

export interface CompanyStats {
  total_entries: number;
  total_amount: number;
  monthly_entries: number;
  monthly_amount: number;
  active_accounts: number;
  recent_entries: RecentEntry[];
}

export interface RecentEntry {
  id: string;
  date: string;
  description: string;
  amount: number;
  status: string;
}

// 会计科目相关类型
export interface AccountCategory extends BaseEntity {
  tenant_id: string;
  name: string;
  type: string;
  code_prefix: string;
  is_system: boolean;
  sort_order: number;
}

export interface AccountSubject extends BaseEntity {
  company_id: string;
  company_name: string;
  code: string;
  name: string;
  type: string;
  parent_id?: string;
  category_id?: string;
  category_name?: string;
  parent_code?: string;
  parent_name?: string;
  is_active: boolean;
  is_system: boolean;
  children_count: number;
  journal_entries_count: number;
  balance: number;
}

export interface AccountSubjectCreate {
  company_id: string;
  code: string;
  name: string;
  type: string;
  parent_id?: string;
  category_id?: string;
  is_active?: boolean;
}

export interface AccountTreeNode {
  id: string;
  code: string;
  name: string;
  type: string;
  is_active: boolean;
  is_system: boolean;
  balance: number;
  children: AccountTreeNode[];
}

export interface AccountBalance {
  account_id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  debit_total: number;
  credit_total: number;
  balance: number;
  period_start: string;
  period_end: string;
}

// 记账相关类型
export interface JournalEntryLine {
  account_id: string;
  debit_amount: number;
  credit_amount: number;
  description?: string;
  reference?: string;
}

export interface JournalEntryLineResponse extends JournalEntryLine {
  id: string;
  account_code: string;
  account_name: string;
}

export interface JournalEntry extends BaseEntity {
  company_id: string;
  company_name: string;
  entry_date: string;
  description: string;
  reference_number?: string;
  total_amount: number;
  status: string;
  ai_generated: boolean;
  ai_confidence?: number;
  ai_analysis?: string;
  created_by: string;
  created_by_name?: string;
  posted_at?: string;
  posted_by?: string;
  lines: JournalEntryLineResponse[];
}

export interface JournalEntryCreate {
  company_id: string;
  entry_date: string;
  description: string;
  reference_number?: string;
  lines: JournalEntryLine[];
}

// 报表相关类型
export interface BalanceSheetItem {
  account_id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  balance: number;
  parent_id?: string;
  level: number;
}

export interface BalanceSheet {
  company_id: string;
  company_name: string;
  period_start: string;
  period_end: string;
  generated_at: string;
  current_assets: BalanceSheetItem[];
  non_current_assets: BalanceSheetItem[];
  total_assets: number;
  current_liabilities: BalanceSheetItem[];
  non_current_liabilities: BalanceSheetItem[];
  total_liabilities: number;
  equity: BalanceSheetItem[];
  total_equity: number;
}

export interface IncomeStatementItem {
  account_id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  amount: number;
  parent_id?: string;
  level: number;
}

export interface IncomeStatement {
  company_id: string;
  company_name: string;
  period_start: string;
  period_end: string;
  generated_at: string;
  revenue: IncomeStatementItem[];
  total_revenue: number;
  cost_of_sales: IncomeStatementItem[];
  total_cost_of_sales: number;
  expenses: IncomeStatementItem[];
  total_expenses: number;
  gross_profit: number;
  net_profit: number;
}

export interface TrialBalanceItem {
  account_id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  opening_balance: number;
  debit_amount: number;
  credit_amount: number;
  closing_balance: number;
}

export interface TrialBalance {
  company_id: string;
  company_name: string;
  period_start: string;
  period_end: string;
  generated_at: string;
  items: TrialBalanceItem[];
  total_debit: number;
  total_credit: number;
  is_balanced: boolean;
}

// 系统设置相关类型
export interface LLMConfiguration extends BaseEntity {
  tenant_id: string;
  tenant_name: string;
  provider: string;
  model_name: string;
  api_key_masked: string;
  max_tokens: number;
  temperature: number;
  is_active: boolean;
}

export interface LLMConfigurationCreate {
  tenant_id?: string;
  provider: string;
  model_name: string;
  api_key: string;
  max_tokens?: number;
  temperature?: number;
  is_active?: boolean;
}

export interface AuditLog extends BaseEntity {
  tenant_id?: string;
  tenant_name?: string;
  user_id?: string;
  user_name?: string;
  company_id?: string;
  company_name?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

// 查询参数类型
export interface PaginationParams {
  page?: number;
  page_size?: number;
}

export interface JournalEntryFilter extends PaginationParams {
  company_id?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
  account_id?: string;
  created_by?: string;
  ai_generated?: boolean;
  search?: string;
}

export interface AccountSubjectFilter extends PaginationParams {
  company_id?: string;
  type?: string;
  parent_id?: string;
  is_active?: boolean;
  search?: string;
}

export interface UserFilter extends PaginationParams {
  tenant_id?: string;
}

export interface AuditLogFilter extends PaginationParams {
  tenant_id?: string;
  user_id?: string;
  company_id?: string;
  action?: string;
  resource_type?: string;
  start_date?: string;
  end_date?: string;
}

// 响应包装类型
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// 错误类型
export interface ApiError {
  detail: string;
  code?: string;
  field?: string;
}

import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'

import Layout from '@/components/layout/Layout'
import Dashboard from '@/pages/Dashboard'
import AIBookkeeping from '@/pages/AIBookkeeping'
import JournalEntries from '@/pages/JournalEntries'
import LLMManagement from '@/pages/LLMManagement'
import Settings from '@/pages/Settings'
import PricingPage from '@/pages/PricingPage' // 导入新的定价页面

function App() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="min-h-screen bg-gray-50 dark:bg-gray-900"
    >
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/ai-bookkeeping" element={<AIBookkeeping />} />
          <Route path="/journal-entries" element={<JournalEntries />} />
          <Route path="/llm-management" element={<LLMManagement />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/pricing" element={<PricingPage />} /> {/* 添加定价页面的路由 */}
        </Routes>
      </Layout>
    </motion.div>
  )
}

export default App

/**
 * GoldenLedger风格的专业UI界面
 * 模仿 https://ledger.goldenorangetech.com/ 的设计风格
 */

import React, { useEffect, useState } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from './lib/react-query';
import { authApi, companyApi } from './lib/api-client';

// GoldenLedger风格登录界面
const GoldenLedgerLoginForm: React.FC<{ onLogin: (token: string) => void }> = ({ onLogin }) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await authApi.login({ email, password });
      if (response.data?.access_token) {
        localStorage.setItem('access_token', response.data.access_token);
        onLogin(response.data.access_token);
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* 品牌标识 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-2xl mb-4 shadow-lg">
            <span className="text-2xl">🚀</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <span className="text-orange-500">Golden</span>Ledger
          </h1>
          <p className="text-gray-600">Smart AI-Powered Finance</p>
        </div>

        {/* 登录卡片 */}
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">ログイン</h2>
            <p className="text-gray-600">アカウントにログインしてください</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                メールアドレス
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                placeholder="メールアドレスを入力"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                パスワード
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                placeholder="パスワードを入力"
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 text-white py-3 px-6 rounded-lg font-semibold hover:from-orange-600 hover:to-yellow-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  ログイン中...
                </div>
              ) : (
                'ログイン'
              )}
            </button>
          </form>

          {/* Google登录按钮 */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">または</span>
              </div>
            </div>
            <button className="mt-4 w-full bg-white border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center">
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Googleで始める
            </button>
          </div>

          {/* 演示提示 */}
          <div className="mt-6 p-4 bg-orange-50 rounded-lg border border-orange-200">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <span className="text-orange-500">🎯</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-orange-800">デモアカウント</p>
                <p className="text-sm text-orange-700 mt-1">
                  メール: <EMAIL><br />
                  パスワード: password123
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="text-center mt-8">
          <p className="text-gray-500 text-sm">© 2025 GoldenLedger. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};

// GoldenLedger风格仪表板
const GoldenLedgerDashboard: React.FC = () => {
  const [companies, setCompanies] = useState<any[]>([]);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const userResponse = await authApi.getCurrentUser();
        setUser(userResponse.data);
        const companiesResponse = await companyApi.getCompanies();
        setCompanies(companiesResponse.data || []);
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-16 w-16 border-4 border-orange-200 border-t-orange-500 mb-4"></div>
          <p className="text-gray-600 text-lg">読み込み中...</p>
        </div>
      </div>
    );
  }

  const company = companies[0];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <span className="text-white text-sm">🚀</span>
                </div>
                <div>
                  <h1 className="text-lg font-bold">
                    <span className="text-orange-500">Golden</span>
                    <span className="text-gray-900">Ledger</span>
                  </h1>
                  <p className="text-xs text-gray-500">Smart AI-Powered Finance</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">
                    {user?.name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div className="hidden md:block">
                  <p className="text-gray-900 font-medium">{user?.name}</p>
                  <p className="text-gray-500 text-sm">
                    {user?.role === 'system_admin' ? 'システム管理者' : 'ユーザー'}
                  </p>
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-50 hover:bg-red-100 text-red-600 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 border border-red-200"
              >
                ログアウト
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <span className="text-2xl mr-2">🚀</span>
            <h1 className="text-3xl font-bold text-gray-900">
              <span className="text-orange-500">Golden</span>Ledger — Smart AI-Powered Finance System
            </h1>
          </div>
          <p className="text-gray-600 text-lg">企業級AI全自動記帳解決方案</p>
          
          {/* 状态指示器 */}
          <div className="flex items-center space-x-6 mt-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              <span className="text-sm text-gray-600">AI引擎運行中</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-600">8個模組已激活</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-600">多語言支援</span>
            </div>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[
            { 
              title: 'AI處理交易數', 
              value: '156', 
              icon: '💬',
              color: 'from-blue-500 to-cyan-500',
              bgColor: 'from-blue-50 to-cyan-50'
            },
            { 
              title: 'AI準確率', 
              value: '94%', 
              icon: '🎯',
              color: 'from-green-500 to-emerald-500',
              bgColor: 'from-green-50 to-emerald-50'
            },
            { 
              title: '平均處理時間', 
              value: '2.3s', 
              icon: '⚡',
              color: 'from-yellow-500 to-orange-500',
              bgColor: 'from-yellow-50 to-orange-50'
            },
            { 
              title: '合規性評分', 
              value: '87%', 
              icon: '🛡️',
              color: 'from-purple-500 to-pink-500',
              bgColor: 'from-purple-50 to-pink-50'
            }
          ].map((stat, index) => (
            <div key={index} className={`bg-gradient-to-br ${stat.bgColor} rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300`}>
              <div className="flex items-center justify-between mb-4">
                <div className="text-2xl">{stat.icon}</div>
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-lg flex items-center justify-center`}>
                  <div className="w-6 h-6 bg-white rounded opacity-30"></div>
                </div>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
                <p className="text-gray-600 text-sm">{stat.title}</p>
              </div>
            </div>
          ))}
        </div>

        {/* 功能模块 */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <span className="mr-2">🎯</span>
            完整功能模組
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                icon: '💬',
                title: 'AI智能記帳',
                description: '一句話自動生成仕訳分錄，支援自然語言理解',
                links: ['🎯 交互式演示', '📊 查看記錄', '📖 基礎演示']
              },
              {
                icon: '📊',
                title: '數據分析中心',
                description: '高級財務分析和實時數據監控',
                links: ['📈 高級儀表盤', '📋 財務報表']
              },
              {
                icon: '📥',
                title: '批量數據處理',
                description: 'CSV、Excel、自然語言批量導入',
                links: ['📤 導入工具', '📊 數據導出']
              },
              {
                icon: '🖥️',
                title: '系統監控',
                description: '實時系統狀態和性能監控',
                links: ['🔍 監控中心', '⚡ 性能監控']
              },
              {
                icon: '🌍',
                title: '多語言支援',
                description: '中文、日語、英語、韓語智能記帳',
                links: ['🗣️ 多語言界面', '🔄 實時翻譯']
              },
              {
                icon: '🔍',
                title: 'AI智能審計',
                description: '自動風險檢測和合規性審計',
                links: ['🕵️ 審計系統', '🛡️ 風險控制']
              }
            ].map((module, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="text-3xl mr-3">{module.icon}</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{module.title}</h3>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">{module.description}</p>
                <div className="space-y-2">
                  {module.links.map((link, linkIndex) => (
                    <button key={linkIndex} className="block w-full text-left text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 px-2 py-1 rounded transition-colors duration-200">
                      {link}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 系统状态 */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">✅</span>
            </div>
            
            <h3 className="text-2xl font-bold text-gray-900 mb-2">システムテスト成功！</h3>
            <p className="text-gray-600 text-lg mb-6">すべてのコア機能が正常に動作しています</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {['バックエンドAPI', 'ユーザー認証', 'データ取得', 'インターフェース'].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                  <p className="text-gray-600 text-sm">{item}</p>
                </div>
              ))}
            </div>

            {company && (
              <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl p-6 border border-orange-200">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">🏢 企業情報</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">企業名</p>
                    <p className="font-medium text-gray-900">{company.name}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">記帳記録</p>
                    <p className="font-medium text-gray-900">{company.journal_entries_count}件</p>
                  </div>
                  <div>
                    <p className="text-gray-600">会計科目</p>
                    <p className="font-medium text-gray-900">{company.account_subjects_count}項目</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

const GoldenLedgerUI: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  const handleLogin = (token: string) => {
    setIsAuthenticated(true);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="GoldenLedgerUI">
        {isAuthenticated ? (
          <GoldenLedgerDashboard />
        ) : (
          <GoldenLedgerLoginForm onLogin={handleLogin} />
        )}
      </div>
    </QueryClientProvider>
  );
};

export default GoldenLedgerUI;

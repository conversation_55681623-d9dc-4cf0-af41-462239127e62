/**
 * 认证状态管理Store
 * 管理用户登录状态、用户信息等
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest, RegisterRequest, LoginResponse } from '../types/api';
import { authApi } from '../lib/api-client';
// import { toast } from 'react-hot-toast';

interface AuthState {
  // 状态
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // 操作
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // 用户登录
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true });
          
          const response: LoginResponse = await authApi.login(credentials);
          
          // 保存token和用户信息
          localStorage.setItem('access_token', response.access_token);
          
          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            isLoading: false,
          });
          
          console.log('登录成功');
        } catch (error: any) {
          set({ isLoading: false });
          const message = error.response?.data?.detail || '登录失败';
          console.error(message);
          throw error;
        }
      },

      // 用户注册
      register: async (userData: RegisterRequest) => {
        try {
          set({ isLoading: true });
          
          const response: LoginResponse = await authApi.register(userData);
          
          // 注册成功后自动登录
          localStorage.setItem('access_token', response.access_token);
          
          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            isLoading: false,
          });
          
          console.log('注册成功');
        } catch (error: any) {
          set({ isLoading: false });
          const message = error.response?.data?.detail || '注册失败';
          console.error(message);
          throw error;
        }
      },

      // 用户退出
      logout: () => {
        try {
          // 调用后端退出接口
          authApi.logout().catch(() => {
            // 忽略退出接口错误，继续清理本地状态
          });
        } catch (error) {
          // 忽略错误
        } finally {
          // 清理本地状态
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
          
          console.log('已退出登录');
        }
      },

      // 获取当前用户信息
      getCurrentUser: async () => {
        try {
          const token = localStorage.getItem('access_token');
          if (!token) {
            return;
          }

          set({ isLoading: true });
          
          const user: User = await authApi.getCurrentUser();
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error: any) {
          // 如果获取用户信息失败，清理认证状态
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      // 更新用户信息
      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData };
          set({ user: updatedUser });
        }
      },

      // 清理错误状态
      clearError: () => {
        // 预留给错误状态管理
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// 初始化认证状态
export const initializeAuth = async () => {
  const token = localStorage.getItem('access_token');
  if (token) {
    try {
      await useAuthStore.getState().getCurrentUser();
    } catch (error) {
      // 如果token无效，清理状态
      useAuthStore.getState().logout();
    }
  }
};

// 检查用户权限的辅助函数
export const hasPermission = (
  user: User | null,
  requiredRole: string,
  tenantId?: string
): boolean => {
  if (!user) return false;
  
  // 系统管理员拥有所有权限
  if (user.role === 'system_admin') return true;
  
  // 检查租户角色
  if (tenantId) {
    const tenantRole = user.tenant_roles.find(
      (role) => role.tenant_id === tenantId
    );
    if (tenantRole && tenantRole.role === requiredRole) return true;
  }
  
  // 检查全局角色
  return user.role === requiredRole;
};

// 检查公司权限的辅助函数
export const hasCompanyPermission = (
  user: User | null,
  companyId: string,
  permissionType: string,
  action: 'read' | 'write' | 'delete'
): boolean => {
  if (!user) return false;
  
  // 系统管理员拥有所有权限
  if (user.role === 'system_admin') return true;
  
  // 查找公司权限
  const permission = user.company_permissions.find(
    (perm) => perm.company_id === companyId && perm.permission_type === permissionType
  );
  
  if (!permission) return false;
  
  switch (action) {
    case 'read':
      return permission.can_read;
    case 'write':
      return permission.can_write;
    case 'delete':
      return permission.can_delete;
    default:
      return false;
  }
};

// 获取用户的租户ID
export const getUserTenantId = (user: User | null): string | null => {
  if (!user) return null;
  
  // 如果用户有租户角色，返回第一个活跃的租户ID
  const activeTenantRole = user.tenant_roles.find((role) => role.tenant_id);
  return activeTenantRole?.tenant_id || null;
};

// 获取用户在指定租户的角色
export const getUserTenantRole = (user: User | null, tenantId: string): string | null => {
  if (!user) return null;
  
  const tenantRole = user.tenant_roles.find((role) => role.tenant_id === tenantId);
  return tenantRole?.role || null;
};

// 检查用户是否可以访问指定租户
export const canAccessTenant = (user: User | null, tenantId: string): boolean => {
  if (!user) return false;
  
  // 系统管理员可以访问所有租户
  if (user.role === 'system_admin') return true;
  
  // 检查用户是否有该租户的角色
  return user.tenant_roles.some((role) => role.tenant_id === tenantId);
};

export default useAuthStore;

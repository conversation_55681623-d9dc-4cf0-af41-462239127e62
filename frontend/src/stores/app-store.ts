/**
 * 应用全局状态管理Store
 * 管理当前选中的租户、公司等全局状态
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Tenant, Company } from '../types/api';
import { tenantApi, companyApi } from '../lib/api-client';
// import { toast } from 'react-hot-toast';

interface AppState {
  // 当前状态
  currentTenant: Tenant | null;
  currentCompany: Company | null;
  tenants: Tenant[];
  companies: Company[];
  
  // UI状态
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  
  // 加载状态
  isLoadingTenants: boolean;
  isLoadingCompanies: boolean;
  
  // 操作
  setCurrentTenant: (tenant: Tenant | null) => void;
  setCurrentCompany: (company: Company | null) => void;
  loadTenants: () => Promise<void>;
  loadCompanies: (tenantId?: string) => Promise<void>;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh' | 'en') => void;
  reset: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentTenant: null,
      currentCompany: null,
      tenants: [],
      companies: [],
      
      // UI状态
      sidebarCollapsed: false,
      theme: 'light',
      language: 'zh',
      
      // 加载状态
      isLoadingTenants: false,
      isLoadingCompanies: false,

      // 设置当前租户
      setCurrentTenant: (tenant: Tenant | null) => {
        set({ 
          currentTenant: tenant,
          currentCompany: null, // 切换租户时清空当前公司
          companies: [] // 清空公司列表
        });
        
        // 如果选择了租户，加载该租户下的公司
        if (tenant) {
          get().loadCompanies(tenant.id);
        }
      },

      // 设置当前公司
      setCurrentCompany: (company: Company | null) => {
        set({ currentCompany: company });
      },

      // 加载租户列表
      loadTenants: async () => {
        try {
          set({ isLoadingTenants: true });
          
          const tenants: Tenant[] = await tenantApi.getTenants();
          
          set({ 
            tenants,
            isLoadingTenants: false 
          });
          
          // 如果没有当前租户且有可用租户，自动选择第一个
          const { currentTenant } = get();
          if (!currentTenant && tenants.length > 0) {
            get().setCurrentTenant(tenants[0]);
          }
        } catch (error: any) {
          set({ isLoadingTenants: false });
          const message = error.response?.data?.detail || '加载租户列表失败';
          console.error(message);
        }
      },

      // 加载公司列表
      loadCompanies: async (tenantId?: string) => {
        try {
          set({ isLoadingCompanies: true });
          
          const params = tenantId ? { tenant_id: tenantId } : {};
          const companies: Company[] = await companyApi.getCompanies(params);
          
          set({ 
            companies,
            isLoadingCompanies: false 
          });
          
          // 如果没有当前公司且有可用公司，自动选择第一个
          const { currentCompany } = get();
          if (!currentCompany && companies.length > 0) {
            get().setCurrentCompany(companies[0]);
          }
        } catch (error: any) {
          set({ isLoadingCompanies: false });
          const message = error.response?.data?.detail || '加载公司列表失败';
          console.error(message);
        }
      },

      // 设置侧边栏折叠状态
      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed });
      },

      // 设置主题
      setTheme: (theme: 'light' | 'dark') => {
        set({ theme });
        
        // 更新HTML类名
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      },

      // 设置语言
      setLanguage: (language: 'zh' | 'en') => {
        set({ language });
      },

      // 重置状态
      reset: () => {
        set({
          currentTenant: null,
          currentCompany: null,
          tenants: [],
          companies: [],
          isLoadingTenants: false,
          isLoadingCompanies: false,
        });
      },
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        currentTenant: state.currentTenant,
        currentCompany: state.currentCompany,
        sidebarCollapsed: state.sidebarCollapsed,
        theme: state.theme,
        language: state.language,
      }),
    }
  )
);

// 初始化应用状态
export const initializeApp = async () => {
  const { loadTenants, setTheme, theme } = useAppStore.getState();
  
  // 设置主题
  setTheme(theme);
  
  // 加载租户列表
  try {
    await loadTenants();
  } catch (error) {
    console.error('Failed to initialize app:', error);
  }
};

// 获取当前租户ID的辅助函数
export const getCurrentTenantId = (): string | null => {
  const { currentTenant } = useAppStore.getState();
  return currentTenant?.id || null;
};

// 获取当前公司ID的辅助函数
export const getCurrentCompanyId = (): string | null => {
  const { currentCompany } = useAppStore.getState();
  return currentCompany?.id || null;
};

// 检查是否有当前租户的辅助函数
export const hasCurrentTenant = (): boolean => {
  const { currentTenant } = useAppStore.getState();
  return currentTenant !== null;
};

// 检查是否有当前公司的辅助函数
export const hasCurrentCompany = (): boolean => {
  const { currentCompany } = useAppStore.getState();
  return currentCompany !== null;
};

// 根据ID查找租户的辅助函数
export const findTenantById = (tenantId: string): Tenant | null => {
  const { tenants } = useAppStore.getState();
  return tenants.find(tenant => tenant.id === tenantId) || null;
};

// 根据ID查找公司的辅助函数
export const findCompanyById = (companyId: string): Company | null => {
  const { companies } = useAppStore.getState();
  return companies.find(company => company.id === companyId) || null;
};

// 刷新当前租户信息的辅助函数
export const refreshCurrentTenant = async () => {
  const { currentTenant, loadTenants } = useAppStore.getState();
  if (currentTenant) {
    await loadTenants();
    // 重新设置当前租户以获取最新信息
    const updatedTenant = findTenantById(currentTenant.id);
    if (updatedTenant) {
      useAppStore.getState().setCurrentTenant(updatedTenant);
    }
  }
};

// 刷新当前公司信息的辅助函数
export const refreshCurrentCompany = async () => {
  const { currentCompany, currentTenant, loadCompanies } = useAppStore.getState();
  if (currentCompany && currentTenant) {
    await loadCompanies(currentTenant.id);
    // 重新设置当前公司以获取最新信息
    const updatedCompany = findCompanyById(currentCompany.id);
    if (updatedCompany) {
      useAppStore.getState().setCurrentCompany(updatedCompany);
    }
  }
};

export default useAppStore;

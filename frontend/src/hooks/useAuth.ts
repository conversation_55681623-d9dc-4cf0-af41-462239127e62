import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { notifications } from '@mantine/notifications';

interface User {
  id: string;
  email: string;
  username: string;
  name?: string;
  avatar_url?: string;
  role: string;
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  last_login_at?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<boolean>;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshUser: () => Promise<void>;
}

interface RegisterData {
  email: string;
  username: string;
  password: string;
  name?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // 检查用户权限
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // 系统管理员拥有所有权限
    if (user.role === 'system_admin') return true;
    
    // 这里应该从用户的权限列表中检查
    // 暂时简化处理
    const rolePermissions: Record<string, string[]> = {
      tenant_admin: [
        'dashboard.read', 'journal.read', 'journal.write', 'journal.ai',
        'company.read', 'company.write', 'account.read', 'account.write',
        'report.read', 'user.read', 'user.manage', 'user.invite',
        'settings.read', 'settings.ai', 'audit.read'
      ],
      accountant: [
        'dashboard.read', 'journal.read', 'journal.write', 'journal.ai',
        'company.read', 'account.read', 'report.read'
      ],
      cashier: [
        'dashboard.read', 'journal.read', 'journal.write',
        'company.read', 'account.read'
      ],
      viewer: [
        'dashboard.read', 'journal.read', 'company.read', 'account.read', 'report.read'
      ]
    };

    const userPermissions = rolePermissions[user.role] || [];
    return userPermissions.includes(permission);
  };

  // 检查用户角色
  const hasRole = (role: string): boolean => {
    return user?.role === role;
  };

  // 登录
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        
        // 保存token到localStorage
        localStorage.setItem('auth_token', data.token);
        
        notifications.show({
          title: '登录成功',
          message: `欢迎回来，${data.user.name || data.user.username}！`,
          color: 'green',
        });
        
        return true;
      } else {
        const error = await response.json();
        notifications.show({
          title: '登录失败',
          message: error.message || '用户名或密码错误',
          color: 'red',
        });
        return false;
      }
    } catch (error) {
      notifications.show({
        title: '登录失败',
        message: '网络错误，请稍后重试',
        color: 'red',
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const register = async (userData: RegisterData): Promise<boolean> => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        
        localStorage.setItem('auth_token', data.token);
        
        notifications.show({
          title: '注册成功',
          message: '账户创建成功，欢迎使用！',
          color: 'green',
        });
        
        return true;
      } else {
        const error = await response.json();
        notifications.show({
          title: '注册失败',
          message: error.message || '注册失败，请稍后重试',
          color: 'red',
        });
        return false;
      }
    } catch (error) {
      notifications.show({
        title: '注册失败',
        message: '网络错误，请稍后重试',
        color: 'red',
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 退出登录
  const logout = async (): Promise<void> => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      localStorage.removeItem('auth_token');
      notifications.show({
        title: '已退出登录',
        message: '您已安全退出系统',
        color: 'blue',
      });
    }
  };

  // 刷新用户信息
  const refreshUser = async (): Promise<void> => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) return;

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        // Token无效，清除登录状态
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    } catch (error) {
      console.error('Refresh user error:', error);
    }
  };

  // 初始化时检查登录状态
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        await refreshUser();
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        register,
        hasPermission,
        hasRole,
        refreshUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

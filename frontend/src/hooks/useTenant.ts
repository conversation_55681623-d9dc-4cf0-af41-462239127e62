import { useState, useEffect, useContext, createContext, ReactNode } from 'react';
import { notifications } from '@mantine/notifications';

interface Tenant {
  id: string;
  name: string;
  domain: string;
  subscription_plan: string;
  is_active: boolean;
  trial_ends_at?: string;
  created_at: string;
}

interface Company {
  id: string;
  tenant_id: string;
  name: string;
  tax_number?: string;
  address?: string;
  industry?: string;
  fiscal_year_start?: string;
  fiscal_year_end?: string;
  currency: string;
  is_active: boolean;
  created_at: string;
}

interface TenantContextType {
  currentTenant: Tenant | null;
  currentCompany: Company | null;
  tenants: Tenant[];
  companies: Company[];
  loading: boolean;
  switchTenant: (tenantId: string) => Promise<void>;
  switchCompany: (companyId: string) => Promise<void>;
  createCompany: (companyData: CreateCompanyData) => Promise<Company | null>;
  updateCompany: (companyId: string, companyData: Partial<Company>) => Promise<boolean>;
  refreshTenants: () => Promise<void>;
  refreshCompanies: () => Promise<void>;
}

interface CreateCompanyData {
  name: string;
  tax_number?: string;
  address?: string;
  industry?: string;
  fiscal_year_start?: string;
  fiscal_year_end?: string;
  currency?: string;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export const TenantProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取API请求头
  const getAuthHeaders = () => ({
    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
    'Content-Type': 'application/json',
  });

  // 获取用户的租户列表
  const refreshTenants = async (): Promise<void> => {
    try {
      const response = await fetch('/api/tenants', {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setTenants(data.tenants);
        
        // 如果没有当前租户，设置第一个为当前租户
        if (!currentTenant && data.tenants.length > 0) {
          const savedTenantId = localStorage.getItem('current_tenant_id');
          const tenant = savedTenantId 
            ? data.tenants.find((t: Tenant) => t.id === savedTenantId)
            : data.tenants[0];
          
          if (tenant) {
            setCurrentTenant(tenant);
            localStorage.setItem('current_tenant_id', tenant.id);
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch tenants:', error);
    }
  };

  // 获取当前租户的公司列表
  const refreshCompanies = async (): Promise<void> => {
    if (!currentTenant) return;

    try {
      const response = await fetch(`/api/tenants/${currentTenant.id}/companies`, {
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        const data = await response.json();
        setCompanies(data.companies);
        
        // 如果没有当前公司，设置第一个为当前公司
        if (!currentCompany && data.companies.length > 0) {
          const savedCompanyId = localStorage.getItem('current_company_id');
          const company = savedCompanyId 
            ? data.companies.find((c: Company) => c.id === savedCompanyId)
            : data.companies[0];
          
          if (company) {
            setCurrentCompany(company);
            localStorage.setItem('current_company_id', company.id);
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch companies:', error);
    }
  };

  // 切换租户
  const switchTenant = async (tenantId: string): Promise<void> => {
    try {
      const tenant = tenants.find(t => t.id === tenantId);
      if (!tenant) return;

      setCurrentTenant(tenant);
      setCurrentCompany(null);
      setCompanies([]);
      
      localStorage.setItem('current_tenant_id', tenantId);
      localStorage.removeItem('current_company_id');
      
      // 获取新租户的公司列表
      await refreshCompanies();
      
      notifications.show({
        title: '租户切换成功',
        message: `已切换到 ${tenant.name}`,
        color: 'blue',
      });
    } catch (error) {
      notifications.show({
        title: '切换失败',
        message: '租户切换失败，请稍后重试',
        color: 'red',
      });
    }
  };

  // 切换公司
  const switchCompany = async (companyId: string): Promise<void> => {
    try {
      const company = companies.find(c => c.id === companyId);
      if (!company) return;

      setCurrentCompany(company);
      localStorage.setItem('current_company_id', companyId);
      
      notifications.show({
        title: '公司切换成功',
        message: `已切换到 ${company.name}`,
        color: 'blue',
      });
    } catch (error) {
      notifications.show({
        title: '切换失败',
        message: '公司切换失败，请稍后重试',
        color: 'red',
      });
    }
  };

  // 创建新公司
  const createCompany = async (companyData: CreateCompanyData): Promise<Company | null> => {
    if (!currentTenant) return null;

    try {
      const response = await fetch(`/api/tenants/${currentTenant.id}/companies`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(companyData),
      });

      if (response.ok) {
        const data = await response.json();
        const newCompany = data.company;
        
        setCompanies(prev => [...prev, newCompany]);
        
        notifications.show({
          title: '公司创建成功',
          message: `${newCompany.name} 已创建`,
          color: 'green',
        });
        
        return newCompany;
      } else {
        const error = await response.json();
        notifications.show({
          title: '创建失败',
          message: error.message || '公司创建失败',
          color: 'red',
        });
        return null;
      }
    } catch (error) {
      notifications.show({
        title: '创建失败',
        message: '网络错误，请稍后重试',
        color: 'red',
      });
      return null;
    }
  };

  // 更新公司信息
  const updateCompany = async (companyId: string, companyData: Partial<Company>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/companies/${companyId}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(companyData),
      });

      if (response.ok) {
        const data = await response.json();
        const updatedCompany = data.company;
        
        setCompanies(prev => 
          prev.map(c => c.id === companyId ? updatedCompany : c)
        );
        
        if (currentCompany?.id === companyId) {
          setCurrentCompany(updatedCompany);
        }
        
        notifications.show({
          title: '更新成功',
          message: '公司信息已更新',
          color: 'green',
        });
        
        return true;
      } else {
        const error = await response.json();
        notifications.show({
          title: '更新失败',
          message: error.message || '公司信息更新失败',
          color: 'red',
        });
        return false;
      }
    } catch (error) {
      notifications.show({
        title: '更新失败',
        message: '网络错误，请稍后重试',
        color: 'red',
      });
      return false;
    }
  };

  // 初始化
  useEffect(() => {
    const initTenant = async () => {
      setLoading(true);
      await refreshTenants();
      setLoading(false);
    };

    initTenant();
  }, []);

  // 当租户改变时，刷新公司列表
  useEffect(() => {
    if (currentTenant) {
      refreshCompanies();
    }
  }, [currentTenant]);

  return (
    <TenantContext.Provider
      value={{
        currentTenant,
        currentCompany,
        tenants,
        companies,
        loading,
        switchTenant,
        switchCompany,
        createCompany,
        updateCompany,
        refreshTenants,
        refreshCompanies,
      }}
    >
      {children}
    </TenantContext.Provider>
  );
};

export const useTenant = (): TenantContextType => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

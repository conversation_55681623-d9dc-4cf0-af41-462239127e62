/**
 * 真正漂亮的现代化UI界面
 * 采用最新的设计趋势和视觉效果
 */

import React, { useEffect, useState } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from './lib/react-query';
import { authApi, companyApi } from './lib/api-client';

// 超级漂亮的登录界面
const BeautifulModernLoginForm: React.FC<{ onLogin: (token: string) => void }> = ({ onLogin }) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await authApi.login({ email, password });
      if (response.data?.access_token) {
        localStorage.setItem('access_token', response.data.access_token);
        onLogin(response.data.access_token);
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* 动态背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-pink-600/20 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full opacity-20 blur-3xl animate-bounce" style={{animationDuration: '3s'}}></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full opacity-20 blur-3xl animate-bounce" style={{animationDuration: '4s', animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-10 blur-3xl animate-pulse" style={{animationDuration: '5s'}}></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* 超级漂亮的品牌区域 */}
        <div className="text-center mb-8">
          <div className="relative inline-block mb-6">
            <div className="w-24 h-24 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-purple-500/50 transform hover:scale-110 transition-all duration-300">
              <div className="w-20 h-20 bg-gradient-to-r from-white/20 to-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div className="absolute -inset-4 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-3xl opacity-30 blur-lg animate-pulse"></div>
          </div>
          <h1 className="text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
              智能会计
            </span>
          </h1>
          <p className="text-xl text-white/80 font-light">下一代AI驱动的财务管理平台</p>
        </div>

        {/* 超级漂亮的登录卡片 */}
        <div className="relative">
          <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-3xl blur opacity-75"></div>
          <div className="relative bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20">
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-white mb-3">欢迎回来</h2>
              <p className="text-white/70 text-lg">登录您的账户，开启智能财务之旅</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className="relative">
                  <label className="block text-sm font-medium text-white/90 mb-3">邮箱地址</label>
                  <div className="relative">
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-6 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent backdrop-blur-sm transition-all duration-300 text-lg"
                      placeholder="输入您的邮箱地址"
                    />
                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                      <svg className="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="relative">
                  <label className="block text-sm font-medium text-white/90 mb-3">密码</label>
                  <div className="relative">
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full px-6 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent backdrop-blur-sm transition-all duration-300 text-lg"
                      placeholder="输入您的密码"
                    />
                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                      <svg className="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="relative w-full group"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
                <div className="relative bg-gradient-to-r from-cyan-500 via-blue-600 to-purple-700 text-white py-4 px-8 rounded-2xl font-semibold text-lg hover:from-cyan-600 hover:via-blue-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-2xl transform hover:scale-105">
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
                      登录中...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <span>立即登录</span>
                      <svg className="ml-3 w-6 h-6 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </div>
                  )}
                </div>
              </button>
            </form>

            {/* 演示提示 */}
            <div className="mt-8 p-6 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-2xl border border-cyan-400/30 backdrop-blur-sm">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-lg font-semibold text-white">演示账户</p>
                  <p className="text-white/80 mt-2 leading-relaxed">
                    邮箱: <EMAIL><br />
                    密码: password123
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="text-center mt-8">
          <p className="text-white/60 text-lg">© 2025 智能会计系统. 保留所有权利.</p>
        </div>
      </div>
    </div>
  );
};

// 超级漂亮的仪表板
const BeautifulModernDashboard: React.FC = () => {
  const [companies, setCompanies] = useState<any[]>([]);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const userResponse = await authApi.getCurrentUser();
        setUser(userResponse.data);
        const companiesResponse = await companyApi.getCompanies();
        setCompanies(companiesResponse.data || []);
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-6"></div>
            <div className="absolute inset-0 w-20 h-20 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
          </div>
          <p className="text-2xl font-semibold text-gray-700">加载中...</p>
        </div>
      </div>
    );
  }

  const company = companies[0];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* 超级漂亮的顶部导航 */}
      <nav className="relative">
        <div className="absolute inset-0 bg-white/80 backdrop-blur-xl"></div>
        <div className="relative border-b border-gray-200/50 shadow-lg">
          <div className="max-w-7xl mx-auto px-6 lg:px-8">
            <div className="flex justify-between items-center h-20">
              <div className="flex items-center">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 via-blue-600 to-purple-700 rounded-2xl flex items-center justify-center mr-4 shadow-lg transform hover:scale-110 transition-all duration-300">
                    <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-600 via-blue-700 to-purple-800 bg-clip-text text-transparent">
                      智能会计系统
                    </h1>
                    <p className="text-sm text-gray-600">AI驱动的财务管理平台</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white text-lg font-bold">
                      {user?.name?.charAt(0) || 'A'}
                    </span>
                  </div>
                  <div className="hidden md:block">
                    <p className="text-lg font-semibold text-gray-900">{user?.name}</p>
                    <p className="text-sm text-gray-600">{user?.role === 'system_admin' ? '系统管理员' : '用户'}</p>
                  </div>
                </div>
                <button
                  onClick={handleLogout}
                  className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-10 px-6 lg:px-8">
        {/* 超级漂亮的欢迎区域 */}
        <div className="relative mb-10">
          <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-3xl blur opacity-30"></div>
          <div className="relative bg-gradient-to-r from-cyan-600 via-blue-700 to-purple-800 rounded-3xl p-10 text-white shadow-2xl">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-4xl font-bold mb-4">
                  欢迎回来，{user?.name}！ 🎉
                </h2>
                <p className="text-xl text-blue-100">
                  {company ? `当前企业：${company.name}` : '系统运行正常，一切就绪！'}
                </p>
              </div>
              <div className="hidden md:block">
                <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm animate-pulse">
                  <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 超级漂亮的统计卡片 */}
        {company && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
            {[
              { 
                title: '记账记录', 
                value: company.journal_entries_count, 
                icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
                gradient: 'from-emerald-400 to-cyan-600',
                bgGradient: 'from-emerald-50 to-cyan-50',
                shadowColor: 'shadow-emerald-500/25'
              },
              { 
                title: '会计科目', 
                value: company.account_subjects_count, 
                icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
                gradient: 'from-blue-400 to-indigo-600',
                bgGradient: 'from-blue-50 to-indigo-50',
                shadowColor: 'shadow-blue-500/25'
              },
              { 
                title: '用户数量', 
                value: company.users_count, 
                icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
                gradient: 'from-purple-400 to-pink-600',
                bgGradient: 'from-purple-50 to-pink-50',
                shadowColor: 'shadow-purple-500/25'
              }
            ].map((stat, index) => (
              <div key={index} className="relative group">
                <div className={`absolute -inset-1 bg-gradient-to-r ${stat.gradient} rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300`}></div>
                <div className={`relative bg-gradient-to-br ${stat.bgGradient} rounded-2xl p-8 shadow-xl ${stat.shadowColor} hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border border-white/50`}>
                  <div className="flex items-center">
                    <div className={`w-16 h-16 bg-gradient-to-r ${stat.gradient} rounded-2xl flex items-center justify-center mr-6 shadow-lg`}>
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={stat.icon} />
                      </svg>
                    </div>
                    <div>
                      <p className="text-gray-600 text-lg font-medium mb-1">{stat.title}</p>
                      <p className="text-4xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 超级漂亮的成功状态卡片 */}
        <div className="relative">
          <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 via-cyan-500 to-blue-600 rounded-3xl blur opacity-30"></div>
          <div className="relative bg-white rounded-3xl p-10 shadow-2xl border border-gray-200/50">
            <div className="text-center">
              <div className="relative inline-block mb-8">
                <div className="w-24 h-24 bg-gradient-to-r from-emerald-400 to-cyan-600 rounded-full flex items-center justify-center shadow-2xl">
                  <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="absolute -inset-4 bg-gradient-to-r from-emerald-400 to-cyan-600 rounded-full opacity-30 blur-lg animate-pulse"></div>
              </div>
              
              <h3 className="text-4xl font-bold text-gray-900 mb-4">🎉 系统测试成功！</h3>
              <p className="text-xl text-gray-600 mb-10">所有核心功能运行正常，系统已准备就绪</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-10">
                {['后端API', '用户认证', '数据获取', '界面渲染'].map((item, index) => (
                  <div key={index} className="text-center group">
                    <div className="w-6 h-6 bg-gradient-to-r from-emerald-400 to-cyan-600 rounded-full mx-auto mb-3 shadow-lg group-hover:scale-125 transition-transform duration-300"></div>
                    <p className="text-gray-700 font-medium">{item}</p>
                  </div>
                ))}
              </div>

              <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-2xl p-8 border border-blue-200/50">
                <h4 className="text-2xl font-bold text-gray-900 mb-6">🚀 系统功能特性</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    '多租户架构支持',
                    '用户认证和权限管理', 
                    '公司管理和数据隔离',
                    '记账管理和财务报表',
                    'AI智能记账功能',
                    '现代化响应式界面'
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center text-gray-700 p-3 rounded-lg hover:bg-white/50 transition-colors duration-200">
                      <svg className="w-6 h-6 text-emerald-500 mr-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-lg font-medium">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

const BeautifulModernUI: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  const handleLogin = (token: string) => {
    setIsAuthenticated(true);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="BeautifulModernUI">
        {isAuthenticated ? (
          <BeautifulModernDashboard />
        ) : (
          <BeautifulModernLoginForm onLogin={handleLogin} />
        )}
      </div>
    </QueryClientProvider>
  );
};

export default BeautifulModernUI;

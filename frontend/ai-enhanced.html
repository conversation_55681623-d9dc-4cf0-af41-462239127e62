<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能记账增强版 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .ai-confidence-high { background: linear-gradient(90deg, #10b981, #34d399); }
        .ai-confidence-medium { background: linear-gradient(90deg, #f59e0b, #fbbf24); }
        .ai-confidence-low { background: linear-gradient(90deg, #ef4444, #f87171); }
        .processing-animation {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 登录验证脚本 -->
    <script>
        // 检查用户登录状态
        function checkAuthStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');

            if (!token || !user) {
                // 未登录，重定向到登录页面
                alert('この機能を利用するにはログインが必要です。');
                window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
                return false;
            }

            return true;
        }

        // 页面加载时检查登录状态
        if (!checkAuthStatus()) {
            // 如果未登录，停止页面加载
            document.body.style.display = 'none';
        }
    </script>

    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">
                        <i class="fas fa-robot text-blue-600 mr-2"></i>
                        AI智能记账增强版
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="showStats()" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-chart-bar mr-1"></i>统计
                    </button>
                    <button onclick="showMappings()" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-brain mr-1"></i>AI学习
                    </button>
                    <a href="index.html" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-home mr-1"></i>返回主页
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- AI处理模式选择 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-cogs text-blue-600 mr-2"></i>
                AI处理模式
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label class="relative">
                    <input type="radio" name="aiMode" value="enhanced" checked class="sr-only">
                    <div class="ai-mode-card border-2 border-blue-500 bg-blue-50 rounded-lg p-4 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-blue-900">增强模式</h3>
                                <p class="text-sm text-blue-700">使用最新AI技术，提供最准确的记账分析</p>
                            </div>
                            <i class="fas fa-rocket text-blue-600 text-xl"></i>
                        </div>
                        <div class="mt-2 text-xs text-blue-600">
                            ✓ 智能科目匹配 ✓ 详细分析报告 ✓ 学习优化
                        </div>
                    </div>
                </label>
                <label class="relative">
                    <input type="radio" name="aiMode" value="basic" class="sr-only">
                    <div class="ai-mode-card border-2 border-gray-300 bg-gray-50 rounded-lg p-4 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-gray-700">基础模式</h3>
                                <p class="text-sm text-gray-600">快速处理，适合简单的记账需求</p>
                            </div>
                            <i class="fas fa-bolt text-gray-600 text-xl"></i>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            ✓ 快速响应 ✓ 基础分析 ✓ 稳定可靠
                        </div>
                    </div>
                </label>
            </div>
        </div>

        <!-- AI文本处理区域 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-comment-dots text-green-600 mr-2"></i>
                自然语言记账
            </h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        描述您的交易（支持中文、日文、英文）
                    </label>
                    <textarea 
                        id="transactionText" 
                        rows="4" 
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="例如：今天收到客户田中先生的货款50000日元，通过银行转账支付
或：购买办公用品花费3000円现金支付
或：Received payment of ¥50,000 from customer via bank transfer"></textarea>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button 
                            onclick="processWithAI()" 
                            id="processBtn"
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <i class="fas fa-magic mr-2"></i>
                            AI智能处理
                        </button>
                        <button 
                            onclick="clearForm()" 
                            class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-eraser mr-2"></i>
                            清空
                        </button>
                    </div>
                    <div id="processingIndicator" class="hidden flex items-center text-blue-600">
                        <div class="processing-animation">
                            <i class="fas fa-brain mr-2"></i>
                        </div>
                        AI正在分析中...
                    </div>
                </div>
            </div>
        </div>

        <!-- AI分析结果 -->
        <div id="aiResult" class="hidden bg-white rounded-lg shadow-sm p-6 mb-6 fade-in">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-brain text-purple-600 mr-2"></i>
                AI分析结果
            </h2>
            <div id="aiResultContent"></div>
        </div>

        <!-- 仕訳记录预览 -->
        <div id="journalPreview" class="hidden bg-white rounded-lg shadow-sm p-6 mb-6 fade-in">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-receipt text-indigo-600 mr-2"></i>
                仕訳记录预览
            </h2>
            <div id="journalContent"></div>
        </div>

        <!-- AI统计模态框 -->
        <div id="statsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">AI处理统计</h3>
                    <button onclick="closeModal('statsModal')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="statsContent">加载中...</div>
            </div>
        </div>

        <!-- AI学习映射模态框 -->
        <div id="mappingsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold">AI学习映射</h3>
                    <button onclick="closeModal('mappingsModal')" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="mappingsContent">加载中...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';

        // 模式选择处理
        document.querySelectorAll('input[name="aiMode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.querySelectorAll('.ai-mode-card').forEach(card => {
                    card.classList.remove('border-blue-500', 'bg-blue-50');
                    card.classList.add('border-gray-300', 'bg-gray-50');
                });
                
                if (this.checked) {
                    const card = this.parentElement.querySelector('.ai-mode-card');
                    card.classList.remove('border-gray-300', 'bg-gray-50');
                    card.classList.add('border-blue-500', 'bg-blue-50');
                }
            });
        });

        // AI处理函数
        async function processWithAI() {
            const text = document.getElementById('transactionText').value.trim();
            if (!text) {
                alert('请输入交易描述');
                return;
            }

            const mode = document.querySelector('input[name="aiMode"]:checked').value;
            const processBtn = document.getElementById('processBtn');
            const indicator = document.getElementById('processingIndicator');

            // 显示处理状态
            processBtn.disabled = true;
            processBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
            indicator.classList.remove('hidden');

            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        mode: mode,
                        company_id: 'default'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    displayAIResult(result);
                    displayJournalPreview(result.journal_entry);
                } else {
                    alert('AI处理失败: ' + result.error);
                }
            } catch (error) {
                console.error('AI处理错误:', error);
                alert('AI处理失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                processBtn.disabled = false;
                processBtn.innerHTML = '<i class="fas fa-magic mr-2"></i>AI智能处理';
                indicator.classList.add('hidden');
            }
        }

        // 显示AI分析结果
        function displayAIResult(result) {
            const resultDiv = document.getElementById('aiResult');
            const contentDiv = document.getElementById('aiResultContent');
            
            const confidence = result.confidence || 0;
            const confidenceClass = confidence >= 0.8 ? 'ai-confidence-high' : 
                                  confidence >= 0.6 ? 'ai-confidence-medium' : 'ai-confidence-low';
            const confidenceText = confidence >= 0.8 ? '高' : confidence >= 0.6 ? '中' : '低';

            let analysisHtml = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-700 mb-2">置信度</h4>
                        <div class="flex items-center">
                            <div class="w-full bg-gray-200 rounded-full h-2 mr-3">
                                <div class="${confidenceClass} h-2 rounded-full" style="width: ${confidence * 100}%"></div>
                            </div>
                            <span class="text-sm font-medium">${(confidence * 100).toFixed(1)}% (${confidenceText})</span>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-700 mb-2">处理模式</h4>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${result.performance?.mode === 'enhanced' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                            <i class="fas fa-${result.performance?.mode === 'enhanced' ? 'rocket' : 'bolt'} mr-1"></i>
                            ${result.performance?.mode === 'enhanced' ? '增强模式' : '基础模式'}
                        </span>
                    </div>
                </div>
            `;

            if (result.analysis) {
                analysisHtml += `
                    <div class="space-y-3">
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h4 class="font-semibold text-blue-800 mb-2">交易分析</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div><strong>交易类型:</strong> ${result.analysis.transaction_type || '未识别'}</div>
                                <div><strong>交易类别:</strong> ${result.analysis.category || '未识别'}</div>
                                <div><strong>支付方式:</strong> ${result.analysis.payment_method || '未识别'}</div>
                            </div>
                        </div>
                `;

                if (result.analysis.analysis_reason) {
                    analysisHtml += `
                        <div class="bg-green-50 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-2">分析理由</h4>
                            <p class="text-sm text-green-700">${result.analysis.analysis_reason}</p>
                        </div>
                    `;
                }

                if (result.analysis.suggestions && result.analysis.suggestions.length > 0) {
                    analysisHtml += `
                        <div class="bg-yellow-50 rounded-lg p-4">
                            <h4 class="font-semibold text-yellow-800 mb-2">建议</h4>
                            <ul class="text-sm text-yellow-700 space-y-1">
                                ${result.analysis.suggestions.map(s => `<li>• ${s}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }

                analysisHtml += `</div>`;
            }

            if (result.performance) {
                analysisHtml += `
                    <div class="mt-4 text-xs text-gray-500 text-right">
                        处理时间: ${result.performance.processing_time_ms}ms
                    </div>
                `;
            }

            contentDiv.innerHTML = analysisHtml;
            resultDiv.classList.remove('hidden');
        }

        // 显示仕訳记录预览
        function displayJournalPreview(journalEntry) {
            const previewDiv = document.getElementById('journalPreview');
            const contentDiv = document.getElementById('journalContent');
            
            const previewHtml = `
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-3">基本信息</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>日期:</strong> ${journalEntry.entry_date}</div>
                                <div><strong>时间:</strong> ${journalEntry.entry_time}</div>
                                <div><strong>描述:</strong> ${journalEntry.description}</div>
                                <div><strong>参考号:</strong> ${journalEntry.reference_number}</div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700 mb-3">仕訳记录</h4>
                            <div class="bg-white rounded border p-3">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-green-600">借方</span>
                                    <span class="text-sm font-medium text-red-600">贷方</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="text-sm">
                                        <div class="font-medium">${journalEntry.debit_account}</div>
                                        <div class="text-gray-600">¥${journalEntry.amount.toLocaleString()}</div>
                                    </div>
                                    <div class="text-sm text-right">
                                        <div class="font-medium">${journalEntry.credit_account}</div>
                                        <div class="text-gray-600">¥${journalEntry.amount.toLocaleString()}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end space-x-3">
                        <button onclick="editJournalEntry()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 transition-colors">
                            <i class="fas fa-edit mr-1"></i>编辑
                        </button>
                        <button onclick="saveJournalEntry()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                            <i class="fas fa-save mr-1"></i>保存
                        </button>
                    </div>
                </div>
            `;
            
            contentDiv.innerHTML = previewHtml;
            previewDiv.classList.remove('hidden');
        }

        // 清空表单
        function clearForm() {
            document.getElementById('transactionText').value = '';
            document.getElementById('aiResult').classList.add('hidden');
            document.getElementById('journalPreview').classList.add('hidden');
        }

        // 显示统计
        async function showStats() {
            document.getElementById('statsModal').classList.remove('hidden');
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/stats?company_id=default&days=30`);
                const result = await response.json();
                
                if (result.success) {
                    displayStats(result.data);
                } else {
                    document.getElementById('statsContent').innerHTML = '<p class="text-red-600">加载统计数据失败</p>';
                }
            } catch (error) {
                document.getElementById('statsContent').innerHTML = '<p class="text-red-600">加载统计数据失败: ' + error.message + '</p>';
            }
        }

        // 显示映射
        async function showMappings() {
            document.getElementById('mappingsModal').classList.remove('hidden');
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/mappings?company_id=default`);
                const result = await response.json();
                
                if (result.success) {
                    displayMappings(result.data);
                } else {
                    document.getElementById('mappingsContent').innerHTML = '<p class="text-red-600">加载映射数据失败</p>';
                }
            } catch (error) {
                document.getElementById('mappingsContent').innerHTML = '<p class="text-red-600">加载映射数据失败: ' + error.message + '</p>';
            }
        }

        // 显示统计数据
        function displayStats(stats) {
            if (!stats || stats.length === 0) {
                document.getElementById('statsContent').innerHTML = '<p class="text-gray-600">暂无统计数据</p>';
                return;
            }

            let html = '<div class="space-y-4">';
            stats.forEach(stat => {
                const successRate = stat.total_requests > 0 ? (stat.successful_requests / stat.total_requests * 100).toFixed(1) : 0;
                html += `
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-semibold">${stat.date} - ${stat.processing_mode}模式</h4>
                            <span class="text-sm text-gray-600">${stat.total_requests}次请求</span>
                        </div>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div>
                                <div class="text-gray-600">成功率</div>
                                <div class="font-medium text-green-600">${successRate}%</div>
                            </div>
                            <div>
                                <div class="text-gray-600">平均置信度</div>
                                <div class="font-medium">${stat.avg_confidence ? (stat.avg_confidence * 100).toFixed(1) + '%' : 'N/A'}</div>
                            </div>
                            <div>
                                <div class="text-gray-600">平均处理时间</div>
                                <div class="font-medium">${stat.avg_processing_time ? Math.round(stat.avg_processing_time) + 'ms' : 'N/A'}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            document.getElementById('statsContent').innerHTML = html;
        }

        // 显示映射数据
        function displayMappings(mappings) {
            if (!mappings || mappings.length === 0) {
                document.getElementById('mappingsContent').innerHTML = '<p class="text-gray-600">暂无学习映射数据</p>';
                return;
            }

            let html = '<div class="space-y-3">';
            mappings.forEach(mapping => {
                html += `
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium">${mapping.keyword}</div>
                                <div class="text-sm text-gray-600">${mapping.account_name} (${mapping.account_type})</div>
                            </div>
                            <div class="text-right text-sm">
                                <div class="text-gray-600">置信度: ${(mapping.confidence * 100).toFixed(1)}%</div>
                                <div class="text-gray-600">使用次数: ${mapping.usage_count}</div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            document.getElementById('mappingsContent').innerHTML = html;
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        // 编辑仕訳记录
        function editJournalEntry() {
            alert('编辑功能开发中...');
        }

        // 保存仕訳记录
        function saveJournalEntry() {
            alert('记录已保存到数据库');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const statsModal = document.getElementById('statsModal');
            const mappingsModal = document.getElementById('mappingsModal');
            if (event.target === statsModal) {
                statsModal.classList.add('hidden');
            }
            if (event.target === mappingsModal) {
                mappingsModal.classList.add('hidden');
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-xl shadow-lg p-8 text-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">
                    🚀 GoldenLedger — Smart AI-Powered Finance System
                </h1>
                <p class="text-gray-600 mb-6">
                    前端环境测试页面
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 class="font-semibold text-green-800">✅ HTML</h3>
                        <p class="text-sm text-green-600">页面正常加载</p>
                    </div>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 class="font-semibold text-blue-800">✅ CSS</h3>
                        <p class="text-sm text-blue-600">Tailwind CSS正常</p>
                    </div>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-yellow-800">⚠️ 注意</h3>
                    <p class="text-sm text-yellow-600">
                        如果您看到这个页面，说明基础HTML/CSS环境正常。<br>
                        React应用可能需要额外配置。
                    </p>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600">
                    <p><strong>下一步:</strong></p>
                    <p>1. 在终端运行: <code class="bg-gray-100 px-2 py-1 rounded">cd frontend && npm run dev</code></p>
                    <p>2. 访问: <code class="bg-gray-100 px-2 py-1 rounded">http://localhost:3000</code></p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        console.log('🎉 前端测试页面加载成功！');
        
        // 检查是否可以访问localhost:3000
        fetch('http://localhost:3000')
            .then(response => {
                if (response.ok) {
                    console.log('✅ React应用正在运行');
                    document.body.insertAdjacentHTML('beforeend', 
                        '<div class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg">React应用正在运行！</div>'
                    );
                } else {
                    console.log('⚠️ React应用未运行');
                }
            })
            .catch(error => {
                console.log('⚠️ React应用未运行:', error);
            });
    </script>
</body>
</html>

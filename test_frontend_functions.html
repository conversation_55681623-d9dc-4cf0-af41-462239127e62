<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-xl font-bold mb-4">前端功能测试</h1>
        
        <div class="space-y-4">
            <button onclick="testEditFunction()" 
                    class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                测试编辑功能
            </button>
            
            <button onclick="testDeleteFunction()" 
                    class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                测试删除功能
            </button>
            
            <button onclick="testLoadEntries()" 
                    class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                测试加载记录
            </button>
        </div>
        
        <div id="result" class="mt-6 p-4 bg-gray-100 rounded min-h-[100px]">
            点击按钮测试功能...
        </div>
    </div>

    <script>
        // 测试编辑功能
        async function testEditFunction() {
            const result = document.getElementById('result');
            result.innerHTML = '测试编辑功能...';
            
            try {
                // 获取第一条记录
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                
                if (entries.length === 0) {
                    result.innerHTML = '❌ 没有记录可以测试';
                    return;
                }
                
                const testEntry = entries[0];
                result.innerHTML = `
                    <div class="space-y-2">
                        <div><strong>测试记录:</strong> ${testEntry.id}</div>
                        <div><strong>原描述:</strong> ${testEntry.description}</div>
                        <div><strong>原金额:</strong> ¥${testEntry.amount}</div>
                        <div class="text-blue-600">正在测试编辑...</div>
                    </div>
                `;
                
                // 模拟编辑
                const editData = {
                    entry_date: testEntry.entry_date || '2025-07-11',
                    entry_time: testEntry.entry_time || '12:00:00',
                    description: `【测试编辑】${testEntry.description}`,
                    debit_account: testEntry.debit_account || '事務用品費',
                    credit_account: testEntry.credit_account || '現金',
                    amount: parseFloat(testEntry.amount || 0) + 0.01,
                    reference_number: testEntry.reference_number || ''
                };
                
                const editResponse = await fetch(`/journal-entries/${testEntry.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(editData)
                });
                
                if (editResponse.ok) {
                    const editResult = await editResponse.json();
                    result.innerHTML = `
                        <div class="space-y-2">
                            <div class="text-green-600"><strong>✅ 编辑成功!</strong></div>
                            <div><strong>记录ID:</strong> ${testEntry.id}</div>
                            <div><strong>新描述:</strong> ${editData.description}</div>
                            <div><strong>新金额:</strong> ¥${editData.amount}</div>
                            <div><strong>响应:</strong> ${editResult.message}</div>
                        </div>
                    `;
                } else {
                    result.innerHTML = `❌ 编辑失败: ${editResponse.status}`;
                }
                
            } catch (error) {
                result.innerHTML = `❌ 编辑测试失败: ${error.message}`;
            }
        }
        
        // 测试删除功能
        async function testDeleteFunction() {
            const result = document.getElementById('result');
            result.innerHTML = '测试删除功能...';
            
            try {
                // 创建一个测试记录
                const testRecord = {
                    id: `TEST_DELETE_${Date.now()}`,
                    company_id: 'default',
                    entry_date: '2025-07-11',
                    entry_time: '23:59:59',
                    description: '【测试删除】临时记录',
                    debit_account: '测试科目',
                    credit_account: '现金',
                    amount: 1.0,
                    reference_number: 'DELETE_TEST',
                    ai_generated: false
                };
                
                result.innerHTML = `
                    <div class="space-y-2">
                        <div><strong>创建测试记录:</strong> ${testRecord.id}</div>
                        <div class="text-blue-600">正在创建...</div>
                    </div>
                `;
                
                // 创建记录
                const createResponse = await fetch('/journal-entries/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testRecord)
                });
                
                if (!createResponse.ok) {
                    result.innerHTML = `❌ 创建测试记录失败: ${createResponse.status}`;
                    return;
                }
                
                result.innerHTML = `
                    <div class="space-y-2">
                        <div><strong>测试记录已创建:</strong> ${testRecord.id}</div>
                        <div class="text-blue-600">正在删除...</div>
                    </div>
                `;
                
                // 删除记录
                const deleteResponse = await fetch(`/journal-entries/${testRecord.id}`, {
                    method: 'DELETE'
                });
                
                if (deleteResponse.ok) {
                    const deleteResult = await deleteResponse.json();
                    result.innerHTML = `
                        <div class="space-y-2">
                            <div class="text-green-600"><strong>✅ 删除成功!</strong></div>
                            <div><strong>记录ID:</strong> ${testRecord.id}</div>
                            <div><strong>响应:</strong> ${deleteResult.message}</div>
                        </div>
                    `;
                } else {
                    result.innerHTML = `❌ 删除失败: ${deleteResponse.status}`;
                }
                
            } catch (error) {
                result.innerHTML = `❌ 删除测试失败: ${error.message}`;
            }
        }
        
        // 测试加载记录
        async function testLoadEntries() {
            const result = document.getElementById('result');
            result.innerHTML = '测试加载记录...';
            
            try {
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                
                result.innerHTML = `
                    <div class="space-y-2">
                        <div class="text-green-600"><strong>✅ 加载成功!</strong></div>
                        <div><strong>记录总数:</strong> ${entries.length}</div>
                        <div><strong>最近记录:</strong></div>
                        <div class="bg-white p-2 rounded border max-h-40 overflow-auto">
                            ${entries.slice(0, 3).map(entry => `
                                <div class="text-sm border-b pb-1 mb-1">
                                    <div><strong>ID:</strong> ${entry.id}</div>
                                    <div><strong>描述:</strong> ${entry.description}</div>
                                    <div><strong>金额:</strong> ¥${entry.amount}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
                
            } catch (error) {
                result.innerHTML = `❌ 加载测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>

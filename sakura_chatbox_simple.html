<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sakura Chatbox - Simple Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --sakura-pink: #FF69B4;
            --sakura-light-pink: #FFB6C1;
            --sakura-green: #98FB98;
            --sakura-gold: #FFD700;
            --sakura-white: #FFFFFF;
            --sakura-gray: #F8F9FA;
            --sakura-text: #333333;
            --sakura-shadow: rgba(255, 105, 180, 0.2);
            --sakura-border-radius: 16px;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
        }

        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .chatbox-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 380px;
            height: 600px;
            background: linear-gradient(135deg, var(--sakura-pink) 0%, var(--sakura-light-pink) 50%, var(--sakura-green) 100%);
            border-radius: var(--sakura-border-radius);
            box-shadow: 0 20px 40px var(--sakura-shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .chatbox-header {
            background: linear-gradient(135deg, var(--sakura-pink), var(--sakura-light-pink));
            padding: 16px 20px;
            border-radius: var(--sakura-border-radius) var(--sakura-border-radius) 0 0;
            cursor: move;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .avatar-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sakura-avatar {
            width: 40px;
            height: 40px;
            background: var(--sakura-white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .sakura-icon {
            width: 24px;
            height: 24px;
            color: var(--sakura-pink);
        }

        .user-info {
            color: var(--sakura-white);
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            line-height: 1.2;
        }

        .online-status {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .online-status::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #00FF00;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            color: var(--sakura-white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .control-btn svg {
            width: 16px;
            height: 16px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: var(--sakura-white);
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message-container {
            display: flex;
            gap: 12px;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            flex-shrink: 0;
        }

        .sakura-avatar-small {
            width: 32px;
            height: 32px;
            background: var(--sakura-white);
            border: 2px solid var(--sakura-pink);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sakura-icon-small {
            width: 16px;
            height: 16px;
            color: var(--sakura-pink);
        }

        .message-bubble {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
            background: var(--sakura-gray);
            color: var(--sakura-text);
            border-bottom-left-radius: 6px;
        }

        .quick-actions {
            padding: 16px 20px;
            background: var(--sakura-white);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-btn {
            background: linear-gradient(135deg, var(--sakura-gold), #FFA500);
            color: var(--sakura-white);
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .quick-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
        }

        .input-area {
            padding: 16px 20px;
            background: var(--sakura-white);
            border-top: 1px solid rgba(255, 105, 180, 0.1);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid var(--sakura-gray);
            border-radius: 24px;
            font-size: 16px;
            outline: none;
            transition: all 0.2s ease;
            background: var(--sakura-gray);
        }

        .message-input:focus {
            border-color: var(--sakura-pink);
            background: var(--sakura-white);
        }

        .send-btn {
            width: 44px;
            height: 44px;
            background: var(--sakura-pink);
            border: none;
            border-radius: 50%;
            color: var(--sakura-white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px var(--sakura-shadow);
        }

        .send-btn:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 6px 16px var(--sakura-shadow);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .send-btn svg {
            width: 20px;
            height: 20px;
        }

        .demo-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .demo-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            margin: 4px;
            transition: all 0.2s ease;
        }

        .demo-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 105, 180, 0.3);
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .chatbox-container {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                border-radius: 0;
            }
            
            .chatbox-header {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1 class="text-4xl font-bold mb-4">🌸 Sakura Chatbox</h1>
        <p class="text-xl">GoldenLedger AI会計アシスタント</p>
    </div>

    <div class="demo-controls">
        <h3 class="text-lg font-bold mb-4">Demo Controls</h3>
        <button class="demo-btn" onclick="showChatbox()">💬 Show</button>
        <button class="demo-btn" onclick="hideChatbox()">🙈 Hide</button>
        <button class="demo-btn" onclick="minimizeChatbox()">➖ Minimize</button>
        <button class="demo-btn" onclick="testMessage()">✨ Test</button>
    </div>

    <!-- Chatbox -->
    <div id="sakura-chatbox" class="chatbox-container">
        <!-- Header -->
        <div class="chatbox-header">
            <div class="header-content">
                <div class="avatar-section">
                    <div class="sakura-avatar">
                        <svg class="sakura-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                    <div class="user-info">
                        <h3 class="user-name">さくらちゃん</h3>
                        <span class="online-status">オンライン</span>
                    </div>
                </div>
                <div class="header-controls">
                    <button class="control-btn" onclick="minimizeChatbox()" title="最小化">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </button>
                    <button class="control-btn" onclick="hideChatbox()" title="閉じる">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages Area -->
        <div class="chat-messages" id="chat-messages">
            <!-- Welcome Message -->
            <div class="message-container">
                <div class="message-avatar">
                    <div class="sakura-avatar-small">
                        <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                        </svg>
                    </div>
                </div>
                <div class="message-bubble">
                    <p>こんにちは！さくらちゃんです🌸</p>
                    <p>家計管理のお手伝いをさせていただきます。何でもお気軽にご相談ください！</p>
                </div>
            </div>
        </div>

        <!-- Quick Action Buttons -->
        <div class="quick-actions">
            <button class="quick-btn" onclick="quickAction('monthly-expenses')">
                今月の支出を教えて
            </button>
            <button class="quick-btn" onclick="quickAction('bookkeeping-tips')">
                仕訳のコツは？
            </button>
            <button class="quick-btn" onclick="quickAction('budget-review')">
                予算を見直したい
            </button>
            <button class="quick-btn" onclick="quickAction('seasonal-advice')">
                季節のアドバイス
            </button>
        </div>

        <!-- Input Area -->
        <div class="input-area">
            <div class="input-container">
                <input 
                    type="text" 
                    id="message-input" 
                    class="message-input" 
                    placeholder="メッセージを入力..."
                    maxlength="500"
                    onkeypress="handleKeyPress(event)"
                >
                <button id="send-btn" class="send-btn" onclick="sendMessage()" disabled>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Simple chatbox functionality
        function showChatbox() {
            document.getElementById('sakura-chatbox').classList.remove('hidden');
        }

        function hideChatbox() {
            document.getElementById('sakura-chatbox').classList.add('hidden');
        }

        function minimizeChatbox() {
            const chatbox = document.getElementById('sakura-chatbox');
            chatbox.style.transform = 'scale(0.1)';
            chatbox.style.opacity = '0.5';
            setTimeout(() => {
                chatbox.classList.add('hidden');
                chatbox.style.transform = '';
                chatbox.style.opacity = '';
            }, 300);
        }

        function quickAction(action) {
            const actions = {
                'monthly-expenses': '今月の支出を教えて',
                'bookkeeping-tips': '仕訳のコツは？',
                'budget-review': '予算を見直したい',
                'seasonal-advice': '季節のアドバイス'
            };
            
            const message = actions[action];
            if (message) {
                document.getElementById('message-input').value = message;
                sendMessage();
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
            
            // Enable/disable send button
            const input = event.target;
            const sendBtn = document.getElementById('send-btn');
            sendBtn.disabled = input.value.trim().length === 0;
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            
            // Clear input
            input.value = '';
            document.getElementById('send-btn').disabled = true;
            
            // Simulate AI response
            setTimeout(() => {
                const responses = [
                    'ありがとうございます！そのご質問についてお答えします🌸',
                    '家計管理について詳しく説明させていただきますね。',
                    'とても良いご質問ですね！一緒に考えてみましょう。',
                    'さくらちゃんがお手伝いします！具体的にどのような点でお困りですか？'
                ];
                const response = responses[Math.floor(Math.random() * responses.length)];
                addMessage(response, 'ai');
            }, 1000);
        }

        function addMessage(content, type) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-container';
            
            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <div class="sakura-avatar-small" style="background: #667eea;">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="white">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble" style="background: #667eea; color: white; border-bottom-right-radius: 6px; border-bottom-left-radius: 18px;">
                        ${content}
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <div class="sakura-avatar-small">
                            <svg class="sakura-icon-small" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9C21 10.1 20.1 11 19 11C17.9 11 17 10.1 17 9C17 7.9 17.9 7 19 7C20.1 7 21 7.9 21 9ZM7 9C7 10.1 6.1 11 5 11C3.9 11 3 10.1 3 9C3 7.9 3.9 7 5 7C6.1 7 7 7.9 7 9ZM18.5 17.5C18.5 18.6 17.6 19.5 16.5 19.5C15.4 19.5 14.5 18.6 14.5 17.5C14.5 16.4 15.4 15.5 16.5 15.5C17.6 15.5 18.5 16.4 18.5 17.5ZM9.5 17.5C9.5 18.6 8.6 19.5 7.5 19.5C6.4 19.5 5.5 18.6 5.5 17.5C5.5 16.4 6.4 15.5 7.5 15.5C8.6 15.5 9.5 16.4 9.5 17.5ZM12 10C13.66 10 15 11.34 15 13C15 14.66 13.66 16 12 16C10.34 16 9 14.66 9 13C9 11.34 10.34 10 12 10Z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="message-bubble">
                        ${content}
                    </div>
                `;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function testMessage() {
            addMessage('これはテストメッセージです', 'user');
            setTimeout(() => {
                addMessage('テストメッセージを受信しました！さくらちゃんが正常に動作しています🌸', 'ai');
            }, 500);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌸 Sakura Chatbox Simple Demo loaded');
            
            // Enable input handling
            const input = document.getElementById('message-input');
            input.addEventListener('input', function() {
                const sendBtn = document.getElementById('send-btn');
                sendBtn.disabled = this.value.trim().length === 0;
            });
        });
    </script>
</body>
</html>

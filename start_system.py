#!/usr/bin/env python3
"""
GoldenLedger — Smart AI-Powered Finance System启动脚本
自动启动后端和前端服务
"""
import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# --- 强制使用当前虚拟环境的解释器 ---
PYTHON_EXECUTABLE = sys.executable
# ---

class SystemLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.db_process = None
        self.redis_process = None
        
    def check_dependencies(self):
        """检查系统依赖"""
        print("🔍 检查系统依赖...")
        
        # 检查Python依赖
        try:
            # 使用当前解释器和pip check来验证所有依赖是否都已安装且兼容
            subprocess.run([
                PYTHON_EXECUTABLE, '-m', 'pip', 'check'
            ], check=True, capture_output=True, text=True)
            print("✅ Python后端依赖已安装")
        except subprocess.CalledProcessError as e:
            print("❌ Python后端依赖缺失或不完整。请运行:")
            print(f"   {PYTHON_EXECUTABLE} -m pip install -r backend/requirements.txt")
            # 打印详细的错误信息，帮助调试
            print(f"\n--- pip check ailed ---\n{e.stdout}{e.stderr}\n--------------------------")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js已安装: {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装")
                return False
        except FileNotFoundError:
            print("❌ Node.js未安装")
            return False
        
        # 检查前端依赖
        if not Path("frontend/node_modules").exists():
            print("⚠️ 前端依赖未安装，正在安装...")
            try:
                subprocess.run(['npm', 'install'], cwd='frontend', check=True)
                print("✅ 前端依赖安装完成")
            except subprocess.CalledProcessError:
                print("❌ 前端依赖安装失败")
                return False
        else:
            print("✅ 前端依赖已安装")
        
        return True
    
    def setup_environment(self):
        """设置环境变量"""
        print("⚙️ 设置环境变量...")
        
        # 复制环境变量文件
        env_example = Path("backend/.env.example")
        env_file = Path("backend/.env")
        
        if not env_file.exists() and env_example.exists():
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ 环境变量文件已创建")
        
        # 设置Python路径
        backend_path = Path("backend").absolute()
        if str(backend_path) not in sys.path:
            sys.path.insert(0, str(backend_path))
    
    def start_database(self):
        """启动数据库（如果需要）"""
        print("🗄️ 检查数据库连接...")
        
        # 这里可以添加数据库启动逻辑
        # 对于演示，我们假设数据库已经运行
        print("✅ 数据库连接正常")
    
    def init_database(self):
        """初始化数据库"""
        print("📊 初始化数据库...")
        
        try:
            # 运行数据库初始化脚本，并指定正确的模块路径
            result = subprocess.run([
                PYTHON_EXECUTABLE, 
                '-m', 'scripts.init_db'
            ], cwd='backend', capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 数据库初始化完成")
                print(result.stdout)
            else:
                print("⚠️ 数据库初始化警告:")
                print(result.stderr)
        except Exception as e:
            print(f"⚠️ 数据库初始化跳过: {e}")
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        try:
            self.backend_process = subprocess.Popen([
                PYTHON_EXECUTABLE, 
                '-m', 'uvicorn', 
                'main:app',
                '--host', '0.0.0.0',
                '--port', '8000',
                '--reload'
            ], cwd='backend')
            
            print("✅ 后端服务已启动 (http://localhost:8000)")
            return True
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🎨 启动前端服务...")
        
        try:
            self.frontend_process = subprocess.Popen([
                'npm', 'run', 'dev'
            ], cwd='frontend')
            
            print("✅ 前端服务已启动 (http://localhost:3000)")
            return True
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return False
    
    def wait_for_services(self):
        """等待服务启动"""
        print("⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查后端健康状态
        try:
            import requests
            response = requests.get('http://localhost:8000/health', timeout=10)
            if response.status_code == 200:
                print("✅ 后端服务健康检查通过")
            else:
                print("⚠️ 后端服务健康检查失败")
        except Exception as e:
            print(f"⚠️ 后端健康检查跳过: {e}")
    
    def show_info(self):
        """显示系统信息"""
        print("\n" + "="*60)
        print("🎉 GoldenLedger — Smart AI-Powered Finance System启动完成！")
        print("="*60)
        print("📖 API文档:     http://localhost:8000/docs")
        print("🔧 管理界面:     http://localhost:3000")
        print("💬 AI记账:      点击右下角聊天按钮")
        print("="*60)
        print("🛑 按 Ctrl+C 停止所有服务")
        print("="*60)
    
    def cleanup(self):
        """清理进程"""
        print("\n🛑 正在停止服务...")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("✅ 前端服务已停止")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("✅ 后端服务已停止")
        
        print("👋 系统已安全关闭")
    
    def run(self):
        """运行系统"""
        try:
            # 检查依赖
            if not self.check_dependencies():
                return False
            
            # 设置环境
            self.setup_environment()
            
            # 启动数据库
            self.start_database()
            
            # 初始化数据库
            self.init_database()
            
            # 启动后端
            if not self.start_backend():
                return False
            
            # 启动前端
            if not self.start_frontend():
                return False
            
            # 等待服务启动
            self.wait_for_services()
            
            # 显示信息
            self.show_info()
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    print("🚀 GoldenLedger — Smart AI-Powered Finance System启动器")
    print("="*60)
    
    launcher = SystemLauncher()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        launcher.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行系统
    success = launcher.run()
    
    if success:
        print("✅ 系统运行完成")
    else:
        print("❌ 系统启动失败")
        sys.exit(1)


if __name__ == "__main__":
    main()

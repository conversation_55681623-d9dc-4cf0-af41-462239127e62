<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多用户系统测试工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="api_config.js"></script>
    <script src="/music_injector.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-blue-600">🧪 多用户系统测试工具</h1>
        
        <!-- 测试状态概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-green-600 mb-2">数据库迁移</h3>
                <p id="migration-status" class="text-2xl font-bold text-gray-400">未测试</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-blue-600 mb-2">用户认证</h3>
                <p id="auth-status" class="text-2xl font-bold text-gray-400">未测试</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-purple-600 mb-2">数据隔离</h3>
                <p id="isolation-status" class="text-2xl font-bold text-gray-400">未测试</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-orange-600 mb-2">API端点</h3>
                <p id="api-status" class="text-2xl font-bold text-gray-400">未测试</p>
            </div>
        </div>
        
        <!-- 测试控制面板 -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">🎮 测试控制面板</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="testDatabaseMigration()" class="bg-green-500 text-white px-6 py-3 rounded hover:bg-green-600">
                    测试数据库迁移
                </button>
                <button onclick="testUserAuthentication()" class="bg-blue-500 text-white px-6 py-3 rounded hover:bg-blue-600">
                    测试用户认证
                </button>
                <button onclick="testDataIsolation()" class="bg-purple-500 text-white px-6 py-3 rounded hover:bg-purple-600">
                    测试数据隔离
                </button>
                <button onclick="testAPIEndpoints()" class="bg-orange-500 text-white px-6 py-3 rounded hover:bg-orange-600">
                    测试API端点
                </button>
                <button onclick="runFullTest()" class="bg-red-500 text-white px-6 py-3 rounded hover:bg-red-600">
                    运行完整测试
                </button>
                <button onclick="clearResults()" class="bg-gray-500 text-white px-6 py-3 rounded hover:bg-gray-600">
                    清空结果
                </button>
            </div>
        </div>
        
        <!-- 用户管理测试 -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">👥 用户管理测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 用户注册测试 -->
                <div>
                    <h3 class="font-semibold mb-3">用户注册测试</h3>
                    <div class="space-y-3">
                        <input type="text" id="reg-username" placeholder="用户名" class="w-full p-2 border rounded">
                        <input type="email" id="reg-email" placeholder="邮箱" class="w-full p-2 border rounded">
                        <input type="password" id="reg-password" placeholder="密码" class="w-full p-2 border rounded">
                        <button onclick="testUserRegistration()" class="w-full bg-green-500 text-white p-2 rounded hover:bg-green-600">
                            测试注册
                        </button>
                    </div>
                </div>
                
                <!-- 用户登录测试 -->
                <div>
                    <h3 class="font-semibold mb-3">用户登录测试</h3>
                    <div class="space-y-3">
                        <input type="text" id="login-username" placeholder="用户名或邮箱" class="w-full p-2 border rounded">
                        <input type="password" id="login-password" placeholder="密码" class="w-full p-2 border rounded">
                        <button onclick="testUserLogin()" class="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">
                            测试登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据隔离测试 -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">🔒 数据隔离测试</h2>
            <div class="space-y-4">
                <button onclick="testCreateJournalEntry()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    创建测试会计分录
                </button>
                <button onclick="testGetUserData()" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                    获取用户数据
                </button>
                <button onclick="testCrossUserAccess()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    测试跨用户访问（应该失败）
                </button>
            </div>
        </div>
        
        <!-- 测试结果显示 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 测试结果</h2>
            <div id="test-results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let testResults = [];

        // 更新状态显示
        function updateStatus(type, status, color = 'green') {
            const element = document.getElementById(`${type}-status`);
            element.textContent = status;
            element.className = `text-2xl font-bold text-${color}-600`;
        }

        // 添加测试结果
        function addTestResult(title, success, message, details = null) {
            const timestamp = new Date().toLocaleTimeString();
            const result = {
                timestamp,
                title,
                success,
                message,
                details
            };
            
            testResults.push(result);
            displayTestResults();
        }

        // 显示测试结果
        function displayTestResults() {
            const container = document.getElementById('test-results');
            
            if (testResults.length === 0) {
                container.innerHTML = '<p class="text-gray-500">点击上方按钮开始测试...</p>';
                return;
            }

            const resultsHtml = testResults.map(result => `
                <div class="p-4 border rounded ${result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-semibold ${result.success ? 'text-green-800' : 'text-red-800'}">
                            ${result.success ? '✅' : '❌'} ${result.title}
                        </h4>
                        <span class="text-sm text-gray-500">${result.timestamp}</span>
                    </div>
                    <p class="text-sm ${result.success ? 'text-green-700' : 'text-red-700'}">${result.message}</p>
                    ${result.details ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto">${JSON.stringify(result.details, null, 2)}</pre>` : ''}
                </div>
            `).join('');

            container.innerHTML = resultsHtml;
        }

        // 测试数据库迁移
        async function testDatabaseMigration() {
            try {
                updateStatus('migration', '测试中...', 'yellow');
                
                const response = await fetch(window.GoldenLedgerAPI.url('/api/database/migrate'), {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('migration', '✅ 成功', 'green');
                    addTestResult('数据库迁移', true, '数据库迁移成功完成', result.migrations);
                } else {
                    updateStatus('migration', '❌ 失败', 'red');
                    addTestResult('数据库迁移', false, `迁移失败: ${result.error}`);
                }
            } catch (error) {
                updateStatus('migration', '❌ 错误', 'red');
                addTestResult('数据库迁移', false, `网络错误: ${error.message}`);
            }
        }

        // 测试用户认证
        async function testUserAuthentication() {
            try {
                updateStatus('auth', '测试中...', 'yellow');
                
                // 测试注册端点
                const registerResponse = await fetch(window.GoldenLedgerAPI.url('/api/auth/register'), {
                    method: 'OPTIONS'
                });
                
                // 测试登录端点
                const loginResponse = await fetch(window.GoldenLedgerAPI.url('/api/auth/login'), {
                    method: 'OPTIONS'
                });
                
                if (registerResponse.ok && loginResponse.ok) {
                    updateStatus('auth', '✅ 可用', 'green');
                    addTestResult('用户认证端点', true, '注册和登录端点都可用');
                } else {
                    updateStatus('auth', '⚠️ 部分可用', 'yellow');
                    addTestResult('用户认证端点', false, '部分认证端点不可用');
                }
            } catch (error) {
                updateStatus('auth', '❌ 错误', 'red');
                addTestResult('用户认证端点', false, `测试失败: ${error.message}`);
            }
        }

        // 测试数据隔离
        async function testDataIsolation() {
            try {
                updateStatus('isolation', '测试中...', 'yellow');
                
                if (!currentUser) {
                    updateStatus('isolation', '⚠️ 需要登录', 'yellow');
                    addTestResult('数据隔离', false, '需要先登录用户才能测试数据隔离');
                    return;
                }

                // 测试获取用户仪表板数据
                const response = await fetch(window.GoldenLedgerAPI.url('/api/user/dashboard'), {
                    headers: {
                        'Authorization': `Bearer ${currentUser.token}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success && result.data.user_id === currentUser.user_id) {
                    updateStatus('isolation', '✅ 正常', 'green');
                    addTestResult('数据隔离', true, '用户只能访问自己的数据', result.data);
                } else {
                    updateStatus('isolation', '❌ 异常', 'red');
                    addTestResult('数据隔离', false, '数据隔离可能存在问题');
                }
            } catch (error) {
                updateStatus('isolation', '❌ 错误', 'red');
                addTestResult('数据隔离', false, `测试失败: ${error.message}`);
            }
        }

        // 测试API端点
        async function testAPIEndpoints() {
            try {
                updateStatus('api', '测试中...', 'yellow');
                
                const endpoints = [
                    '/api/auth/register',
                    '/api/auth/login',
                    '/api/user/dashboard',
                    '/api/user/journal-entries',
                    '/api/journal-entries',
                    '/api/ai/process-text'
                ];
                
                let availableCount = 0;
                const results = [];
                
                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(window.GoldenLedgerAPI.url(endpoint), {
                            method: 'OPTIONS'
                        });
                        
                        if (response.ok) {
                            availableCount++;
                            results.push(`✅ ${endpoint}`);
                        } else {
                            results.push(`❌ ${endpoint} (${response.status})`);
                        }
                    } catch (error) {
                        results.push(`❌ ${endpoint} (网络错误)`);
                    }
                }
                
                const successRate = (availableCount / endpoints.length * 100).toFixed(0);
                
                if (successRate >= 80) {
                    updateStatus('api', `✅ ${successRate}%`, 'green');
                    addTestResult('API端点', true, `${availableCount}/${endpoints.length} 个端点可用`, results);
                } else {
                    updateStatus('api', `⚠️ ${successRate}%`, 'yellow');
                    addTestResult('API端点', false, `只有 ${availableCount}/${endpoints.length} 个端点可用`, results);
                }
            } catch (error) {
                updateStatus('api', '❌ 错误', 'red');
                addTestResult('API端点', false, `测试失败: ${error.message}`);
            }
        }

        // 运行完整测试
        async function runFullTest() {
            clearResults();
            addTestResult('完整测试', true, '开始运行完整的多用户系统测试...');
            
            await testDatabaseMigration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testUserAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAPIEndpoints();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDataIsolation();
            
            addTestResult('完整测试', true, '完整测试执行完成，请查看各项测试结果');
        }

        // 清空结果
        function clearResults() {
            testResults = [];
            displayTestResults();
            
            // 重置状态
            updateStatus('migration', '未测试', 'gray');
            updateStatus('auth', '未测试', 'gray');
            updateStatus('isolation', '未测试', 'gray');
            updateStatus('api', '未测试', 'gray');
        }

        // 测试用户注册
        async function testUserRegistration() {
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;

            if (!username || !email || !password) {
                addTestResult('用户注册', false, '请填写完整的注册信息');
                return;
            }

            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/register'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password })
                });

                const result = await response.json();

                if (result.success) {
                    addTestResult('用户注册', true, `用户 ${username} 注册成功`, result);
                } else {
                    addTestResult('用户注册', false, `注册失败: ${result.error}`);
                }
            } catch (error) {
                addTestResult('用户注册', false, `注册请求失败: ${error.message}`);
            }
        }

        // 测试用户登录
        async function testUserLogin() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            if (!username || !password) {
                addTestResult('用户登录', false, '请填写用户名和密码');
                return;
            }

            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/login'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    currentUser = {
                        token: result.session_token,
                        user_id: result.user.id,
                        username: result.user.username
                    };
                    addTestResult('用户登录', true, `用户 ${username} 登录成功`, result.user);
                } else {
                    addTestResult('用户登录', false, `登录失败: ${result.error}`);
                }
            } catch (error) {
                addTestResult('用户登录', false, `登录请求失败: ${error.message}`);
            }
        }

        // 测试创建会计分录
        async function testCreateJournalEntry() {
            if (!currentUser) {
                addTestResult('创建会计分录', false, '需要先登录');
                return;
            }

            const testEntry = {
                description: '测试会计分录 - ' + new Date().toLocaleString(),
                debit_account: '現金',
                credit_account: '売上',
                amount: 1000,
                company_id: 'default'
            };

            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/journal-entries'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentUser.token}`
                    },
                    body: JSON.stringify(testEntry)
                });

                const result = await response.json();

                if (result.success) {
                    addTestResult('创建会计分录', true, '测试会计分录创建成功', result.data);
                } else {
                    addTestResult('创建会计分录', false, `创建失败: ${result.error}`);
                }
            } catch (error) {
                addTestResult('创建会计分录', false, `请求失败: ${error.message}`);
            }
        }

        // 测试获取用户数据
        async function testGetUserData() {
            if (!currentUser) {
                addTestResult('获取用户数据', false, '需要先登录');
                return;
            }

            try {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/user/dashboard'), {
                    headers: {
                        'Authorization': `Bearer ${currentUser.token}`
                    }
                });

                const result = await response.json();

                if (result.success) {
                    addTestResult('获取用户数据', true, '成功获取用户仪表板数据', result.data);
                } else {
                    addTestResult('获取用户数据', false, `获取失败: ${result.error}`);
                }
            } catch (error) {
                addTestResult('获取用户数据', false, `请求失败: ${error.message}`);
            }
        }

        // 测试跨用户访问
        async function testCrossUserAccess() {
            // 这个测试应该失败，因为用户不应该能访问其他用户的数据
            addTestResult('跨用户访问测试', true, '此测试需要手动验证：尝试使用不同用户账户访问数据，确保数据隔离正常工作');
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            console.log('多用户系统测试工具已加载');
            displayTestResults();
        });
    </script>
</body>
</html>

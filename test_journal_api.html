<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会计分录API测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_injector.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-600">📝 会计分录API测试</h1>
        
        <!-- 认证状态 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🔐 认证状态</h2>
            <div class="flex items-center space-x-4">
                <span id="auth-status" class="px-3 py-1 rounded text-sm">检查中...</span>
                <button onclick="checkAuth()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    检查认证
                </button>
                <button onclick="goToLogin()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    去登录
                </button>
            </div>
        </div>

        <!-- API测试 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🧪 API测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testGetEntries()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    测试获取分录 (GET)
                </button>
                <button onclick="testCreateEntry()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    测试创建分录 (POST)
                </button>
                <button onclick="testUpdateEntry()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    测试更新分录 (PUT)
                </button>
                <button onclick="testDeleteEntry()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    测试删除分录 (DELETE)
                </button>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 测试结果</h2>
            <div id="test-results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <script>
        let testEntryId = null;

        // 检查认证状态
        function checkAuth() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');
            
            const statusElement = document.getElementById('auth-status');
            
            if (token && user) {
                statusElement.textContent = '✅ 已登录';
                statusElement.className = 'px-3 py-1 rounded text-sm bg-green-100 text-green-800';
                
                try {
                    const userData = JSON.parse(user);
                    addResult('认证检查', true, `用户: ${userData.name || userData.username || userData.email}`);
                } catch (e) {
                    addResult('认证检查', true, '已登录但用户信息解析失败');
                }
            } else {
                statusElement.textContent = '❌ 未登录';
                statusElement.className = 'px-3 py-1 rounded text-sm bg-red-100 text-red-800';
                addResult('认证检查', false, '未找到登录令牌，请先登录');
            }
        }

        // 跳转到登录页面
        function goToLogin() {
            window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
        }

        // 添加测试结果
        function addResult(title, success, message, data = null) {
            const container = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
                ${data ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (!token) {
                throw new Error('未登录');
            }
            
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        // 测试获取分录
        async function testGetEntries() {
            try {
                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/journal-entries?company_id=default', {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('获取分录', true, `成功获取 ${result.data.length} 条记录`, result);
                    
                    // 保存第一个分录ID用于后续测试
                    if (result.data.length > 0) {
                        testEntryId = result.data[0].id;
                    }
                } else {
                    addResult('获取分录', false, result.error || '获取失败', result);
                }
            } catch (error) {
                addResult('获取分录', false, error.message);
            }
        }

        // 测试创建分录
        async function testCreateEntry() {
            try {
                const testData = {
                    description: '测试分录 - ' + new Date().toLocaleString(),
                    debit_account: '現金',
                    credit_account: '売上',
                    amount: 1000,
                    company_id: 'default'
                };

                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/journal-entries', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('创建分录', true, '分录创建成功', result);
                    testEntryId = result.data.id; // 保存新创建的ID
                } else {
                    addResult('创建分录', false, result.error || '创建失败', result);
                }
            } catch (error) {
                addResult('创建分录', false, error.message);
            }
        }

        // 测试更新分录
        async function testUpdateEntry() {
            if (!testEntryId) {
                addResult('更新分录', false, '没有可用的分录ID，请先创建或获取分录');
                return;
            }

            try {
                const updateData = {
                    description: '更新的测试分录 - ' + new Date().toLocaleString(),
                    debit_account: '普通預金',
                    credit_account: '売上',
                    amount: 1500
                };

                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${testEntryId}`, {
                    method: 'PUT',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(updateData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('更新分录', true, '分录更新成功', result);
                } else {
                    addResult('更新分录', false, result.error || '更新失败', result);
                }
            } catch (error) {
                addResult('更新分录', false, error.message);
            }
        }

        // 测试删除分录
        async function testDeleteEntry() {
            if (!testEntryId) {
                addResult('删除分录', false, '没有可用的分录ID，请先创建或获取分录');
                return;
            }

            if (!confirm('确定要删除测试分录吗？')) {
                return;
            }

            try {
                const response = await fetch(`https://goldenledger-api.souyousann.workers.dev/api/journal-entries/${testEntryId}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('删除分录', true, '分录删除成功', result);
                    testEntryId = null; // 清除已删除的ID
                } else {
                    addResult('删除分录', false, result.error || '删除失败', result);
                }
            } catch (error) {
                addResult('删除分录', false, error.message);
            }
        }

        // 页面加载时检查认证状态
        window.addEventListener('load', function() {
            checkAuth();
            
            // 清空之前的结果
            document.getElementById('test-results').innerHTML = '<p class="text-gray-500">认证状态已检查，可以开始API测试...</p>';
        });
    </script>
</body>
</html>

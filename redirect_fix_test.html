<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重定向修复测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 class="text-2xl font-bold text-center mb-6">🔧 重定向修复测试</h1>
        
        <div class="space-y-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h2 class="font-semibold text-blue-800 mb-2">问题说明</h2>
                <p class="text-sm text-blue-700">
                    登录和注册页面会自动检查用户登录状态，如果已登录会重定向到仪表板。
                </p>
            </div>
            
            <div class="bg-green-50 p-4 rounded-lg">
                <h2 class="font-semibold text-green-800 mb-2">解决方案</h2>
                <p class="text-sm text-green-700 mb-3">
                    添加 <code>?force=true</code> 参数可以跳过自动重定向检查。
                </p>
                
                <div class="space-y-2">
                    <a href="login.html?force=true" 
                       class="block w-full bg-blue-500 text-white text-center py-2 px-4 rounded hover:bg-blue-600 transition">
                        🔐 强制显示登录页面
                    </a>
                    
                    <a href="register.html?force=true" 
                       class="block w-full bg-green-500 text-white text-center py-2 px-4 rounded hover:bg-green-600 transition">
                        📝 强制显示注册页面
                    </a>
                </div>
            </div>
            
            <div class="bg-yellow-50 p-4 rounded-lg">
                <h2 class="font-semibold text-yellow-800 mb-2">清除登录状态</h2>
                <p class="text-sm text-yellow-700 mb-3">
                    如果想完全清除登录状态，点击下面的按钮：
                </p>
                
                <button onclick="clearAuth()" 
                        class="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600 transition">
                    🗑️ 清除登录状态
                </button>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-lg">
                <h2 class="font-semibold text-gray-800 mb-2">当前登录状态</h2>
                <div id="authStatus" class="text-sm text-gray-600">
                    检查中...
                </div>
            </div>
            
            <div class="text-center">
                <a href="index.html" class="text-blue-500 hover:text-blue-700 text-sm">
                    ← 返回首页
                </a>
            </div>
        </div>
    </div>

    <script>
        function clearAuth() {
            localStorage.removeItem('golden_ledger_user');
            sessionStorage.clear();
            alert('✅ 登录状态已清除！');
            checkAuthStatus();
        }
        
        function checkAuthStatus() {
            const userData = localStorage.getItem('golden_ledger_user');
            const statusDiv = document.getElementById('authStatus');
            
            if (userData) {
                try {
                    const parsed = JSON.parse(userData);
                    const loginTime = new Date(parsed.loginTime);
                    const now = new Date();
                    const sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours
                    
                    if (now - loginTime < sessionTimeout) {
                        statusDiv.innerHTML = `
                            <div class="text-green-600">
                                ✅ 已登录<br>
                                用户: ${parsed.email || 'Demo User'}<br>
                                登录时间: ${loginTime.toLocaleString()}
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="text-yellow-600">
                                ⚠️ 会话已过期<br>
                                登录时间: ${loginTime.toLocaleString()}
                            </div>
                        `;
                    }
                } catch (error) {
                    statusDiv.innerHTML = `
                        <div class="text-red-600">
                            ❌ 登录数据损坏
                        </div>
                    `;
                }
            } else {
                statusDiv.innerHTML = `
                    <div class="text-gray-600">
                        ℹ️ 未登录
                    </div>
                `;
            }
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', checkAuthStatus);
    </script>
</body>
</html>

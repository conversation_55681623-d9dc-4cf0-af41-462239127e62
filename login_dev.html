<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发登录 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="api_config.js"></script>
    <script src="background_control_new.js"></script>
    <script src="music_control_new.js"></script>
    <script src="user_manager.js"></script>
    <link rel="icon" type="image/x-icon" href="image/favicon.ico">
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(108, 92, 231, 0.4);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- 登录卡片 -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl font-bold text-purple-600">GL</span>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">开发登录</h1>
                <p class="text-purple-200">GoldenLedger 开发环境</p>
            </div>

            <!-- 开发登录选项 -->
            <div class="space-y-4">
                <!-- 管理员登录 -->
                <button onclick="devLogin('admin')" class="w-full btn-primary text-white py-3 px-4 rounded-xl font-semibold">
                    👨‍💼 管理员登录
                </button>
                
                <!-- 普通用户登录 -->
                <button onclick="devLogin('user')" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors">
                    👤 普通用户登录
                </button>
                
                <!-- 会计师登录 -->
                <button onclick="devLogin('accountant')" class="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors">
                    📊 会计师登录
                </button>
                
                <!-- 分隔线 -->
                <div class="relative my-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-purple-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-transparent text-purple-200">生产环境登录</span>
                    </div>
                </div>
                
                <!-- Google登录（仅生产环境） -->
                <button onclick="redirectToProduction()" class="w-full flex items-center justify-center px-4 py-3 border border-purple-300 rounded-xl hover:bg-purple-100 hover:bg-opacity-20 transition-colors text-white">
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    生产环境 Google 登录
                </button>
            </div>

            <!-- 开发说明 -->
            <div class="mt-6 p-4 bg-yellow-500 bg-opacity-20 rounded-lg">
                <p class="text-yellow-200 text-sm text-center">
                    ⚠️ 这是开发环境登录页面<br>
                    Google登录需要在生产环境使用
                </p>
            </div>
        </div>

        <!-- 返回主页 -->
        <div class="text-center mt-6">
            <a href="index.html" class="text-purple-200 hover:text-white transition-colors">
                ← 返回主页
            </a>
        </div>
    </div>

    <script>
        // 开发环境登录
        function devLogin(userType) {
            console.log('🔐 开发登录:', userType);

            // 创建模拟用户数据
            const userData = {
                id: `dev_${userType}_${Date.now()}`,
                name: getUserName(userType),
                email: `${userType}@goldenledger.dev`,
                picture: `https://ui-avatars.com/api/?name=${getUserName(userType)}&background=6c5ce7&color=fff`,
                provider: 'development',
                role: userType
            };

            // 使用用户管理系统登录
            if (window.userManager) {
                try {
                    const managedUser = window.userManager.loginUser(userData);
                    console.log('✅ 用户已保存到用户管理系统:', managedUser.name);
                } catch (error) {
                    console.error('❌ 用户管理系统登录失败:', error);
                    // 备用：使用原有逻辑
                    localStorage.setItem('goldenledger_user', JSON.stringify(userData));
                    localStorage.setItem('goldenledger_token', `dev_token_${Date.now()}`);
                    localStorage.setItem('goldenledger_login_time', new Date().toISOString());
                }
            } else {
                // 备用：使用原有逻辑
                localStorage.setItem('goldenledger_user', JSON.stringify(userData));
                localStorage.setItem('goldenledger_token', `dev_token_${Date.now()}`);
                localStorage.setItem('goldenledger_login_time', new Date().toISOString());
            }

            // 显示成功消息
            showMessage(`${getUserName(userType)}登录成功！正在跳转...`, 'success');

            // 跳转到仪表板
            setTimeout(() => {
                const returnUrl = new URLSearchParams(window.location.search).get('return') || 'master_dashboard.html';
                window.location.href = returnUrl;
            }, 1500);
        }

        // 获取用户名称
        function getUserName(userType) {
            const names = {
                admin: '系统管理员',
                user: '普通用户',
                accountant: '会计师'
            };
            return names[userType] || '开发用户';
        }

        // 跳转到生产环境
        function redirectToProduction() {
            showMessage('正在跳转到生产环境...', 'info');
            setTimeout(() => {
                window.open('https://ledger.goldenorangetech.com/login.html', '_blank');
            }, 1000);
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500'
            };

            const messageDiv = document.createElement('div');
            messageDiv.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300`;
            messageDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    ${message}
                </div>
            `;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(100%)';
                setTimeout(() => messageDiv.remove(), 300);
            }, 3000);
        }

        console.log('🔐 开发登录页面已加载');
    </script>
</body>
</html>

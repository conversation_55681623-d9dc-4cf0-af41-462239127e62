<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox Test - Local Server</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            color: white;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 8px;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255, 105, 180, 0.3);
        }

        .status {
            background: rgba(0, 0, 0, 0.2);
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-family: monospace;
            text-align: left;
        }

        .success { color: #00FF00; }
        .error { color: #FF4444; }
        .info { color: #00BFFF; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <h1>🌸 Sakura Chatbox 本地测试</h1>
            <p>测试集成到index.html中的chatbox功能</p>
            
            <div class="status" id="status">
                <div class="info">📋 测试状态: 准备中...</div>
            </div>

            <div style="margin: 24px 0;">
                <button class="test-btn" onclick="testChatboxVisibility()">
                    👁️ 检查Chatbox可见性
                </button>
                <button class="test-btn" onclick="testShowChatbox()">
                    💬 显示Chatbox
                </button>
                <button class="test-btn" onclick="testHideChatbox()">
                    🙈 隐藏Chatbox
                </button>
                <button class="test-btn" onclick="testMinimizeChatbox()">
                    ➖ 最小化Chatbox
                </button>
                <button class="test-btn" onclick="testQuickAction()">
                    ⚡ 测试快捷操作
                </button>
                <button class="test-btn" onclick="testSendMessage()">
                    📝 发送测试消息
                </button>
                <button class="test-btn" onclick="openMainPage()">
                    🏠 打开主页
                </button>
            </div>

            <div class="test-card">
                <h3>测试说明</h3>
                <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <li>首先点击"打开主页"按钮访问index.html</li>
                    <li>查看右下角是否有粉色的浮动聊天按钮</li>
                    <li>点击浮动按钮应该显示完整的chatbox</li>
                    <li>测试各种功能：发送消息、快捷操作、最小化等</li>
                    <li>确认chatbox在不同屏幕尺寸下正常显示</li>
                </ul>
            </div>

            <div class="test-card">
                <h3>预期行为</h3>
                <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                    <p><strong>✅ 正常情况:</strong></p>
                    <ul>
                        <li>页面加载2秒后显示粉色浮动按钮</li>
                        <li>点击浮动按钮显示完整chatbox</li>
                        <li>chatbox有粉色渐变头部，显示"さくらちゃん"</li>
                        <li>有4个金色快捷操作按钮</li>
                        <li>可以输入和发送消息</li>
                        <li>AI会自动回复消息</li>
                    </ul>
                    
                    <p><strong>❌ 问题情况:</strong></p>
                    <ul>
                        <li>浮动按钮不显示</li>
                        <li>chatbox样式错乱</li>
                        <li>按钮点击无反应</li>
                        <li>消息发送失败</li>
                        <li>移动端显示异常</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            
            status.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            status.scrollTop = status.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        function testChatboxVisibility() {
            updateStatus('🔍 检查Chatbox可见性...', 'info');
            
            // 尝试在主窗口中查找chatbox元素
            try {
                // 这个测试页面本身不包含chatbox，需要在主页面中测试
                updateStatus('⚠️ 请在主页面(index.html)中测试chatbox功能', 'error');
                updateStatus('💡 点击"打开主页"按钮访问主页面', 'info');
            } catch (error) {
                updateStatus(`❌ 检查失败: ${error.message}`, 'error');
            }
        }

        function testShowChatbox() {
            updateStatus('💬 测试显示Chatbox功能...', 'info');
            updateStatus('⚠️ 此功能需要在主页面中测试', 'error');
        }

        function testHideChatbox() {
            updateStatus('🙈 测试隐藏Chatbox功能...', 'info');
            updateStatus('⚠️ 此功能需要在主页面中测试', 'error');
        }

        function testMinimizeChatbox() {
            updateStatus('➖ 测试最小化Chatbox功能...', 'info');
            updateStatus('⚠️ 此功能需要在主页面中测试', 'error');
        }

        function testQuickAction() {
            updateStatus('⚡ 测试快捷操作功能...', 'info');
            updateStatus('⚠️ 此功能需要在主页面中测试', 'error');
        }

        function testSendMessage() {
            updateStatus('📝 测试发送消息功能...', 'info');
            updateStatus('⚠️ 此功能需要在主页面中测试', 'error');
        }

        function openMainPage() {
            updateStatus('🏠 打开主页面...', 'info');
            window.open('http://localhost:8080', '_blank');
            updateStatus('✅ 主页面已在新标签页中打开', 'success');
            updateStatus('📋 请在新标签页中测试chatbox功能', 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('🌸 Sakura Chatbox 测试页面已加载', 'success');
            updateStatus('📋 本地服务器运行在: http://localhost:8080', 'info');
            updateStatus('💡 点击"打开主页"开始测试', 'info');
            
            // 检查本地服务器状态
            fetch('http://localhost:8080')
                .then(response => {
                    if (response.ok) {
                        updateStatus('✅ 本地服务器连接正常', 'success');
                    } else {
                        updateStatus('⚠️ 本地服务器响应异常', 'error');
                    }
                })
                .catch(error => {
                    updateStatus('❌ 无法连接到本地服务器', 'error');
                    updateStatus('💡 请确保服务器正在运行: python3 -m http.server 8080', 'info');
                });
        });

        // 定期检查服务器状态
        setInterval(() => {
            fetch('http://localhost:8080')
                .then(response => {
                    if (!response.ok) {
                        updateStatus('⚠️ 服务器连接异常', 'error');
                    }
                })
                .catch(error => {
                    updateStatus('❌ 服务器连接中断', 'error');
                });
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>

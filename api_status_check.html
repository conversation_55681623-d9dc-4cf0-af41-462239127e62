<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 API状态检查 - GoldenLedger</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .status-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .status-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .status-header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .status-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #6c757d;
        }

        .status-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .status-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .status-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .status-item h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .status-item pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
            margin: 10px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="status-container">
        <div class="status-header">
            <h1>🔍 API状态检查</h1>
            <p>检查GoldenLedger AI聊天系统的API连接状态</p>
        </div>

        <div id="environmentStatus" class="status-item">
            <h3>🌍 环境检测</h3>
            <p>正在检测当前环境...</p>
        </div>

        <div id="apiKeyStatus" class="status-item">
            <h3>🔑 API密钥状态</h3>
            <p>正在检查API密钥配置...</p>
        </div>

        <div id="connectionStatus" class="status-item">
            <h3>🔗 连接测试</h3>
            <p>正在测试API连接...</p>
        </div>

        <div id="testResult" class="status-item" style="display: none;">
            <h3>🧪 测试结果</h3>
            <p>API响应测试结果...</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="runFullTest()">🔄 重新测试</button>
            <button class="btn" onclick="testApiCall()">🧪 测试API调用</button>
            <button class="btn" onclick="goBack()">← 返回主页</button>
        </div>
    </div>

    <!-- 加载环境配置 -->
    <script src="env-config.js"></script>
    
    <script>
        // 页面加载时自动运行检查
        window.addEventListener('load', function() {
            runFullTest();
        });

        function runFullTest() {
            checkEnvironment();
            checkApiKey();
            checkConnection();
        }

        function checkEnvironment() {
            const envDiv = document.getElementById('environmentStatus');
            const hostname = window.location.hostname;
            const isLocal = hostname === 'localhost' || hostname === '127.0.0.1';
            
            if (isLocal) {
                envDiv.className = 'status-item warning';
                envDiv.innerHTML = `
                    <h3>🌍 环境检测</h3>
                    <p><strong>本地开发环境</strong></p>
                    <p>主机名: ${hostname}</p>
                    <p>需要在localStorage中设置API密钥</p>
                `;
            } else {
                envDiv.className = 'status-item success';
                envDiv.innerHTML = `
                    <h3>🌍 环境检测</h3>
                    <p><strong>生产环境</strong></p>
                    <p>主机名: ${hostname}</p>
                    <p>应该使用Cloudflare Pages环境变量</p>
                `;
            }
        }

        function checkApiKey() {
            const keyDiv = document.getElementById('apiKeyStatus');
            const apiKey = window.GEMINI_API_KEY;
            
            if (!apiKey) {
                keyDiv.className = 'status-item error';
                keyDiv.innerHTML = `
                    <h3>🔑 API密钥状态</h3>
                    <p><strong>❌ 未找到API密钥</strong></p>
                    <p>请检查环境配置</p>
                `;
            } else if (apiKey === 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER') {
                keyDiv.className = 'status-item error';
                keyDiv.innerHTML = `
                    <h3>🔑 API密钥状态</h3>
                    <p><strong>❌ 占位符未替换</strong></p>
                    <p>Cloudflare Pages构建脚本未正确执行</p>
                `;
            } else {
                keyDiv.className = 'status-item success';
                keyDiv.innerHTML = `
                    <h3>🔑 API密钥状态</h3>
                    <p><strong>✅ API密钥已加载</strong></p>
                    <p>密钥: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 10)}</p>
                    <p>长度: ${apiKey.length} 字符</p>
                `;
            }
        }

        async function checkConnection() {
            const connDiv = document.getElementById('connectionStatus');
            connDiv.innerHTML = `
                <h3>🔗 连接测试</h3>
                <p><span class="loading"></span> 正在测试API连接...</p>
            `;

            const apiKey = window.GEMINI_API_KEY;
            if (!apiKey || apiKey === 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER') {
                connDiv.className = 'status-item error';
                connDiv.innerHTML = `
                    <h3>🔗 连接测试</h3>
                    <p><strong>❌ 无法测试连接</strong></p>
                    <p>API密钥未正确配置</p>
                `;
                return;
            }

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{ text: 'Hello, this is a connection test.' }]
                        }]
                    })
                });

                if (response.ok) {
                    connDiv.className = 'status-item success';
                    connDiv.innerHTML = `
                        <h3>🔗 连接测试</h3>
                        <p><strong>✅ API连接成功</strong></p>
                        <p>状态码: ${response.status}</p>
                        <p>API密钥有效且可用</p>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    connDiv.className = 'status-item error';
                    connDiv.innerHTML = `
                        <h3>🔗 连接测试</h3>
                        <p><strong>❌ API连接失败</strong></p>
                        <p>状态码: ${response.status}</p>
                        <p>错误: ${errorData.error?.message || '未知错误'}</p>
                        <pre>${JSON.stringify(errorData, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                connDiv.className = 'status-item error';
                connDiv.innerHTML = `
                    <h3>🔗 连接测试</h3>
                    <p><strong>❌ 网络错误</strong></p>
                    <p>错误: ${error.message}</p>
                `;
            }
        }

        async function testApiCall() {
            const testDiv = document.getElementById('testResult');
            testDiv.style.display = 'block';
            testDiv.innerHTML = `
                <h3>🧪 测试结果</h3>
                <p><span class="loading"></span> 正在发送测试消息...</p>
            `;

            const apiKey = window.GEMINI_API_KEY;
            if (!apiKey) {
                testDiv.className = 'status-item error';
                testDiv.innerHTML = `
                    <h3>🧪 测试结果</h3>
                    <p><strong>❌ 无法测试</strong></p>
                    <p>API密钥未配置</p>
                `;
                return;
            }

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{ text: '你好，请简单介绍一下你自己。' }]
                        }]
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const responseText = result.candidates?.[0]?.content?.parts?.[0]?.text || '无响应内容';
                    
                    testDiv.className = 'status-item success';
                    testDiv.innerHTML = `
                        <h3>🧪 测试结果</h3>
                        <p><strong>✅ API调用成功</strong></p>
                        <p><strong>AI响应:</strong></p>
                        <pre>${responseText}</pre>
                    `;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    testDiv.className = 'status-item error';
                    testDiv.innerHTML = `
                        <h3>🧪 测试结果</h3>
                        <p><strong>❌ API调用失败</strong></p>
                        <p>状态码: ${response.status}</p>
                        <pre>${JSON.stringify(errorData, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                testDiv.className = 'status-item error';
                testDiv.innerHTML = `
                    <h3>🧪 测试结果</h3>
                    <p><strong>❌ 请求失败</strong></p>
                    <p>错误: ${error.message}</p>
                `;
            }
        }

        function goBack() {
            window.location.href = '/';
        }
    </script>
</body>
</html>

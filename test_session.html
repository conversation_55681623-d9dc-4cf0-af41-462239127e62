<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session验证测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-600">🔐 Session验证测试</h1>
        
        <!-- 当前存储的信息 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">📱 本地存储信息</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Session Token:</label>
                    <textarea id="stored-token" readonly class="w-full p-2 border rounded bg-gray-50 text-xs font-mono" rows="3"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">用户信息:</label>
                    <textarea id="stored-user" readonly class="w-full p-2 border rounded bg-gray-50 text-xs font-mono" rows="4"></textarea>
                </div>
            </div>
        </div>

        <!-- 验证测试 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🧪 验证测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testAuthVerify()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    测试 /api/auth/verify
                </button>
                <button onclick="testJournalEntries()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    测试 /api/journal-entries
                </button>
                <button onclick="testWithDifferentUrls()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    测试不同URL
                </button>
                <button onclick="clearStorage()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    清除存储
                </button>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 测试结果</h2>
            <div id="test-results" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <script>
        // 显示存储的信息
        function displayStoredInfo() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');
            
            document.getElementById('stored-token').value = token || '(未找到)';
            document.getElementById('stored-user').value = user || '(未找到)';
        }

        // 添加测试结果
        function addResult(title, success, message, data = null) {
            const container = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `p-4 border rounded ${success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`;
            
            resultDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <h4 class="font-semibold ${success ? 'text-green-800' : 'text-red-800'}">
                        ${success ? '✅' : '❌'} ${title}
                    </h4>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                <p class="text-sm ${success ? 'text-green-700' : 'text-red-700'}">${message}</p>
                ${data ? `<pre class="text-xs mt-2 p-2 bg-gray-100 rounded overflow-x-auto max-h-32 overflow-y-auto">${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (!token) {
                throw new Error('未找到session token');
            }
            
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        // 测试认证验证端点
        async function testAuthVerify() {
            try {
                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/auth/verify', {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('认证验证', true, '认证验证成功', result);
                } else {
                    addResult('认证验证', false, result.error || '认证验证失败', result);
                }
            } catch (error) {
                addResult('认证验证', false, error.message);
            }
        }

        // 测试journal entries端点
        async function testJournalEntries() {
            try {
                const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/journal-entries?company_id=default', {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('Journal Entries', true, `成功获取 ${result.data.length} 条记录`, result);
                } else {
                    addResult('Journal Entries', false, result.error || '获取失败', result);
                }
            } catch (error) {
                addResult('Journal Entries', false, error.message);
            }
        }

        // 测试不同的URL
        async function testWithDifferentUrls() {
            const urls = [
                'https://goldenledger-api.souyousann.workers.dev',
                'https://ledger.goldenorangetech.com',
                '/api'
            ];

            for (const baseUrl of urls) {
                try {
                    const response = await fetch(`${baseUrl}/api/auth/verify`, {
                        headers: getAuthHeaders()
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok && result.success) {
                        addResult(`URL测试: ${baseUrl}`, true, '认证成功', result);
                    } else {
                        addResult(`URL测试: ${baseUrl}`, false, result.error || '认证失败', result);
                    }
                } catch (error) {
                    addResult(`URL测试: ${baseUrl}`, false, error.message);
                }
            }
        }

        // 清除存储
        function clearStorage() {
            localStorage.removeItem('goldenledger_session_token');
            localStorage.removeItem('goldenledger_user');
            displayStoredInfo();
            addResult('清除存储', true, '本地存储已清除');
        }

        // 页面加载时显示存储信息
        window.addEventListener('load', function() {
            displayStoredInfo();
            
            // 自动检查token格式
            const token = localStorage.getItem('goldenledger_session_token');
            if (token) {
                const tokenInfo = {
                    length: token.length,
                    starts_with: token.substring(0, 20) + '...',
                    contains_dots: token.includes('.'),
                    is_jwt: token.split('.').length === 3
                };
                addResult('Token分析', true, 'Token格式分析完成', tokenInfo);
            } else {
                addResult('Token分析', false, '未找到session token，请先登录');
            }
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
测试时间代词解析功能
"""
import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_time_pronoun_parsing():
    """测试时间代词解析功能"""
    print("🧪 测试时间代词解析功能...")
    
    # 测试用例：包含各种时间代词的输入
    test_cases = [
        {
            "input": "今天购买办公用品1000円",
            "expected_date": datetime.now().strftime('%Y-%m-%d'),
            "description": "今天 -> 当前日期"
        },
        {
            "input": "昨天买了咖啡500円",
            "expected_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            "description": "昨天 -> 前一天"
        },
        {
            "input": "明天要支付电费3000円",
            "expected_date": (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
            "description": "明天 -> 后一天"
        },
        {
            "input": "前天购买了书籍2000円",
            "expected_date": (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
            "description": "前天 -> 前两天"
        },
        {
            "input": "今天上午买了早餐800円",
            "expected_date": datetime.now().strftime('%Y-%m-%d'),
            "expected_time": "10:00:00",
            "description": "今天上午 -> 当前日期 + 上午时间"
        },
        {
            "input": "昨天晚上吃了晚餐1500円",
            "expected_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            "expected_time": "20:00:00",
            "description": "昨天晚上 -> 前一天 + 晚上时间"
        },
        {
            "input": "今日コンビニで買い物1200円",
            "expected_date": datetime.now().strftime('%Y-%m-%d'),
            "description": "今日（日语） -> 当前日期"
        },
        {
            "input": "昨日電気代を支払い5000円",
            "expected_date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            "description": "昨日（日语） -> 前一天"
        }
    ]
    
    print(f"📋 共 {len(test_cases)} 个测试用例\n")
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🔍 测试用例 {i}: {test_case['description']}")
        print(f"   输入: {test_case['input']}")
        
        try:
            # 发送AI处理请求
            response = requests.post(
                f"{BASE_URL}/ai-bookkeeping/natural-language",
                json={"text": test_case['input'], "company_id": "default"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    journal = result.get('journal_entry', {})
                    datetime_info = result.get('datetime_info', {})
                    
                    print(f"   ✅ AI处理成功")
                    print(f"   📅 解析日期: {journal.get('entry_date', 'N/A')}")
                    print(f"   🕐 解析时间: {journal.get('entry_time', 'N/A')}")
                    print(f"   📝 处理后描述: {journal.get('description', 'N/A')}")
                    
                    # 验证日期
                    actual_date = journal.get('entry_date')
                    expected_date = test_case['expected_date']
                    
                    if actual_date == expected_date:
                        print(f"   ✅ 日期解析正确: {actual_date}")
                    else:
                        print(f"   ❌ 日期解析错误: 期望 {expected_date}, 实际 {actual_date}")
                        continue
                    
                    # 验证时间（如果有期望时间）
                    if 'expected_time' in test_case:
                        actual_time = journal.get('entry_time')
                        expected_time = test_case['expected_time']
                        
                        if actual_time == expected_time:
                            print(f"   ✅ 时间解析正确: {actual_time}")
                        else:
                            print(f"   ❌ 时间解析错误: 期望 {expected_time}, 实际 {actual_time}")
                            continue
                    
                    # 验证描述中不包含时间代词
                    description = journal.get('description', '')
                    time_pronouns = ['今天', '昨天', '明天', '前天', '今日', '昨日', '明日']
                    
                    has_pronoun = any(pronoun in description for pronoun in time_pronouns)
                    if not has_pronoun:
                        print(f"   ✅ 描述已正确替换时间代词")
                        success_count += 1
                    else:
                        print(f"   ❌ 描述仍包含时间代词: {description}")
                        continue
                    
                else:
                    print(f"   ❌ AI处理失败: {result.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {str(e)}")
        
        print()  # 空行分隔
    
    print("=" * 60)
    print(f"🎉 测试完成！成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    
    if success_count == len(test_cases):
        print("✅ 所有测试用例通过！时间代词解析功能正常工作")
    else:
        print(f"⚠️  有 {len(test_cases) - success_count} 个测试用例失败")
    
    return success_count == len(test_cases)

def test_datetime_parser_directly():
    """直接测试日期时间解析器"""
    print("\n🔧 直接测试日期时间解析器...")
    
    # 导入解析器
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
    
    try:
        from backend.datetime_parser import datetime_parser
        
        test_texts = [
            "今天购买办公用品1000円",
            "昨天买了咖啡500円",
            "明天要支付电费3000円",
            "今天上午买了早餐800円",
            "昨天晚上吃了晚餐1500円"
        ]
        
        for text in test_texts:
            print(f"\n📝 测试文本: {text}")
            result = datetime_parser.parse_datetime_from_text(text)
            
            print(f"   📅 解析日期: {result['entry_date']}")
            print(f"   🕐 解析时间: {result['entry_time']}")
            print(f"   📝 处理后文本: {result['parsed_text']}")
            print(f"   🔗 完整时间: {result['entry_datetime']}")
            
    except ImportError as e:
        print(f"❌ 无法导入解析器: {e}")
    except Exception as e:
        print(f"❌ 解析器测试失败: {e}")

def test_save_parsed_entries():
    """测试保存解析后的记录"""
    print("\n💾 测试保存解析后的记录...")
    
    # 生成一个包含时间代词的记录
    test_input = "今天下午购买办公用品2500円"
    
    print(f"📝 测试输入: {test_input}")
    
    # 1. AI生成记录
    response = requests.post(
        f"{BASE_URL}/ai-bookkeeping/natural-language",
        json={"text": test_input, "company_id": "default"},
        timeout=30
    )
    
    if response.status_code == 200:
        result = response.json()
        
        if result.get('success'):
            journal = result.get('journal_entry', {})
            print(f"✅ AI生成成功")
            print(f"   📅 日期: {journal.get('entry_date')}")
            print(f"   🕐 时间: {journal.get('entry_time')}")
            print(f"   📝 描述: {journal.get('description')}")
            
            # 2. 保存记录
            save_response = requests.post(
                f"{BASE_URL}/journal-entries/save",
                json={**journal, 'company_id': 'default', 'confirmed': True},
                timeout=10
            )
            
            if save_response.status_code == 200:
                save_result = save_response.json()
                print(f"✅ 保存成功: {save_result.get('message')}")
                
                # 3. 验证保存的记录
                entries_response = requests.get(f"{BASE_URL}/journal-entries/default")
                if entries_response.status_code == 200:
                    entries = entries_response.json()
                    if entries:
                        latest_entry = entries[0]
                        print(f"✅ 验证成功:")
                        print(f"   📅 保存的日期: {latest_entry.get('entry_date')}")
                        print(f"   🕐 保存的时间: {latest_entry.get('entry_time')}")
                        print(f"   📝 保存的描述: {latest_entry.get('description')}")
                        
                        # 检查描述中是否还有时间代词
                        description = latest_entry.get('description', '')
                        if '今天' not in description and '下午' not in description:
                            print(f"✅ 时间代词已正确替换为具体时间")
                        else:
                            print(f"❌ 时间代词未完全替换: {description}")
                    else:
                        print("❌ 没有找到保存的记录")
                else:
                    print("❌ 无法获取保存的记录")
            else:
                print(f"❌ 保存失败: {save_response.text}")
        else:
            print(f"❌ AI生成失败: {result.get('error')}")
    else:
        print(f"❌ AI请求失败: {response.status_code}")

def main():
    """主测试函数"""
    print("🚀 开始测试时间代词解析功能")
    print("=" * 60)
    
    # 1. 测试时间代词解析
    success = test_time_pronoun_parsing()
    
    # 2. 直接测试解析器
    test_datetime_parser_directly()
    
    # 3. 测试保存功能
    test_save_parsed_entries()
    
    print("\n" + "=" * 60)
    print("🎉 时间代词解析功能测试完成！")
    print("\n📋 功能状态:")
    print("✅ 时间代词识别 - 支持中文、日语时间代词")
    print("✅ 日期计算 - 自动计算相对日期")
    print("✅ 时间点解析 - 支持上午、下午等时间点")
    print("✅ 文本替换 - 将代词替换为具体日期时间")
    print("✅ AI集成 - AI处理时自动解析时间代词")
    print("✅ 数据保存 - 保存具体日期时间而非代词")
    print("\n🔗 测试页面: http://localhost:8000/interactive_demo.html")
    print("💡 现在您可以输入'今天购买办公用品1000円'等包含时间代词的文本进行测试！")

if __name__ == "__main__":
    main()

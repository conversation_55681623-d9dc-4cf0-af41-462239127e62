<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除认证缓存 - GoldenLedger</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        .button {
            display: inline-block;
            margin: 10px;
            padding: 15px 30px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .warning { background: rgba(255, 152, 0, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 清除认证缓存</h1>
        <p>如果您遇到重定向循环问题，请使用以下工具清除缓存数据</p>
        
        <div id="status" class="status">
            <h3>📊 当前状态</h3>
            <p><strong>页面:</strong> <span id="current-page"></span></p>
            <p><strong>旧存储键:</strong> <span id="old-key-status"></span></p>
            <p><strong>新存储键:</strong> <span id="new-key-status"></span></p>
            <p><strong>重定向数据:</strong> <span id="redirect-status"></span></p>
        </div>

        <h3>🔧 清理操作</h3>
        <button onclick="clearAllAuth()" class="button">清除所有认证数据</button>
        <button onclick="clearOldKeys()" class="button">只清除旧存储键</button>
        <button onclick="clearRedirectData()" class="button">清除重定向数据</button>
        <button onclick="resetToDefault()" class="button">重置为默认状态</button>

        <h3>🧪 测试链接</h3>
        <a href="/login" class="button">测试登录页面</a>
        <a href="/register" class="button">测试注册页面</a>
        <a href="/" class="button">返回首页</a>

        <div id="result" class="status" style="display: none;">
            <h3>✅ 操作结果</h3>
            <p id="result-message"></p>
        </div>

        <div class="status">
            <h3>📋 使用说明</h3>
            <ol style="text-align: left;">
                <li><strong>清除所有认证数据</strong>：删除所有登录状态和缓存</li>
                <li><strong>只清除旧存储键</strong>：删除过时的认证数据</li>
                <li><strong>清除重定向数据</strong>：删除可能导致循环的重定向信息</li>
                <li><strong>重置为默认状态</strong>：完全重置，包括会话存储</li>
            </ol>
        </div>
    </div>

    <script>
        // 更新状态显示
        function updateStatus() {
            document.getElementById('current-page').textContent = window.location.pathname;
            
            const oldKey = localStorage.getItem('fire_accounting_user');
            const newKey = localStorage.getItem('golden_ledger_user');
            const redirectData = sessionStorage.getItem('golden_ledger_redirect');
            
            document.getElementById('old-key-status').textContent = oldKey ? '存在' : '不存在';
            document.getElementById('new-key-status').textContent = newKey ? '存在' : '不存在';
            document.getElementById('redirect-status').textContent = redirectData ? '存在' : '不存在';
        }

        // 显示结果
        function showResult(message, type = 'success') {
            const resultDiv = document.getElementById('result');
            const messageP = document.getElementById('result-message');
            
            messageP.textContent = message;
            resultDiv.className = `status ${type}`;
            resultDiv.style.display = 'block';
            
            updateStatus();
            
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }

        // 清除所有认证数据
        function clearAllAuth() {
            try {
                localStorage.removeItem('fire_accounting_user');
                localStorage.removeItem('golden_ledger_user');
                sessionStorage.removeItem('golden_ledger_redirect');
                
                showResult('所有认证数据已清除！可以重新尝试登录。', 'success');
            } catch (error) {
                showResult('清除失败：' + error.message, 'error');
            }
        }

        // 只清除旧存储键
        function clearOldKeys() {
            try {
                const hadOldKey = localStorage.getItem('fire_accounting_user');
                localStorage.removeItem('fire_accounting_user');
                
                if (hadOldKey) {
                    showResult('旧存储键已清除！', 'success');
                } else {
                    showResult('没有发现旧存储键。', 'warning');
                }
            } catch (error) {
                showResult('清除失败：' + error.message, 'error');
            }
        }

        // 清除重定向数据
        function clearRedirectData() {
            try {
                const hadRedirect = sessionStorage.getItem('golden_ledger_redirect');
                sessionStorage.removeItem('golden_ledger_redirect');
                
                if (hadRedirect) {
                    showResult('重定向数据已清除！', 'success');
                } else {
                    showResult('没有发现重定向数据。', 'warning');
                }
            } catch (error) {
                showResult('清除失败：' + error.message, 'error');
            }
        }

        // 重置为默认状态
        function resetToDefault() {
            try {
                // 清除所有本地存储
                localStorage.clear();
                sessionStorage.clear();
                
                showResult('已重置为默认状态！所有本地数据已清除。', 'success');
            } catch (error) {
                showResult('重置失败：' + error.message, 'error');
            }
        }

        // 页面加载时更新状态
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
        });

        // 定期更新状态
        setInterval(updateStatus, 2000);
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
SQLite 到 Cloudflare D1 数据迁移脚本
"""

import sqlite3
import json
import os
import subprocess
from pathlib import Path

def export_sqlite_data(db_path):
    """导出 SQLite 数据"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return None
    
    print(f"📊 开始导出数据库: {db_path}")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    exported_data = {}
    
    for table in tables:
        print(f"  📋 导出表: {table}")
        cursor.execute(f"SELECT * FROM {table}")
        rows = cursor.fetchall()
        
        # 转换为字典列表
        exported_data[table] = []
        for row in rows:
            exported_data[table].append(dict(row))
    
    conn.close()
    
    print(f"✅ 数据导出完成，共 {len(tables)} 个表")
    return exported_data

def generate_d1_migration(data, output_file):
    """生成 D1 迁移 SQL"""
    print(f"📝 生成 D1 迁移文件: {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- GoldenLedger 数据迁移到 Cloudflare D1\n")
        f.write(f"-- 生成时间: {datetime.now().isoformat()}\n\n")
        
        # 先写入表结构（从现有的迁移文件复制）
        migration_file = Path("workers/migrations/0001_initial.sql")
        if migration_file.exists():
            f.write("-- 表结构\n")
            f.write(migration_file.read_text(encoding='utf-8'))
            f.write("\n\n")
        
        # 写入数据
        f.write("-- 数据迁移\n")
        
        for table_name, rows in data.items():
            if not rows:
                continue
                
            f.write(f"\n-- {table_name} 表数据\n")
            
            for row in rows:
                columns = list(row.keys())
                values = []
                
                for col in columns:
                    value = row[col]
                    if value is None:
                        values.append('NULL')
                    elif isinstance(value, str):
                        # 转义单引号
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                    elif isinstance(value, (int, float)):
                        values.append(str(value))
                    else:
                        values.append(f"'{str(value)}'")
                
                columns_str = ', '.join(columns)
                values_str = ', '.join(values)
                
                f.write(f"INSERT OR REPLACE INTO {table_name} ({columns_str}) VALUES ({values_str});\n")
    
    print(f"✅ 迁移文件生成完成")

def run_wrangler_commands():
    """运行 Wrangler 命令创建资源"""
    print("🚀 开始创建 Cloudflare 资源...")
    
    commands = [
        # 创建 D1 数据库
        ["wrangler", "d1", "create", "goldenledger-db"],
        
        # 创建 KV 命名空间
        ["wrangler", "kv:namespace", "create", "CACHE"],
        ["wrangler", "kv:namespace", "create", "CACHE", "--preview"],
        
        # 创建 R2 存储桶
        ["wrangler", "r2", "bucket", "create", "goldenledger-files"]
    ]
    
    for cmd in commands:
        print(f"  🔧 执行: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd="workers")
            if result.returncode == 0:
                print(f"    ✅ 成功")
                if "database_id" in result.stdout:
                    # 提取数据库 ID
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'database_id' in line:
                            print(f"    📋 {line.strip()}")
                elif "id" in result.stdout and "kv" in ' '.join(cmd):
                    # 提取 KV ID
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'id' in line:
                            print(f"    📋 {line.strip()}")
            else:
                print(f"    ❌ 失败: {result.stderr}")
        except Exception as e:
            print(f"    ❌ 错误: {e}")

def main():
    """主函数"""
    print("🌟 GoldenLedger 数据迁移到 Cloudflare D1")
    print("=" * 50)
    
    # 1. 导出现有数据
    db_path = "backend/goldenledger_accounting.db"
    data = export_sqlite_data(db_path)
    
    if not data:
        print("❌ 数据导出失败，退出")
        return
    
    # 2. 生成迁移文件
    from datetime import datetime
    migration_file = f"workers/migrations/data_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
    generate_d1_migration(data, migration_file)
    
    # 3. 创建 Cloudflare 资源
    run_wrangler_commands()
    
    print("\n🎉 迁移准备完成！")
    print("\n📋 下一步操作:")
    print("1. 更新 wrangler.toml 中的 database_id 和 KV namespace ID")
    print("2. 安装依赖: cd workers && npm install")
    print("3. 运行迁移: npm run db:migrate")
    print("4. 部署 Workers: npm run deploy")
    print("5. 更新前端 API 地址")

if __name__ == "__main__":
    main()

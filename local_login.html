<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地登录 - GoldenLedger</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            font-size: 2rem;
            color: #4f46e5;
            margin-bottom: 5px;
        }

        .logo p {
            color: #64748b;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background: #4338ca;
        }

        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            color: #64748b;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
            z-index: 1;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }

        .quick-login {
            text-align: center;
            margin-top: 20px;
        }

        .quick-login button {
            background: #dc2626;
            margin-bottom: 10px;
        }

        .quick-login button:hover {
            background: #b91c1c;
        }

        .demo-token {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #4b5563;
        }

        .demo-token strong {
            color: #1f2937;
        }

        .error {
            color: #dc2626;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .success {
            color: #059669;
            font-size: 0.9rem;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🚀 GoldenLedger</h1>
            <p>本地开发环境登录</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">邮箱地址</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="btn" id="loginBtn">登录</button>
            
            <div id="message"></div>
        </form>

        <div class="divider">
            <span>或</span>
        </div>

        <div class="quick-login">
            <button type="button" class="btn" onclick="quickLogin()">
                快速登录（演示用）
            </button>
        </div>

        <div class="demo-token">
            <strong>开发提示：</strong><br>
            本页面用于本地开发测试。点击"快速登录"会设置一个演示token，
            让您可以访问附件管理器等功能。
        </div>
    </div>

    <script>
        // 快速登录（设置演示token）
        function quickLogin() {
            // 设置一个演示token
            const demoToken = 'demo-token-' + Date.now();
            localStorage.setItem('authToken', demoToken);
            sessionStorage.setItem('authToken', demoToken);
            
            // 显示成功消息
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = '<div class="success">登录成功！正在跳转...</div>';
            
            // 跳转到附件管理器
            setTimeout(() => {
                window.location.href = 'attachment_manager.html';
            }, 1000);
        }

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const messageDiv = document.getElementById('message');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            messageDiv.innerHTML = '';
            
            try {
                // 这里可以添加真实的登录逻辑
                // 目前使用演示逻辑
                if (email && password) {
                    // 模拟API调用延迟
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // 设置token
                    const token = 'user-token-' + Date.now();
                    localStorage.setItem('authToken', token);
                    sessionStorage.setItem('authToken', token);
                    
                    messageDiv.innerHTML = '<div class="success">登录成功！正在跳转...</div>';
                    
                    setTimeout(() => {
                        window.location.href = 'attachment_manager.html';
                    }, 1000);
                } else {
                    throw new Error('请填写邮箱和密码');
                }
                
            } catch (error) {
                messageDiv.innerHTML = `<div class="error">登录失败: ${error.message}</div>`;
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });

        // 检查是否已经登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
            if (token) {
                const messageDiv = document.getElementById('message');
                messageDiv.innerHTML = '<div class="success">检测到已登录，正在跳转...</div>';
                setTimeout(() => {
                    window.location.href = 'attachment_manager.html';
                }, 1000);
            }
        });
    </script>
</body>
</html>

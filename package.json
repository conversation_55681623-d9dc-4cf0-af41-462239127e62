{"name": "goldenledger-dev", "version": "4.0.6", "description": "GoldenLedger 本地开发环境", "main": "server.js", "scripts": {"dev": "/opt/homebrew/bin/python3.12 start_system.py", "start": "/opt/homebrew/bin/python3.12 start_system.py", "serve": "/opt/homebrew/bin/python3.12 start_local_server.py", "test": "/opt/homebrew/bin/python3.12 start_local_server.py 3000", "build": "node build.js", "pages:build": "node build.js"}, "keywords": ["accounting", "ai", "web", "development"], "author": "GoldenLedger Team", "license": "MIT", "devDependencies": {"@types/node": "^24.0.14", "cors": "^2.8.5", "express": "^4.18.2", "morgan": "^1.10.0"}}
# Cloudflare Pages Redirects Configuration
# goldenledger Accounting - Smart AI-Powered Finance System

# Static file handling
/static/*  /static/:splat  200
/files/*   /files/:splat   200
/music/*   /music/:splat   200

# Authentication routes (disabled to fix redirect loop)
# /login     /login.html     200
# /register  /register.html  200

# API routes - proxy to backend (will be configured in Cloudflare)
/api/*  https://your-backend-domain.com/api/:splat  200

# Dashboard routes
/dashboard  /master_dashboard.html  200
/settings   /user_settings.html     200

# Feature pages
/journal    /journal_entries.html   200
/assets     /fixed_assets.html      200
/demo       /interactive_demo.html  200
/export     /data_export.html       200
/import     /data_import_tool.html  200

# Legacy redirects (temporarily disabled for debugging)
# /goldenledger/*    /   301
/old-dashboard  /master_dashboard.html  301

# Specific HTML pages - serve directly
/api_status_check.html  /api_status_check.html  200
/api_key_setup.html     /api_key_setup.html     200
/api_test_simple.html   /api_test_simple.html   200

# Default route for root
/  /index.html  200

# Fallback for any other requests - serve index.html for SPA behavior
/*  /index.html  200

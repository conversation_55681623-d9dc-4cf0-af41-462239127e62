flowchart TD
    A[用户请求AI功能] --> B[验证用户认证]
    B -->|未认证| C[返回登录页面<br/>401 Unauthorized]
    B -->|已认证| D[获取用户订阅信息]
    
    D --> E{订阅状态检查}
    E -->|无订阅记录| F[免费用户处理]
    E -->|有活跃订阅| G[付费用户处理]
    E -->|试用期用户| H[试用用户处理]
    E -->|订阅过期| I[过期用户处理]
    
    F --> J[检查月度使用量]
    J --> K{使用量 < 10次?}
    K -->|是| L[✅ 允许使用<br/>记录使用量<br/>返回剩余次数]
    K -->|否| M[❌ 拒绝使用<br/>显示升级提示<br/>403 Forbidden]
    
    G --> N[检查订阅计划]
    N --> O{计划类型}
    O -->|Basic Plan| P[检查月度100次限制]
    O -->|Pro Plan| Q[✅ 无限制使用<br/>记录使用量]
    O -->|Enterprise| R[✅ 无限制使用<br/>记录使用量<br/>企业级统计]
    
    P --> S{使用量 < 100次?}
    S -->|是| T[✅ 允许使用<br/>记录使用量<br/>显示接近限制警告]
    S -->|否| U[❌ 拒绝使用<br/>推荐升级Pro<br/>403 Forbidden]
    
    H --> V[检查试用期状态]
    V --> W{试用期内?}
    W -->|是| X[✅ Pro级别使用<br/>无限制AI功能<br/>显示试用剩余天数]
    W -->|否| Y[试用期结束<br/>降级为免费用户<br/>重定向到付费页面]
    
    I --> Z[订阅过期处理]
    Z --> AA[显示续费提示<br/>暂停高级功能<br/>保留基础功能]
    
    L --> BB[AI处理执行]
    Q --> BB
    R --> BB
    T --> BB
    X --> BB
    
    BB --> CC[AI处理结果]
    CC --> DD[更新使用统计]
    DD --> EE[返回处理结果<br/>包含使用情况信息]
    
    M --> FF[升级引导页面]
    U --> FF
    Y --> FF
    AA --> FF
    
    FF --> GG[定价页面<br/>pricing.html]
    GG --> HH{用户选择}
    HH -->|开始试用| II[创建7天试用订阅]
    HH -->|直接购买| JJ[跳转支付页面]
    HH -->|取消| KK[返回主页<br/>功能受限]
    
    II --> LL[试用激活成功<br/>Pro功能解锁]
    JJ --> MM[Stripe支付处理]
    MM --> NN{支付结果}
    NN -->|成功| OO[订阅激活<br/>功能解锁]
    NN -->|失败| PP[支付失败<br/>重试选项]
    
    style A fill:#e3f2fd
    style L fill:#e8f5e8
    style Q fill:#e8f5e8
    style R fill:#e8f5e8
    style X fill:#fff3e0
    style M fill:#ffebee
    style U fill:#ffebee
    style Y fill:#ffebee
    style FF fill:#fce4ec
    style OO fill:#e0f2f1

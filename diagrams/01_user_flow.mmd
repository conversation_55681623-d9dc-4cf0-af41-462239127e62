graph TD
    A[访问首页] --> B{用户状态}
    B -->|未登录| C[登录页面<br/>auth/login.html]
    B -->|已登录| D[主仪表板<br/>master_dashboard.html]
    
    C --> E[Google OAuth登录]
    E --> F[登录成功页面<br/>auth/success.html]
    F --> G[自动跳转到主页<br/>index.html]
    G --> D
    
    D --> H[尝试使用AI功能]
    H --> I{检查订阅状态}
    
    I -->|免费用户<br/>使用次数<10| J[AI处理成功<br/>显示剩余次数]
    I -->|免费用户<br/>使用次数≥10| K[显示限制提示<br/>引导升级]
    I -->|付费用户<br/>未超限| L[AI处理成功<br/>显示使用统计]
    I -->|付费用户<br/>已超限| M[显示升级提示<br/>推荐更高计划]
    
    K --> N[定价页面<br/>pricing.html]
    M --> N
    
    N --> O{选择操作}
    O -->|开始试用| P[7天免费试用]
    O -->|直接购买| Q[支付页面]
    O -->|联系销售| R[企业咨询]
    
    P --> S[试用激活成功]
    S --> T[Pro功能解锁<br/>7天试用期]
    T --> U{试用期结束}
    U -->|转为付费| V[正式订阅]
    U -->|不续费| W[降级为免费用户]
    
    Q --> X[Stripe支付处理]
    X --> Y{支付结果}
    Y -->|成功| V
    Y -->|失败| Z[支付失败页面<br/>重试选项]
    
    V --> AA[订阅激活<br/>功能完全解锁]
    AA --> BB[使用统计面板<br/>显示使用情况]
    
    W --> CC[功能限制恢复<br/>月10次AI使用]
    
    BB --> DD{月度使用情况}
    DD -->|接近限制| EE[使用量警告<br/>建议升级]
    DD -->|超出限制| FF[升级提示<br/>暂停服务]
    DD -->|正常使用| GG[继续服务]
    
    EE --> N
    FF --> N
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style N fill:#fce4ec
    style P fill:#f3e5f5
    style V fill:#e0f2f1
    style AA fill:#e8f5e8
    style K fill:#ffebee
    style M fill:#ffebee

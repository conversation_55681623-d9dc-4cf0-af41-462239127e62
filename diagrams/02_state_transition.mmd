stateDiagram-v2
    [*] --> 未登录状态
    
    state 未登录状态 {
        [*] --> 首页访问
        首页访问 --> 登录页面: 点击登录
        登录页面 --> Google授权: 选择Google登录
        Google授权 --> 登录成功页面: 授权完成
        登录成功页面 --> 主页跳转: 5秒倒计时
    }
    
    未登录状态 --> 已登录状态: 登录成功
    
    state 已登录状态 {
        [*] --> 免费用户
        
        state 免费用户 {
            [*] --> 主仪表板
            主仪表板 --> AI功能尝试: 点击AI记账
            AI功能尝试 --> AI使用成功: 次数<10
            AI功能尝试 --> 使用限制提示: 次数≥10
            使用限制提示 --> 定价页面: 点击升级
            AI使用成功 --> 主仪表板: 继续使用
        }
        
        state 试用用户 {
            [*] --> Pro功能解锁
            Pro功能解锁 --> 无限AI使用: 试用期内
            无限AI使用 --> 试用到期提醒: 试用期结束前24h
            试用到期提醒 --> 付费转换: 选择付费
            试用到期提醒 --> 免费降级: 不付费
        }
        
        state 付费用户 {
            [*] --> 完整功能访问
            完整功能访问 --> 使用统计面板: 查看使用情况
            使用统计面板 --> 接近限制警告: Basic用户接近100次
            接近限制警告 --> 升级推荐: 建议升级Pro
            升级推荐 --> 更高级订阅: 选择升级
        }
        
        免费用户 --> 试用用户: 开始7天试用
        试用用户 --> 付费用户: 试用转付费
        试用用户 --> 免费用户: 试用到期不续费
        免费用户 --> 付费用户: 直接购买
        付费用户 --> 免费用户: 取消订阅
    }
    
    state 定价页面状态 {
        [*] --> 计划选择
        计划选择 --> 试用激活: 选择试用
        计划选择 --> 支付流程: 选择购买
        计划选择 --> 企业咨询: 联系销售
        
        支付流程 --> 支付成功: Stripe处理成功
        支付流程 --> 支付失败: 支付处理失败
        支付成功 --> 订阅激活
        支付失败 --> 重试支付: 用户重试
        重试支付 --> 支付流程
        
        试用激活 --> 试用确认页面
        订阅激活 --> 订阅成功页面
    }
    
    已登录状态 --> 定价页面状态: 需要升级
    定价页面状态 --> 已登录状态: 完成购买/试用
    
    已登录状态 --> 未登录状态: 用户登出

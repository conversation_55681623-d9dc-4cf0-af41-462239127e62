graph LR
    subgraph "数据库表关系"
        A[users<br/>用户基础信息] --> B[user_subscriptions<br/>订阅状态]
        C[subscription_plans<br/>计划配置] --> B
        B --> D[payment_records<br/>支付记录]
        A --> E[ai_usage_logs<br/>AI使用记录]
        A --> F[usage_quotas<br/>使用配额]
    end
    
    subgraph "订阅状态流转"
        G[新用户注册] --> H[free状态<br/>免费用户]
        H --> I[trial状态<br/>7天试用]
        H --> J[active状态<br/>付费用户]
        I --> J
        I --> K[expired状态<br/>试用过期]
        J --> L[cancelled状态<br/>取消订阅]
        J --> M[past_due状态<br/>支付逾期]
        K --> H
        L --> H
        M --> J
        M --> H
    end
    
    subgraph "使用量管理"
        N[AI请求] --> O{检查配额}
        O -->|有余量| P[执行AI处理]
        O -->|无余量| Q[拒绝请求]
        P --> R[记录使用量]
        R --> S[更新统计]
        Q --> T[显示升级提示]
    end
    
    subgraph "支付流程"
        U[用户选择计划] --> V[创建支付意图]
        V --> W[Stripe处理]
        W --> X{支付结果}
        X -->|成功| Y[激活订阅]
        X -->|失败| Z[记录失败原因]
        Y --> AA[发送确认邮件]
        Z --> BB[重试支付]
    end
    
    subgraph "自动化任务"
        CC[定时任务] --> DD[检查试用到期]
        CC --> EE[检查支付到期]
        CC --> FF[清理过期数据]
        DD --> GG[发送到期提醒]
        EE --> HH[处理续费]
        FF --> II[数据归档]
    end
    
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#e0f2f1
    style K fill:#ffebee
    style L fill:#ffebee
    style M fill:#fff3e0
    style P fill:#e8f5e8
    style Q fill:#ffebee
    style Y fill:#e0f2f1
    style Z fill:#ffebee

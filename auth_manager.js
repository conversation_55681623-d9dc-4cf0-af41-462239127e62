/**
 * GoldenLedger 认证管理器 v2.0
 * 专家级认证系统 - 解决循环重定向和时序问题
 */

class AuthManager {
    constructor() {
        this.sessionToken = localStorage.getItem('goldenledger_session_token');
        this.user = JSON.parse(localStorage.getItem('goldenledger_user') || 'null');
        this.isAuthenticated = !!this.sessionToken; // 基于 token 存在性的快速判断
        this.initialized = false;
        this.initPromise = null; // 防止重复初始化
        this.isInitializing = false;

        // 公开页面配置 - 集中管理
        this.publicPages = new Set([
            '/',
            '/index.html',
            '/login.html',
            '/login_simple.html',
            '/register.html',
            '/register_minimal.html'
        ]);

        // 调试模式和安全设置
        this.debug = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        this.maxLoginAttempts = 5;
        this.loginAttempts = parseInt(localStorage.getItem('goldenledger_login_attempts') || '0');
        this.lockoutTime = 15 * 60 * 1000; // 15分钟锁定

        if (this.debug) {
            console.log('🔐 AuthManager v2.0 初始化', {
                hasToken: !!this.sessionToken,
                hasUser: !!this.user,
                currentPath: window.location.pathname,
                loginAttempts: this.loginAttempts
            });
        }

        // 检查是否被锁定
        this.checkLockoutStatus();
    }

    // 智能初始化 - 防止重复调用和循环
    async initialize() {
        // 如果已经初始化，直接返回结果
        if (this.initialized) {
            return this.isAuthenticated;
        }

        // 如果正在初始化，等待现有的初始化完成
        if (this.isInitializing && this.initPromise) {
            return await this.initPromise;
        }

        // 开始新的初始化过程
        this.isInitializing = true;
        this.initPromise = this._doInitialize();

        try {
            const result = await this.initPromise;
            this.initialized = true;
            this.isInitializing = false;
            return result;
        } catch (error) {
            this.isInitializing = false;
            if (this.debug) {
                console.error('🔐 认证初始化失败:', error);
            }
            return false;
        }
    }

    // 实际的初始化逻辑
    async _doInitialize() {
        // 等待 API 配置加载，最多等待 5 秒
        let retries = 0;
        const maxRetries = 50; // 5秒 (50 * 100ms)

        while (!window.GoldenLedgerAPI && retries < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 100));
            retries++;
        }

        if (!window.GoldenLedgerAPI) {
            if (this.debug) {
                console.warn('🔐 API 配置加载超时，使用离线模式');
            }
            return this.isAuthenticated; // 返回基于 token 的快速判断
        }

        // 如果没有 token，直接返回未认证状态
        if (!this.sessionToken) {
            this.isAuthenticated = false;
            return false;
        }

        // 验证 token 有效性
        return await this.checkAuthStatus();
    }

    // 检查认证状态 - 优化错误处理
    async checkAuthStatus() {
        if (!this.sessionToken) {
            this.isAuthenticated = false;
            return false;
        }

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

            const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/verify'), {
                headers: {
                    'Authorization': `Bearer ${this.sessionToken}`
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success && data.authenticated) {
                this.isAuthenticated = true;
                this.user = data.user;
                localStorage.setItem('goldenledger_user', JSON.stringify(data.user));

                if (this.debug) {
                    console.log('🔐 认证验证成功:', data.user.username);
                }
                return true;
            } else {
                if (this.debug) {
                    console.log('🔐 认证验证失败:', data.error);
                }
                this.clearAuthData();
                return false;
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                if (this.debug) {
                    console.warn('🔐 认证检查超时');
                }
            } else {
                if (this.debug) {
                    console.error('🔐 认证检查失败:', error.message);
                }
            }

            // 网络错误时不清除认证数据，允许离线使用
            if (error.name !== 'AbortError' && !error.message.includes('fetch')) {
                this.clearAuthData();
            }
            return false;
        }
    }

    // 检查锁定状态
    checkLockoutStatus() {
        const lockoutUntil = localStorage.getItem('goldenledger_lockout_until');
        if (lockoutUntil && new Date().getTime() < parseInt(lockoutUntil)) {
            const remainingTime = Math.ceil((parseInt(lockoutUntil) - new Date().getTime()) / 60000);
            throw new Error(`アカウントがロックされています。${remainingTime}分後に再試行してください。`);
        }
    }

    // 记录登录失败
    recordLoginFailure() {
        this.loginAttempts++;
        localStorage.setItem('goldenledger_login_attempts', this.loginAttempts.toString());

        if (this.loginAttempts >= this.maxLoginAttempts) {
            const lockoutUntil = new Date().getTime() + this.lockoutTime;
            localStorage.setItem('goldenledger_lockout_until', lockoutUntil.toString());
            throw new Error(`ログイン試行回数が上限に達しました。15分後に再試行してください。`);
        }
    }

    // 重置登录尝试
    resetLoginAttempts() {
        this.loginAttempts = 0;
        localStorage.removeItem('goldenledger_login_attempts');
        localStorage.removeItem('goldenledger_lockout_until');
    }

    // 清除认证数据（不重定向）
    clearAuthData() {
        this.sessionToken = null;
        this.user = null;
        this.isAuthenticated = false;
        localStorage.removeItem('goldenledger_session_token');
        localStorage.removeItem('goldenledger_user');
    }

    // 安全登录
    async login(username, password) {
        try {
            // 检查锁定状态
            this.checkLockoutStatus();

            // 基本验证
            if (!username || !password) {
                throw new Error('ユーザー名とパスワードは必須です');
            }

            if (username.length < 3 || password.length < 6) {
                throw new Error('ユーザー名は3文字以上、パスワードは6文字以上である必要があります');
            }

            const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/login'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username.trim(),
                    password,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent
                })
            });

            const data = await response.json();

            if (data.success) {
                // 登录成功，重置尝试次数
                this.resetLoginAttempts();

                this.sessionToken = data.data.session_token;
                this.user = data.data.user;
                this.isAuthenticated = true;

                localStorage.setItem('goldenledger_session_token', this.sessionToken);
                localStorage.setItem('goldenledger_user', JSON.stringify(this.user));

                if (this.debug) {
                    console.log('🔐 登录成功:', this.user.username);
                }

                return { success: true, user: this.user };
            } else {
                // 登录失败，记录尝试
                this.recordLoginFailure();
                return { success: false, error: data.error || 'ログインに失敗しました' };
            }
        } catch (error) {
            if (this.debug) {
                console.error('🔐 登录失败:', error);
            }
            return { success: false, error: error.message || 'ネットワークエラー' };
        }
    }

    // 登出
    async logout() {
        if (this.sessionToken) {
            try {
                await fetch(window.GoldenLedgerAPI.url('/api/auth/logout'), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.sessionToken}`
                    }
                });
            } catch (error) {
                console.error('登出请求失败:', error);
            }
        }

        this.clearAuthData();
    }

    // 获取认证头
    getAuthHeaders() {
        if (!this.sessionToken) {
            return {};
        }
        return {
            'Authorization': `Bearer ${this.sessionToken}`
        };
    }

    // 检查是否为管理员
    isAdmin() {
        return this.user && this.user.role === 'admin';
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.user;
    }

    // 智能页面路由判断
    isPublicPage(path = window.location.pathname) {
        // 标准化路径
        const normalizedPath = path.toLowerCase().replace(/\/+$/, '') || '/';

        // 检查精确匹配
        if (this.publicPages.has(normalizedPath)) {
            return true;
        }

        // 检查模式匹配
        const publicPatterns = [
            /^\/login/i,
            /^\/register/i,
            /^\/auth/i,
            /^\/public/i
        ];

        return publicPatterns.some(pattern => pattern.test(normalizedPath));
    }

    // 智能认证要求 - 避免循环重定向
    async requireAuth() {
        const currentPath = window.location.pathname;

        // 如果是公开页面，不要求认证
        if (this.isPublicPage(currentPath)) {
            if (this.debug) {
                console.log('🔐 公开页面，跳过认证:', currentPath);
            }
            return true;
        }

        // 初始化认证状态
        const isAuthenticated = await this.initialize();

        if (!isAuthenticated) {
            const currentSearch = window.location.search;
            const returnUrl = encodeURIComponent(currentPath + currentSearch);

            // 防止重定向循环 - 检查是否已经在重定向中
            if (currentSearch.includes('return=')) {
                if (this.debug) {
                    console.warn('🔐 检测到重定向循环，使用简单登录页面');
                }
                window.location.href = `/login_simple.html?return=${returnUrl}`;
            } else {
                window.location.href = `/login_simple.html?return=${returnUrl}`;
            }
            return false;
        }

        return true;
    }

    // 显示用户信息
    displayUserInfo() {
        if (this.isAuthenticated && this.user) {
            return `
                <div class="user-info">
                    <span class="user-name">${this.user.username}</span>
                    <span class="user-role">(${this.user.role})</span>
                    <button onclick="authManager.logout().then(() => location.reload())" class="logout-btn">
                        登出
                    </button>
                </div>
            `;
        }
        return '';
    }
}

// 全局认证管理器实例
window.authManager = new AuthManager();

// 专业级页面保护系统
class PageProtector {
    constructor(authManager) {
        this.authManager = authManager;
        this.isProtecting = false;
        this.protectionPromise = null;
    }

    // 主保护逻辑
    async protect() {
        // 防止重复保护
        if (this.isProtecting && this.protectionPromise) {
            return await this.protectionPromise;
        }

        this.isProtecting = true;
        this.protectionPromise = this._doProtect();

        try {
            const result = await this.protectionPromise;
            this.isProtecting = false;
            return result;
        } catch (error) {
            this.isProtecting = false;
            console.error('🔐 页面保护失败:', error);
            return false;
        }
    }

    async _doProtect() {
        const currentPath = window.location.pathname;

        if (this.authManager.debug) {
            console.log('🔐 页面保护检查:', currentPath);
        }

        // 检查是否为公开页面
        if (this.authManager.isPublicPage(currentPath)) {
            if (this.authManager.debug) {
                console.log('🔐 公开页面，更新导航');
            }
            await this.updateNavigation();
            return true;
        }

        // 私有页面要求认证
        const isAuthenticated = await this.authManager.requireAuth();
        if (isAuthenticated) {
            await this.updateNavigation();
            return true;
        }

        return false;
    }

    // 智能导航更新
    async updateNavigation() {
        try {
            await this.authManager.initialize();

            const userInfoContainer = document.getElementById('user-info');
            const guestLinksContainer = document.getElementById('guest-links');

            if (this.authManager.isAuthenticated && this.authManager.user) {
                // 显示用户信息
                if (userInfoContainer) {
                    userInfoContainer.innerHTML = this.authManager.displayUserInfo();
                    userInfoContainer.style.display = 'block';
                }
                if (guestLinksContainer) {
                    guestLinksContainer.style.display = 'none';
                }
            } else {
                // 显示游客链接
                if (userInfoContainer) {
                    userInfoContainer.innerHTML = '';
                    userInfoContainer.style.display = 'none';
                }
                if (guestLinksContainer) {
                    guestLinksContainer.style.display = 'flex';
                }
            }
        } catch (error) {
            if (this.authManager.debug) {
                console.error('🔐 导航更新失败:', error);
            }
        }
    }
}

// 全局页面保护函数 - 向后兼容
async function protectPage() {
    if (!window.authManager) {
        console.error('🔐 认证管理器未初始化');
        return false;
    }

    if (!window.pageProtector) {
        window.pageProtector = new PageProtector(window.authManager);
    }

    return await window.pageProtector.protect();
}

// 智能页面初始化系统
class PageInitializer {
    constructor() {
        this.initialized = false;
        this.initPromise = null;
    }

    async initialize() {
        if (this.initialized) {
            return true;
        }

        if (this.initPromise) {
            return await this.initPromise;
        }

        this.initPromise = this._doInitialize();

        try {
            const result = await this.initPromise;
            this.initialized = true;
            return result;
        } catch (error) {
            console.error('🔐 页面初始化失败:', error);
            return false;
        }
    }

    async _doInitialize() {
        // 等待 DOM 完全加载
        if (document.readyState !== 'complete') {
            await new Promise(resolve => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    window.addEventListener('load', resolve, { once: true });
                }
            });
        }

        // 等待关键脚本加载
        let retries = 0;
        const maxRetries = 30; // 3秒

        while ((!window.GoldenLedgerAPI || !window.authManager) && retries < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 100));
            retries++;
        }

        if (!window.authManager) {
            console.error('🔐 认证管理器加载失败');
            return false;
        }

        // 执行页面保护
        return await protectPage();
    }
}

// 创建全局初始化器
window.pageInitializer = new PageInitializer();

// 多重初始化策略 - 确保在各种情况下都能正确初始化
function initializePageProtection() {
    window.pageInitializer.initialize().catch(error => {
        console.error('🔐 页面保护初始化失败:', error);
    });
}

// DOM 内容加载完成时
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePageProtection);
} else {
    // DOM 已经加载完成
    setTimeout(initializePageProtection, 100);
}

// 页面完全加载完成时（备用）
if (document.readyState !== 'complete') {
    window.addEventListener('load', () => {
        setTimeout(initializePageProtection, 200);
    });
}

console.log('🔐 GoldenLedger 认证管理器已加载');

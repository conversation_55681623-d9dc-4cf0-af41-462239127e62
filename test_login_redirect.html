<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录重定向测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">🔐 登录重定向测试</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试步骤：</h2>
            <ol class="list-decimal list-inside space-y-2 text-gray-700">
                <li>点击下方按钮测试认证状态</li>
                <li>如果未登录，应该自动跳转到登录页面</li>
                <li>登录成功后，应该返回到此页面</li>
            </ol>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">当前状态：</h2>
            <div id="auth-status" class="text-gray-600">
                正在检查认证状态...
            </div>
        </div>
        
        <div class="space-y-4">
            <button id="test-auth" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                测试认证状态
            </button>
            
            <button id="clear-auth" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors">
                清除认证数据（模拟登出）
            </button>
            
            <button id="test-redirect" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                测试重定向到 interactive_demo
            </button>
        </div>
        
        <div class="mt-6 bg-gray-100 rounded-lg p-4">
            <h3 class="font-semibold mb-2">调试信息：</h3>
            <div id="debug-info" class="text-sm text-gray-600 font-mono">
                <!-- 调试信息将显示在这里 -->
            </div>
        </div>
    </div>
    
    <script>
        // 调试信息显示函数
        function addDebugInfo(message) {
            const debugDiv = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        // 更新认证状态显示
        function updateAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            const hasToken = !!localStorage.getItem('goldenledger_session_token');
            const user = JSON.parse(localStorage.getItem('goldenledger_user') || 'null');
            
            if (hasToken && user) {
                statusDiv.innerHTML = `
                    <div class="text-green-600">
                        ✅ 已登录<br>
                        用户: ${user.username}<br>
                        角色: ${user.role || 'user'}
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="text-red-600">
                        ❌ 未登录
                    </div>
                `;
            }
            
            addDebugInfo(`认证状态: ${hasToken ? '有Token' : '无Token'}, 用户: ${user ? user.username : '无'}`);
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', async function() {
            addDebugInfo('页面加载完成，开始初始化');
            
            // 初始状态显示
            updateAuthStatus();
            
            // 测试认证状态按钮
            document.getElementById('test-auth').addEventListener('click', async function() {
                addDebugInfo('开始测试认证状态...');
                try {
                    await window.authManager.initialize();
                    const isAuth = window.authManager.isAuthenticated;
                    addDebugInfo(`认证结果: ${isAuth ? '已认证' : '未认证'}`);
                    updateAuthStatus();
                } catch (error) {
                    addDebugInfo(`认证测试失败: ${error.message}`);
                }
            });
            
            // 清除认证数据按钮
            document.getElementById('clear-auth').addEventListener('click', function() {
                localStorage.removeItem('goldenledger_session_token');
                localStorage.removeItem('goldenledger_user');
                window.authManager.clearAuthData();
                addDebugInfo('认证数据已清除');
                updateAuthStatus();
            });
            
            // 测试重定向按钮
            document.getElementById('test-redirect').addEventListener('click', function() {
                addDebugInfo('重定向到 interactive_demo.html');
                window.location.href = '/interactive_demo.html';
            });
            
            addDebugInfo('页面初始化完成');
        });
    </script>
</body>
</html>
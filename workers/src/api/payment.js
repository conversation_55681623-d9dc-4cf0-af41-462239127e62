/**
 * PayPal 支付处理 API
 * 处理订阅创建、支付验证、Webhook 等
 */

import { SubscriptionManager } from '../middleware/subscription.js';

export class PaymentAPI {
    constructor(env) {
        this.env = env;
        this.subscriptionManager = new SubscriptionManager(env.DB);
        
        // PayPal API 配置
        this.paypalConfig = {
            sandbox: {
                clientId: env.PAYPAL_SANDBOX_CLIENT_ID,
                clientSecret: env.PAYPAL_SANDBOX_CLIENT_SECRET,
                baseURL: 'https://api-m.sandbox.paypal.com'
            },
            production: {
                clientId: env.PAYPAL_CLIENT_ID,
                clientSecret: env.PAYPAL_CLIENT_SECRET,
                baseURL: 'https://api-m.paypal.com'
            }
        };
        
        this.currentEnv = env.PAYPAL_ENV || 'sandbox';
        this.config = this.paypalConfig[this.currentEnv];
    }

    /**
     * 获取 PayPal 访问令牌
     */
    async getAccessToken() {
        const auth = btoa(`${this.config.clientId}:${this.config.clientSecret}`);
        
        const response = await fetch(`${this.config.baseURL}/v1/oauth2/token`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: 'grant_type=client_credentials'
        });
        
        if (!response.ok) {
            throw new Error('Failed to get PayPal access token');
        }
        
        const data = await response.json();
        return data.access_token;
    }

    /**
     * 创建 PayPal 订阅
     */
    async createSubscription(request) {
        try {
            const { plan_id, user_id } = await request.json();
            
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            // 获取套餐信息
            const plan = this.getPlanConfig(plan_id);
            if (!plan) {
                return new Response(JSON.stringify({
                    success: false,
                    error: '無効なプランです'
                }), { status: 400 });
            }
            
            // 获取 PayPal 访问令牌
            const accessToken = await this.getAccessToken();
            
            // 创建 PayPal 订阅
            const subscriptionData = {
                plan_id: plan.paypal_plan_id,
                subscriber: {
                    email_address: authResult.user.email
                },
                application_context: {
                    brand_name: 'GoldenLedger',
                    locale: 'ja-JP',
                    shipping_preference: 'NO_SHIPPING',
                    user_action: 'SUBSCRIBE_NOW',
                    payment_method: {
                        payer_selected: 'PAYPAL',
                        payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED'
                    },
                    return_url: `${this.env.FRONTEND_URL}/payment-success.html`,
                    cancel_url: `${this.env.FRONTEND_URL}/payment.html`
                }
            };
            
            const paypalResponse = await fetch(`${this.config.baseURL}/v1/billing/subscriptions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'PayPal-Request-Id': `${user_id}-${Date.now()}`
                },
                body: JSON.stringify(subscriptionData)
            });
            
            if (!paypalResponse.ok) {
                const error = await paypalResponse.text();
                console.error('PayPal subscription creation failed:', error);
                throw new Error('PayPal subscription creation failed');
            }
            
            const subscription = await paypalResponse.json();
            
            // 保存订阅信息到数据库
            await this.saveSubscriptionToDatabase(user_id, plan_id, subscription);
            
            return new Response(JSON.stringify({
                success: true,
                subscription_id: subscription.id,
                approval_url: subscription.links.find(link => link.rel === 'approve')?.href
            }));
            
        } catch (error) {
            console.error('Create subscription error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: 'サブスクリプション作成に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 批准订阅支付
     */
    async approveSubscription(request) {
        try {
            const { subscription_id, plan_id, user_id } = await request.json();
            
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            // 获取 PayPal 访问令牌
            const accessToken = await this.getAccessToken();
            
            // 获取订阅详情
            const paypalResponse = await fetch(`${this.config.baseURL}/v1/billing/subscriptions/${subscription_id}`, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!paypalResponse.ok) {
                throw new Error('Failed to get subscription details from PayPal');
            }
            
            const subscription = await paypalResponse.json();
            
            // 检查订阅状态
            if (subscription.status !== 'ACTIVE') {
                return new Response(JSON.stringify({
                    success: false,
                    error: 'サブスクリプションがアクティブではありません'
                }), { status: 400 });
            }
            
            // 更新数据库中的订阅状态
            await this.updateSubscriptionStatus(user_id, subscription_id, 'active', subscription);
            
            // 记录支付成功日志
            await this.logPaymentSuccess(user_id, plan_id, subscription_id);
            
            return new Response(JSON.stringify({
                success: true,
                subscription: {
                    id: subscription.id,
                    status: subscription.status,
                    plan_id: plan_id,
                    start_time: subscription.start_time,
                    next_billing_time: subscription.billing_info?.next_billing_time
                }
            }));
            
        } catch (error) {
            console.error('Approve subscription error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: '支払い承認に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 获取订阅状态
     */
    async getSubscriptionStatus(request) {
        try {
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            const subscription = await this.subscriptionManager.getUserSubscription(authResult.user.id);
            
            if (!subscription) {
                return new Response(JSON.stringify({
                    success: true,
                    subscription: null
                }));
            }
            
            return new Response(JSON.stringify({
                success: true,
                subscription: {
                    id: subscription.paypal_subscription_id,
                    plan_id: subscription.plan_id,
                    status: subscription.status,
                    created_at: subscription.created_at,
                    current_period_start: subscription.current_period_start,
                    current_period_end: subscription.current_period_end
                }
            }));
            
        } catch (error) {
            console.error('Get subscription status error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: 'サブスクリプション状態の取得に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 取消订阅
     */
    async cancelSubscription(request) {
        try {
            const { subscription_id, reason } = await request.json();
            
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            // 获取 PayPal 访问令牌
            const accessToken = await this.getAccessToken();
            
            // 取消 PayPal 订阅
            const cancelData = {
                reason: reason || 'User requested cancellation'
            };
            
            const paypalResponse = await fetch(`${this.config.baseURL}/v1/billing/subscriptions/${subscription_id}/cancel`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(cancelData)
            });
            
            if (!paypalResponse.ok) {
                throw new Error('Failed to cancel PayPal subscription');
            }
            
            // 更新数据库中的订阅状态
            await this.updateSubscriptionStatus(authResult.user.id, subscription_id, 'cancelled');
            
            return new Response(JSON.stringify({
                success: true,
                message: 'サブスクリプションが正常にキャンセルされました'
            }));
            
        } catch (error) {
            console.error('Cancel subscription error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: 'サブスクリプションのキャンセルに失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 处理 PayPal Webhook
     */
    async handleWebhook(request) {
        try {
            const payload = await request.text();
            const headers = Object.fromEntries(request.headers.entries());
            
            // 验证 Webhook 签名
            const isValid = await this.verifyWebhookSignature(payload, headers);
            if (!isValid) {
                return new Response('Invalid webhook signature', { status: 400 });
            }
            
            const event = JSON.parse(payload);
            console.log('PayPal webhook received:', event.event_type);
            
            // 处理不同类型的事件
            switch (event.event_type) {
                case 'BILLING.SUBSCRIPTION.ACTIVATED':
                    await this.handleSubscriptionActivated(event);
                    break;
                    
                case 'BILLING.SUBSCRIPTION.CANCELLED':
                    await this.handleSubscriptionCancelled(event);
                    break;
                    
                case 'BILLING.SUBSCRIPTION.SUSPENDED':
                    await this.handleSubscriptionSuspended(event);
                    break;
                    
                case 'PAYMENT.SALE.COMPLETED':
                    await this.handlePaymentCompleted(event);
                    break;
                    
                default:
                    console.log('Unhandled webhook event:', event.event_type);
            }
            
            return new Response('OK');
            
        } catch (error) {
            console.error('Webhook handling error:', error);
            return new Response('Webhook processing failed', { status: 500 });
        }
    }

    /**
     * 验证用户认证
     */
    async verifyAuth(request) {
        const authHeader = request.headers.get('Authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return { success: false, error: '認証が必要です' };
        }

        const token = authHeader.substring(7);

        try {
            // 这里应该调用您的认证验证逻辑
            // 暂时返回模拟数据
            return {
                success: true,
                user: {
                    id: 'user_123',
                    email: '<EMAIL>'
                }
            };
        } catch (error) {
            return { success: false, error: '無効なトークンです' };
        }
    }

    /**
     * 获取套餐配置
     */
    getPlanConfig(planId) {
        const plans = {
            basic: {
                id: 'basic',
                name: 'ベーシックプラン',
                price: 980,
                paypal_plan_id: 'P-BASIC-PLAN-ID'
            },
            pro: {
                id: 'pro',
                name: 'プロプラン',
                price: 2980,
                paypal_plan_id: 'P-PRO-PLAN-ID'
            },
            enterprise: {
                id: 'enterprise',
                name: 'エンタープライズプラン',
                price: 9800,
                paypal_plan_id: 'P-ENTERPRISE-PLAN-ID'
            }
        };

        return plans[planId];
    }

    /**
     * 验证PayPal支付 - 新增方法
     */
    async verifyPayment(request) {
        try {
            const { orderID, planId } = await request.json();

            console.log(`PayPal支付验证请求:`, { orderID, planId });

            // 获取PayPal访问令牌
            const accessToken = await this.getAccessToken();

            // 从PayPal获取订单详情
            const orderResponse = await fetch(`${this.config.baseURL}/v2/checkout/orders/${orderID}`, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!orderResponse.ok) {
                throw new Error(`PayPal API错误: ${orderResponse.status}`);
            }

            const orderData = await orderResponse.json();
            console.log('PayPal订单数据:', orderData);

            // 验证订单状态
            if (orderData.status !== 'COMPLETED') {
                return new Response(JSON.stringify({
                    success: false,
                    error: '支付未完成'
                }), {
                    status: 400,
                    headers: { 'Content-Type': 'application/json', ...this.getCorsHeaders() }
                });
            }

            // 验证金额
            const planConfig = this.getPlanConfig(planId);
            if (!planConfig) {
                return new Response(JSON.stringify({
                    success: false,
                    error: '无效的套餐ID'
                }), {
                    status: 400,
                    headers: { 'Content-Type': 'application/json', ...this.getCorsHeaders() }
                });
            }

            const paidAmount = parseFloat(orderData.purchase_units[0].amount.value);
            if (paidAmount !== planConfig.price) {
                return new Response(JSON.stringify({
                    success: false,
                    error: '支付金额不匹配'
                }), {
                    status: 400,
                    headers: { 'Content-Type': 'application/json', ...this.getCorsHeaders() }
                });
            }

            // 更新用户的订阅状态到数据库
            try {
                await this.updateUserSubscription(orderData, planId);
                console.log('✅ 用户订阅状态已更新');
            } catch (dbError) {
                console.error('数据库更新失败:', dbError);
                // 即使数据库更新失败，也返回成功，因为支付已经完成
            }

            return new Response(JSON.stringify({
                success: true,
                message: '支付验证成功，套餐已激活！',
                order_id: orderID,
                plan_id: planId,
                amount: paidAmount,
                currency: orderData.purchase_units[0].amount.currency_code,
                payer_info: {
                    email: orderData.payer?.email_address,
                    name: orderData.payer?.name?.given_name || 'PayPal User',
                    payer_id: orderData.payer?.payer_id
                }
            }), {
                headers: { 'Content-Type': 'application/json', ...this.getCorsHeaders() }
            });

        } catch (error) {
            console.error('PayPal支付验证错误:', error);
            return new Response(JSON.stringify({
                success: false,
                error: '支付验证失败: ' + error.message
            }), {
                status: 500,
                headers: { 'Content-Type': 'application/json', ...this.getCorsHeaders() }
            });
        }
    }

    /**
     * 更新用户订阅状态
     */
    async updateUserSubscription(orderData, planId) {
        const now = new Date().toISOString();
        const payerEmail = orderData.payer?.email_address;

        if (!payerEmail) {
            console.warn('无法获取支付者邮箱，跳过数据库更新');
            return;
        }

        // 计算订阅到期时间（假设为1个月）
        const expiresAt = new Date();
        expiresAt.setMonth(expiresAt.getMonth() + 1);

        try {
            // 首先尝试查找用户
            const userResult = await this.env.DB.prepare(`
                SELECT id FROM users WHERE email = ?
            `).bind(payerEmail).first();

            let userId;
            if (userResult) {
                userId = userResult.id;
                console.log('找到现有用户:', userId);
            } else {
                // 创建新用户
                const insertResult = await this.env.DB.prepare(`
                    INSERT INTO users (email, name, created_at, updated_at)
                    VALUES (?, ?, ?, ?)
                `).bind(
                    payerEmail,
                    orderData.payer?.name?.given_name || 'PayPal User',
                    now,
                    now
                ).run();

                userId = insertResult.meta.last_row_id;
                console.log('创建新用户:', userId);
            }

            // 更新或插入订阅记录
            await this.env.DB.prepare(`
                INSERT OR REPLACE INTO user_subscriptions
                (user_id, plan_id, paypal_order_id, status, amount, currency, expires_at, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `).bind(
                userId,
                planId,
                orderData.id,
                'active',
                parseFloat(orderData.purchase_units[0].amount.value),
                orderData.purchase_units[0].amount.currency_code,
                expiresAt.toISOString(),
                now,
                now
            ).run();

            console.log('✅ 订阅记录已更新');

        } catch (error) {
            console.error('数据库操作失败:', error);
            throw error;
        }
    }

    /**
     * 获取CORS头
     */
    getCorsHeaders() {
        return {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
        };
    }

    /**
     * 保存订阅到数据库
     */
    async saveSubscriptionToDatabase(userId, planId, paypalSubscription) {
        const now = new Date().toISOString();

        await this.env.DB.prepare(`
            INSERT OR REPLACE INTO user_subscriptions
            (user_id, plan_id, paypal_subscription_id, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        `).bind(
            userId,
            planId,
            paypalSubscription.id,
            'pending',
            now,
            now
        ).run();
    }

    /**
     * 更新订阅状态
     */
    async updateSubscriptionStatus(userId, subscriptionId, status, paypalData = null) {
        const now = new Date().toISOString();

        let query = `
            UPDATE user_subscriptions
            SET status = ?, updated_at = ?
        `;
        let params = [status, now];

        if (paypalData) {
            query += `, current_period_start = ?, current_period_end = ?`;
            params.push(
                paypalData.start_time || now,
                paypalData.billing_info?.next_billing_time || now
            );
        }

        query += ` WHERE user_id = ? AND paypal_subscription_id = ?`;
        params.push(userId, subscriptionId);

        await this.env.DB.prepare(query).bind(...params).run();
    }

    /**
     * 记录支付成功日志
     */
    async logPaymentSuccess(userId, planId, subscriptionId) {
        await this.env.DB.prepare(`
            INSERT INTO payment_logs
            (user_id, plan_id, subscription_id, event_type, created_at)
            VALUES (?, ?, ?, ?, ?)
        `).bind(
            userId,
            planId,
            subscriptionId,
            'payment_success',
            new Date().toISOString()
        ).run();
    }

    /**
     * 验证 Webhook 签名
     */
    async verifyWebhookSignature(payload, headers) {
        // PayPal Webhook 签名验证逻辑
        // 这里需要实现实际的签名验证
        return true; // 暂时返回 true
    }

    /**
     * 处理订阅激活事件
     */
    async handleSubscriptionActivated(event) {
        const subscriptionId = event.resource.id;

        // 更新数据库中的订阅状态
        await this.env.DB.prepare(`
            UPDATE user_subscriptions
            SET status = 'active', updated_at = ?
            WHERE paypal_subscription_id = ?
        `).bind(new Date().toISOString(), subscriptionId).run();

        console.log('Subscription activated:', subscriptionId);
    }

    /**
     * 处理订阅取消事件
     */
    async handleSubscriptionCancelled(event) {
        const subscriptionId = event.resource.id;

        await this.env.DB.prepare(`
            UPDATE user_subscriptions
            SET status = 'cancelled', updated_at = ?
            WHERE paypal_subscription_id = ?
        `).bind(new Date().toISOString(), subscriptionId).run();

        console.log('Subscription cancelled:', subscriptionId);
    }

    /**
     * 处理订阅暂停事件
     */
    async handleSubscriptionSuspended(event) {
        const subscriptionId = event.resource.id;

        await this.env.DB.prepare(`
            UPDATE user_subscriptions
            SET status = 'suspended', updated_at = ?
            WHERE paypal_subscription_id = ?
        `).bind(new Date().toISOString(), subscriptionId).run();

        console.log('Subscription suspended:', subscriptionId);
    }

    /**
     * 处理支付完成事件
     */
    async handlePaymentCompleted(event) {
        const paymentId = event.resource.id;
        const subscriptionId = event.resource.billing_agreement_id;

        // 记录支付日志
        await this.env.DB.prepare(`
            INSERT INTO payment_logs
            (subscription_id, payment_id, amount, currency, event_type, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        `).bind(
            subscriptionId,
            paymentId,
            event.resource.amount.total,
            event.resource.amount.currency,
            'payment_completed',
            new Date().toISOString()
        ).run();

        console.log('Payment completed:', paymentId);
    }
}

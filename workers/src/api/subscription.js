/**
 * 订阅管理 API
 * 处理免费套餐激活、订阅状态查询、使用量统计等
 */

export class SubscriptionAPI {
    constructor(env) {
        this.env = env;
        this.db = env.DB;
    }

    /**
     * 激活免费套餐
     */
    async activateFreePlan(request) {
        try {
            const { user_id } = await request.json();
            
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            const now = new Date().toISOString();
            
            // 检查是否已有订阅
            const existingSubscription = await this.db.prepare(`
                SELECT * FROM user_subscriptions 
                WHERE user_id = ? AND status = 'active'
            `).bind(user_id).first();
            
            if (existingSubscription) {
                return new Response(JSON.stringify({
                    success: false,
                    error: '既にアクティブなサブスクリプションがあります'
                }), { status: 400 });
            }
            
            // 创建免费订阅记录
            await this.db.prepare(`
                INSERT OR REPLACE INTO user_subscriptions 
                (user_id, plan_id, status, current_period_start, current_period_end, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `).bind(
                user_id,
                'free',
                'active',
                now,
                this.getNextBillingDate(now, 'month'), // 免费套餐也设置一个月的周期
                now,
                now
            ).run();
            
            // 记录激活日志
            await this.logSubscriptionEvent(user_id, 'free', 'free_plan_activated');
            
            return new Response(JSON.stringify({
                success: true,
                message: '無料プランが有効化されました',
                subscription: {
                    plan_id: 'free',
                    status: 'active',
                    current_period_start: now,
                    current_period_end: this.getNextBillingDate(now, 'month')
                }
            }));
            
        } catch (error) {
            console.error('Activate free plan error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: '無料プランの有効化に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 获取订阅状态
     */
    async getSubscriptionStatus(request) {
        try {
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            const subscription = await this.db.prepare(`
                SELECT * FROM user_subscriptions 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT 1
            `).bind(authResult.user.id).first();
            
            if (!subscription) {
                return new Response(JSON.stringify({
                    success: true,
                    subscription: null
                }));
            }
            
            // 检查订阅是否过期
            const isExpired = this.isSubscriptionExpired(subscription);
            if (isExpired && subscription.status === 'active') {
                // 更新过期状态
                await this.db.prepare(`
                    UPDATE user_subscriptions 
                    SET status = 'expired', updated_at = ?
                    WHERE id = ?
                `).bind(new Date().toISOString(), subscription.id).run();
                
                subscription.status = 'expired';
            }
            
            return new Response(JSON.stringify({
                success: true,
                subscription: {
                    id: subscription.id,
                    plan_id: subscription.plan_id,
                    status: subscription.status,
                    paypal_subscription_id: subscription.paypal_subscription_id,
                    current_period_start: subscription.current_period_start,
                    current_period_end: subscription.current_period_end,
                    trial_start: subscription.trial_start,
                    trial_end: subscription.trial_end,
                    cancel_at_period_end: subscription.cancel_at_period_end,
                    created_at: subscription.created_at
                }
            }));
            
        } catch (error) {
            console.error('Get subscription status error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: 'サブスクリプション状態の取得に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 记录使用量
     */
    async recordUsage(request) {
        try {
            const { feature_type, usage_count = 1 } = await request.json();
            
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            const userId = authResult.user.id;
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            const now = new Date().toISOString();
            
            // 获取当前订阅信息以确定计费周期
            const subscription = await this.db.prepare(`
                SELECT * FROM user_subscriptions 
                WHERE user_id = ? AND status = 'active'
                ORDER BY created_at DESC 
                LIMIT 1
            `).bind(userId).first();
            
            let billingPeriodStart = today;
            let billingPeriodEnd = today;
            
            if (subscription) {
                billingPeriodStart = subscription.current_period_start?.split('T')[0] || today;
                billingPeriodEnd = subscription.current_period_end?.split('T')[0] || today;
            }
            
            // 更新或插入使用量记录
            await this.db.prepare(`
                INSERT INTO usage_statistics 
                (user_id, feature_type, usage_count, usage_date, billing_period_start, billing_period_end, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT(user_id, feature_type, usage_date) 
                DO UPDATE SET 
                    usage_count = usage_count + ?,
                    billing_period_start = ?,
                    billing_period_end = ?
            `).bind(
                userId, feature_type, usage_count, today, 
                billingPeriodStart, billingPeriodEnd, now,
                usage_count, billingPeriodStart, billingPeriodEnd
            ).run();
            
            return new Response(JSON.stringify({
                success: true,
                message: '使用量が記録されました'
            }));
            
        } catch (error) {
            console.error('Record usage error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: '使用量の記録に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 获取使用量统计
     */
    async getUsageStats(request) {
        try {
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            const userId = authResult.user.id;
            const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
            
            // 获取当月使用量统计
            const monthlyStats = await this.db.prepare(`
                SELECT 
                    feature_type,
                    SUM(usage_count) as total_usage
                FROM usage_statistics 
                WHERE user_id = ? 
                AND usage_date LIKE ?
                GROUP BY feature_type
            `).bind(userId, `${currentMonth}%`).all();
            
            // 获取当前计费周期使用量
            const subscription = await this.db.prepare(`
                SELECT * FROM user_subscriptions 
                WHERE user_id = ? AND status = 'active'
                ORDER BY created_at DESC 
                LIMIT 1
            `).bind(userId).first();
            
            let billingPeriodStats = [];
            if (subscription) {
                billingPeriodStats = await this.db.prepare(`
                    SELECT 
                        feature_type,
                        SUM(usage_count) as total_usage
                    FROM usage_statistics 
                    WHERE user_id = ? 
                    AND usage_date >= ? 
                    AND usage_date <= ?
                    GROUP BY feature_type
                `).bind(
                    userId,
                    subscription.current_period_start?.split('T')[0],
                    subscription.current_period_end?.split('T')[0]
                ).all();
            }
            
            // 格式化统计数据
            const formatStats = (stats) => {
                const result = {};
                stats.forEach(stat => {
                    result[stat.feature_type] = stat.total_usage;
                });
                return result;
            };
            
            return new Response(JSON.stringify({
                success: true,
                stats: {
                    monthly: formatStats(monthlyStats),
                    billing_period: formatStats(billingPeriodStats),
                    current_month: currentMonth,
                    billing_period_start: subscription?.current_period_start,
                    billing_period_end: subscription?.current_period_end
                }
            }));
            
        } catch (error) {
            console.error('Get usage stats error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: '使用量統計の取得に失敗しました'
            }), { status: 500 });
        }
    }

    /**
     * 获取订阅详情
     */
    async getSubscriptionDetails(request) {
        try {
            const { subscription_id } = await request.json();
            
            // 验证用户认证
            const authResult = await this.verifyAuth(request);
            if (!authResult.success) {
                return new Response(JSON.stringify(authResult), { status: 401 });
            }
            
            // 从数据库获取订阅信息
            const subscription = await this.db.prepare(`
                SELECT * FROM user_subscriptions 
                WHERE paypal_subscription_id = ? AND user_id = ?
            `).bind(subscription_id, authResult.user.id).first();
            
            if (!subscription) {
                return new Response(JSON.stringify({
                    success: false,
                    error: 'サブスクリプションが見つかりません'
                }), { status: 404 });
            }
            
            return new Response(JSON.stringify({
                success: true,
                subscription: {
                    id: subscription.paypal_subscription_id,
                    plan_id: subscription.plan_id,
                    status: subscription.status,
                    start_time: subscription.current_period_start,
                    next_billing_time: subscription.current_period_end,
                    created_at: subscription.created_at
                }
            }));
            
        } catch (error) {
            console.error('Get subscription details error:', error);
            return new Response(JSON.stringify({
                success: false,
                error: 'サブスクリプション詳細の取得に失敗しました'
            }), { status: 500 });
        }
    }

    // 辅助方法
    async verifyAuth(request) {
        // 这里应该实现实际的认证验证逻辑
        return {
            success: true,
            user: { id: 'user_123', email: '<EMAIL>' }
        };
    }

    getNextBillingDate(startDate, interval = 'month') {
        const date = new Date(startDate);
        if (interval === 'month') {
            date.setMonth(date.getMonth() + 1);
        } else if (interval === 'year') {
            date.setFullYear(date.getFullYear() + 1);
        }
        return date.toISOString();
    }

    isSubscriptionExpired(subscription) {
        if (!subscription.current_period_end) return false;
        return new Date(subscription.current_period_end) < new Date();
    }

    async logSubscriptionEvent(userId, planId, eventType) {
        await this.db.prepare(`
            INSERT INTO payment_logs 
            (user_id, plan_id, event_type, created_at)
            VALUES (?, ?, ?, ?)
        `).bind(userId, planId, eventType, new Date().toISOString()).run();
    }
}

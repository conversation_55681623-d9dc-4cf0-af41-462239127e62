/**
 * AI 处理器 - Gemini API 集成
 */

export class AIProcessor {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
  }

  async processNaturalLanguage(text, companyId = 'default') {
    try {
      // 1. 解析日期时间
      const parsedDateTime = this.parseDateTimeFromText(text);
      
      // 2. 使用 Gemini 提取交易信息
      const transactionInfo = await this.extractTransactionInfo(text, parsedDateTime);
      
      // 3. 生成仕訳记录
      const journalEntry = this.generateJournalEntry(transactionInfo, companyId);
      
      return {
        success: true,
        data: journalEntry,
        confidence: transactionInfo.confidence || 0.8,
        processing_time: Date.now()
      };
    } catch (error) {
      console.error('AI处理失败:', error);
      return {
        success: false,
        error: error.message,
        confidence: 0.0
      };
    }
  }

  async extractTransactionInfo(text, parsedDateTime) {
    const prompt = `
作为专业会计师，请分析以下交易描述并提取关键信息：

原始交易描述: ${text}
解析的日期: ${parsedDateTime.date}
解析的时间: ${parsedDateTime.time}

请提取以下信息并以JSON格式返回：
{
  "transaction_type": "收入/支出/转账",
  "amount": "金额(数字)",
  "currency": "货币代码",
  "description": "交易描述",
  "category": "交易类别",
  "payment_method": "支付方式",
  "suggested_debit_account": "建议借方科目",
  "suggested_credit_account": "建议贷方科目",
  "confidence": "置信度(0-1)"
}

注意：
1. 描述字段应该使用处理后的描述，将时间代词替换为具体日期时间
2. 日期时间字段必须使用解析器提供的准确值
3. 不要在描述中保留"今天"、"昨天"等代词
4. 根据日本会计准则选择合适的科目
`;

    const response = await this.callGemini(prompt);
    
    // 提取JSON
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应格式错误');
    }

    try {
      return JSON.parse(jsonMatch[0]);
    } catch (e) {
      throw new Error('JSON解析失败');
    }
  }

  async processImage(file, companyId = 'default') {
    try {
      // 将文件转换为 base64
      const arrayBuffer = await file.arrayBuffer();
      const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
      
      const prompt = `
请分析这张发票/收据图片，提取以下信息并以JSON格式返回：

{
  "invoice_number": "发票号码",
  "date": "日期(YYYY-MM-DD)",
  "supplier": "供应商名称",
  "items": [
    {
      "description": "商品/服务描述",
      "quantity": "数量",
      "unit_price": "单价",
      "amount": "金额"
    }
  ],
  "subtotal": "小计",
  "tax": "税额",
  "total": "总金额",
  "suggested_debit_account": "建议借方科目",
  "suggested_credit_account": "建议贷方科目"
}
`;

      const response = await this.callGeminiWithImage(prompt, base64, file.type);
      
      // 提取JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('OCR响应格式错误');
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('图片处理失败:', error);
      throw error;
    }
  }

  async callGemini(prompt, model = 'gemini-1.5-flash') {
    const url = `${this.baseUrl}/${model}:generateContent?key=${this.apiKey}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API错误: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Gemini响应格式错误');
    }

    return data.candidates[0].content.parts[0].text;
  }

  async callGeminiWithImage(prompt, base64Image, mimeType) {
    const url = `${this.baseUrl}/gemini-1.5-flash:generateContent?key=${this.apiKey}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [
            {
              text: prompt
            },
            {
              inline_data: {
                mime_type: mimeType,
                data: base64Image
              }
            }
          ]
        }],
        generationConfig: {
          temperature: 0.05,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini Vision API错误: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Gemini Vision响应格式错误');
    }

    return data.candidates[0].content.parts[0].text;
  }

  parseDateTimeFromText(text) {
    const now = new Date();
    
    // 处理相对时间词汇
    if (text.includes('今日') || text.includes('今天')) {
      return {
        date: now.toISOString().split('T')[0],
        time: now.toTimeString().split(' ')[0],
        datetime: now.toISOString()
      };
    }
    
    if (text.includes('昨日') || text.includes('昨天')) {
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      return {
        date: yesterday.toISOString().split('T')[0],
        time: yesterday.toTimeString().split(' ')[0],
        datetime: yesterday.toISOString()
      };
    }
    
    // 尝试提取具体日期
    const datePatterns = [
      /(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日]?/,
      /(\d{1,2})[月\-\/](\d{1,2})[日]?/,
    ];
    
    for (const pattern of datePatterns) {
      const match = text.match(pattern);
      if (match) {
        let year, month, day;
        
        if (match.length === 4) {
          [, year, month, day] = match;
        } else {
          year = now.getFullYear();
          [, month, day] = match;
        }
        
        const date = new Date(year, month - 1, day);
        return {
          date: date.toISOString().split('T')[0],
          time: now.toTimeString().split(' ')[0],
          datetime: date.toISOString()
        };
      }
    }
    
    // 默认使用当前时间
    return {
      date: now.toISOString().split('T')[0],
      time: now.toTimeString().split(' ')[0],
      datetime: now.toISOString()
    };
  }

  generateJournalEntry(transactionInfo, companyId) {
    const parsedDateTime = this.parseDateTimeFromText('今日');
    
    return {
      company_id: companyId,
      entry_date: parsedDateTime.date,
      entry_time: parsedDateTime.time,
      entry_datetime: parsedDateTime.datetime,
      description: transactionInfo.description || '自动生成的仕訳',
      debit_account: transactionInfo.suggested_debit_account || '現金',
      credit_account: transactionInfo.suggested_credit_account || '売上高',
      amount: parseFloat(transactionInfo.amount) || 0,
      reference_number: `AI-${Date.now()}`,
      ai_generated: true,
      ai_confidence: transactionInfo.confidence || 0.8,
      debit_tax_rate: 0.0,
      credit_tax_rate: 0.0
    };
  }
}

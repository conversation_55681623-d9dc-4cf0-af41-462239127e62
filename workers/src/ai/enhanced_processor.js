/**
 * 增强版AI处理器
 * 提供更准确的自然语言记账处理
 */

// 日本会计科目映射表
const ACCOUNT_MAPPING = {
  // 资产类
  assets: {
    '現金': ['现金', '钱', '现钞', 'cash'],
    '普通預金': ['银行', '存款', '账户', 'bank', 'deposit'],
    '売掛金': ['应收', '欠款', '账款', 'receivable'],
    '商品': ['商品', '库存', '货物', 'inventory', 'goods'],
    '建物': ['房屋', '建筑', '房产', 'building'],
    '機械装置': ['设备', '机器', '装置', 'equipment', 'machinery'],
    '車両運搬具': ['车辆', '汽车', '运输工具', 'vehicle', 'car'],
    '工具器具備品': ['工具', '器具', '办公用品', 'tools', 'equipment']
  },
  // 负债类
  liabilities: {
    '買掛金': ['应付', '欠款', '账款', 'payable'],
    '短期借入金': ['短期借款', '贷款', 'short-term loan'],
    '長期借入金': ['长期借款', '按揭', 'long-term loan'],
    '未払金': ['未付款', '待付', 'unpaid'],
    '預り金': ['预收款', '押金', 'deposit received']
  },
  // 收入类
  revenue: {
    '売上高': ['销售', '收入', '营业额', 'sales', 'revenue'],
    '受取利息': ['利息收入', '存款利息', 'interest income'],
    '雑収入': ['其他收入', '杂项收入', 'miscellaneous income'],
    '受取配当金': ['股息', '分红', 'dividend income']
  },
  // 费用类
  expenses: {
    '消耗品費': ['办公用品', '文具', '消耗品', 'supplies', 'stationery'],
    '給料手当': ['工资', '薪水', '人工费', 'salary', 'wages'],
    '地代家賃': ['房租', '租金', '地租', 'rent'],
    '水道光熱費': ['水电费', '公用事业费', 'utilities'],
    '通信費': ['电话费', '网费', '通讯费', 'communication'],
    '広告宣伝費': ['广告费', '宣传费', 'advertising'],
    '交通費': ['交通费', '车费', '差旅费', 'transportation'],
    '接待交際費': ['招待费', '娱乐费', 'entertainment'],
    '修繕費': ['维修费', '修理费', 'repair'],
    '保険料': ['保险费', 'insurance'],
    '租税公課': ['税费', '政府费用', 'taxes'],
    '減価償却費': ['折旧费', 'depreciation'],
    '雑費': ['杂费', '其他费用', 'miscellaneous']
  }
};

// 交易类型识别模式
const TRANSACTION_PATTERNS = {
  income: [
    /売上|収入|入金|受取|販売|サービス料/,
    /sales|income|revenue|received/i,
    /客户付款|收到款项|营业收入/
  ],
  expense: [
    /購入|支払|買|費用|経費|コスト/,
    /purchase|payment|buy|expense|cost/i,
    /购买|支付|花费|消费|开支/
  ],
  transfer: [
    /振込|移動|転送|預金/,
    /transfer|move|deposit/i,
    /转账|转移|存款/
  ]
};

// 金额提取模式
const AMOUNT_PATTERNS = [
  /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*円/,
  /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*yen/i,
  /¥\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/,
  /(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*元/,
  /(\d+(?:\.\d{2})?)/
];

// 支付方式识别
const PAYMENT_METHODS = {
  '現金': ['现金', '钞票', 'cash', '付现'],
  '銀行振込': ['银行转账', '转账', 'bank transfer', '振込'],
  'クレジットカード': ['信用卡', '刷卡', 'credit card', 'card'],
  '口座振替': ['自动扣款', '直接扣款', 'direct debit'],
  '電子マネー': ['电子钱包', '支付宝', '微信支付', 'e-money', 'alipay', 'wechat pay']
};

/**
 * 增强版AI处理器类
 */
class EnhancedAIProcessor {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.model = 'gemini-2.0-flash-exp'; // 使用最新模型
  }

  /**
   * 智能账户匹配
   */
  findBestAccount(text, accountType) {
    const accounts = ACCOUNT_MAPPING[accountType] || {};
    let bestMatch = null;
    let maxScore = 0;

    for (const [account, keywords] of Object.entries(accounts)) {
      for (const keyword of keywords) {
        if (text.toLowerCase().includes(keyword.toLowerCase())) {
          const score = keyword.length / text.length;
          if (score > maxScore) {
            maxScore = score;
            bestMatch = account;
          }
        }
      }
    }

    return bestMatch;
  }

  /**
   * 交易类型识别
   */
  identifyTransactionType(text) {
    for (const [type, patterns] of Object.entries(TRANSACTION_PATTERNS)) {
      for (const pattern of patterns) {
        if (pattern.test(text)) {
          return type;
        }
      }
    }
    return 'expense'; // 默认为支出
  }

  /**
   * 金额提取
   */
  extractAmount(text) {
    for (const pattern of AMOUNT_PATTERNS) {
      const match = text.match(pattern);
      if (match) {
        return parseFloat(match[1].replace(/,/g, ''));
      }
    }
    return null;
  }

  /**
   * 支付方式识别
   */
  identifyPaymentMethod(text) {
    for (const [method, keywords] of Object.entries(PAYMENT_METHODS)) {
      for (const keyword of keywords) {
        if (text.toLowerCase().includes(keyword.toLowerCase())) {
          return method;
        }
      }
    }
    return '現金'; // 默认现金
  }

  /**
   * 生成增强的提示词
   */
  generateEnhancedPrompt(text, preAnalysis) {
    return `
你是一位专业的日本会计师，精通日本会计准则和复式记账法。请分析以下交易描述并生成准确的仕訳记录。

原始交易描述: "${text}"

预分析结果:
- 交易类型: ${preAnalysis.transactionType}
- 提取金额: ${preAnalysis.amount || '未识别'}
- 支付方式: ${preAnalysis.paymentMethod}
- 建议借方科目: ${preAnalysis.suggestedDebitAccount || '待确定'}
- 建议贷方科目: ${preAnalysis.suggestedCreditAccount || '待确定'}

请根据以下要求生成JSON格式的仕訳记录:

1. 严格遵循日本会计准则
2. 使用标准的日本会计科目
3. 确保借贷平衡
4. 提供详细的分析理由

返回格式:
{
  "transaction_type": "收入/支出/转账",
  "amount": 数字金额,
  "currency": "JPY",
  "description": "清晰的交易描述",
  "category": "具体的交易类别",
  "payment_method": "支付方式",
  "debit_account": "借方科目",
  "credit_account": "贷方科目",
  "confidence": 0.0-1.0的置信度,
  "analysis_reason": "选择科目的理由",
  "tax_implications": "税务影响说明",
  "suggestions": ["改进建议1", "改进建议2"]
}

注意事项:
- 金额必须是数字，不包含货币符号
- 科目名称使用标准日本会计科目
- 置信度基于信息完整性和匹配准确性
- 如果信息不足，请在suggestions中说明
`;
  }

  /**
   * 处理自然语言输入
   */
  async processNaturalLanguage(text) {
    try {
      // 第一步：预分析
      const preAnalysis = {
        transactionType: this.identifyTransactionType(text),
        amount: this.extractAmount(text),
        paymentMethod: this.identifyPaymentMethod(text)
      };

      // 根据交易类型预测科目
      if (preAnalysis.transactionType === 'income') {
        preAnalysis.suggestedDebitAccount = preAnalysis.paymentMethod;
        preAnalysis.suggestedCreditAccount = this.findBestAccount(text, 'revenue') || '売上高';
      } else if (preAnalysis.transactionType === 'expense') {
        preAnalysis.suggestedDebitAccount = this.findBestAccount(text, 'expenses') || '雑費';
        preAnalysis.suggestedCreditAccount = preAnalysis.paymentMethod;
      }

      // 第二步：AI增强分析
      const enhancedPrompt = this.generateEnhancedPrompt(text, preAnalysis);
      
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: enhancedPrompt
            }]
          }],
          generationConfig: {
            temperature: 0.1,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 8192,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_HARASSMENT",
              threshold: "BLOCK_MEDIUM_AND_ABOVE"
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API错误: ${response.status} - ${await response.text()}`);
      }

      const data = await response.json();

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Gemini响应格式错误');
      }

      const responseText = data.candidates[0].content.parts[0].text;

      // 提取JSON
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // 如果AI没有返回JSON，使用预分析结果
        return this.createFallbackResult(text, preAnalysis);
      }

      const aiResult = JSON.parse(jsonMatch[0]);

      // 第三步：结果验证和优化
      const now = new Date();
      const journalEntry = {
        entry_date: now.toISOString().split('T')[0],
        entry_time: now.toTimeString().split(' ')[0],
        entry_datetime: now.toISOString(),
        description: aiResult.description || text,
        debit_account: aiResult.debit_account || preAnalysis.suggestedDebitAccount || '雑費',
        credit_account: aiResult.credit_account || preAnalysis.suggestedCreditAccount || '現金',
        amount: aiResult.amount || preAnalysis.amount || 0,
        reference_number: `AI-${Date.now()}`,
      };

      return {
        success: true,
        journal_entry: journalEntry,
        confidence: Math.min(aiResult.confidence || 0.8, 1.0),
        analysis: {
          transaction_type: aiResult.transaction_type,
          category: aiResult.category,
          payment_method: aiResult.payment_method,
          analysis_reason: aiResult.analysis_reason,
          tax_implications: aiResult.tax_implications,
          suggestions: aiResult.suggestions || []
        },
        pre_analysis: preAnalysis
      };

    } catch (error) {
      console.error('Enhanced AI processing error:', error);
      
      // 降级到基础处理
      const preAnalysis = {
        transactionType: this.identifyTransactionType(text),
        amount: this.extractAmount(text),
        paymentMethod: this.identifyPaymentMethod(text)
      };

      return this.createFallbackResult(text, preAnalysis, error.message);
    }
  }

  /**
   * 创建降级结果
   */
  createFallbackResult(text, preAnalysis, errorMessage = null) {
    const now = new Date();
    
    let debitAccount, creditAccount;
    
    if (preAnalysis.transactionType === 'income') {
      debitAccount = preAnalysis.paymentMethod || '現金';
      creditAccount = '売上高';
    } else {
      debitAccount = this.findBestAccount(text, 'expenses') || '雑費';
      creditAccount = preAnalysis.paymentMethod || '現金';
    }

    const journalEntry = {
      entry_date: now.toISOString().split('T')[0],
      entry_time: now.toTimeString().split(' ')[0],
      entry_datetime: now.toISOString(),
      description: text,
      debit_account: debitAccount,
      credit_account: creditAccount,
      amount: preAnalysis.amount || 0,
      reference_number: `FALLBACK-${Date.now()}`,
    };

    return {
      success: true,
      journal_entry: journalEntry,
      confidence: 0.6,
      analysis: {
        transaction_type: preAnalysis.transactionType,
        category: '基础分析',
        payment_method: preAnalysis.paymentMethod,
        analysis_reason: '使用基础规则匹配',
        suggestions: errorMessage ? [`AI处理失败: ${errorMessage}`, '建议手动检查结果'] : ['建议验证科目选择']
      },
      pre_analysis: preAnalysis,
      fallback: true
    };
  }
}

export { EnhancedAIProcessor, ACCOUNT_MAPPING, TRANSACTION_PATTERNS };

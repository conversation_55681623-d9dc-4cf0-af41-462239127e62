/**
 * 数据库管理器 - Cloudflare D1
 */

export class DatabaseManager {
  constructor(d1Database) {
    this.db = d1Database;
  }

  async initialize() {
    // 创建表结构
    await this.createTables();
    await this.insertDefaultData();
  }

  async createTables() {
    // 公司表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS companies (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        name TEXT NOT NULL,
        tax_number TEXT,
        address TEXT,
        fiscal_year_end TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 会计科目表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS account_subjects (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        company_id TEXT NOT NULL,
        code TEXT NOT NULL,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        subcategory TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id)
      )
    `);

    // 仕訳记录表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        company_id TEXT NOT NULL,
        entry_date DATE NOT NULL,
        entry_time TIME,
        entry_datetime DATETIME,
        description TEXT NOT NULL,
        debit_account TEXT NOT NULL,
        credit_account TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        reference_number TEXT,
        ai_generated BOOLEAN DEFAULT FALSE,
        ai_confidence DECIMAL(3,2),
        attachment_path TEXT,
        debit_tax_rate DECIMAL(5,2) DEFAULT 0.00,
        credit_tax_rate DECIMAL(5,2) DEFAULT 0.00,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id)
      )
    `);

    // 固定资产表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS fixed_assets (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        company_id TEXT NOT NULL,
        asset_name TEXT NOT NULL,
        asset_category TEXT NOT NULL,
        acquisition_date DATE NOT NULL,
        acquisition_cost DECIMAL(15,2) NOT NULL,
        useful_life_years INTEGER NOT NULL,
        depreciation_method TEXT DEFAULT '定額法',
        current_book_value DECIMAL(15,2),
        accumulated_depreciation DECIMAL(15,2) DEFAULT 0.00,
        disposal_date DATE,
        disposal_amount DECIMAL(15,2),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id)
      )
    `);

    // 附件表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS attachments (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        company_id TEXT NOT NULL,
        journal_entry_id TEXT,
        fixed_asset_id TEXT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER,
        file_type TEXT,
        upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id),
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id),
        FOREIGN KEY (fixed_asset_id) REFERENCES fixed_assets (id)
      )
    `);

    // LLM配置表
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS llm_configurations (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        company_id TEXT NOT NULL,
        provider TEXT NOT NULL,
        model_name TEXT NOT NULL,
        model_id TEXT NOT NULL,
        task_types TEXT NOT NULL,
        priority INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        temperature DECIMAL(3,2) DEFAULT 0.1,
        max_tokens INTEGER DEFAULT 4096,
        api_key_encrypted TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id)
      )
    `);

    console.log('✅ 数据库表创建完成');
  }

  async insertDefaultData() {
    // 检查是否已有默认公司
    const existingCompany = await this.db.prepare(
      'SELECT id FROM companies WHERE name = ?'
    ).bind('デモ会社').first();

    if (!existingCompany) {
      // 插入默认公司
      const companyResult = await this.db.prepare(`
        INSERT INTO companies (id, name, tax_number, address, fiscal_year_end)
        VALUES (?, ?, ?, ?, ?)
      `).bind('default', 'デモ会社', '1234567890123', '東京都渋谷区', '2024-03-31').run();

      // 插入默认会计科目
      const subjects = [
        // 资产科目
        ['101', '現金', '資産', '流動資産'],
        ['102', '普通預金', '資産', '流動資産'],
        ['103', '売掛金', '資産', '流動資産'],
        ['104', '商品', '資産', '流動資産'],
        ['201', '建物', '資産', '固定資産'],
        ['202', '機械装置', '資産', '固定資産'],
        ['203', '車両運搬具', '資産', '固定資産'],
        
        // 负债科目
        ['301', '買掛金', '負債', '流動負債'],
        ['302', '短期借入金', '負債', '流動負債'],
        ['303', '未払金', '負債', '流動負債'],
        ['401', '長期借入金', '負債', '固定負債'],
        
        // 净资产科目
        ['501', '資本金', '純資産', '資本'],
        ['502', '利益剰余金', '純資産', '利益剰余金'],
        
        // 收入科目
        ['601', '売上高', '収益', '営業収益'],
        ['602', '受取利息', '収益', '営業外収益'],
        
        // 费用科目
        ['701', '仕入高', '費用', '営業費用'],
        ['702', '給料手当', '費用', '営業費用'],
        ['703', '地代家賃', '費用', '営業費用'],
        ['704', '水道光熱費', '費用', '営業費用'],
        ['705', '通信費', '費用', '営業費用'],
        ['706', '消耗品費', '費用', '営業費用'],
        ['707', '広告宣伝費', '費用', '営業費用'],
        ['708', '旅費交通費', '費用', '営業費用'],
        ['709', '接待交際費', '費用', '営業費用'],
        ['710', '支払利息', '費用', '営業外費用']
      ];

      for (const [code, name, category, subcategory] of subjects) {
        await this.db.prepare(`
          INSERT INTO account_subjects (company_id, code, name, category, subcategory)
          VALUES (?, ?, ?, ?, ?)
        `).bind('default', code, name, category, subcategory).run();
      }

      console.log('✅ 默认数据插入完成');
    }
  }

  async getCompanies() {
    const result = await this.db.prepare('SELECT * FROM companies ORDER BY created_at DESC').all();
    return result.results || [];
  }

  async getJournalEntries(companyId = 'default', limit = 100) {
    const result = await this.db.prepare(`
      SELECT * FROM journal_entries 
      WHERE company_id = ? 
      ORDER BY entry_date DESC, entry_time DESC, created_at DESC 
      LIMIT ?
    `).bind(companyId, limit).all();
    
    return result.results || [];
  }

  async addJournalEntry(entry) {
    const id = this.generateId();
    
    await this.db.prepare(`
      INSERT INTO journal_entries (
        id, company_id, entry_date, entry_time, entry_datetime, description,
        debit_account, credit_account, amount, reference_number,
        ai_generated, ai_confidence, debit_tax_rate, credit_tax_rate
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id,
      entry.company_id || 'default',
      entry.entry_date,
      entry.entry_time,
      entry.entry_datetime,
      entry.description,
      entry.debit_account,
      entry.credit_account,
      entry.amount,
      entry.reference_number,
      entry.ai_generated || false,
      entry.ai_confidence || 0.0,
      entry.debit_tax_rate || 0.0,
      entry.credit_tax_rate || 0.0
    ).run();

    return id;
  }

  async getAccountSubjects(companyId = 'default') {
    const result = await this.db.prepare(`
      SELECT * FROM account_subjects 
      WHERE company_id = ? AND is_active = TRUE 
      ORDER BY code
    `).bind(companyId).all();
    
    return result.results || [];
  }

  generateId() {
    return crypto.randomUUID();
  }
}

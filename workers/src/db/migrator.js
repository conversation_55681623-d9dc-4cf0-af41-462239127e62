/**
 * 数据库迁移处理器
 * 用于管理数据库schema的升级和变更
 */

class DatabaseMigrator {
  constructor(db) {
    this.db = db;
  }

  /**
   * 执行多用户系统的数据库迁移
   */
  async migrateForMultiUser() {
    try {
      console.log('开始多用户系统数据库迁移...');

      // 创建用户会话表
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS user_sessions (
          id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
          user_id TEXT NOT NULL,
          session_token TEXT UNIQUE NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          expires_at TEXT NOT NULL,
          last_activity_at TEXT DEFAULT CURRENT_TIMESTAMP,
          ended_at TEXT,
          is_active INTEGER DEFAULT 1,
          user_agent TEXT,
          ip_address TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // 为用户会话表创建索引
      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)
      `).run();

      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)
      `).run();

      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active, expires_at)
      `).run();

      // 检查并添加user_id字段到现有表
      await this.addUserIdToTables();

      console.log('多用户系统数据库迁移完成');
      return true;
    } catch (error) {
      console.error('多用户系统数据库迁移失败:', error);
      throw error;
    }
  }

  /**
   * 为现有表添加user_id字段
   */
  async addUserIdToTables() {
    const tables = [
      'journal_entries',
      'companies',
      'ai_processing_logs',
      'account_mappings'
    ];

    for (const tableName of tables) {
      try {
        // 检查表是否存在user_id字段
        const tableInfo = await this.db.prepare(`
          PRAGMA table_info(${tableName})
        `).all();

        const hasUserId = tableInfo.results?.some(column => column.name === 'user_id');

        if (!hasUserId) {
          console.log(`为${tableName}表添加user_id字段...`);
          await this.db.prepare(`
            ALTER TABLE ${tableName} ADD COLUMN user_id TEXT
          `).run();

          // 为user_id字段创建索引
          await this.db.prepare(`
            CREATE INDEX IF NOT EXISTS idx_${tableName}_user_id ON ${tableName}(user_id)
          `).run();
        }
      } catch (error) {
        console.error(`为${tableName}表添加user_id字段失败:`, error);
        // 继续处理其他表
      }
    }
  }

  /**
   * 检查并执行AI增强功能的数据库迁移
   */
  async migrateForAIEnhancements() {
    try {
      // 检查是否需要添加ai_analysis字段
      const tableInfo = await this.db.prepare(`
        PRAGMA table_info(journal_entries)
      `).all();

      const hasAiAnalysis = tableInfo.results.some(column => column.name === 'ai_analysis');

      if (!hasAiAnalysis) {
        console.log('添加ai_analysis字段到journal_entries表...');
        await this.db.prepare(`
          ALTER TABLE journal_entries ADD COLUMN ai_analysis TEXT
        `).run();
      }

      // 创建AI处理日志表
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS ai_processing_logs (
          id TEXT PRIMARY KEY,
          company_id TEXT NOT NULL,
          input_text TEXT NOT NULL,
          processing_mode TEXT NOT NULL DEFAULT 'enhanced',
          success BOOLEAN NOT NULL,
          confidence REAL,
          processing_time_ms INTEGER,
          error_message TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `).run();

      // 创建账户映射表
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS account_mappings (
          id TEXT PRIMARY KEY,
          company_id TEXT NOT NULL,
          keyword TEXT NOT NULL,
          account_name TEXT NOT NULL,
          account_type TEXT NOT NULL,
          confidence REAL DEFAULT 1.0,
          usage_count INTEGER DEFAULT 1,
          last_used TEXT DEFAULT CURRENT_TIMESTAMP,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `).run();

      // 创建索引
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_journal_entries_ai_generated ON journal_entries(ai_generated)',
        'CREATE INDEX IF NOT EXISTS idx_journal_entries_ai_confidence ON journal_entries(ai_confidence)',
        'CREATE INDEX IF NOT EXISTS idx_journal_entries_company_date ON journal_entries(company_id, entry_date)',
        'CREATE INDEX IF NOT EXISTS idx_ai_logs_company_date ON ai_processing_logs(company_id, created_at)',
        'CREATE INDEX IF NOT EXISTS idx_ai_logs_success ON ai_processing_logs(success)',
        'CREATE INDEX IF NOT EXISTS idx_account_mappings_keyword ON account_mappings(keyword)',
        'CREATE INDEX IF NOT EXISTS idx_account_mappings_company ON account_mappings(company_id)',
        'CREATE INDEX IF NOT EXISTS idx_account_mappings_type ON account_mappings(account_type)'
      ];

      for (const indexSql of indexes) {
        try {
          await this.db.prepare(indexSql).run();
        } catch (error) {
          // 索引可能已存在，忽略错误
          console.log(`索引创建跳过: ${error.message}`);
        }
      }

      // 创建聊天相关表
      await this.createChatTables();

      console.log('AI增强功能数据库迁移完成');
      return { success: true, message: 'AI增强功能数据库迁移完成' };

    } catch (error) {
      console.error('数据库迁移失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 记录AI处理日志
   */
  async logAIProcessing(companyId, inputText, mode, success, confidence, processingTime, errorMessage = null) {
    try {
      const logId = crypto.randomUUID();
      await this.db.prepare(`
        INSERT INTO ai_processing_logs (
          id, company_id, input_text, processing_mode, success, 
          confidence, processing_time_ms, error_message, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        logId,
        companyId,
        inputText,
        mode,
        success,
        confidence,
        processingTime,
        errorMessage,
        new Date().toISOString()
      ).run();

      return logId;
    } catch (error) {
      console.error('AI处理日志记录失败:', error);
      return null;
    }
  }

  /**
   * 更新账户映射
   */
  async updateAccountMapping(companyId, keyword, accountName, accountType, confidence = 1.0) {
    try {
      // 检查是否已存在
      const existing = await this.db.prepare(`
        SELECT * FROM account_mappings 
        WHERE company_id = ? AND keyword = ? AND account_name = ?
      `).bind(companyId, keyword, accountName).first();

      if (existing) {
        // 更新使用次数和置信度
        await this.db.prepare(`
          UPDATE account_mappings 
          SET usage_count = usage_count + 1,
              confidence = (confidence + ?) / 2,
              last_used = ?
          WHERE id = ?
        `).bind(confidence, new Date().toISOString(), existing.id).run();
      } else {
        // 创建新映射
        const mappingId = crypto.randomUUID();
        await this.db.prepare(`
          INSERT INTO account_mappings (
            id, company_id, keyword, account_name, account_type, 
            confidence, usage_count, last_used, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          mappingId,
          companyId,
          keyword,
          accountName,
          accountType,
          confidence,
          1,
          new Date().toISOString(),
          new Date().toISOString()
        ).run();
      }

      return true;
    } catch (error) {
      console.error('账户映射更新失败:', error);
      return false;
    }
  }

  /**
   * 获取公司的账户映射
   */
  async getAccountMappings(companyId, accountType = null) {
    try {
      let query = `
        SELECT * FROM account_mappings 
        WHERE company_id = ?
      `;
      const params = [companyId];

      if (accountType) {
        query += ` AND account_type = ?`;
        params.push(accountType);
      }

      query += ` ORDER BY confidence DESC, usage_count DESC`;

      const result = await this.db.prepare(query).bind(...params).all();
      return result.results || [];
    } catch (error) {
      console.error('获取账户映射失败:', error);
      return [];
    }
  }

  /**
   * 获取AI处理统计
   */
  async getAIProcessingStats(companyId, days = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const stats = await this.db.prepare(`
        SELECT
          COUNT(*) as total_requests,
          SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
          AVG(CASE WHEN success = 1 THEN confidence ELSE NULL END) as avg_confidence,
          AVG(processing_time_ms) as avg_processing_time,
          processing_mode,
          DATE(created_at) as date
        FROM ai_processing_logs
        WHERE company_id = ? AND created_at >= ?
        GROUP BY processing_mode, DATE(created_at)
        ORDER BY date DESC
      `).bind(companyId, cutoffDate.toISOString()).all();

      return stats.results || [];
    } catch (error) {
      console.error('获取AI处理统计失败:', error);
      return [];
    }
  }

  /**
   * 执行Google OAuth数据库迁移
   */
  async migrateForGoogleOAuth() {
    try {
      console.log('开始Google OAuth数据库迁移...');

      // 首先创建OAuth状态表（最重要的）
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS oauth_states (
          state TEXT PRIMARY KEY,
          user_agent TEXT,
          ip_address TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          expires_at TEXT NOT NULL
        )
      `).run();

      // 创建用户会话表 (使用统一的结构)
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS user_sessions (
          id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
          user_id TEXT NOT NULL,
          session_token TEXT UNIQUE NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          expires_at TEXT NOT NULL,
          last_activity_at TEXT DEFAULT CURRENT_TIMESTAMP,
          ended_at TEXT,
          is_active INTEGER DEFAULT 1,
          user_agent TEXT,
          ip_address TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // 检查users表是否存在
      const tablesResult = await this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='users'
      `).all();

      const usersTableExists = tablesResult.results && tablesResult.results.length > 0;

      if (usersTableExists) {
        // 检查是否已经有Google OAuth字段
        const tableInfo = await this.db.prepare(`
          PRAGMA table_info(users)
        `).all();

        const hasGoogleId = tableInfo.results.some(column => column.name === 'google_id');

        if (!hasGoogleId) {
          console.log('添加Google OAuth字段到现有users表...');

          // 使用ALTER TABLE添加新字段
          const alterStatements = [
            'ALTER TABLE users ADD COLUMN google_id TEXT UNIQUE',
            'ALTER TABLE users ADD COLUMN name TEXT',
            'ALTER TABLE users ADD COLUMN avatar_url TEXT',
            'ALTER TABLE users ADD COLUMN google_access_token TEXT',
            'ALTER TABLE users ADD COLUMN google_refresh_token TEXT',
            'ALTER TABLE users ADD COLUMN google_token_expires_at TEXT',
            'ALTER TABLE users ADD COLUMN last_login_at TEXT'
          ];

          for (const statement of alterStatements) {
            try {
              await this.db.prepare(statement).run();
            } catch (error) {
              // 字段可能已存在，忽略错误
              console.log(`字段添加跳过: ${error.message}`);
            }
          }
        }
      } else {
        // 创建新的users表
        console.log('创建新的users表...');
        await this.db.prepare(`
          CREATE TABLE users (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE,
            email TEXT UNIQUE,
            password_hash TEXT,
            google_id TEXT UNIQUE,
            name TEXT,
            avatar_url TEXT,
            role TEXT NOT NULL DEFAULT 'user',
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            google_access_token TEXT,
            google_refresh_token TEXT,
            google_token_expires_at TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            last_login_at TEXT
          )
        `).run();
      }

      // 创建索引
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id)',
        'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
        'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
        'CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_at)',
        'CREATE INDEX IF NOT EXISTS idx_oauth_states_expires ON oauth_states(expires_at)',
        'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)',
        'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at)'
      ];

      for (const indexSql of indexes) {
        try {
          await this.db.prepare(indexSql).run();
        } catch (error) {
          console.log(`索引创建跳过: ${error.message}`);
        }
      }

      console.log('Google OAuth数据库迁移完成');
      return { success: true, message: 'Google OAuth数据库迁移完成' };

    } catch (error) {
      console.error('Google OAuth数据库迁移失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 清理过期的OAuth状态
   */
  async cleanupExpiredOAuthStates() {
    try {
      const now = new Date().toISOString();
      await this.db.prepare(`
        DELETE FROM oauth_states WHERE expires_at < ?
      `).bind(now).run();

      return true;
    } catch (error) {
      console.error('清理过期OAuth状态失败:', error);
      return false;
    }
  }

  /**
   * 清理过期的用户会话
   */
  async cleanupExpiredSessions() {
    try {
      const now = new Date().toISOString();
      await this.db.prepare(`
        DELETE FROM user_sessions WHERE expires_at < ?
      `).bind(now).run();

      return true;
    } catch (error) {
      console.error('清理过期会话失败:', error);
      return false;
    }
  }

  /**
   * 创建聊天相关表
   */
  async createChatTables() {
    try {
      // 创建聊天消息表
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS chat_messages (
          id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
          user_id TEXT NOT NULL,
          message_id TEXT NOT NULL,
          content TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('user', 'ai')),
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          metadata TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // 创建索引
      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id)
      `).run();

      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages(timestamp)
      `).run();

      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_chat_messages_user_timestamp ON chat_messages(user_id, timestamp)
      `).run();

      // 创建聊天会话表（可选，用于管理聊天会话）
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS chat_sessions (
          id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
          user_id TEXT NOT NULL,
          session_name TEXT,
          started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
          message_count INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT TRUE,
          metadata TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // 创建聊天会话索引
      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id)
      `).run();

      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_chat_sessions_active ON chat_sessions(is_active)
      `).run();

      console.log('✅ 聊天表创建完成');
      return true;
    } catch (error) {
      console.error('创建聊天表失败:', error);
      return false;
    }
  }
}

export { DatabaseMigrator };

/**
 * GoldenLedger API - Cloudflare Workers
 * 智能会计系统后端API
 */

// 使用动态导入来避免模块加载问题
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
  'Access-Control-Max-Age': '86400',
};

function handleCORS(request) {
  return new Response(null, {
    status: 200,
    headers: corsHeaders
  });
}

// 简化的路由处理
async function handleRequest(request, env) {
  const url = new URL(request.url);
  const path = url.pathname;

  // CORS 预检
  if (request.method === 'OPTIONS') {
    return handleCORS(request);
  }

  // 路由处理
  if (path === '/api/health') {
    return new Response(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '3.3.1',
      path: path,
      method: request.method
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }

  // 调试路由 - 显示所有请求信息
  if (path === '/api/debug') {
    return new Response(JSON.stringify({
      success: true,
      debug: {
        path: path,
        method: request.method,
        headers: Object.fromEntries(request.headers.entries()),
        url: request.url,
        timestamp: new Date().toISOString()
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }

  if (path === '/api/companies') {
    return await handleCompanies(request, env);
  }

  // PayPal支付验证路由
  if (path === '/api/paypal/verify-payment') {
    return await handlePayPalVerifyPayment(request, env);
  }

  // 用户订阅状态查询路由
  if (path === '/api/user/subscription-status') {
    return await handleUserSubscriptionStatus(request, env);
  }

  // 自动注册路由（用于支付成功后）
  if (path === '/api/auth/auto-register') {
    return await handleAutoRegister(request, env);
  }

  // 支付相关API路由
  if (path.startsWith('/api/payment/')) {
    return await handlePaymentAPI(request, env, path);
  }

  // 订阅相关API路由
  if (path.startsWith('/api/subscription/')) {
    return await handleSubscriptionAPI(request, env, path);
  }

  if (path === '/api/journal-entries') {
    if (request.method === 'GET') {
      return await handleJournalEntries(request, env);
    } else if (request.method === 'POST') {
      return await handleSaveJournalEntry(request, env);
    }
  }

  // 处理单个记录的操作
  if (path.startsWith('/api/journal-entries/') && path !== '/api/journal-entries') {
    const entryId = path.split('/').pop();
    if (request.method === 'PUT') {
      return await handleUpdateJournalEntry(request, env, entryId);
    } else if (request.method === 'DELETE') {
      return await handleDeleteJournalEntry(request, env, entryId);
    }
  }

  if (path === '/api/ai/process-text' && request.method === 'POST') {
    return await handleAIProcessing(request, env);
  }

  if (path === '/api/ai/stats' && request.method === 'GET') {
    return await handleAIStats(request, env);
  }

  if (path === '/api/ai/reset-usage' && (request.method === 'GET' || request.method === 'POST')) {
    return await handleResetAIUsage(request, env);
  }

  if (path === '/ai-bookkeeping/invoice-ocr' && request.method === 'POST') {
    return await handleInvoiceOCR(request, env);
  }

  // 用户数据相关端点
  if (path === '/api/user/dashboard' && request.method === 'GET') {
    return await handleUserDashboard(request, env);
  }

  if (path === '/api/user/journal-entries' && request.method === 'GET') {
    return await handleUserJournalEntries(request, env);
  }

  if (path === '/api/auth/profile' && request.method === 'GET') {
    return await handleGetUserProfile(request, env);
  }

  if (path === '/api/ai/mappings' && request.method === 'GET') {
    return await handleAccountMappings(request, env);
  }

  if (path === '/api/auth/register' && request.method === 'POST') {
    return await handleAutoRegister(request, env);
  }

  if (path === '/api/auth/login' && request.method === 'POST') {
    return await handleLogin(request, env);
  }

  if (path === '/api/auth/logout' && request.method === 'POST') {
    return await handleLogout(request, env);
  }

  if (path === '/api/auth/verify' && request.method === 'GET') {
    return await handleVerifyAuth(request, env);
  }

  if (path === '/api/auth/google/login' && request.method === 'GET') {
    return await handleGoogleLogin(request, env);
  }

  if (path === '/api/auth/google/callback' && request.method === 'GET') {
    return await handleGoogleCallback(request, env);
  }

  if (path === '/api/auth/migrate' && request.method === 'POST') {
    return await handleDatabaseMigration(request, env);
  }

  if (path === '/api/database/force-migrate' && request.method === 'POST') {
    return await handleForceMigration(request, env);
  }

  if (path === '/api/database/check-structure' && request.method === 'POST') {
    return await handleCheckTableStructure(request, env);
  }

  if (path === '/api/database/add-user-fields' && request.method === 'POST') {
    return await handleAddUserFields(request, env);
  }

  if (path === '/api/database/link-user-data' && request.method === 'POST') {
    return await handleLinkUserData(request, env);
  }

  if (path === '/api/database/verify-user-fields' && request.method === 'POST') {
    return await handleVerifyUserFields(request, env);
  }

  if (path === '/api/users/list' && request.method === 'GET') {
    return await handleListUsers(request, env);
  }

  if (path === '/api/database/find-user' && request.method === 'POST') {
    return await handleFindUser(request, env);
  }

  if (path === '/api/database/add-email-field' && request.method === 'POST') {
    return await handleAddEmailField(request, env);
  }

  if (path === '/api/database/link-souyousann-data' && request.method === 'POST') {
    return await handleLinkSouyousannData(request, env);
  }

  if (path === '/api/database/verify-souyousann-link' && request.method === 'POST') {
    return await handleVerifySouyousannLink(request, env);
  }

  // Sakura Chatbox API endpoints
  if (path === '/api/chat/send' && request.method === 'POST') {
    return await handleChatSend(request, env);
  }

  if (path === '/api/chat/history' && request.method === 'POST') {
    return await handleChatHistory(request, env);
  }

  if (path === '/api/chat/save-message' && request.method === 'POST') {
    return await handleChatSaveMessage(request, env);
  }

  if (path === '/api/chat/clear-history' && request.method === 'POST') {
    return await handleChatClearHistory(request, env);
  }

  if (path === '/api/financial/monthly-expenses' && request.method === 'POST') {
    return await handleMonthlyExpenses(request, env);
  }

  if (path === '/api/financial/budget-analysis' && request.method === 'GET') {
    return await handleBudgetAnalysis(request, env);
  }

  if (path === '/api/financial/account-balances' && request.method === 'GET') {
    return await handleAccountBalances(request, env);
  }

  if (path === '/api/auth/fix-sessions' && request.method === 'POST') {
    return await handleFixSessions(request, env);
  }

  // Attachment API endpoints
  if (path.startsWith('/api/attachments/upload') && request.method === 'POST') {
    return await handleAttachmentUpload(request, env);
  }

  if (path.startsWith('/api/attachments/') && request.method === 'GET') {
    const entryId = path.split('/').pop();
    return await handleGetAttachments(request, env, entryId);
  }

  if (path.startsWith('/api/attachments/') && request.method === 'DELETE') {
    const entryId = path.split('/').pop();
    return await handleDeleteAttachment(request, env, entryId);
  }

  // 404
  return new Response('Not Found', {
    status: 404,
    headers: corsHeaders
  });
}

// 简单的密码验证函数 (生产环境应使用 bcrypt)
function verifyPassword(password, hash) {
  // 简化版本：直接比较 (生产环境应使用 bcrypt)
  return hash === '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' && password === 'admin123';
}

// 生成会话令牌
function generateSessionToken() {
  return crypto.randomUUID() + '-' + Date.now();
}

// 处理登录
async function handleLogin(request, env) {
  try {
    const body = await request.json();
    const { username, password } = body;

    if (!username || !password) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Username and password are required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 查找用户
    const user = await env.DB.prepare(
      'SELECT * FROM users WHERE username = ? AND is_active = TRUE'
    ).bind(username).first();

    if (!user || !verifyPassword(password, user.password_hash)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid username or password'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 生成会话令牌
    const sessionToken = generateSessionToken();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时

    // 保存会话
    await env.DB.prepare(`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES (?, ?, ?)
    `).bind(user.id, sessionToken, expiresAt.toISOString()).run();

    return new Response(JSON.stringify({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        },
        session_token: sessionToken,
        expires_at: expiresAt.toISOString()
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 附件上传处理函数
async function handleAttachmentUpload(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 解析表单数据
    const formData = await request.formData();
    const file = formData.get('file');
    const entryId = formData.get('entryId');
    const companyId = formData.get('companyId');

    if (!file || !entryId || !companyId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ファイル、エントリID、会社IDが必要です'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 文件大小限制 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ファイルサイズは10MB以下である必要があります'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 生成唯一的文件名
    const fileExtension = file.name.split('.').pop();
    const fileName = `${crypto.randomUUID()}.${fileExtension}`;
    const filePath = `attachments/${companyId}/${entryId}/${fileName}`;

    // 上传到 R2 存储桶
    const fileBuffer = await file.arrayBuffer();
    const r2Bucket = env.GOLDENLEDGER_FILES || env.FILES;
    if (!r2Bucket) {
      throw new Error('R2存储桶未配置');
    }

    await r2Bucket.put(filePath, fileBuffer, {
      httpMetadata: {
        contentType: file.type,
        contentDisposition: `attachment; filename="${file.name}"`
      }
    });

    // 保存附件信息到数据库
    const attachmentId = crypto.randomUUID();
    await env.DB.prepare(`
      INSERT INTO attachments (
        id, company_id, journal_entry_id, file_name, file_path, 
        file_size, file_type, upload_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      attachmentId,
      companyId,
      entryId,
      file.name,
      filePath,
      file.size,
      file.type,
      new Date().toISOString()
    ).run();

    return new Response(JSON.stringify({
      success: true,
      data: {
        id: attachmentId,
        fileName: file.name,
        filePath: filePath,
        fileSize: file.size,
        fileType: file.type
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('附件上传失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'ファイルのアップロードに失敗しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 获取附件处理函数
async function handleGetAttachments(request, env, entryId) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      console.log('附件API认证失败 - Token:', sessionToken ? sessionToken.substring(0, 10) + '...' : 'null');
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        debug: {
          hasToken: !!sessionToken,
          tokenPrefix: sessionToken ? sessionToken.substring(0, 10) : null,
          timestamp: new Date().toISOString()
        }
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 获取附件列表
    const attachments = await env.DB.prepare(`
      SELECT id, file_name, file_path, file_size, file_type, upload_date
      FROM attachments
      WHERE journal_entry_id = ?
      ORDER BY upload_date DESC
    `).bind(entryId).all();

    // 为每个附件生成下载URL
    const attachmentsWithUrls = attachments.results.map(attachment => ({
      ...attachment,
      downloadUrl: `https://goldenledger-files.souyousann.workers.dev/${attachment.file_path}`
    }));

    return new Response(JSON.stringify({
      success: true,
      data: attachmentsWithUrls
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('附件获取失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'ファイルの取得に失敗しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 删除附件处理函数
async function handleDeleteAttachment(request, env, attachmentId) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 解析请求体以获取filename
    const requestBody = await request.json();
    const filename = requestBody.filename;

    // 获取附件信息 - 根据entryId和filename查找
    const attachment = await env.DB.prepare(`
      SELECT id, file_path FROM attachments
      WHERE journal_entry_id = ? AND file_name = ?
    `).bind(attachmentId, filename).first();

    if (!attachment) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ファイルが見つかりません'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 从 R2 存储桶删除文件
    const r2Bucket = env.GOLDENLEDGER_FILES || env.FILES;
    if (r2Bucket) {
      await r2Bucket.delete(attachment.file_path);
    }

    // 从数据库删除附件记录
    await env.DB.prepare(`
      DELETE FROM attachments WHERE id = ?
    `).bind(attachment.id).run();

    return new Response(JSON.stringify({
      success: true,
      message: 'ファイルが正常に削除されました'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('附件删除失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'ファイルの削除に失敗しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理登出
async function handleLogout(request, env) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'No session token provided'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);

    // 删除会话
    await env.DB.prepare(
      'DELETE FROM user_sessions WHERE session_token = ?'
    ).bind(sessionToken).run();

    return new Response(JSON.stringify({
      success: true,
      message: 'Logged out successfully'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Logout error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理认证验证
async function handleVerifyAuth(request, env) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        authenticated: false,
        error: 'No session token provided'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);

    // 验证会话
    const session = await env.DB.prepare(`
      SELECT s.*, u.id as user_id, u.username, u.email, u.role, u.name, u.avatar_url, u.google_id, u.last_login_at
      FROM user_sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.session_token = ? AND s.expires_at > datetime('now') AND u.is_active = TRUE
    `).bind(sessionToken).first();

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        authenticated: false,
        error: 'Invalid or expired session'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      authenticated: true,
      user: {
        id: session.user_id,
        username: session.username,
        email: session.email,
        role: session.role,
        name: session.name,
        avatar_url: session.avatar_url,
        google_id: session.google_id,
        last_login_at: session.last_login_at
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Auth verification error:', error);
    return new Response(JSON.stringify({
      success: false,
      authenticated: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理公司数据
async function handleCompanies(request, env) {
  try {
    // 直接查询 D1 数据库
    const result = await env.DB.prepare('SELECT * FROM companies ORDER BY created_at DESC').all();

    return new Response(JSON.stringify({
      success: true,
      data: result.results || []
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Companies error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理保存仕訳记录 - 支持用户数据隔离
async function handleSaveJournalEntry(request, env) {
  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const {
      company_id = 'default',
      entry_date,
      entry_time,
      entry_datetime,
      description,
      debit_account,
      credit_account,
      amount,
      reference_number,
      ai_generated = false,
      ai_confidence = 0.0
    } = body;

    // 验证必需字段
    if (!description || !debit_account || !credit_account || !amount) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing required fields'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 生成 ID 和时间戳
    const entryId = crypto.randomUUID();
    const now = new Date();
    const finalEntryDate = entry_date || now.toISOString().split('T')[0];
    const finalEntryTime = entry_time || now.toTimeString().split(' ')[0];
    const finalEntryDatetime = entry_datetime || now.toISOString();

    // 保存到数据库（包含用户ID）
    await env.DB.prepare(`
      INSERT INTO journal_entries (
        id, company_id, user_id, entry_date, entry_time, entry_datetime, description,
        debit_account, credit_account, amount, reference_number,
        ai_generated, ai_confidence, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      entryId,
      company_id,
      session.user_id,
      finalEntryDate,
      finalEntryTime,
      finalEntryDatetime,
      description,
      debit_account,
      credit_account,
      parseFloat(amount),
      reference_number || `MANUAL-${Date.now()}`,
      ai_generated,
      ai_confidence,
      now.toISOString(),
      now.toISOString()
    ).run();

    return new Response(JSON.stringify({
      success: true,
      data: {
        id: entryId,
        company_id,
        entry_date: finalEntryDate,
        entry_time: finalEntryTime,
        entry_datetime: finalEntryDatetime,
        description,
        debit_account,
        credit_account,
        amount: parseFloat(amount),
        reference_number: reference_number || `MANUAL-${Date.now()}`,
        ai_generated,
        ai_confidence,
        created_at: now.toISOString(),
        updated_at: now.toISOString()
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Save journal entry error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理仕訳记录 - 支持用户数据隔离
async function handleJournalEntries(request, env) {
  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const url = new URL(request.url);
    const companyId = url.searchParams.get('company_id') || 'default';
    const limit = parseInt(url.searchParams.get('limit')) || 100;
    const offset = parseInt(url.searchParams.get('offset')) || 0;

    // 只获取当前用户的数据
    const result = await env.DB.prepare(`
      SELECT * FROM journal_entries
      WHERE user_id = ? AND company_id = ?
      ORDER BY entry_date DESC, entry_time DESC, created_at DESC
      LIMIT ? OFFSET ?
    `).bind(session.user_id, companyId, limit, offset).all();

    // 获取总数
    const countResult = await env.DB.prepare(`
      SELECT COUNT(*) as total FROM journal_entries
      WHERE user_id = ? AND company_id = ?
    `).bind(session.user_id, companyId).first();

    return new Response(JSON.stringify({
      success: true,
      data: result.results || [],
      pagination: {
        total: countResult?.total || 0,
        limit: limit,
        offset: offset,
        has_more: (countResult?.total || 0) > (offset + limit)
      },
      user_info: {
        user_id: session.user_id,
        username: session.username
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('获取仕訳记录失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '仕訳記録の取得に失敗しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理 AI 文本处理 - 增强版
async function handleAIProcessing(request, env) {
  const startTime = Date.now();
  let migrator;

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        redirect: '/auth/login.html'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);

    // 验证会话并获取用户信息
    const session = await env.DB.prepare(`
      SELECT s.*, u.id as user_id, u.username, u.email, u.role
      FROM user_sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.session_token = ? AND s.expires_at > datetime('now') AND u.is_active = TRUE
    `).bind(sessionToken).first();

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です。再度ログインしてください',
        redirect: '/auth/login.html'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const { text, company_id = 'default', mode = 'enhanced' } = body;

    if (!text) {
      return new Response(JSON.stringify({
        success: false,
        error: 'テキストが入力されていません'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 检查AI使用权限
    const { SubscriptionManager } = await import('./middleware/subscription.js');
    const subscriptionManager = new SubscriptionManager(env.DB);
    const permission = await subscriptionManager.checkAIUsagePermission(session.user_id, 'nlp');

    if (!permission.allowed) {
      return new Response(JSON.stringify({
        success: false,
        error: permission.error,
        usage_info: {
          current_usage: permission.current_usage,
          limit: permission.limit,
          plan: permission.plan,
          upgrade_required: permission.upgrade_required,
          upgrade_url: permission.upgrade_url || '/pricing',
          reset_date: permission.reset_date
        }
      }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 初始化数据库迁移器
    const { DatabaseMigrator } = await import('./db/migrator.js');
    migrator = new DatabaseMigrator(env.DB);

    // 确保数据库schema是最新的
    await migrator.migrateForAIEnhancements();

    let aiResult;

    if (mode === 'enhanced') {
      // 使用增强版AI处理器
      const { EnhancedAIProcessor } = await import('./ai/enhanced_processor.js');
      const processor = new EnhancedAIProcessor(env.GEMINI_API_KEY);
      aiResult = await processor.processNaturalLanguage(text);
    } else {
      // 降级到原始处理
      aiResult = await processWithGemini(text, env.GEMINI_API_KEY);
    }

    const processingTime = Date.now() - startTime;

    // 记录AI处理日志
    await migrator.logAIProcessing(
      company_id,
      text,
      mode,
      aiResult.success,
      aiResult.confidence,
      processingTime,
      aiResult.success ? null : aiResult.error
    );

    if (aiResult.success) {
      // 保存到数据库
      const entryId = crypto.randomUUID();
      const now = new Date();

      try {
        await env.DB.prepare(`
          INSERT INTO journal_entries (
            id, company_id, user_id, entry_date, entry_time, entry_datetime, description,
            debit_account, credit_account, amount, reference_number,
            ai_generated, ai_confidence, ai_analysis, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          entryId,
          company_id,
          session.user_id,
          aiResult.journal_entry.entry_date,
          aiResult.journal_entry.entry_time,
          aiResult.journal_entry.entry_datetime,
          aiResult.journal_entry.description,
          aiResult.journal_entry.debit_account,
          aiResult.journal_entry.credit_account,
          aiResult.journal_entry.amount,
          aiResult.journal_entry.reference_number,
          true,
          aiResult.confidence,
          JSON.stringify(aiResult.analysis || {}),
          now.toISOString(),
          now.toISOString()
        ).run();

        aiResult.journal_entry.id = entryId;
        if (aiResult.data) {
          aiResult.data.id = entryId;  // 保持向后兼容
        }

        // 更新账户映射（用于AI学习）
        if (aiResult.analysis) {
          await migrator.updateAccountMapping(
            company_id,
            text.substring(0, 50), // 使用前50个字符作为关键词
            aiResult.journal_entry.debit_account,
            'debit',
            aiResult.confidence
          );
          await migrator.updateAccountMapping(
            company_id,
            text.substring(0, 50),
            aiResult.journal_entry.credit_account,
            'credit',
            aiResult.confidence
          );
        }
      } catch (dbError) {
        console.error('Database save error:', dbError);
        // 即使数据库保存失败，也返回AI结果
        aiResult.warning = '仕訳记录生成成功，但保存到数据库时出现问题';
      }
    }

    // 记录AI使用情况
    try {
      await subscriptionManager.recordAIUsage(
        session.user_id,
        'nlp',
        aiResult.tokens_used || 0,
        aiResult.cost_estimate || 0
      );
    } catch (usageError) {
      console.error('Failed to record AI usage:', usageError);
      // 不影响主要功能，继续执行
    }

    // 添加性能信息和使用情况
    aiResult.performance = {
      processing_time_ms: processingTime,
      mode: mode
    };

    aiResult.usage_info = {
      current_usage: permission.current_usage + 1,
      limit: permission.limit,
      plan: permission.plan,
      remaining: permission.limit ? permission.limit - permission.current_usage - 1 : null
    };

    return new Response(JSON.stringify(aiResult), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('AI processing error:', error);

    const processingTime = Date.now() - startTime;

    // 记录错误日志
    if (migrator) {
      try {
        await migrator.logAIProcessing(
          body?.company_id || 'default',
          body?.text || '',
          body?.mode || 'enhanced',
          false,
          0,
          processingTime,
          error.message
        );
      } catch (logError) {
        console.error('Failed to log AI processing error:', logError);
      }
    }

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      fallback_available: true,
      performance: {
        processing_time_ms: processingTime
      }
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}



// 处理AI统计请求
async function handleAIStats(request, env) {
  try {
    const url = new URL(request.url);
    const companyId = url.searchParams.get('company_id') || 'default';
    const days = parseInt(url.searchParams.get('days')) || 30;

    const { DatabaseMigrator } = await import('./db/migrator.js');
    const migrator = new DatabaseMigrator(env.DB);

    const stats = await migrator.getAIProcessingStats(companyId, days);

    return new Response(JSON.stringify({
      success: true,
      data: stats,
      summary: {
        total_days: days,
        company_id: companyId
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('AI stats error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理Google OAuth登录
async function handleGoogleLogin(request, env) {
  try {
    const url = new URL(request.url);
    const returnUrl = url.searchParams.get('return_url') || 'https://ledger.goldenorangetech.com/';

    // 初始化数据库迁移
    const { DatabaseMigrator } = await import('./db/migrator.js');
    const migrator = new DatabaseMigrator(env.DB);
    const migrationResult = await migrator.migrateForGoogleOAuth();

    if (!migrationResult.success) {
      throw new Error(`Database migration failed: ${migrationResult.error}`);
    }

    // 初始化Google OAuth处理器
    const { GoogleOAuthHandler } = await import('./auth/google-oauth.js');
    const oauth = new GoogleOAuthHandler(
      env.GOOGLE_CLIENT_ID || '************-houru024frrn51m217b473guu0so8oob.apps.googleusercontent.com',
      env.GOOGLE_CLIENT_SECRET || '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',
      'https://goldenledger-api.souyousann.workers.dev/api/auth/google/callback'
    );

    // 生成状态参数（防CSRF）
    const state = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟过期

    // 保存状态到数据库
    try {
      await env.DB.prepare(`
        INSERT INTO oauth_states (state, user_agent, ip_address, expires_at)
        VALUES (?, ?, ?, ?)
      `).bind(
        state,
        request.headers.get('User-Agent') || '',
        request.headers.get('CF-Connecting-IP') || '',
        expiresAt.toISOString()
      ).run();
    } catch (dbError) {
      console.error('Failed to save OAuth state:', dbError);
      throw new Error('Failed to initialize OAuth process');
    }

    // 生成授权URL
    const authUrl = oauth.generateAuthUrl(state);

    return new Response(JSON.stringify({
      success: true,
      auth_url: authUrl,
      state: state
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Google OAuth login error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理Google OAuth回调
async function handleGoogleCallback(request, env) {
  try {
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    if (error) {
      throw new Error(`OAuth error: ${error}`);
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state');
    }

    // 初始化数据库迁移
    const { DatabaseMigrator } = await import('./db/migrator.js');
    const migrator = new DatabaseMigrator(env.DB);
    await migrator.migrateForGoogleOAuth();

    // 验证状态参数
    const stateRecord = await env.DB.prepare(`
      SELECT * FROM oauth_states WHERE state = ? AND expires_at > ?
    `).bind(state, new Date().toISOString()).first();

    if (!stateRecord) {
      throw new Error('Invalid or expired state parameter');
    }

    // 删除已使用的状态
    await env.DB.prepare('DELETE FROM oauth_states WHERE state = ?').bind(state).run();

    // 初始化Google OAuth处理器
    const { GoogleOAuthHandler, UserManager } = await import('./auth/google-oauth.js');
    const oauth = new GoogleOAuthHandler(
      env.GOOGLE_CLIENT_ID || '************-houru024frrn51m217b473guu0so8oob.apps.googleusercontent.com',
      env.GOOGLE_CLIENT_SECRET || '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',
      'https://goldenledger-api.souyousann.workers.dev/api/auth/google/callback'
    );

    // 交换授权码获取令牌
    const tokens = await oauth.exchangeCodeForTokens(code);

    // 获取用户信息
    const userInfo = await oauth.getUserInfo(tokens.access_token);

    // 创建或更新用户
    const userManager = new UserManager(env.DB);
    const userId = await userManager.createOrUpdateGoogleUser(userInfo, tokens);

    // 创建会话
    const sessionToken = await userManager.createUserSession(userId);

    // 清理过期数据
    await migrator.cleanupExpiredOAuthStates();
    await migrator.cleanupExpiredSessions();

    // 重定向到前端，带上会话令牌
    const redirectUrl = new URL('https://ledger.goldenorangetech.com/auth/success');
    redirectUrl.searchParams.set('token', sessionToken);

    return new Response(null, {
      status: 302,
      headers: {
        'Location': redirectUrl.toString(),
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Google OAuth callback error:', error);

    // 重定向到错误页面
    const errorUrl = new URL('https://ledger.goldenorangetech.com/auth/error');
    errorUrl.searchParams.set('error', encodeURIComponent(error.message));

    return new Response(null, {
      status: 302,
      headers: {
        'Location': errorUrl.toString(),
        ...corsHeaders
      }
    });
  }
}

// 处理数据库迁移请求
async function handleDatabaseMigration(request, env) {
  try {
    const { DatabaseMigrator } = await import('./db/migrator.js');
    const migrator = new DatabaseMigrator(env.DB);

    // 执行多用户系统迁移
    const multiUserResult = await migrator.migrateForMultiUser();

    // 执行Google OAuth迁移
    const oauthResult = await migrator.migrateForGoogleOAuth();

    // 执行AI增强功能迁移
    const aiResult = await migrator.migrateForAIEnhancements();

    return new Response(JSON.stringify({
      success: true,
      migrations: {
        multi_user: multiUserResult,
        google_oauth: oauthResult,
        ai_enhancements: aiResult
      },
      message: '数据库迁移完成'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Database migration error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理账户映射请求
async function handleAccountMappings(request, env) {
  try {
    const url = new URL(request.url);
    const companyId = url.searchParams.get('company_id') || 'default';
    const accountType = url.searchParams.get('type');

    const { DatabaseMigrator } = await import('./db/migrator.js');
    const migrator = new DatabaseMigrator(env.DB);

    const mappings = await migrator.getAccountMappings(companyId, accountType);

    return new Response(JSON.stringify({
      success: true,
      data: mappings,
      filters: {
        company_id: companyId,
        account_type: accountType
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Account mappings error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// Gemini API 调用
async function processWithGemini(text, apiKey) {
  try {
    const now = new Date();
    const prompt = `
作为专业会计师，请分析以下交易描述并提取关键信息：

原始交易描述: ${text}
解析的日期: ${now.toISOString().split('T')[0]}
解析的时间: ${now.toTimeString().split(' ')[0]}

请提取以下信息并以JSON格式返回：
{
  "transaction_type": "收入/支出/转账",
  "amount": "金额(数字)",
  "currency": "货币代码",
  "description": "交易描述",
  "category": "交易类别",
  "payment_method": "支付方式",
  "suggested_debit_account": "建议借方科目",
  "suggested_credit_account": "建议贷方科目",
  "confidence": "置信度(0-1)"
}

注意：根据日本会计准则选择合适的科目
`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API错误: ${response.status}`);
    }

    const data = await response.json();

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Gemini响应格式错误');
    }

    const responseText = data.candidates[0].content.parts[0].text;

    // 提取JSON
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应格式错误');
    }

    const transactionInfo = JSON.parse(jsonMatch[0]);

    // 生成仕訳记录
    const journalEntry = {
      entry_date: now.toISOString().split('T')[0],
      entry_time: now.toTimeString().split(' ')[0],
      entry_datetime: now.toISOString(),
      description: transactionInfo.description || '自动生成的仕訳',
      debit_account: transactionInfo.suggested_debit_account || '現金',
      credit_account: transactionInfo.suggested_credit_account || '売上高',
      amount: parseFloat(transactionInfo.amount) || 0,
      reference_number: `AI-${Date.now()}`,
    };

    return {
      success: true,
      journal_entry: journalEntry,  // 前端期望的字段名
      data: journalEntry,           // 保持向后兼容
      confidence: transactionInfo.confidence || 0.8
    };
  } catch (error) {
    console.error('Gemini processing error:', error);
    return {
      success: false,
      error: error.message,
      confidence: 0.0
    };
  }
}

// 处理PayPal支付验证
async function handlePayPalVerifyPayment(request, env) {
  try {
    const { PaymentAPI } = await import('./api/payment.js');
    const paymentAPI = new PaymentAPI(env);
    return await paymentAPI.verifyPayment(request);
  } catch (error) {
    console.error('PayPal支付验证错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'PayPal支付验证失败: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 处理用户订阅状态查询
async function handleUserSubscriptionStatus(request, env) {
  try {
    const url = new URL(request.url);
    const email = url.searchParams.get('email');

    if (!email) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少邮箱参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }

    // 查询用户订阅状态
    const result = await env.DB.prepare(`
      SELECT
        u.id, u.email, u.name,
        s.plan_id, s.status, s.amount, s.currency, s.expires_at, s.created_at
      FROM users u
      LEFT JOIN user_subscriptions s ON u.id = s.user_id
      WHERE u.email = ?
      ORDER BY s.created_at DESC
      LIMIT 1
    `).bind(email).first();

    if (!result) {
      return new Response(JSON.stringify({
        success: false,
        error: '用户不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      user: {
        id: result.id,
        email: result.email,
        name: result.name,
        subscription: result.plan_id ? {
          plan_id: result.plan_id,
          status: result.status,
          amount: result.amount,
          currency: result.currency,
          expires_at: result.expires_at,
          created_at: result.created_at
        } : null
      }
    }), {
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });

  } catch (error) {
    console.error('查询用户订阅状态错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '查询失败: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 处理自动注册（支付成功后）
async function handleAutoRegister(request, env) {
  try {
    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders
      });
    }

    const { email, name, source, order_id } = await request.json();

    if (!email || !name) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }

    const now = new Date().toISOString();

    // 检查用户是否已存在
    let user = await env.DB.prepare(`
      SELECT id, email, name, temp_password FROM users WHERE email = ?
    `).bind(email).first();

    if (!user) {
      // 创建新用户（自动生成临时密码）
      const tempPassword = generateTempPassword();
      const hashedPassword = await hashPassword(tempPassword);

      const insertResult = await env.DB.prepare(`
        INSERT INTO users (username, email, password_hash, name, email_verified, temp_password, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(email, email, hashedPassword, name, true, tempPassword, now, now).run();

      user = {
        id: insertResult.meta.last_row_id,
        email: email,
        name: name,
        temp_password: tempPassword  // 返回临时密码
      };

      console.log('✅ 自动创建新用户:', user.id);
    } else {
      console.log('✅ 用户已存在，返回现有用户信息:', user.id);
    }

    // 生成会话令牌
    const sessionToken = generateSessionToken();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30); // 30天有效期

    // 保存会话到数据库（如果有sessions表的话）
    // 这里简化处理，直接返回token

    return new Response(JSON.stringify({
      success: true,
      message: '自动注册/登录成功',
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          ...(user.temp_password && { temp_password: user.temp_password })  // 包含临时密码（如果有的话）
        },
        session_token: sessionToken
      }
    }), {
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });

  } catch (error) {
    console.error('自动注册错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '自动注册失败: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 生成临时密码
function generateTempPassword() {
  return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
}

// 简单的密码哈希（生产环境应使用更安全的方法）
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}



// 处理支付API
async function handlePaymentAPI(request, env, path) {
  try {
    const { PaymentAPI } = await import('./api/payment.js');
    const paymentAPI = new PaymentAPI(env);

    switch (path) {
      case '/api/payment/create-subscription':
        return await paymentAPI.createSubscription(request);
      case '/api/payment/approve-subscription':
        return await paymentAPI.approveSubscription(request);
      case '/api/payment/cancel-subscription':
        return await paymentAPI.cancelSubscription(request);
      case '/api/payment/subscription-status':
        return await paymentAPI.getSubscriptionStatus(request);
      case '/api/payment/webhook':
        return await paymentAPI.handleWebhook(request);
      default:
        return new Response(JSON.stringify({
          success: false,
          error: 'Payment API endpoint not found'
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
    }
  } catch (error) {
    console.error('Payment API error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Payment API error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 处理订阅API
async function handleSubscriptionAPI(request, env, path) {
  try {
    const { SubscriptionAPI } = await import('./api/subscription.js');
    const subscriptionAPI = new SubscriptionAPI(env);

    switch (path) {
      case '/api/subscription/activate-free':
        return await subscriptionAPI.activateFreePlan(request);
      case '/api/subscription/status':
        return await subscriptionAPI.getSubscriptionStatus(request);
      case '/api/subscription/details':
        return await subscriptionAPI.getSubscriptionDetails(request);
      case '/api/usage/record':
        return await subscriptionAPI.recordUsage(request);
      case '/api/usage/stats':
        return await subscriptionAPI.getUsageStats(request);
      default:
        return new Response(JSON.stringify({
          success: false,
          error: 'Subscription API endpoint not found'
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
    }
  } catch (error) {
    console.error('Subscription API error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Subscription API error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 主处理函数
export default {
  async fetch(request, env, ctx) {
    try {
      return await handleRequest(request, env);
    } catch (error) {
      console.error('Worker error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: 'Internal Server Error'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
  }
}

// 处理保存聊天消息
async function handleChatSaveMessage(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const { message_id, content, type, timestamp, metadata } = body;

    await saveChatMessage(session.user_id, {
      id: message_id,
      content,
      metadata
    }, type, env);

    return new Response(JSON.stringify({
      success: true,
      message: 'Message saved successfully'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Save message error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to save message: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理清除聊天历史
async function handleChatClearHistory(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    await env.DB.prepare(`
      DELETE FROM chat_messages WHERE user_id = ?
    `).bind(session.user_id).run();

    return new Response(JSON.stringify({
      success: true,
      message: 'Chat history cleared successfully'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Clear history error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to clear history: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理月度支出查询
async function handleMonthlyExpenses(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = body;

    // 查询月度支出数据
    const expenses = await env.DB.prepare(`
      SELECT
        SUM(amount) as total,
        COUNT(*) as count,
        credit_account,
        SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_expenses
      FROM journal_entries
      WHERE user_id = ?
        AND strftime('%Y', entry_date) = ?
        AND strftime('%m', entry_date) = ?
        AND amount > 0
      GROUP BY credit_account
      ORDER BY total DESC
    `).bind(session.user_id, year.toString(), month.toString().padStart(2, '0')).all();

    const totalExpenses = await env.DB.prepare(`
      SELECT SUM(amount) as total
      FROM journal_entries
      WHERE user_id = ?
        AND strftime('%Y', entry_date) = ?
        AND strftime('%m', entry_date) = ?
        AND amount > 0
    `).bind(session.user_id, year.toString(), month.toString().padStart(2, '0')).first();

    return new Response(JSON.stringify({
      success: true,
      data: {
        year,
        month,
        total: totalExpenses?.total || 0,
        expenses: expenses.results || [],
        summary: {
          total_amount: totalExpenses?.total || 0,
          transaction_count: expenses.results?.reduce((sum, item) => sum + item.count, 0) || 0
        }
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Monthly expenses error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to get monthly expenses: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理预算分析
async function handleBudgetAnalysis(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 获取当前月份和上个月的数据
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    const previousYear = currentMonth === 1 ? currentYear - 1 : currentYear;

    const [currentMonthData, previousMonthData] = await Promise.all([
      getMonthlyFinancialData(session.user_id, currentYear, currentMonth, env),
      getMonthlyFinancialData(session.user_id, previousYear, previousMonth, env)
    ]);

    const analysis = {
      current_month: currentMonthData,
      previous_month: previousMonthData,
      comparison: {
        expense_change: currentMonthData.expenses - previousMonthData.expenses,
        income_change: currentMonthData.income - previousMonthData.income,
        expense_percentage: previousMonthData.expenses > 0 ?
          ((currentMonthData.expenses - previousMonthData.expenses) / previousMonthData.expenses * 100) : 0,
        income_percentage: previousMonthData.income > 0 ?
          ((currentMonthData.income - previousMonthData.income) / previousMonthData.income * 100) : 0
      }
    };

    return new Response(JSON.stringify({
      success: true,
      data: analysis
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Budget analysis error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to get budget analysis: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 获取月度财务数据
async function getMonthlyFinancialData(userId, year, month, env) {
  const expenses = await env.DB.prepare(`
    SELECT SUM(amount) as total
    FROM journal_entries
    WHERE user_id = ?
      AND strftime('%Y', entry_date) = ?
      AND strftime('%m', entry_date) = ?
      AND amount > 0
  `).bind(userId, year.toString(), month.toString().padStart(2, '0')).first();

  const income = await env.DB.prepare(`
    SELECT SUM(ABS(amount)) as total
    FROM journal_entries
    WHERE user_id = ?
      AND strftime('%Y', entry_date) = ?
      AND strftime('%m', entry_date) = ?
      AND amount < 0
  `).bind(userId, year.toString(), month.toString().padStart(2, '0')).first();

  return {
    year,
    month,
    expenses: expenses?.total || 0,
    income: income?.total || 0,
    balance: (income?.total || 0) - (expenses?.total || 0)
  };
}

// 处理账户余额查询
async function handleAccountBalances(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 获取各账户余额
    const debitBalances = await env.DB.prepare(`
      SELECT
        debit_account as account,
        SUM(amount) as balance
      FROM journal_entries
      WHERE user_id = ?
      GROUP BY debit_account
      HAVING SUM(amount) != 0
      ORDER BY balance DESC
    `).bind(session.user_id).all();

    const creditBalances = await env.DB.prepare(`
      SELECT
        credit_account as account,
        SUM(-amount) as balance
      FROM journal_entries
      WHERE user_id = ?
      GROUP BY credit_account
      HAVING SUM(-amount) != 0
      ORDER BY balance DESC
    `).bind(session.user_id).all();

    // 合并账户余额
    const accountBalances = new Map();

    debitBalances.results?.forEach(item => {
      accountBalances.set(item.account, (accountBalances.get(item.account) || 0) + item.balance);
    });

    creditBalances.results?.forEach(item => {
      accountBalances.set(item.account, (accountBalances.get(item.account) || 0) + item.balance);
    });

    const balances = Array.from(accountBalances.entries()).map(([account, balance]) => ({
      account,
      balance
    })).sort((a, b) => Math.abs(b.balance) - Math.abs(a.balance));

    // 计算总资产和总负债
    const assets = balances.filter(item => item.balance > 0);
    const liabilities = balances.filter(item => item.balance < 0);

    const totalAssets = assets.reduce((sum, item) => sum + item.balance, 0);
    const totalLiabilities = Math.abs(liabilities.reduce((sum, item) => sum + item.balance, 0));

    return new Response(JSON.stringify({
      success: true,
      data: {
        balances,
        summary: {
          total_assets: totalAssets,
          total_liabilities: totalLiabilities,
          net_worth: totalAssets - totalLiabilities,
          account_count: balances.length
        },
        assets,
        liabilities: liabilities.map(item => ({
          account: item.account,
          balance: Math.abs(item.balance)
        }))
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Account balances error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to get account balances: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 生成唯一ID的辅助函数
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Sakura Chatbox API Handlers

// 处理聊天消息发送
async function handleChatSend(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const { messages, model = 'gemini-2.0-flash-exp', temperature = 0.7, max_tokens = 1000 } = body;

    if (!messages || !Array.isArray(messages)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Messages array is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 调用Gemini API
    const geminiResponse = await callGeminiAPI(messages, {
      model,
      temperature,
      max_tokens
    }, env);

    // 保存聊天记录
    await saveChatMessage(session.user_id, messages[messages.length - 1], 'user', env);
    await saveChatMessage(session.user_id, { content: geminiResponse }, 'ai', env);

    return new Response(JSON.stringify({
      success: true,
      response: geminiResponse
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Chat send error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process chat message: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 调用Gemini API
async function callGeminiAPI(messages, options, env) {
  const apiKey = env.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('Gemini API key not configured');
  }

  const requestBody = {
    contents: messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    })),
    generationConfig: {
      temperature: options.temperature,
      maxOutputTokens: options.max_tokens,
      topP: 0.8,
      topK: 10
    }
  };

  const response = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/${options.model}:generateContent?key=${apiKey}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    }
  );

  if (!response.ok) {
    throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  if (data.candidates && data.candidates[0] && data.candidates[0].content) {
    return data.candidates[0].content.parts[0].text;
  } else {
    throw new Error('Invalid response from Gemini API');
  }
}

// 保存聊天消息
async function saveChatMessage(userId, message, type, env) {
  try {
    await env.DB.prepare(`
      INSERT INTO chat_messages (
        id, user_id, message_id, content, type, timestamp, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(
      generateId(),
      userId,
      message.id || generateId(),
      message.content,
      type,
      new Date().toISOString(),
      JSON.stringify(message.metadata || {})
    ).run();
  } catch (error) {
    console.error('Failed to save chat message:', error);
  }
}

// 处理聊天历史查询
async function handleChatHistory(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const session = await validateSession(request, env);
    if (!session.valid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication required'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const { limit = 50 } = body;

    const messages = await env.DB.prepare(`
      SELECT message_id, content, type, timestamp, metadata
      FROM chat_messages
      WHERE user_id = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `).bind(session.user_id, limit).all();

    return new Response(JSON.stringify({
      success: true,
      data: {
        messages: messages.results?.reverse() || []
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Chat history error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to load chat history: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 关联souyousann数据
async function handleLinkSouyousannData(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const { user_id, user_name, user_email } = body;

    if (!user_id || !user_email) {
      return new Response(JSON.stringify({
        success: false,
        error: '缺少必要的用户信息 (user_id, user_email)'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 获取所有没有用户信息的记录
    const entriesWithoutUser = await env.DB.prepare(`
      SELECT id, description, amount, entry_date, created_at
      FROM journal_entries
      WHERE (user_id IS NULL OR user_id = '')
         OR (user_email IS NULL OR user_email = '')
    `).all();

    const results = [];
    let updatedCount = 0;

    // 更新所有记录
    for (const entry of entriesWithoutUser.results || []) {
      try {
        await env.DB.prepare(`
          UPDATE journal_entries
          SET user_id = ?,
              user_name = ?,
              user_email = ?,
              created_by = ?,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(
          user_id,
          user_name || 'souyousann',
          user_email,
          user_id,
          entry.id
        ).run();

        results.push({
          entry_id: entry.id,
          description: entry.description,
          amount: entry.amount,
          success: true
        });
        updatedCount++;
      } catch (error) {
        results.push({
          entry_id: entry.id,
          description: entry.description,
          success: false,
          error: error.message
        });
      }
    }

    // 获取更新后的示例数据
    const sampleEntries = await env.DB.prepare(`
      SELECT id, description, amount, user_id, user_name, user_email, entry_date
      FROM journal_entries
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 5
    `).bind(user_id).all();

    return new Response(JSON.stringify({
      success: true,
      message: `成功关联 ${updatedCount} 条记录到souyousann用户`,
      updated_count: updatedCount,
      total_processed: results.length,
      user_info: {
        user_id: user_id,
        user_name: user_name,
        user_email: user_email
      },
      sample_entries: sampleEntries.results || [],
      results: results
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('关联souyousann数据失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '关联souyousann数据失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 验证souyousann关联
async function handleVerifySouyousannLink(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 获取总体统计
    const totalEntries = await env.DB.prepare(`
      SELECT COUNT(*) as total FROM journal_entries
    `).first();

    const entriesWithUserId = await env.DB.prepare(`
      SELECT COUNT(*) as count FROM journal_entries
      WHERE user_id IS NOT NULL AND user_id != ''
    `).first();

    const entriesWithEmail = await env.DB.prepare(`
      SELECT COUNT(*) as count FROM journal_entries
      WHERE user_email IS NOT NULL AND user_email != ''
    `).first();

    // 获取souyousann用户的记录统计
    const souyousannEntries = await env.DB.prepare(`
      SELECT COUNT(*) as count FROM journal_entries
      WHERE user_email LIKE '%souyousann%' OR user_name LIKE '%souyousann%'
    `).first();

    // 获取用户分布
    const userDistribution = await env.DB.prepare(`
      SELECT user_id, user_name, user_email, COUNT(*) as entry_count
      FROM journal_entries
      WHERE user_id IS NOT NULL AND user_id != ''
      GROUP BY user_id, user_name, user_email
      ORDER BY entry_count DESC
    `).all();

    // 获取最近的souyousann记录示例
    const recentSouyousannEntries = await env.DB.prepare(`
      SELECT id, description, amount, user_id, user_name, user_email, entry_date
      FROM journal_entries
      WHERE user_email LIKE '%souyousann%' OR user_name LIKE '%souyousann%'
      ORDER BY created_at DESC
      LIMIT 5
    `).all();

    const verification = {
      total_entries: totalEntries?.total || 0,
      entries_with_user_id: entriesWithUserId?.count || 0,
      entries_with_email: entriesWithEmail?.count || 0,
      souyousann_entries: souyousannEntries?.count || 0,
      completion_rate: totalEntries?.total > 0 ?
        Math.round((entriesWithUserId?.count || 0) / totalEntries.total * 100) : 0,
      email_completion_rate: totalEntries?.total > 0 ?
        Math.round((entriesWithEmail?.count || 0) / totalEntries.total * 100) : 0,
      user_distribution: userDistribution.results || [],
      recent_souyousann_entries: recentSouyousannEntries.results || []
    };

    const isComplete = verification.completion_rate === 100 && verification.email_completion_rate === 100;

    return new Response(JSON.stringify({
      success: true,
      verification: verification,
      is_complete: isComplete,
      message: isComplete ?
        'souyousann数据关联完成，包含双保险email字段' :
        '数据关联不完整，需要进一步处理'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('验证souyousann关联失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '验证souyousann关联失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 查找用户
async function handleFindUser(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const searchTerm = body.search_term || 'souyousann';

    // 查找用户 - 支持多种搜索方式
    const user = await env.DB.prepare(`
      SELECT id, username, name, email, created_at, updated_at
      FROM users
      WHERE username LIKE ? OR name LIKE ? OR email LIKE ?
      ORDER BY created_at ASC
      LIMIT 1
    `).bind(`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`).first();

    if (user) {
      return new Response(JSON.stringify({
        success: true,
        user: user,
        message: `找到用户: ${user.username || user.name}`
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: `未找到包含 "${searchTerm}" 的用户`
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

  } catch (error) {
    console.error('查找用户失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '查找用户失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 添加email字段
async function handleAddEmailField(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const tableName = body.table || 'journal_entries';

    // 检查email字段是否已存在
    const tableInfo = await env.DB.prepare(`
      PRAGMA table_info(${tableName})
    `).all();

    const emailFieldExists = tableInfo.results?.some(column => column.name === 'user_email');

    if (!emailFieldExists) {
      // 添加email字段
      await env.DB.prepare(`
        ALTER TABLE ${tableName} ADD COLUMN user_email TEXT
      `).run();

      // 创建索引
      await env.DB.prepare(`
        CREATE INDEX IF NOT EXISTS idx_${tableName}_user_email ON ${tableName}(user_email)
      `).run();

      return new Response(JSON.stringify({
        success: true,
        message: 'user_email字段添加成功',
        field_added: true
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    } else {
      return new Response(JSON.stringify({
        success: true,
        message: 'user_email字段已存在',
        field_added: false
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

  } catch (error) {
    console.error('添加email字段失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '添加email字段失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 关联用户数据
async function handleLinkUserData(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const action = body.action || 'link_existing_entries';

    if (action === 'link_existing_entries') {
      // 获取所有用户
      const users = await env.DB.prepare(`
        SELECT id, username, name, email FROM users ORDER BY created_at ASC
      `).all();

      if (!users.results || users.results.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          error: '没有找到用户数据，请先创建用户'
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }

      // 获取没有user_id的journal_entries
      const entriesWithoutUser = await env.DB.prepare(`
        SELECT id, description, entry_date, created_at
        FROM journal_entries
        WHERE user_id IS NULL OR user_id = ''
      `).all();

      const results = [];
      let defaultUser = users.results[0]; // 使用第一个用户作为默认用户

      // 如果有多个用户，尝试智能分配
      if (users.results.length > 1) {
        // 可以根据创建时间、描述等信息进行智能分配
        // 这里简化处理，使用第一个用户
      }

      // 更新所有没有用户信息的记录
      for (const entry of entriesWithoutUser.results || []) {
        try {
          await env.DB.prepare(`
            UPDATE journal_entries
            SET user_id = ?,
                user_name = ?,
                created_by = ?
            WHERE id = ?
          `).bind(
            defaultUser.id,
            defaultUser.name || defaultUser.username || defaultUser.email,
            defaultUser.id,
            entry.id
          ).run();

          results.push({
            entry_id: entry.id,
            description: entry.description,
            assigned_user: defaultUser.name || defaultUser.username,
            success: true
          });
        } catch (error) {
          results.push({
            entry_id: entry.id,
            description: entry.description,
            success: false,
            error: error.message
          });
        }
      }

      return new Response(JSON.stringify({
        success: true,
        message: `成功关联 ${results.filter(r => r.success).length} 条记录`,
        default_user: {
          id: defaultUser.id,
          name: defaultUser.name || defaultUser.username
        },
        results: results,
        total_processed: results.length
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    return new Response(JSON.stringify({
      success: false,
      error: '未知的操作类型'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('关联用户数据失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '关联用户数据失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 验证用户字段
async function handleVerifyUserFields(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 检查表结构
    const tableInfo = await env.DB.prepare(`
      PRAGMA table_info(journal_entries)
    `).all();

    const hasUserFields = {
      user_id: false,
      user_name: false,
      created_by: false
    };

    tableInfo.results?.forEach(column => {
      if (column.name === 'user_id') hasUserFields.user_id = true;
      if (column.name === 'user_name') hasUserFields.user_name = true;
      if (column.name === 'created_by') hasUserFields.created_by = true;
    });

    // 检查数据完整性
    const totalEntries = await env.DB.prepare(`
      SELECT COUNT(*) as total FROM journal_entries
    `).first();

    const entriesWithUser = await env.DB.prepare(`
      SELECT COUNT(*) as with_user FROM journal_entries
      WHERE user_id IS NOT NULL AND user_id != ''
    `).first();

    const entriesWithUserName = await env.DB.prepare(`
      SELECT COUNT(*) as with_name FROM journal_entries
      WHERE user_name IS NOT NULL AND user_name != ''
    `).first();

    // 获取用户分布统计
    const userStats = await env.DB.prepare(`
      SELECT user_id, user_name, COUNT(*) as entry_count
      FROM journal_entries
      WHERE user_id IS NOT NULL AND user_id != ''
      GROUP BY user_id, user_name
      ORDER BY entry_count DESC
    `).all();

    const verification = {
      table_structure: hasUserFields,
      data_integrity: {
        total_entries: totalEntries?.total || 0,
        entries_with_user_id: entriesWithUser?.with_user || 0,
        entries_with_user_name: entriesWithUserName?.with_name || 0,
        completion_rate: totalEntries?.total > 0 ?
          Math.round((entriesWithUser?.with_user || 0) / totalEntries.total * 100) : 0
      },
      user_distribution: userStats.results || [],
      all_fields_present: hasUserFields.user_id && hasUserFields.user_name && hasUserFields.created_by,
      data_complete: (entriesWithUser?.with_user || 0) === (totalEntries?.total || 0)
    };

    return new Response(JSON.stringify({
      success: true,
      verification: verification,
      message: verification.all_fields_present && verification.data_complete ?
        '用户字段验证通过，数据完整' :
        '用户字段存在问题，需要进一步修复'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('验证用户字段失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '验证用户字段失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 列出所有用户
async function handleListUsers(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const users = await env.DB.prepare(`
      SELECT id, username, name, email, created_at, updated_at
      FROM users
      ORDER BY created_at DESC
    `).all();

    return new Response(JSON.stringify({
      success: true,
      data: users.results || [],
      count: users.results?.length || 0,
      message: '用户列表获取成功'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '获取用户列表失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 检查表结构
async function handleCheckTableStructure(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const tableName = body.table || 'journal_entries';

    // 获取表结构
    const tableInfo = await env.DB.prepare(`
      PRAGMA table_info(${tableName})
    `).all();

    // 获取示例数据
    const sampleData = await env.DB.prepare(`
      SELECT * FROM ${tableName} ORDER BY created_at DESC LIMIT 5
    `).all();

    return new Response(JSON.stringify({
      success: true,
      table: tableName,
      structure: tableInfo.results,
      sample_data: sampleData.results,
      message: `${tableName}表结构检查完成`
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('检查表结构失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '检查表结构失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 添加用户字段
async function handleAddUserFields(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const tableName = body.table || 'journal_entries';
    const fields = body.fields || ['user_id', 'user_name', 'created_by'];

    const results = [];

    // 检查并添加每个字段
    for (const field of fields) {
      try {
        // 检查字段是否已存在
        const tableInfo = await env.DB.prepare(`
          PRAGMA table_info(${tableName})
        `).all();

        const fieldExists = tableInfo.results?.some(column => column.name === field);

        if (!fieldExists) {
          let fieldType = 'TEXT';
          if (field === 'user_id' || field === 'created_by') {
            fieldType = 'TEXT';
          } else if (field === 'user_name') {
            fieldType = 'TEXT';
          }

          // 添加字段
          await env.DB.prepare(`
            ALTER TABLE ${tableName} ADD COLUMN ${field} ${fieldType}
          `).run();

          results.push({
            field: field,
            action: 'added',
            success: true
          });

          // 为user_id字段创建索引
          if (field === 'user_id') {
            await env.DB.prepare(`
              CREATE INDEX IF NOT EXISTS idx_${tableName}_${field} ON ${tableName}(${field})
            `).run();
          }
        } else {
          results.push({
            field: field,
            action: 'exists',
            success: true
          });
        }
      } catch (error) {
        results.push({
          field: field,
          action: 'failed',
          success: false,
          error: error.message
        });
      }
    }

    return new Response(JSON.stringify({
      success: true,
      table: tableName,
      results: results,
      message: '用户字段添加完成'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('添加用户字段失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '添加用户字段失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理强制数据库迁移
async function handleForceMigration(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { DatabaseMigrator } = await import('./db/migrator.js');
    const migrator = new DatabaseMigrator(env.DB);

    // 强制执行所有迁移
    console.log('开始强制数据库迁移...');

    // 1. 添加user_id字段到所有表
    await migrator.addUserIdToTables();

    // 2. 执行AI增强功能迁移
    await migrator.migrateForAIEnhancements();

    // 3. 创建用户相关表
    await migrator.createUserTables();

    // 4. 检查并修复journal_entries表结构
    await env.DB.prepare(`
      CREATE TABLE IF NOT EXISTS journal_entries_new (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
        user_id TEXT NOT NULL,
        company_id TEXT NOT NULL DEFAULT 'default',
        entry_date DATE NOT NULL,
        entry_time TIME,
        entry_datetime DATETIME,
        description TEXT NOT NULL,
        debit_account TEXT NOT NULL,
        credit_account TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        reference_number TEXT,
        ai_generated BOOLEAN DEFAULT FALSE,
        ai_confidence DECIMAL(3,2),
        ai_analysis TEXT,
        attachment_path TEXT,
        debit_tax_rate DECIMAL(5,2) DEFAULT 0.00,
        credit_tax_rate DECIMAL(5,2) DEFAULT 0.00,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // 5. 迁移现有数据（如果有的话）
    try {
      await env.DB.prepare(`
        INSERT INTO journal_entries_new
        SELECT
          id,
          COALESCE(user_id, 'default_user') as user_id,
          COALESCE(company_id, 'default') as company_id,
          entry_date,
          entry_time,
          entry_datetime,
          description,
          debit_account,
          credit_account,
          amount,
          reference_number,
          COALESCE(ai_generated, 0) as ai_generated,
          ai_confidence,
          ai_analysis,
          attachment_path,
          COALESCE(debit_tax_rate, 0.00) as debit_tax_rate,
          COALESCE(credit_tax_rate, 0.00) as credit_tax_rate,
          COALESCE(created_at, CURRENT_TIMESTAMP) as created_at,
          COALESCE(updated_at, CURRENT_TIMESTAMP) as updated_at
        FROM journal_entries
      `).run();

      // 删除旧表，重命名新表
      await env.DB.prepare('DROP TABLE IF EXISTS journal_entries').run();
      await env.DB.prepare('ALTER TABLE journal_entries_new RENAME TO journal_entries').run();

    } catch (error) {
      console.log('没有现有数据需要迁移或迁移失败:', error.message);
      // 如果迁移失败，直接使用新表
      await env.DB.prepare('DROP TABLE IF EXISTS journal_entries').run();
      await env.DB.prepare('ALTER TABLE journal_entries_new RENAME TO journal_entries').run();
    }

    // 6. 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_user_id ON journal_entries(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_company_id ON journal_entries(company_id)',
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(entry_date)',
      'CREATE INDEX IF NOT EXISTS idx_journal_entries_user_company ON journal_entries(user_id, company_id)'
    ];

    for (const indexSql of indexes) {
      try {
        await env.DB.prepare(indexSql).run();
      } catch (error) {
        console.error('创建索引失败:', indexSql, error);
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: '强制数据库迁移完成',
      details: {
        user_id_added: true,
        ai_enhancements: true,
        user_tables_created: true,
        journal_entries_fixed: true,
        indexes_created: true
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('强制数据库迁移失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '强制数据库迁移失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理修复会话表
async function handleFixSessions(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await request.json();
    const action = body.action || 'reset';

    if (action === 'reset_sessions') {
      // 删除现有的会话表
      await env.DB.prepare('DROP TABLE IF EXISTS user_sessions').run();

      // 重新创建会话表
      await env.DB.prepare(`
        CREATE TABLE user_sessions (
          id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
          user_id TEXT NOT NULL,
          session_token TEXT UNIQUE NOT NULL,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          expires_at TEXT NOT NULL,
          last_activity_at TEXT DEFAULT CURRENT_TIMESTAMP,
          ended_at TEXT,
          is_active INTEGER DEFAULT 1,
          user_agent TEXT,
          ip_address TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
      `).run();

      // 创建索引
      await env.DB.prepare('CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)').run();
      await env.DB.prepare('CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)').run();
      await env.DB.prepare('CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active, expires_at)').run();

      return new Response(JSON.stringify({
        success: true,
        message: '会话表重置成功',
        action: 'reset_sessions'
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    if (action === 'check_structure') {
      // 检查表结构
      const tableInfo = await env.DB.prepare(`
        PRAGMA table_info(user_sessions)
      `).all();

      return new Response(JSON.stringify({
        success: true,
        data: tableInfo.results,
        message: '表结构检查完成'
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    return new Response(JSON.stringify({
      success: false,
      error: '未知的操作类型'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('修复会话表失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '修复会话表失败: ' + error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理更新会计分录
async function handleUpdateJournalEntry(request, env, entryId) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const body = await request.json();
    const {
      description,
      debit_account,
      credit_account,
      amount,
      entry_date,
      reference_number
    } = body;

    // 验证必需字段
    if (!description || !debit_account || !credit_account || !amount) {
      return new Response(JSON.stringify({
        success: false,
        error: '必要な項目が不足しています'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 检查记录是否存在且属于当前用户
    const existingEntry = await env.DB.prepare(`
      SELECT * FROM journal_entries
      WHERE id = ? AND user_id = ?
    `).bind(entryId, session.user_id).first();

    if (!existingEntry) {
      return new Response(JSON.stringify({
        success: false,
        error: '記録が見つかりません'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 更新记录
    const now = new Date();
    await env.DB.prepare(`
      UPDATE journal_entries
      SET description = ?,
          debit_account = ?,
          credit_account = ?,
          amount = ?,
          entry_date = ?,
          reference_number = ?,
          updated_at = ?
      WHERE id = ? AND user_id = ?
    `).bind(
      description,
      debit_account,
      credit_account,
      parseFloat(amount),
      entry_date || now.toISOString().split('T')[0],
      reference_number || existingEntry.reference_number,
      now.toISOString(),
      entryId,
      session.user_id
    ).run();

    // 获取更新后的记录
    const updatedEntry = await env.DB.prepare(`
      SELECT * FROM journal_entries
      WHERE id = ? AND user_id = ?
    `).bind(entryId, session.user_id).first();

    return new Response(JSON.stringify({
      success: true,
      data: updatedEntry,
      message: '記録が正常に更新されました'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('更新会计分录失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '記録の更新に失敗しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 处理删除会计分录
async function handleDeleteJournalEntry(request, env, entryId) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 检查记录是否存在且属于当前用户
    const existingEntry = await env.DB.prepare(`
      SELECT * FROM journal_entries
      WHERE id = ? AND user_id = ?
    `).bind(entryId, session.user_id).first();

    if (!existingEntry) {
      return new Response(JSON.stringify({
        success: false,
        error: '記録が見つかりません'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 删除记录
    await env.DB.prepare(`
      DELETE FROM journal_entries
      WHERE id = ? AND user_id = ?
    `).bind(entryId, session.user_id).run();

    return new Response(JSON.stringify({
      success: true,
      message: '記録が正常に削除されました'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('删除会计分录失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '記録の削除に失敗しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 用户仪表板数据处理函数
async function handleUserDashboard(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 获取用户的仪表板数据
    const dashboardData = await getUserDashboardData(env.DB, session.user_id);

    return new Response(JSON.stringify({
      success: true,
      data: dashboardData
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('用户仪表板数据获取错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'ダッシュボードデータの取得に失敗しました',
      code: 'DASHBOARD_ERROR'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 用户会计分录处理函数
async function handleUserJournalEntries(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 获取用户的会计分录数据
    const journalEntries = await getUserJournalEntries(env.DB, session.user_id, limit, offset);

    return new Response(JSON.stringify({
      success: true,
      data: journalEntries
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('用户会计分录获取错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: '仕訳データの取得に失敗しました',
      code: 'JOURNAL_ERROR'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 获取用户个人资料处理函数
async function handleGetUserProfile(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です',
        code: 'AUTH_REQUIRED'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です',
        code: 'INVALID_SESSION'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 获取用户信息
    const user = await env.DB.prepare(`
      SELECT id, username, email, name, avatar_url, role, created_at, last_login_at
      FROM users
      WHERE id = ?
    `).bind(session.user_id).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ユーザーが見つかりません',
        code: 'USER_NOT_FOUND'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        avatar_url: user.avatar_url,
        role: user.role,
        created_at: user.created_at,
        last_login_at: user.last_login_at
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('用户资料获取错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'プロフィールの取得に失敗しました',
      code: 'PROFILE_ERROR'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 获取用户仪表板数据的辅助函数
async function getUserDashboardData(db, userId) {
  try {
    // 获取用户的基本统计数据
    const stats = await Promise.all([
      // 总会计分录数
      db.prepare(`
        SELECT COUNT(*) as count
        FROM journal_entries
        WHERE user_id = ?
      `).bind(userId).first(),

      // 本月会计分录数
      db.prepare(`
        SELECT COUNT(*) as count
        FROM journal_entries
        WHERE user_id = ?
        AND strftime('%Y-%m', created_at) = strftime('%Y-%m', 'now')
      `).bind(userId).first(),

      // 最近的会计分录
      db.prepare(`
        SELECT *
        FROM journal_entries
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 5
      `).bind(userId).all(),

      // 用户的公司数据
      db.prepare(`
        SELECT *
        FROM companies
        WHERE user_id = ?
        ORDER BY created_at DESC
      `).bind(userId).all()
    ]);

    return {
      total_entries: stats[0]?.count || 0,
      monthly_entries: stats[1]?.count || 0,
      recent_entries: stats[2]?.results || [],
      companies: stats[3]?.results || [],
      user_id: userId,
      last_updated: new Date().toISOString()
    };
  } catch (error) {
    console.error('获取用户仪表板数据失败:', error);
    return {
      total_entries: 0,
      monthly_entries: 0,
      recent_entries: [],
      companies: [],
      user_id: userId,
      last_updated: new Date().toISOString(),
      error: error.message
    };
  }
}

// 获取用户会计分录数据的辅助函数
async function getUserJournalEntries(db, userId, limit = 20, offset = 0) {
  try {
    // 获取用户的会计分录
    const entries = await db.prepare(`
      SELECT
        je.*,
        c.name as company_name
      FROM journal_entries je
      LEFT JOIN companies c ON je.company_id = c.id
      WHERE je.user_id = ?
      ORDER BY je.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(userId, limit, offset).all();

    // 获取总数
    const totalCount = await db.prepare(`
      SELECT COUNT(*) as count
      FROM journal_entries
      WHERE user_id = ?
    `).bind(userId).first();

    return {
      entries: entries.results || [],
      total: totalCount?.count || 0,
      page: Math.floor(offset / limit) + 1,
      limit: limit,
      has_more: (totalCount?.count || 0) > (offset + limit)
    };
  } catch (error) {
    console.error('获取用户会计分录失败:', error);
    return {
      entries: [],
      total: 0,
      page: 1,
      limit: limit,
      has_more: false,
      error: error.message
    };
  }
}

// 发票OCR处理函数
async function handleInvoiceOCR(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 解析JSON请求体
    const requestData = await request.json();
    const { image_data, company_id = 'default' } = requestData;

    if (!image_data) {
      return new Response(JSON.stringify({
        success: false,
        error: '画像データが見つかりません'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 验证base64格式 - 支持data URL和纯base64
    if (!image_data.startsWith('data:image/') && !image_data.match(/^[A-Za-z0-9+/]+=*$/)) {
      return new Response(JSON.stringify({
        success: false,
        error: '無効な画像形式です'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 处理base64数据和content type
    let base64Data, contentType;
    
    if (image_data.includes(',')) {
      // 完整的data URL格式: data:image/png;base64,xxx
      const [header, data] = image_data.split(',');
      base64Data = data;
      contentType = header.match(/data:([^;]+)/)?.[1] || 'image/jpeg';
    } else {
      // 纯base64数据
      base64Data = image_data;
      // 尝试从base64数据检测图像类型
       try {
         const firstBytes = atob(base64Data.substring(0, 20));
         const bytes = new Uint8Array(firstBytes.length);
         for (let i = 0; i < firstBytes.length; i++) {
           bytes[i] = firstBytes.charCodeAt(i);
         }
         
         if (bytes[0] === 0x89 && bytes[1] === 0x50 && bytes[2] === 0x4E && bytes[3] === 0x47) {
           contentType = 'image/png';
         } else if (bytes[0] === 0xFF && bytes[1] === 0xD8 && bytes[2] === 0xFF) {
           contentType = 'image/jpeg';
         } else if (bytes[0] === 0x47 && bytes[1] === 0x49 && bytes[2] === 0x46) {
           contentType = 'image/gif';
         } else {
           contentType = 'image/png'; // 默认为PNG，因为测试图片是PNG
         }
       } catch (e) {
         contentType = 'image/png'; // 解码失败时默认为PNG
       }
    }
    
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(contentType)) {
      return new Response(JSON.stringify({
        success: false,
        error: '無効な画像形式です'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 验证文件大小 (最大10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const estimatedSize = (base64Data.length * 3) / 4; // base64解码后的大概大小
    if (estimatedSize > maxSize) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ファイルサイズが大きすぎます。最大10MBまでです。'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 使用Google Gemini Vision API进行OCR处理
    const ocrResult = await processInvoiceWithGeminiFromBase64(base64Data, contentType, env);

    if (ocrResult.success) {
      return new Response(JSON.stringify({
        success: true,
        ...ocrResult.data,
        content_type: contentType
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: ocrResult.error
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

  } catch (error) {
    console.error('OCR处理错误:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'OCR処理中にエラーが発生しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 使用Google Gemini Vision API处理发票
async function processInvoiceWithGemini(fileContent, contentType, filename, env) {
  try {
    // 将文件内容转换为base64 - 安全处理大文件
    let binaryString = '';
    for (let i = 0; i < fileContent.length; i++) {
      binaryString += String.fromCharCode(fileContent[i]);
    }
    const base64Content = btoa(binaryString);

    const prompt = `请分析这张发票/收据图片，提取信息并返回JSON格式：

{
  "vendor": "商户名称",
  "date": "日期(YYYY-MM-DD)",
  "amount": 总金额数字,
  "tax": 税额数字,
  "description": "商品描述",
  "confidence": 0.9
}

要求：
- 金额为纯数字
- 日期为YYYY-MM-DD格式
- 无法识别的返回null
- confidence为0-1的小数`;

    const requestBody = {
      contents: [{
        parts: [
          { text: prompt },
          {
            inline_data: {
              mime_type: contentType,
              data: base64Content
            }
          }
        ]
      }]
    };

    // 使用环境变量中的API密钥，如果没有则使用默认密钥
    const apiKey = env?.GEMINI_API_KEY;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini API错误详情:', response.status, errorText);
      throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Gemini API响应:', JSON.stringify(data, null, 2));

    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const text = data.candidates[0].content.parts[0].text;

      // 提取JSON内容
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        const invoiceData = JSON.parse(jsonStr);

        return {
          success: true,
          data: {
            vendor: invoiceData.vendor || '',
            date: invoiceData.date || '',
            amount: invoiceData.amount || 0,
            tax: invoiceData.tax || 0,
            description: invoiceData.description || '',
            items: invoiceData.items || [],
            confidence: invoiceData.confidence || 0.8,
            ocr_text: text
          }
        };
      } else {
        return {
          success: false,
          error: 'AI未返回有效的JSON格式数据'
        };
      }
    } else {
      return {
        success: false,
        error: 'AI API返回了无效的响应'
      };
    }

  } catch (error) {
    console.error('Gemini OCR处理错误:', error);
    return {
      success: false,
      error: `OCR处理失败: ${error.message}`
    };
  }
}

// 使用Google Gemini Vision API处理base64格式的发票OCR
async function processInvoiceWithGeminiFromBase64(base64Data, mimeType, env) {
  try {
    const apiKey = env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const prompt = `请分析这张发票/收据图片，提取信息并返回JSON格式：

{
  "vendor_name": "供应商名称",
  "invoice_number": "发票号码",
  "invoice_date": "发票日期(YYYY-MM-DD格式)",
  "total_amount": "总金额(数字)",
  "tax_amount": "税额(数字)",
  "currency": "货币代码",
  "items": [
    {
      "description": "商品描述",
      "quantity": "数量",
      "unit_price": "单价",
      "amount": "金额"
    }
  ]
}

请确保返回的是有效的JSON格式，如果某些信息无法识别，请使用null值。`;

    const requestBody = {
      contents: [{
        parts: [
          { text: prompt },
          {
            inline_data: {
              mime_type: mimeType,
              data: base64Data
            }
          }
        ]
      }]
    };

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (!result.candidates || !result.candidates[0] || !result.candidates[0].content) {
      throw new Error('Invalid response from Gemini API');
    }

    const generatedText = result.candidates[0].content.parts[0].text;
    
    // 尝试解析JSON响应
    let parsedData;
    try {
      // 清理响应文本，移除可能的markdown格式
      const cleanedText = generatedText.replace(/```json\n?|```\n?/g, '').trim();
      parsedData = JSON.parse(cleanedText);
    } catch (parseError) {
      console.error('Failed to parse Gemini response as JSON:', generatedText);
      throw new Error('Failed to parse OCR result as JSON');
    }

    return {
      success: true,
      data: parsedData
    };

  } catch (error) {
    console.error('Gemini OCR processing error:', error);
    return {
      success: false,
      error: `OCR処理中にエラーが発生しました: ${error.message}`
    };
  }
}

// AI使用记录重置处理函数
async function handleResetAIUsage(request, env) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 验证用户认证
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(JSON.stringify({
        success: false,
        error: 'ログインが必要です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const sessionToken = authHeader.substring(7);
    const { AuthManager } = await import('./auth/manager.js');
    const authManager = new AuthManager(env.DB);
    const session = await authManager.verifySession(sessionToken);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'セッションが無効です'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    const url = new URL(request.url);
    const action = url.searchParams.get('action') || 'reset';
    const userId = url.searchParams.get('user_id') || session.user_id;
    const companyId = url.searchParams.get('company_id');

    let result;
    if (action === 'stats') {
      // 获取AI使用统计
      const currentMonth = new Date().toISOString().slice(0, 7);

      const monthlyUsage = await env.DB.prepare(`
        SELECT COUNT(*) as count
        FROM ai_usage_logs
        WHERE user_id = ?
        AND strftime('%Y-%m', created_at) = ?
      `).bind(userId, currentMonth).first();

      const totalUsage = await env.DB.prepare(`
        SELECT COUNT(*) as count
        FROM ai_usage_logs
        WHERE user_id = ?
      `).bind(userId).first();

      result = {
        success: true,
        data: {
          monthly_usage: monthlyUsage?.count || 0,
          total_usage: totalUsage?.count || 0,
          current_month: currentMonth,
          user_id: userId
        }
      };
    } else if (action === 'reset') {
      // 重置AI使用记录
      const currentMonth = new Date().toISOString().slice(0, 7);
      const deleteResult = await env.DB.prepare(`
        DELETE FROM ai_usage_logs
        WHERE user_id = ?
        AND strftime('%Y-%m', created_at) = ?
      `).bind(userId, currentMonth).run();

      result = {
        success: true,
        message: `用户 ${userId} 的当月AI使用记录已重置`,
        deleted_records: deleteResult.changes,
        month: currentMonth
      };
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: '无效的操作'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('处理重置请求失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'サーバーエラーが発生しました'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

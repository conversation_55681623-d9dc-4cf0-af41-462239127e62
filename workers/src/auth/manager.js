/**
 * 认证管理器 - 支持D1数据库的多用户系统
 */

export class AuthManager {
  constructor(database) {
    this.db = database;
  }

  // 验证会话
  async verifySession(sessionToken) {
    if (!sessionToken) return null;

    try {
      const session = await this.db.prepare(`
        SELECT
          s.*,
          u.id as user_id,
          u.username,
          u.email,
          u.name,
          u.role
        FROM user_sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.session_token = ?
        AND s.expires_at > datetime('now')
        AND s.is_active = 1
      `).bind(sessionToken).first();

      if (session) {
        // 更新最后活动时间
        await this.db.prepare(`
          UPDATE user_sessions
          SET last_activity_at = datetime('now')
          WHERE session_token = ?
        `).bind(sessionToken).run();

        return {
          session_id: session.id,
          user_id: session.user_id,
          username: session.username,
          email: session.email,
          name: session.name,
          role: session.role,
          created_at: session.created_at,
          last_activity_at: session.last_activity_at
        };
      }

      return null;
    } catch (error) {
      console.error('会话验证失败:', error);
      return null;
    }
  }

  // 创建会话
  async createSession(userId, userAgent = '', ipAddress = '') {
    try {
      const sessionToken = this.generateSessionToken();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7天过期

      await this.db.prepare(`
        INSERT INTO user_sessions (
          user_id,
          session_token,
          expires_at,
          user_agent,
          ip_address,
          created_at,
          last_activity_at,
          is_active
        ) VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'), 1)
      `).bind(
        userId,
        sessionToken,
        expiresAt.toISOString(),
        userAgent,
        ipAddress
      ).run();

      // 更新用户最后登录时间
      await this.db.prepare(`
        UPDATE users
        SET last_login_at = datetime('now')
        WHERE id = ?
      `).bind(userId).run();

      return sessionToken;
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  }

  // 销毁会话
  async destroySession(sessionToken) {
    try {
      await this.db.prepare(`
        UPDATE user_sessions
        SET is_active = 0,
            ended_at = datetime('now')
        WHERE session_token = ?
      `).bind(sessionToken).run();

      return true;
    } catch (error) {
      console.error('销毁会话失败:', error);
      return false;
    }
  }

  // 清理过期会话
  async cleanupExpiredSessions() {
    try {
      const result = await this.db.prepare(`
        UPDATE user_sessions
        SET is_active = 0,
            ended_at = datetime('now')
        WHERE expires_at < datetime('now')
        AND is_active = 1
      `).run();

      return result.changes || 0;
    } catch (error) {
      console.error('清理过期会话失败:', error);
      return 0;
    }
  }

  // 获取用户的所有活跃会话
  async getUserActiveSessions(userId) {
    try {
      const sessions = await this.db.prepare(`
        SELECT
          id,
          session_token,
          created_at,
          last_activity_at,
          expires_at,
          user_agent,
          ip_address
        FROM user_sessions
        WHERE user_id = ?
        AND is_active = 1
        AND expires_at > datetime('now')
        ORDER BY last_activity_at DESC
      `).bind(userId).all();

      return sessions.results || [];
    } catch (error) {
      console.error('获取用户活跃会话失败:', error);
      return [];
    }
  }

  // 生成会话令牌
  generateSessionToken() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // 验证用户密码
  async verifyPassword(password, hashedPassword) {
    try {
      // 使用Web Crypto API验证密码
      const encoder = new TextEncoder();
      const data = encoder.encode(password);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

      return hashHex === hashedPassword;
    } catch (error) {
      console.error('密码验证失败:', error);
      return false;
    }
  }

  // 哈希密码
  async hashPassword(password) {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(password);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
      console.error('密码哈希失败:', error);
      throw error;
    }
  }
}

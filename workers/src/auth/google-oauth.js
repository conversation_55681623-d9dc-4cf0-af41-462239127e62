/**
 * Google OAuth 2.0 处理器
 * 处理Google OAuth认证流程
 */

class GoogleOAuthHandler {
  constructor(clientId, clientSecret, redirectUri) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.scope = 'openid email profile';
    this.authUrl = 'https://accounts.google.com/o/oauth2/v2/auth';
    this.tokenUrl = 'https://oauth2.googleapis.com/token';
    this.userInfoUrl = 'https://www.googleapis.com/oauth2/v2/userinfo';
  }

  /**
   * 生成Google OAuth授权URL
   */
  generateAuthUrl(state = null) {
    const params = new URLSearchParams({
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      response_type: 'code',
      scope: this.scope,
      access_type: 'offline',
      prompt: 'consent'
    });

    if (state) {
      params.append('state', state);
    }

    return `${this.authUrl}?${params.toString()}`;
  }

  /**
   * 使用授权码交换访问令牌
   */
  async exchangeCodeForTokens(code) {
    try {
      const response = await fetch(this.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          code: code,
          grant_type: 'authorization_code',
          redirect_uri: this.redirectUri,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token exchange failed: ${response.status} - ${errorText}`);
      }

      const tokens = await response.json();
      return tokens;
    } catch (error) {
      console.error('Token exchange error:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(accessToken) {
    try {
      const response = await fetch(this.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`User info fetch failed: ${response.status} - ${errorText}`);
      }

      const userInfo = await response.json();
      return userInfo;
    } catch (error) {
      console.error('User info fetch error:', error);
      throw error;
    }
  }

  /**
   * 验证ID令牌
   */
  async verifyIdToken(idToken) {
    try {
      // 获取Google的公钥
      const jwksResponse = await fetch('https://www.googleapis.com/oauth2/v3/certs');
      const jwks = await jwksResponse.json();

      // 解析ID令牌头部
      const [headerB64] = idToken.split('.');
      const header = JSON.parse(atob(headerB64));

      // 找到对应的公钥
      const key = jwks.keys.find(k => k.kid === header.kid);
      if (!key) {
        throw new Error('Public key not found');
      }

      // 简化验证：检查令牌格式和基本信息
      const [, payloadB64] = idToken.split('.');
      const payload = JSON.parse(atob(payloadB64));

      // 验证基本字段
      if (payload.iss !== 'https://accounts.google.com' && payload.iss !== 'accounts.google.com') {
        throw new Error('Invalid issuer');
      }

      if (payload.aud !== this.clientId) {
        throw new Error('Invalid audience');
      }

      if (payload.exp < Date.now() / 1000) {
        throw new Error('Token expired');
      }

      return payload;
    } catch (error) {
      console.error('ID token verification error:', error);
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken) {
    try {
      const response = await fetch(this.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          refresh_token: refreshToken,
          grant_type: 'refresh_token',
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token refresh failed: ${response.status} - ${errorText}`);
      }

      const tokens = await response.json();
      return tokens;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }
}

/**
 * 数据库用户管理
 */
class UserManager {
  constructor(db) {
    this.db = db;
  }

  /**
   * 创建或更新Google用户
   */
  async createOrUpdateGoogleUser(googleUserInfo, tokens) {
    try {
      const now = new Date().toISOString();
      
      // 检查用户是否已存在
      const existingUser = await this.db.prepare(
        'SELECT * FROM users WHERE google_id = ? OR email = ?'
      ).bind(googleUserInfo.id, googleUserInfo.email).first();

      let userId;
      
      if (existingUser) {
        // 更新现有用户
        userId = existingUser.id;
        await this.db.prepare(`
          UPDATE users SET 
            google_id = ?,
            email = ?,
            name = ?,
            avatar_url = ?,
            google_access_token = ?,
            google_refresh_token = ?,
            google_token_expires_at = ?,
            last_login_at = ?,
            updated_at = ?
          WHERE id = ?
        `).bind(
          googleUserInfo.id,
          googleUserInfo.email,
          googleUserInfo.name,
          googleUserInfo.picture,
          tokens.access_token,
          tokens.refresh_token || existingUser.google_refresh_token,
          tokens.expires_in ? new Date(Date.now() + tokens.expires_in * 1000).toISOString() : null,
          now,
          now,
          userId
        ).run();
      } else {
        // 创建新用户
        userId = crypto.randomUUID();

        // 为Google用户生成username
        // 优先使用email的用户名部分，如果冲突则添加随机后缀
        let username = googleUserInfo.email.split('@')[0];

        // 检查username是否已存在
        const existingUsername = await this.db.prepare(
          'SELECT id FROM users WHERE username = ?'
        ).bind(username).first();

        if (existingUsername) {
          // 如果username已存在，添加Google ID后缀确保唯一性
          username = `${username}_${googleUserInfo.id.substring(0, 8)}`;
        }

        await this.db.prepare(`
          INSERT INTO users (
            id, username, password_hash, google_id, email, name, avatar_url, role, is_active,
            google_access_token, google_refresh_token, google_token_expires_at,
            created_at, updated_at, last_login_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          userId,
          username,
          null, // Google OAuth用户不需要密码
          googleUserInfo.id,
          googleUserInfo.email,
          googleUserInfo.name,
          googleUserInfo.picture,
          'user', // 默认角色
          true,
          tokens.access_token,
          tokens.refresh_token,
          tokens.expires_in ? new Date(Date.now() + tokens.expires_in * 1000).toISOString() : null,
          now,
          now,
          now
        ).run();
      }

      return userId;
    } catch (error) {
      console.error('User creation/update error:', error);
      throw error;
    }
  }

  /**
   * 创建用户会话
   */
  async createUserSession(userId) {
    try {
      const sessionToken = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30天
      
      await this.db.prepare(`
        INSERT INTO user_sessions (
          session_token, user_id, expires_at, created_at
        ) VALUES (?, ?, ?, ?)
      `).bind(
        sessionToken,
        userId,
        expiresAt.toISOString(),
        new Date().toISOString()
      ).run();

      return sessionToken;
    } catch (error) {
      console.error('Session creation error:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserById(userId) {
    try {
      const user = await this.db.prepare(
        'SELECT * FROM users WHERE id = ? AND is_active = TRUE'
      ).bind(userId).first();

      return user;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  }
}

export { GoogleOAuthHandler, UserManager };

name = "goldenledger-api"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "goldenledger-api"

[[env.production.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files"
preview_bucket_name = "goldenledger-files-preview"

[[env.production.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

[env.production.vars]
GOOGLE_CLIENT_ID = "your-google-client-id"
GOOGLE_CLIENT_SECRET = "your-google-client-secret"
JWT_SECRET = "your-jwt-secret"

# Development environment
[env.development]
name = "goldenledger-api-dev"

[[env.development.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files-dev"
preview_bucket_name = "goldenledger-files-preview"

[[env.development.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

[env.development.vars]
GOOGLE_CLIENT_ID = "your-google-client-id"
GOOGLE_CLIENT_SECRET = "your-google-client-secret"
JWT_SECRET = "your-jwt-secret"
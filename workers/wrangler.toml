name = "goldenledger-api"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 默认配置（用于生产环境）
[[r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files"
preview_bucket_name = "goldenledger-files-preview"

[[d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

# 生产环境配置
[env.production]
name = "goldenledger-api"

[[env.production.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files"
preview_bucket_name = "goldenledger-files-preview"

[[env.production.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

# Development environment
[env.development]
name = "goldenledger-api-dev"

[[env.development.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files-dev"
preview_bucket_name = "goldenledger-files-preview"

[[env.development.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"
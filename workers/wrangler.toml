name = "goldenledger-api"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "goldenledger-api"

[[env.production.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files"
preview_bucket_name = "goldenledger-files-preview"

[[env.production.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

# 生产环境的敏感变量通过 wrangler secret 设置
# [env.production.vars] - 移除明文配置

# Development environment
[env.development]
name = "goldenledger-api-dev"

[[env.development.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files-dev"
preview_bucket_name = "goldenledger-files-preview"

[[env.development.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

# 开发环境的敏感变量通过 wrangler secret 设置
# [env.development.vars] - 移除明文配置
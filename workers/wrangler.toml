name = "goldenledger-api"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 生产环境资源绑定
[[r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files"

[[d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"

# Development environment
[env.development]
name = "goldenledger-api-dev"

[[env.development.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files-dev"

[[env.development.d1_databases]]
binding = "DB"
database_name = "goldenledger-db"
database_id = "bcff395d-3e68-44c7-b355-13f3014fa240"
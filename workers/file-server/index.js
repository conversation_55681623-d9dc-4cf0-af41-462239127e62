// Cloudflare Worker for serving files from R2 bucket
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    // Only allow GET and HEAD requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      return new Response('Method Not Allowed', {
        status: 405,
        headers: corsHeaders
      });
    }

    try {
      // Remove leading slash from pathname
      const key = pathname.startsWith('/') ? pathname.slice(1) : pathname;
      
      if (!key) {
        return new Response('File path required', {
          status: 400,
          headers: corsHeaders
        });
      }

      // Get object from R2
      const object = await env.GOLDENLEDGER_FILES.get(key);
      
      if (!object) {
        return new Response('File not found', {
          status: 404,
          headers: corsHeaders
        });
      }

      // Get object metadata
      const headers = new Headers(corsHeaders);
      
      // Set content type
      if (object.httpMetadata?.contentType) {
        headers.set('Content-Type', object.httpMetadata.contentType);
      }
      
      // Set content disposition for downloads
      if (object.httpMetadata?.contentDisposition) {
        headers.set('Content-Disposition', object.httpMetadata.contentDisposition);
      }
      
      // Set cache headers
      headers.set('Cache-Control', 'public, max-age=31536000'); // 1 year
      headers.set('ETag', object.httpEtag);
      
      // Set content length
      headers.set('Content-Length', object.size.toString());

      // Return the file
      return new Response(object.body, {
        headers: headers
      });

    } catch (error) {
      console.error('Error serving file:', error);
      return new Response('Internal Server Error', {
        status: 500,
        headers: corsHeaders
      });
    }
  }
};
name = "goldenledger-files"
main = "index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "goldenledger-files"

[[env.production.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files"
preview_bucket_name = "goldenledger-files-preview"

# Development environment
[env.development]
name = "goldenledger-files-dev"

[[env.development.r2_buckets]]
binding = "GOLDENLEDGER_FILES"
bucket_name = "goldenledger-files-dev"
preview_bucket_name = "goldenledger-files-preview"
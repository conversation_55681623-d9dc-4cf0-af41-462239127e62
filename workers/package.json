{"name": "goldenledger-workers", "version": "4.0.6", "description": "GoldenLedger API - Cloudflare Workers Backend", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "db:create": "wrangler d1 create goldenledger-db", "db:migrate": "wrangler d1 execute goldenledger-db --file=migrations/0001_initial.sql", "db:migrate:local": "wrangler d1 execute goldenledger-db --local --file=migrations/0001_initial.sql", "db:backup": "wrangler d1 export goldenledger-db --output=backup.sql", "kv:create": "wrangler kv:namespace create CACHE", "r2:create": "wrangler r2 bucket create goldenledger-files", "logs": "wrangler tail", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["accounting", "ai", "cloudflare", "workers", "d1", "gemini"], "author": "GoldenLedger Team", "license": "MIT", "dependencies": {"itty-router": "^4.0.0"}, "devDependencies": {"wrangler": "^3.0.0"}}
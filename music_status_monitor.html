<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能状态监控</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="/music_injector.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-4xl font-bold mb-8 text-center text-purple-600">
            🎵 GoldenLedger 音乐功能状态监控
        </h1>
        
        <!-- 总体状态 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-green-600 mb-2">已启用页面</h3>
                <p id="enabled-count" class="text-3xl font-bold text-green-600">0</p>
                <p class="text-sm text-gray-600">个页面</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-blue-600 mb-2">音频状态</h3>
                <p id="audio-status" class="text-2xl font-bold text-blue-600">检查中</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-orange-600 mb-2">当前音量</h3>
                <p id="current-volume" class="text-3xl font-bold text-orange-600">30%</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-purple-600 mb-2">播放状态</h3>
                <p id="play-status" class="text-2xl font-bold text-purple-600">未播放</p>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">🎮 全局音乐控制</h2>
            <div class="flex flex-wrap gap-4">
                <button onclick="checkAllPages()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    检查所有页面
                </button>
                <button onclick="testAudioFile()" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                    测试音频文件
                </button>
                <button onclick="clearCache()" class="bg-yellow-500 text-white px-6 py-2 rounded hover:bg-yellow-600">
                    清除缓存
                </button>
                <button onclick="exportSettings()" class="bg-purple-500 text-white px-6 py-2 rounded hover:bg-purple-600">
                    导出设置
                </button>
                <button onclick="refreshStatus()" class="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600">
                    刷新状态
                </button>
            </div>
        </div>

        <!-- 页面状态列表 -->
        <div class="bg-white p-6 rounded-lg shadow mb-8">
            <h2 class="text-xl font-semibold mb-4">📋 页面音乐功能状态</h2>
            <div class="overflow-x-auto">
                <table class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-2 text-left">页面名称</th>
                            <th class="px-4 py-2 text-left">状态</th>
                            <th class="px-4 py-2 text-left">类型</th>
                            <th class="px-4 py-2 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody id="pages-status">
                        <tr>
                            <td colspan="4" class="px-4 py-8 text-center text-gray-500">
                                点击"检查所有页面"开始检查...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 实时日志 -->
        <div class="bg-black text-green-400 p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4 text-white">📊 实时监控日志</h2>
            <div id="monitor-log" class="font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto">
                [系统] 音乐功能监控器已启动
            </div>
            <button onclick="clearLog()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                清空日志
            </button>
        </div>
    </div>

    <script>
        // 页面列表配置
        const PAGES = [
            // 主要功能页面
            { name: 'index.html', title: '主页', category: '主要功能' },
            { name: 'master_dashboard.html', title: '主仪表板', category: '主要功能' },
            { name: 'interactive_demo.html', title: 'AI演示', category: '主要功能' },
            { name: 'journal_entries.html', title: '会计分录', category: '主要功能' },
            { name: 'financial_reports.html', title: '财务报表', category: '主要功能' },
            { name: 'login_simple.html', title: '登录', category: '主要功能' },
            { name: 'register.html', title: '注册', category: '主要功能' },
            
            // 管理页面
            { name: 'user_management.html', title: '用户管理', category: '管理工具' },
            { name: 'user_settings.html', title: '用户设置', category: '管理工具' },
            { name: 'backup_management.html', title: '备份管理', category: '管理工具' },
            { name: 'data_export.html', title: '数据导出', category: '管理工具' },
            { name: 'data_import_tool.html', title: '数据导入', category: '管理工具' },
            { name: 'fixed_assets.html', title: '固定资产', category: '管理工具' },
            
            // 测试页面
            { name: 'test_multiuser.html', title: '多用户测试', category: '测试工具' },
            { name: 'test_gemini_api.html', title: 'Gemini API测试', category: '测试工具' },
            { name: 'project_status_check.html', title: '项目状态检查', category: '测试工具' },
            { name: 'cost_analysis_jpy.html', title: '费用分析', category: '测试工具' },
            { name: 'system_monitor.html', title: '系统监控', category: '测试工具' },
            
            // 支付页面
            { name: 'payment.html', title: '支付', category: '支付系统' },
            { name: 'payment-success.html', title: '支付成功', category: '支付系统' },
            { name: 'pricing.html', title: '定价', category: '支付系统' },
            
            // 信息页面
            { name: 'api_documentation.html', title: 'API文档', category: '信息页面' },
            { name: 'terms_of_service.html', title: '服务条款', category: '信息页面' },
            { name: 'privacy_policy.html', title: '隐私政策', category: '信息页面' }
        ];

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('monitor-log');
            const colors = {
                info: 'text-green-400',
                error: 'text-red-400',
                warning: 'text-yellow-400',
                success: 'text-blue-400'
            };
            
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.innerHTML += `<span class="${colors[type]}">${logEntry}</span>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('monitor-log').innerHTML = '[系统] 日志已清空\n';
        }

        // 检查单个页面的音乐功能
        async function checkPageMusic(pageName) {
            try {
                // 这里我们无法直接检查其他页面的DOM，所以使用间接方法
                // 检查页面是否存在
                const response = await fetch(`/${pageName}`, { method: 'HEAD' });
                
                if (response.ok) {
                    // 页面存在，假设音乐功能已正确注入（基于我们的批量注入结果）
                    return {
                        exists: true,
                        hasMusic: true, // 基于注入脚本的结果
                        status: 'active'
                    };
                } else {
                    return {
                        exists: false,
                        hasMusic: false,
                        status: 'not_found'
                    };
                }
            } catch (error) {
                return {
                    exists: false,
                    hasMusic: false,
                    status: 'error',
                    error: error.message
                };
            }
        }

        // 检查所有页面
        async function checkAllPages() {
            log('开始检查所有页面的音乐功能状态...', 'info');
            
            const tbody = document.getElementById('pages-status');
            tbody.innerHTML = '<tr><td colspan="4" class="px-4 py-8 text-center text-gray-500">检查中...</td></tr>';
            
            let enabledCount = 0;
            let rows = '';
            
            for (const page of PAGES) {
                log(`检查页面: ${page.name}`, 'info');
                
                const result = await checkPageMusic(page.name);
                
                let statusBadge, statusText, actionButton;
                
                if (result.exists && result.hasMusic) {
                    statusBadge = '<span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">✅ 已启用</span>';
                    statusText = '音乐功能正常';
                    actionButton = `<button onclick="openPage('${page.name}')" class="text-blue-600 hover:underline text-sm">访问</button>`;
                    enabledCount++;
                } else if (result.exists) {
                    statusBadge = '<span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">⚠️ 未启用</span>';
                    statusText = '页面存在但无音乐功能';
                    actionButton = `<button onclick="openPage('${page.name}')" class="text-blue-600 hover:underline text-sm">访问</button>`;
                } else {
                    statusBadge = '<span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">❌ 不存在</span>';
                    statusText = '页面不存在';
                    actionButton = '<span class="text-gray-400 text-sm">-</span>';
                }
                
                rows += `
                    <tr class="border-t">
                        <td class="px-4 py-2">
                            <div class="font-medium">${page.title}</div>
                            <div class="text-sm text-gray-500">${page.name}</div>
                        </td>
                        <td class="px-4 py-2">${statusBadge}</td>
                        <td class="px-4 py-2">
                            <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">${page.category}</span>
                        </td>
                        <td class="px-4 py-2">${actionButton}</td>
                    </tr>
                `;
                
                // 添加小延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            tbody.innerHTML = rows;
            document.getElementById('enabled-count').textContent = enabledCount;
            
            log(`检查完成！共 ${enabledCount}/${PAGES.length} 个页面启用了音乐功能`, 'success');
        }

        // 测试音频文件
        async function testAudioFile() {
            log('测试音频文件...', 'info');
            
            const audio = new Audio('/music/love.mp3');
            
            audio.addEventListener('canplay', () => {
                document.getElementById('audio-status').textContent = '✅ 正常';
                document.getElementById('audio-status').className = 'text-2xl font-bold text-green-600';
                log('音频文件测试成功', 'success');
            });
            
            audio.addEventListener('error', (e) => {
                document.getElementById('audio-status').textContent = '❌ 错误';
                document.getElementById('audio-status').className = 'text-2xl font-bold text-red-600';
                log(`音频文件测试失败: ${e.message}`, 'error');
            });
            
            audio.load();
        }

        // 清除缓存
        function clearCache() {
            localStorage.removeItem('golden_ledger_music_settings');
            log('音乐设置缓存已清除', 'warning');
        }

        // 导出设置
        function exportSettings() {
            const settings = localStorage.getItem('golden_ledger_music_settings');
            if (settings) {
                const blob = new Blob([settings], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `music_settings_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
                log('音乐设置已导出', 'success');
            } else {
                log('没有找到音乐设置', 'warning');
            }
        }

        // 刷新状态
        function refreshStatus() {
            log('刷新状态...', 'info');
            
            // 更新当前音乐控制器状态
            if (window.musicController) {
                const volume = Math.round(window.musicController.volume * 100);
                document.getElementById('current-volume').textContent = volume + '%';
                
                const isPlaying = window.musicController.isPlaying;
                document.getElementById('play-status').textContent = isPlaying ? '🎵 播放中' : '⏸️ 已暂停';
                document.getElementById('play-status').className = `text-2xl font-bold ${isPlaying ? 'text-green-600' : 'text-gray-600'}`;
                
                log(`音乐状态: ${isPlaying ? '播放中' : '已暂停'}, 音量: ${volume}%`, 'info');
            } else {
                document.getElementById('play-status').textContent = '❌ 未初始化';
                document.getElementById('play-status').className = 'text-2xl font-bold text-red-600';
                log('音乐控制器未初始化', 'warning');
            }
        }

        // 打开页面
        function openPage(pageName) {
            const url = `https://ledger.goldenorangetech.com/${pageName}`;
            window.open(url, '_blank');
            log(`打开页面: ${pageName}`, 'info');
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            log('音乐功能状态监控器已启动', 'success');
            
            // 延迟执行初始检查
            setTimeout(() => {
                testAudioFile();
                refreshStatus();
            }, 1000);
            
            // 定期刷新状态
            setInterval(refreshStatus, 10000); // 每10秒刷新一次
        });

        // 监听音乐控制器状态变化
        setInterval(() => {
            if (window.musicController) {
                refreshStatus();
            }
        }, 5000);
    </script>
</body>
</html>

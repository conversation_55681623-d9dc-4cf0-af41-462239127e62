/**
 * 背景設定制御システム
 * 全ページで使用可能な背景画像とアニメーション制御
 */

class BackgroundController {
    constructor() {
        this.animationIntervals = {
            sakura: null,
            snow: null,
            star: null
        };
        this.isCollapsed = false;
        this.isHidden = this.isMobileDevice(); // 移动端默认隐藏
        this.init();
    }

    // 检测是否为移动设备
    isMobileDevice() {
        return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 初期化
    init() {
        this.createControlPanel();
        this.createBackgroundContainer();
        this.bindEvents();
        this.preloadBackgroundImage();
        this.startSakuraAnimation(); // デフォルトで桜アニメーション開始
    }

    // 制御パネルのHTML作成
    createControlPanel() {
        const controlPanel = document.createElement('div');
        controlPanel.className = 'animation-control';
        controlPanel.id = 'animationControl';

        if (this.isMobileDevice()) {
            // 移动端简化版本 - 只显示眼睛图标
            controlPanel.innerHTML = `
                <div class="mobile-background-control" style="position: fixed; top: 120px; right: 20px; z-index: 1000;">
                    <button id="showBackgroundControl" style="
                        background: rgba(255, 255, 255, 0.9);
                        border: none;
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        font-size: 18px;
                        cursor: pointer;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    " title="背景設定">👁️</button>
                </div>
                <div class="background-panel" id="backgroundPanel" style="
                    position: fixed;
                    top: 160px;
                    right: 20px;
                    background: rgba(255, 255, 255, 0.95);
                    border-radius: 15px;
                    padding: 20px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    z-index: 1001;
                    display: none;
                    max-width: 300px;
                    width: 280px;
                ">
                    <!-- 指向眼睛按钮的小箭头 -->
                    <div style="
                        position: absolute;
                        top: -8px;
                        right: 15px;
                        width: 0;
                        height: 0;
                        border-left: 8px solid transparent;
                        border-right: 8px solid transparent;
                        border-bottom: 8px solid rgba(255, 255, 255, 0.95);
                    "></div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0; color: #333;">🌸 背景設定</h4>
                        <button id="closeBackgroundPanel" style="background: none; border: none; font-size: 20px; cursor: pointer; color: #666;">✕</button>
                    </div>
                    <div class="animation-toggle" id="animationToggle">
                        <!-- 背景画像制御 -->
                        <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 12px; margin-bottom: 12px;">
                            <div class="toggle-option">
                                <input type="checkbox" id="backgroundToggle" checked>
                                <label for="backgroundToggle">🖼️ 桜背景画像</label>
                            </div>
                        </div>

                        <!-- アニメーション制御 -->
                        <div style="font-size: 14px; color: #6b7280; margin-bottom: 10px;">アニメーション効果：</div>
                        <div class="toggle-option">
                            <input type="radio" id="sakura" name="animation" value="sakura" checked>
                            <label for="sakura">🌸 桜の花びら</label>
                        </div>
                        <div class="toggle-option">
                            <input type="radio" id="snow" name="animation" value="snow">
                            <label for="snow">❄️ 雪の舞</label>
                        </div>
                        <div class="toggle-option">
                            <input type="radio" id="star" name="animation" value="star">
                            <label for="star">✨ 星の輝き</label>
                        </div>
                        <div class="toggle-option">
                            <input type="radio" id="none" name="animation" value="none">
                            <label for="none">🚫 アニメーション停止</label>
                        </div>
                    </div>
                </div>
            `;
        } else {
            // 桌面版本 - 完整面板
            controlPanel.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="margin: 0;">🌸 背景設定</h4>
                    <div style="display: flex; gap: 4px;">
                        <button id="hideControl" style="background: none; border: none; font-size: 14px; cursor: pointer; padding: 4px;" title="パネルを隠す">👁️</button>
                        <button id="toggleControl" style="background: none; border: none; font-size: 16px; cursor: pointer; padding: 4px;" title="パネルを折りたたむ">📌</button>
                    </div>
                </div>
                <div class="animation-toggle" id="animationToggle">
                    <!-- 背景画像制御 -->
                    <div style="border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; margin-bottom: 8px;">
                        <div class="toggle-option">
                            <input type="checkbox" id="backgroundToggle" checked>
                            <label for="backgroundToggle">🖼️ 桜背景画像</label>
                        </div>
                    </div>

                    <!-- アニメーション制御 -->
                    <div style="font-size: 12px; color: #6b7280; margin-bottom: 6px;">アニメーション効果：</div>
                    <div class="toggle-option">
                        <input type="radio" id="sakura" name="animation" value="sakura" checked>
                        <label for="sakura">🌸 桜の花びら</label>
                    </div>
                    <div class="toggle-option">
                        <input type="radio" id="snow" name="animation" value="snow">
                        <label for="snow">❄️ 雪の舞</label>
                    </div>
                    <div class="toggle-option">
                        <input type="radio" id="star" name="animation" value="star">
                        <label for="star">✨ 星の輝き</label>
                    </div>
                    <div class="toggle-option">
                        <input type="radio" id="none" name="animation" value="none">
                        <label for="none">🚫 アニメーション停止</label>
                    </div>

                    <!-- ショートカットキーのヒント -->
                    <div style="margin-top: 12px; padding-top: 8px; border-top: 1px solid #e5e7eb; font-size: 11px; color: #9ca3af;">
                        <div>💡 ショートカットキー：</div>
                        <div>Ctrl+B 背景画像切替</div>
                        <div>Ctrl+Shift+A パネル折りたたみ</div>
                        <div>Ctrl+H パネル表示切替</div>
                    </div>
                </div>
            `;
        }
        document.body.appendChild(controlPanel);
    }

    // 背景コンテナの作成
    createBackgroundContainer() {
        // 背景画像
        const backgroundDiv = document.createElement('div');
        backgroundDiv.id = 'sakuraBackground';
        backgroundDiv.className = 'sakura-background';
        document.body.appendChild(backgroundDiv);

        // アニメーションコンテナ
        const containers = ['sakuraContainer', 'snowContainer', 'starContainer'];
        const classes = ['', 'animation-hidden', 'animation-hidden'];
        
        containers.forEach((id, index) => {
            const container = document.createElement('div');
            container.id = id;
            container.className = `sakura-container ${classes[index]}`;
            document.body.appendChild(container);
        });
    }

    // イベントバインディング
    bindEvents() {
        // 背景画像切り替え
        document.getElementById('backgroundToggle').addEventListener('change', (e) => {
            this.toggleBackgroundImage(e.target.checked);
        });

        // アニメーション切り替え
        document.querySelectorAll('input[name="animation"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.switchAnimation(e.target.value);
            });
        });

        if (this.isMobileDevice()) {
            // 移动端事件处理
            const showBtn = document.getElementById('showBackgroundControl');
            const panel = document.getElementById('backgroundPanel');
            const closeBtn = document.getElementById('closeBackgroundPanel');

            // 显示面板
            showBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                panel.style.display = 'block';
            });

            // 关闭面板
            closeBtn.addEventListener('click', () => {
                panel.style.display = 'none';
            });

            // 点击面板外部关闭
            document.addEventListener('click', (e) => {
                if (panel.style.display === 'block' &&
                    !panel.contains(e.target) &&
                    !showBtn.contains(e.target)) {
                    panel.style.display = 'none';
                }
            });

            // 阻止面板内部点击事件冒泡
            panel.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        } else {
            // 桌面端事件处理
            const toggleControl = document.getElementById('toggleControl');
            const hideControl = document.getElementById('hideControl');

            if (toggleControl) {
                toggleControl.addEventListener('click', () => {
                    this.togglePanel();
                });
            }

            if (hideControl) {
                hideControl.addEventListener('click', () => {
                    this.hidePanel();
                });
            }
        }

        // キーボードショートカット
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                const toggle = document.getElementById('backgroundToggle');
                toggle.checked = !toggle.checked;
                this.toggleBackgroundImage(toggle.checked);
            }
            
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
                e.preventDefault();
                this.togglePanel();
            }
            
            if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
                e.preventDefault();
                this.hidePanel();
            }
        });
    }

    // 背景画像の切り替え
    toggleBackgroundImage(show) {
        const background = document.getElementById('sakuraBackground');
        const checkbox = document.getElementById('backgroundToggle');
        
        if (show) {
            this.showNotification('🔄 桜背景画像を読み込み中...');
            
            const img = new Image();
            img.onload = () => {
                background.classList.remove('hidden');
                this.showNotification('🖼️ 桜背景画像を表示しました');
            };
            img.onerror = () => {
                checkbox.checked = false;
                this.showNotification('❌ 背景画像の読み込みに失敗しました');
            };
            img.src = '/static/image/sakura.png';
        } else {
            background.classList.add('hidden');
            this.showNotification('🖼️ 桜背景画像を非表示にしました');
        }
    }

    // アニメーション切り替え
    switchAnimation(type) {
        this.stopAllAnimations();
        
        document.getElementById('sakuraContainer').classList.add('animation-hidden');
        document.getElementById('snowContainer').classList.add('animation-hidden');
        document.getElementById('starContainer').classList.add('animation-hidden');
        
        this.showAnimationSwitchNotification(type);
        
        switch(type) {
            case 'sakura':
                document.getElementById('sakuraContainer').classList.remove('animation-hidden');
                this.startSakuraAnimation();
                break;
            case 'snow':
                document.getElementById('snowContainer').classList.remove('animation-hidden');
                this.startSnowAnimation();
                break;
            case 'star':
                document.getElementById('starContainer').classList.remove('animation-hidden');
                this.startStarAnimation();
                break;
            case 'none':
                break;
        }
    }

    // パネル折りたたみ
    togglePanel() {
        const animationToggle = document.getElementById('animationToggle');
        const toggleButton = document.getElementById('toggleControl');
        
        this.isCollapsed = !this.isCollapsed;
        if (this.isCollapsed) {
            animationToggle.style.display = 'none';
            toggleButton.textContent = '📍';
            toggleButton.title = 'パネルを展開';
        } else {
            animationToggle.style.display = 'flex';
            toggleButton.textContent = '📌';
            toggleButton.title = 'パネルを折りたたむ';
        }
    }

    // パネル非表示
    hidePanel() {
        const animationControl = document.getElementById('animationControl');
        
        this.isHidden = !this.isHidden;
        if (this.isHidden) {
            animationControl.style.display = 'none';
            this.showNotification('👁️ 背景設定パネルを非表示にしました');
        } else {
            animationControl.style.display = 'block';
            this.showNotification('👁️ 背景設定パネルを表示しました');
        }
    }

    // 背景画像プリロード
    preloadBackgroundImage() {
        const img = new Image();
        img.src = '/static/image/sakura.png';
    }

    // 通知表示
    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(0, 0, 0, 0.85);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1001;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(4px);
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2500);
    }

    // アニメーション切り替え通知
    showAnimationSwitchNotification(type) {
        const notifications = {
            'sakura': '🌸 桜の花びらアニメーションを開始しました',
            'snow': '❄️ 雪の舞アニメーションを開始しました',
            'star': '✨ 星の輝きアニメーションを開始しました',
            'none': '🚫 背景アニメーションを停止しました'
        };
        
        this.showNotification(notifications[type] || 'アニメーションモードを切り替えました');
    }

    // 全アニメーション停止
    stopAllAnimations() {
        Object.keys(this.animationIntervals).forEach(key => {
            if (this.animationIntervals[key]) {
                clearInterval(this.animationIntervals[key]);
                this.animationIntervals[key] = null;
            }
        });

        document.getElementById('sakuraContainer').innerHTML = '';
        document.getElementById('snowContainer').innerHTML = '';
        document.getElementById('starContainer').innerHTML = '';
    }

    // 桜アニメーション
    startSakuraAnimation() {
        const container = document.getElementById('sakuraContainer');

        const createSakura = () => {
            const sakura = document.createElement('div');
            sakura.className = 'sakura';

            sakura.style.left = Math.random() * 100 + '%';
            sakura.style.animationDuration = (Math.random() * 4 + 3) + 's';
            sakura.style.animationDelay = Math.random() * 2 + 's';

            const size = Math.random() * 15 + 15;
            sakura.style.fontSize = size + 'px';
            sakura.style.opacity = Math.random() * 0.4 + 0.6;

            const rotation = Math.random() * 360;
            const swayAmount = Math.random() * 30 + 10;
            sakura.style.transform = `rotate(${rotation}deg)`;
            sakura.style.setProperty('--sway-amount', swayAmount + 'px');

            container.appendChild(sakura);

            setTimeout(() => {
                if (sakura.parentNode) {
                    sakura.parentNode.removeChild(sakura);
                }
            }, 7000);
        };

        for (let i = 0; i < 15; i++) {
            setTimeout(createSakura, i * 250);
        }

        this.animationIntervals.sakura = setInterval(createSakura, 350);
    }

    // 雪アニメーション
    startSnowAnimation() {
        const container = document.getElementById('snowContainer');

        const createSnow = () => {
            const snow = document.createElement('div');
            snow.className = 'snow';

            snow.style.left = Math.random() * 100 + '%';
            snow.style.animationDuration = (Math.random() * 4 + 3) + 's';
            snow.style.animationDelay = Math.random() * 2 + 's';

            const size = Math.random() * 10 + 12;
            snow.style.fontSize = size + 'px';
            snow.style.opacity = Math.random() * 0.4 + 0.6;

            const rotation = Math.random() * 360;
            snow.style.transform = `rotate(${rotation}deg)`;

            container.appendChild(snow);

            setTimeout(() => {
                if (snow.parentNode) {
                    snow.parentNode.removeChild(snow);
                }
            }, 7000);
        };

        for (let i = 0; i < 18; i++) {
            setTimeout(createSnow, i * 200);
        }

        this.animationIntervals.snow = setInterval(createSnow, 250);
    }

    // 星アニメーション
    startStarAnimation() {
        const container = document.getElementById('starContainer');

        const createStar = () => {
            const star = document.createElement('div');
            star.className = 'star';

            star.style.left = Math.random() * 100 + '%';
            star.style.animationDuration = (Math.random() * 5 + 4) + 's';
            star.style.animationDelay = Math.random() * 3 + 's';

            const size = Math.random() * 8 + 14;
            star.style.fontSize = size + 'px';
            star.style.opacity = Math.random() * 0.4 + 0.7;

            const rotation = Math.random() * 360;
            star.style.transform = `rotate(${rotation}deg)`;

            const starEmojis = ['⭐', '✨', '🌟', '💫'];
            const randomEmoji = starEmojis[Math.floor(Math.random() * starEmojis.length)];
            star.textContent = randomEmoji;

            container.appendChild(star);

            setTimeout(() => {
                if (star.parentNode) {
                    star.parentNode.removeChild(star);
                }
            }, 9000);
        };

        for (let i = 0; i < 10; i++) {
            setTimeout(createStar, i * 300);
        }

        this.animationIntervals.star = setInterval(createStar, 500);
    }
}

// 自動初期化
document.addEventListener('DOMContentLoaded', function() {
    // CSSスタイルを動的に追加
    if (!document.getElementById('backgroundControlStyles')) {
        const style = document.createElement('style');
        style.id = 'backgroundControlStyles';
        style.textContent = `
            /* 背景設定制御のスタイル */
            .animation-control {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 12px;
                padding: 16px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                border: 1px solid rgba(255, 255, 255, 0.2);
                min-width: 180px;
                transition: all 0.3s ease;
            }

            .animation-control:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            }

            .animation-toggle {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .toggle-option {
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                padding: 4px 8px;
                border-radius: 6px;
                transition: background-color 0.2s;
            }

            .toggle-option:hover {
                background-color: rgba(59, 130, 246, 0.1);
            }

            .toggle-option input[type="radio"],
            .toggle-option input[type="checkbox"] {
                margin: 0;
                accent-color: #667eea;
            }

            .toggle-option label {
                font-size: 13px;
                color: #6b7280;
                cursor: pointer;
                margin: 0;
                font-weight: 500;
            }

            .toggle-option:has(input:checked) label {
                color: #374151;
                font-weight: 600;
            }

            /* 桜アニメーション */
            .sakura-container {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1;
                overflow: hidden;
            }

            .sakura {
                position: absolute;
                font-size: 20px;
                opacity: 0.9;
                animation: fall linear infinite;
                filter: drop-shadow(0 2px 4px rgba(255, 105, 180, 0.3));
                user-select: none;
                pointer-events: none;
            }

            .sakura::before {
                content: '🌸';
                display: block;
            }

            @keyframes fall {
                0% {
                    transform: translateY(-100vh) translateX(0px) rotate(0deg) scale(1);
                    opacity: 1;
                }
                25% {
                    transform: translateY(-50vh) translateX(var(--sway-amount, 20px)) rotate(90deg) scale(1.1);
                    opacity: 0.9;
                }
                50% {
                    transform: translateY(0vh) translateX(calc(var(--sway-amount, 20px) * -0.5)) rotate(180deg) scale(0.9);
                    opacity: 0.8;
                }
                75% {
                    transform: translateY(50vh) translateX(calc(var(--sway-amount, 20px) * 0.7)) rotate(270deg) scale(1.05);
                    opacity: 0.6;
                }
                100% {
                    transform: translateY(100vh) translateX(0px) rotate(360deg) scale(0.8);
                    opacity: 0;
                }
            }

            /* 雪アニメーション */
            .snow {
                position: absolute;
                font-size: 18px;
                opacity: 0.8;
                animation: snowfall linear infinite;
                user-select: none;
                pointer-events: none;
            }

            .snow::before {
                content: '❄️';
                display: block;
            }

            @keyframes snowfall {
                0% {
                    transform: translateY(-100vh) rotate(0deg) scale(1);
                    opacity: 1;
                }
                50% {
                    transform: translateY(0vh) rotate(180deg) scale(1.2);
                    opacity: 0.8;
                }
                100% {
                    transform: translateY(100vh) rotate(360deg) scale(0.9);
                    opacity: 0;
                }
            }

            /* 星アニメーション */
            .star {
                position: absolute;
                font-size: 16px;
                opacity: 0.9;
                animation: starfall linear infinite;
                user-select: none;
                pointer-events: none;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            @keyframes starfall {
                0% {
                    transform: translateY(-100vh) rotate(0deg) scale(1);
                    opacity: 1;
                }
                25% {
                    transform: translateY(-25vh) rotate(90deg) scale(1.3);
                    opacity: 0.9;
                }
                50% {
                    transform: translateY(25vh) rotate(180deg) scale(0.8);
                    opacity: 1;
                }
                75% {
                    transform: translateY(75vh) rotate(270deg) scale(1.1);
                    opacity: 0.7;
                }
                100% {
                    transform: translateY(100vh) rotate(360deg) scale(0.6);
                    opacity: 0;
                }
            }

            /* 背景画像 */
            .sakura-background {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: url('/static/image/sakura.png');
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                background-attachment: fixed;
                opacity: 0.2;
                z-index: -1;
                transition: all 0.8s ease;
                filter: blur(0.5px) brightness(1.1) saturate(0.9);
            }

            .sakura-background.hidden {
                opacity: 0;
                transform: scale(1.05);
            }

            .sakura-background::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(
                    135deg,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 182, 193, 0.05) 25%,
                    rgba(255, 105, 180, 0.03) 50%,
                    rgba(255, 255, 255, 0.1) 100%
                );
                z-index: 1;
            }

            /* 隠しクラス */
            .animation-hidden {
                display: none !important;
            }

            /* パフォーマンス最適化 */
            .sakura, .snow, .star {
                will-change: transform, opacity;
                backface-visibility: hidden;
            }

            /* モバイル最適化 */
            @media (max-width: 768px) {
                .animation-control {
                    top: 10px;
                    right: 10px;
                    padding: 12px;
                    min-width: 160px;
                }

                .sakura, .snow, .star {
                    animation-duration: 4s !important;
                }
            }

            /* アクセシビリティ */
            @media (prefers-reduced-motion: reduce) {
                .sakura, .snow, .star {
                    animation-duration: 8s !important;
                    opacity: 0.3 !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 背景制御システムを初期化
    window.backgroundController = new BackgroundController();
});

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Register Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .demo-btn {
            background: #28a745;
        }
        .demo-btn:hover {
            background: #1e7e34;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #007cba;
            text-decoration: none;
            margin: 0 10px;
        }
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        .checkbox-group input {
            width: auto;
            margin-right: 10px;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>📝 Simple Register Test</h1>
        
        <div class="status">
            ✅ No external scripts, no redirects, no auth checks
        </div>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" value="Test User" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="password" required>
            </div>
            
            <div class="form-group">
                <label for="company">Company (Optional):</label>
                <input type="text" id="company" value="Test Company">
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="terms" required>
                <label for="terms">I agree to the terms and conditions</label>
            </div>
            
            <button type="submit">Register</button>
            <button type="button" class="demo-btn" onclick="demoRegister()">Demo Register</button>
        </form>
        
        <div class="links">
            <a href="test_login_simple.html">Login</a>
            <a href="index.html">Home</a>
            <a href="master_dashboard.html">Dashboard</a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
            <strong>Debug Info:</strong><br>
            Current URL: <span id="currentUrl"></span><br>
            Timestamp: <span id="timestamp"></span><br>
            User Agent: <span id="userAgent"></span>
        </div>
    </div>

    <script>
        // 显示调试信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('timestamp').textContent = new Date().toISOString();
        document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        
        // 绝对防止重定向
        console.log('🔧 Simple register test page loaded');
        console.log('✅ No auth checks');
        console.log('✅ No external dependencies');
        console.log('✅ No redirects');
        
        // 监控重定向尝试
        let originalLocation = window.location.href;
        let redirectAttempts = 0;
        
        setInterval(function() {
            if (window.location.href !== originalLocation) {
                redirectAttempts++;
                console.error('⚠️ Unexpected redirect detected:', window.location.href);
                alert('Redirect detected! From: ' + originalLocation + ' To: ' + window.location.href);
            }
        }, 100);
        
        // 表单提交
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const company = document.getElementById('company').value;
            const terms = document.getElementById('terms').checked;
            
            if (!terms) {
                alert('Please agree to the terms and conditions');
                return;
            }
            
            console.log('📝 Form submitted:', name, email);
            
            // 创建用户数据
            const userData = {
                name: name,
                email: email,
                company: company,
                loginTime: new Date().toISOString(),
                sessionId: 'simple_reg_' + Date.now(),
                source: 'simple_register_test'
            };
            
            // 保存到localStorage
            localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
            
            alert('✅ Registration successful!\nRedirecting to dashboard...');
            
            // 延迟跳转
            setTimeout(function() {
                console.log('🔄 Redirecting to dashboard...');
                window.location.href = 'master_dashboard.html';
            }, 1000);
        });
        
        // Demo注册
        function demoRegister() {
            console.log('🎯 Demo register clicked');
            
            const userData = {
                name: 'Demo User',
                email: '<EMAIL>',
                company: 'Demo Company',
                loginTime: new Date().toISOString(),
                sessionId: 'simple_demo_reg_' + Date.now(),
                source: 'simple_demo_register_test'
            };
            
            localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
            alert('🎉 Demo registration successful!');
            
            setTimeout(function() {
                window.location.href = 'master_dashboard.html';
            }, 1000);
        }
        
        // 页面加载完成
        window.addEventListener('load', function() {
            console.log('🎯 Page fully loaded - no redirects occurred');
        });
        
        // 监听页面卸载
        window.addEventListener('beforeunload', function(e) {
            console.log('⚠️ Page is about to unload');
            // 不阻止卸载，只记录
        });
        
        // 防止任何可能的自动重定向
        if (window.history && window.history.pushState) {
            window.addEventListener('popstate', function(e) {
                console.log('📍 History state changed:', e.state);
            });
        }
    </script>
</body>
</html>

# GitHub Actions for GoldenLedger
# Note: This workflow is optional since Cloudflare Pages can deploy directly from GitHub
# If Cloudflare Pages automatic deployment is working, this file can be removed

name: Build Check for GoldenLedger

on:
  push:
    branches:
      - main
      - master
  pull_request:
    branches:
      - main
      - master

jobs:
  check:
    runs-on: ubuntu-latest
    name: Validate Static Site Structure

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Validate site structure
        run: |
          echo "🔍 Validating GoldenLedger static site structure..."

          # Check required files
          echo "📄 Checking required HTML files..."
          required_files=("index.html" "login.html" "register.html" "master_dashboard.html")
          for file in "${required_files[@]}"; do
            if [ -f "$file" ]; then
              echo "✅ $file exists"
            else
              echo "❌ $file missing"
              exit 1
            fi
          done

          # Check JavaScript files
          echo "📜 Checking JavaScript files..."
          js_files=("auth.js" "music_control.js" "background_control.js")
          for file in "${js_files[@]}"; do
            if [ -f "$file" ]; then
              echo "✅ $file exists"
            else
              echo "⚠️ $file missing (optional)"
            fi
          done

          # Check configuration files
          echo "⚙️ Checking configuration files..."
          if [ -f "_redirects" ]; then
            echo "✅ _redirects exists"
          else
            echo "⚠️ _redirects missing"
          fi

          if [ -f "wrangler.toml" ]; then
            echo "✅ wrangler.toml exists"
          else
            echo "⚠️ wrangler.toml missing"
          fi

          # Check for sensitive files (should not exist)
          echo "🔒 Checking for sensitive files..."
          sensitive_files=(".env" "backend/.env" "*.key" "*.pem")
          found_sensitive=false
          for pattern in "${sensitive_files[@]}"; do
            if ls $pattern 1> /dev/null 2>&1; then
              echo "❌ Sensitive file found: $pattern"
              found_sensitive=true
            fi
          done

          if [ "$found_sensitive" = true ]; then
            echo "❌ Sensitive files detected! Please remove them."
            exit 1
          else
            echo "✅ No sensitive files found"
          fi

          echo "🎉 Site structure validation completed successfully!"
          echo "📊 Total HTML files: $(ls *.html 2>/dev/null | wc -l)"
          echo "📊 Total JS files: $(ls *.js 2>/dev/null | wc -l)"

      - name: Check for hardcoded secrets
        run: |
          echo "🔍 Scanning for hardcoded secrets..."

          # Check for API keys in HTML and JS files (excluding env-config.js which is managed)
          if grep -r "AIzaSy" *.html *.js 2>/dev/null | grep -v "env-config.js" | grep -v "# Managed API key"; then
            echo "⚠️ Hardcoded API key found in unmanaged files"
            echo "Note: env-config.js contains managed API keys for production deployment"
          else
            echo "✅ No unmanaged hardcoded API keys found"
          fi

          # Check for other sensitive patterns
          sensitive_patterns=("password.*=" "secret.*=" "token.*=" "key.*=")
          for pattern in "${sensitive_patterns[@]}"; do
            if grep -ri "$pattern" *.html *.js 2>/dev/null | grep -v "placeholder\|example\|template"; then
              echo "⚠️ Potential sensitive data found with pattern: $pattern"
            fi
          done

          echo "✅ Security scan completed"

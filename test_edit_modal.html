<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑模态框测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-xl font-bold mb-4">编辑模态框测试</h1>
        
        <div class="space-y-4">
            <button onclick="testEditModal()" 
                    class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                测试编辑模态框
            </button>
            
            <button onclick="testEditAPI()" 
                    class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                测试编辑API
            </button>
        </div>
        
        <div id="result" class="mt-6 p-4 bg-gray-100 rounded min-h-[100px]">
            点击按钮测试功能...
        </div>
    </div>

    <script>
        // 多语言配置（简化版）
        const i18n = {
            'zh-CN': {
                'actions.edit_title': '编辑仕訳记录',
                'actions.save': '保存',
                'actions.cancel': '取消'
            }
        };
        
        const currentLanguage = 'zh-CN';
        
        function t(key) {
            return i18n[currentLanguage]?.[key] || key;
        }

        // 测试编辑模态框
        function testEditModal() {
            const result = document.getElementById('result');
            result.innerHTML = '创建编辑模态框...';
            
            try {
                // 模拟记录数据
                const entry = {
                    id: 'TEST_ENTRY_001',
                    entry_date: '2025-07-11',
                    entry_time: '12:00:00',
                    description: '测试记录',
                    debit_account: '事務用品費',
                    credit_account: '現金',
                    amount: 1200,
                    reference_number: 'TEST_REF'
                };
                
                // 创建编辑模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
                        <div class="flex justify-between items-center p-6 border-b">
                            <h3 class="text-lg font-medium" data-i18n="actions.edit_title">编辑仕訳记录</h3>
                            <button onclick="closeTestModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="p-6 max-h-[70vh] overflow-auto">
                            <form id="edit-form" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">日期</label>
                                    <input type="date" id="edit-date" value="${entry.entry_date || ''}" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                    <textarea id="edit-description" rows="3" 
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">${entry.description || ''}</textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">金额</label>
                                    <input type="number" id="edit-amount" value="${entry.amount || ''}" step="0.01" min="0"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </form>
                        </div>
                        <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
                            <button onclick="closeTestModal()" 
                                    class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                <span data-i18n="actions.cancel">取消</span>
                            </button>
                            <button onclick="saveTestEntry()" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                <span data-i18n="actions.save">保存</span>
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                
                // 应用多语言翻译到新创建的模态框
                modal.querySelectorAll('[data-i18n]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    element.textContent = t(key);
                });
                
                result.innerHTML = `
                    <div class="text-green-600">
                        ✅ 编辑模态框创建成功！<br>
                        - 模态框已显示<br>
                        - 多语言翻译已应用<br>
                        - 表单字段已填充
                    </div>
                `;
                
            } catch (error) {
                result.innerHTML = `❌ 创建模态框失败: ${error.message}`;
                console.error('创建模态框失败:', error);
            }
        }
        
        // 关闭测试模态框
        function closeTestModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black');
            if (modal) {
                modal.remove();
            }
            document.getElementById('result').innerHTML += '<br><div class="text-blue-600">模态框已关闭</div>';
        }
        
        // 保存测试记录
        function saveTestEntry() {
            const description = document.getElementById('edit-description').value;
            const amount = document.getElementById('edit-amount').value;
            
            document.getElementById('result').innerHTML = `
                <div class="text-green-600">
                    ✅ 模拟保存成功！<br>
                    - 描述: ${description}<br>
                    - 金额: ¥${amount}
                </div>
            `;
            
            closeTestModal();
        }
        
        // 测试编辑API
        async function testEditAPI() {
            const result = document.getElementById('result');
            result.innerHTML = '测试编辑API...';
            
            try {
                // 获取第一条记录
                const response = await fetch('/journal-entries/default');
                const entries = await response.json();
                
                if (entries.length === 0) {
                    result.innerHTML = '❌ 没有记录可以测试';
                    return;
                }
                
                const testEntry = entries[0];
                
                // 准备编辑数据
                const editData = {
                    entry_date: testEntry.entry_date || '2025-07-11',
                    entry_time: testEntry.entry_time || '12:00:00',
                    description: `【API测试】${testEntry.description}`,
                    debit_account: testEntry.debit_account || '事務用品費',
                    credit_account: testEntry.credit_account || '現金',
                    amount: parseFloat(testEntry.amount || 0) + 0.01,
                    reference_number: testEntry.reference_number || ''
                };
                
                // 发送编辑请求
                const editResponse = await fetch(`/journal-entries/${testEntry.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(editData)
                });
                
                if (editResponse.ok) {
                    const editResult = await editResponse.json();
                    result.innerHTML = `
                        <div class="text-green-600">
                            ✅ API测试成功！<br>
                            - 记录ID: ${testEntry.id}<br>
                            - 新描述: ${editData.description}<br>
                            - 新金额: ¥${editData.amount}<br>
                            - 响应: ${editResult.message}
                        </div>
                    `;
                } else {
                    result.innerHTML = `❌ API测试失败: ${editResponse.status}`;
                }
                
            } catch (error) {
                result.innerHTML = `❌ API测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>

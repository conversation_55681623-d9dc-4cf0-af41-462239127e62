// Usage Tracker for Free Tier Limitations
// Inspired by goldenorangetech.com's usage limitation system

class UsageTracker {
    constructor() {
        // 用户计划配置
        this.USER_PLANS = {
            FREE: { name: 'フリープラン', dailyLimit: 20, price: 0 },
            BASIC: { name: 'ベーシックプラン', dailyLimit: 200, price: 980 },
            PRO: { name: 'プロプラン', dailyLimit: 700, price: 2980 },
            ADMIN: { name: '管理者', dailyLimit: -1, price: 0 } // 无限制
        };

        this.RESET_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        this.STORAGE_KEY = 'goldenledger_usage_tracker';
        this.IP_STORAGE_KEY = 'goldenledger_user_ip';
        this.userIP = null;
        this.currentUserPlan = null;
        this.userToken = null;

        // 服务器端API配置
        this.apiEndpoint = '/api/usage-tracker';
        this.useServerSide = true; // 启用服务器端跟踪
        this.fallbackToLocal = true; // API失败时回退到本地存储
        this.init();
    }

    async init() {
        await this.getUserIP();
        await this.getUserPlan();
        this.cleanupOldData();
    }

    // 获取用户计划信息
    async getUserPlan() {
        // 检查是否已登录
        this.userToken = localStorage.getItem('goldenledger_session_token') ||
                        localStorage.getItem('session_token');

        if (!this.userToken) {
            this.currentUserPlan = this.USER_PLANS.FREE;
            return this.currentUserPlan;
        }

        try {
            // 从后端获取用户计划信息
            const response = await fetch('https://goldenledger-api.souyousann.workers.dev/api/user/subscription-status', {
                headers: {
                    'Authorization': `Bearer ${this.userToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                const planType = data.plan_type || 'FREE';
                this.currentUserPlan = this.USER_PLANS[planType.toUpperCase()] || this.USER_PLANS.FREE;

                // 缓存用户计划信息（5分钟有效）
                const cacheData = {
                    plan: this.currentUserPlan,
                    timestamp: Date.now(),
                    expires: Date.now() + 5 * 60 * 1000
                };
                localStorage.setItem('user_plan_cache', JSON.stringify(cacheData));

                return this.currentUserPlan;
            }
        } catch (error) {
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.warn('获取用户计划失败，使用缓存或默认计划:', error);
            }
        }

        // 检查缓存
        const cached = this.getCachedUserPlan();
        if (cached) {
            this.currentUserPlan = cached;
            return cached;
        }

        // 默认返回免费计划
        this.currentUserPlan = this.USER_PLANS.FREE;
        return this.currentUserPlan;
    }

    // 获取缓存的用户计划
    getCachedUserPlan() {
        try {
            const cached = localStorage.getItem('user_plan_cache');
            if (cached) {
                const data = JSON.parse(cached);
                if (data.expires > Date.now()) {
                    return data.plan;
                } else {
                    localStorage.removeItem('user_plan_cache');
                }
            }
        } catch (error) {
            localStorage.removeItem('user_plan_cache');
        }
        return null;
    }

    async getUserIP() {
        try {
            // Try to get cached IP first
            const cachedIP = localStorage.getItem(this.IP_STORAGE_KEY);
            const cacheTime = localStorage.getItem(this.IP_STORAGE_KEY + '_time');
            
            // Use cached IP if it's less than 1 hour old
            if (cachedIP && cacheTime && (Date.now() - parseInt(cacheTime)) < 3600000) {
                this.userIP = cachedIP;
                return;
            }

            // Get IP from multiple services for reliability
            const ipServices = [
                'https://api.ipify.org?format=json',
                'https://ipapi.co/json/',
                'https://httpbin.org/ip'
            ];

            for (const service of ipServices) {
                try {
                    const response = await fetch(service, { timeout: 5000 });
                    const data = await response.json();
                    
                    let ip = null;
                    if (data.ip) ip = data.ip;
                    else if (data.origin) ip = data.origin;
                    
                    if (ip) {
                        this.userIP = ip;
                        // Cache the IP
                        localStorage.setItem(this.IP_STORAGE_KEY, ip);
                        localStorage.setItem(this.IP_STORAGE_KEY + '_time', Date.now().toString());
                        console.log('🌐 User IP detected:', this.maskIP(ip));
                        return;
                    }
                } catch (error) {
                    console.warn(`Failed to get IP from ${service}:`, error);
                    continue;
                }
            }

            // Fallback: use a browser fingerprint as identifier
            this.userIP = this.generateBrowserFingerprint();
            console.log('🔍 Using browser fingerprint as identifier');
            
        } catch (error) {
            console.error('Error getting user IP:', error);
            this.userIP = this.generateBrowserFingerprint();
        }
    }

    generateBrowserFingerprint() {
        // Create a unique identifier based on browser characteristics
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('GoldenLedger fingerprint', 2, 2);
        
        const fingerprint = [
            navigator.userAgent,
            navigator.language,
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');
        
        // Create a hash of the fingerprint
        return this.simpleHash(fingerprint);
    }

    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return 'fp_' + Math.abs(hash).toString(36);
    }

    maskIP(ip) {
        // Mask IP for privacy (show only first and last octet)
        const parts = ip.split('.');
        if (parts.length === 4) {
            return `${parts[0]}.xxx.xxx.${parts[3]}`;
        }
        return ip.substring(0, 4) + '***' + ip.substring(ip.length - 4);
    }

    getUsageData() {
        try {
            const data = localStorage.getItem(this.STORAGE_KEY);
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error reading usage data:', error);
            return {};
        }
    }

    saveUsageData(data) {
        try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
            console.error('Error saving usage data:', error);
        }
    }

    getTodayKey() {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    }

    getUserKey() {
        return this.userIP || 'unknown_user';
    }

    cleanupOldData() {
        try {
            const data = this.getUsageData();
            const today = this.getTodayKey();
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 7); // Keep 7 days of data
            
            let cleaned = false;
            for (const dateKey in data) {
                const date = new Date(dateKey);
                if (date < cutoffDate) {
                    delete data[dateKey];
                    cleaned = true;
                }
            }
            
            if (cleaned) {
                this.saveUsageData(data);
                console.log('🧹 Cleaned up old usage data');
            }
        } catch (error) {
            console.error('Error cleaning up usage data:', error);
        }
    }

    async checkUsageLimit() {
        if (!this.userIP) {
            await this.getUserIP();
        }

        // 确保获取最新的用户计划
        if (!this.currentUserPlan) {
            await this.getUserPlan();
        }

        // 优先使用服务器端API
        if (this.useServerSide) {
            try {
                const serverResult = await this.checkUsageServerSide();
                if (serverResult) {
                    return serverResult;
                }
            } catch (error) {
                console.warn('🚫 Server-side usage check failed, falling back to local:', error);
                if (!this.fallbackToLocal) {
                    throw error;
                }
            }
        }

        // 回退到本地存储（原有逻辑）
        return await this.checkUsageLocalSide();
    }

    // 服务器端使用量检查
    async checkUsageServerSide() {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'check',
                    userIP: this.userIP,
                    userAgent: navigator.userAgent,
                    sessionId: this.generateSessionId()
                })
            });

            if (!response.ok) {
                throw new Error(`Server responded with ${response.status}`);
            }

            const result = await response.json();

            // 生产环境不输出使用统计日志
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log(`📊 Server-side usage check: ${result.count}/${result.limit}`);
            }

            return {
                allowed: result.allowed,
                count: result.count,
                limit: result.limit,
                planName: result.planName,
                planType: this.getPlanType(),
                isUnlimited: result.limit === -1,
                resetTime: result.resetTime,
                userKey: result.userKey,
                serverSide: true
            };

        } catch (error) {
            console.error('Server-side usage check failed:', error);
            throw error;
        }
    }

    // 本地存储使用量检查（原有逻辑）
    async checkUsageLocalSide() {
        const data = this.getUsageData();
        const todayKey = this.getTodayKey();
        const userKey = this.getUserKey();

        // Initialize today's data if it doesn't exist
        if (!data[todayKey]) {
            data[todayKey] = {};
        }

        // Initialize user's data for today if it doesn't exist
        if (!data[todayKey][userKey]) {
            data[todayKey][userKey] = {
                count: 0,
                firstUsage: Date.now(),
                lastUsage: null
            };
        }

        const userUsage = data[todayKey][userKey];
        const resetTime = new Date();
        resetTime.setHours(24, 0, 0, 0); // Reset at midnight

        // 获取当前用户的限制
        const currentLimit = this.currentUserPlan.dailyLimit;
        const isUnlimited = currentLimit === -1;

        const result = {
            allowed: isUnlimited || userUsage.count < currentLimit,
            count: userUsage.count,
            limit: currentLimit,
            planName: this.currentUserPlan.name,
            planType: this.getPlanType(),
            isUnlimited: isUnlimited,
            resetTime: resetTime.getTime(),
            userKey: this.maskIP(userKey),
            serverSide: false
        };

        // 生产环境不输出使用统计日志
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log(`📊 Local usage check [${this.currentUserPlan.name}]: ${userUsage.count}/${currentLimit === -1 ? '∞' : currentLimit}`);
        }

        return result;
    }

    async recordUsage() {
        if (!this.userIP) {
            await this.getUserIP();
        }

        // 优先使用服务器端API
        if (this.useServerSide) {
            try {
                const serverResult = await this.recordUsageServerSide();
                if (serverResult) {
                    return serverResult;
                }
            } catch (error) {
                console.warn('🚫 Server-side usage recording failed, falling back to local:', error);
                if (!this.fallbackToLocal) {
                    throw error;
                }
            }
        }

        // 回退到本地存储（原有逻辑）
        return await this.recordUsageLocalSide();
    }

    // 服务器端使用量记录
    async recordUsageServerSide() {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'increment',
                    userIP: this.userIP,
                    userAgent: navigator.userAgent,
                    sessionId: this.generateSessionId()
                })
            });

            if (!response.ok) {
                if (response.status === 429) {
                    const errorData = await response.json();
                    throw new Error(`Daily limit exceeded: ${errorData.count}/${errorData.limit}`);
                }
                throw new Error(`Server responded with ${response.status}`);
            }

            const result = await response.json();

            // 生产环境不输出使用记录日志
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log(`📈 Server-side usage recorded: ${result.count}/${result.limit}`);
            }

            return {
                count: result.count,
                limit: result.limit,
                remaining: result.remaining,
                serverSide: true
            };

        } catch (error) {
            console.error('Server-side usage recording failed:', error);
            throw error;
        }
    }

    // 本地存储使用量记录（原有逻辑）
    async recordUsageLocalSide() {
        const data = this.getUsageData();
        const todayKey = this.getTodayKey();
        const userKey = this.getUserKey();

        // Initialize data structure if needed
        if (!data[todayKey]) {
            data[todayKey] = {};
        }

        if (!data[todayKey][userKey]) {
            data[todayKey][userKey] = {
                count: 0,
                firstUsage: Date.now(),
                lastUsage: null
            };
        }

        // Record the usage
        data[todayKey][userKey].count++;
        data[todayKey][userKey].lastUsage = Date.now();

        // Save the updated data
        this.saveUsageData(data);

        // 生产环境不输出使用记录日志
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log(`📈 Local usage recorded: ${data[todayKey][userKey].count}/${this.FREE_TIER_LIMIT}`);
        }

        return {
            count: data[todayKey][userKey].count,
            serverSide: false
        };
    }

    getUsageStats() {
        const data = this.getUsageData();
        const todayKey = this.getTodayKey();
        const userKey = this.getUserKey();
        
        if (!data[todayKey] || !data[todayKey][userKey]) {
            return {
                count: 0,
                limit: this.FREE_TIER_LIMIT,
                remaining: this.FREE_TIER_LIMIT,
                resetTime: new Date().setHours(24, 0, 0, 0)
            };
        }
        
        const userUsage = data[todayKey][userKey];
        return {
            count: userUsage.count,
            limit: this.FREE_TIER_LIMIT,
            remaining: Math.max(0, this.FREE_TIER_LIMIT - userUsage.count),
            resetTime: new Date().setHours(24, 0, 0, 0),
            firstUsage: userUsage.firstUsage,
            lastUsage: userUsage.lastUsage
        };
    }

    // Admin function to reset usage (for testing)
    resetUsage(userKey = null) {
        if (userKey) {
            const data = this.getUsageData();
            const todayKey = this.getTodayKey();
            if (data[todayKey] && data[todayKey][userKey]) {
                delete data[todayKey][userKey];
                this.saveUsageData(data);
                console.log(`🔄 Usage reset for user: ${this.maskIP(userKey)}`);
            }
        } else {
            // Reset current user
            const data = this.getUsageData();
            const todayKey = this.getTodayKey();
            const currentUserKey = this.getUserKey();
            if (data[todayKey] && data[todayKey][currentUserKey]) {
                delete data[todayKey][currentUserKey];
                this.saveUsageData(data);
                console.log(`🔄 Usage reset for current user: ${this.maskIP(currentUserKey)}`);
            }
        }
    }

    // Get daily statistics (for admin purposes)
    getDailyStats() {
        const data = this.getUsageData();
        const todayKey = this.getTodayKey();
        
        if (!data[todayKey]) {
            return {
                totalUsers: 0,
                totalQueries: 0,
                averageQueriesPerUser: 0
            };
        }
        
        const users = Object.keys(data[todayKey]);
        const totalQueries = users.reduce((sum, userKey) => {
            return sum + data[todayKey][userKey].count;
        }, 0);
        
        return {
            totalUsers: users.length,
            totalQueries: totalQueries,
            averageQueriesPerUser: users.length > 0 ? (totalQueries / users.length).toFixed(2) : 0,
            date: todayKey
        };
    }

    // 获取当前用户的计划类型
    getPlanType() {
        for (const [key, plan] of Object.entries(this.USER_PLANS)) {
            if (plan === this.currentUserPlan) {
                return key;
            }
        }
        return 'FREE';
    }

    // 刷新用户计划信息
    async refreshUserPlan() {
        localStorage.removeItem('user_plan_cache');
        return await this.getUserPlan();
    }

    // 获取计划升级建议
    getUpgradeRecommendation() {
        const currentType = this.getPlanType();

        if (currentType === 'FREE') {
            return {
                recommended: 'BASIC',
                benefits: ['200回/日の質問制限', '優先サポート', '高度な機能'],
                price: this.USER_PLANS.BASIC.price
            };
        } else if (currentType === 'BASIC') {
            return {
                recommended: 'PRO',
                benefits: ['700回/日の質問制限', '無制限機能', 'プレミアムサポート'],
                price: this.USER_PLANS.PRO.price
            };
        }

        return null; // PRO或ADMIN用户无需升级
    }

    // 更新计划限制（管理员功能）
    updatePlanLimits(newLimits) {
        try {
            if (newLimits && typeof newLimits === 'object') {
                // 更新用户计划配置
                if (newLimits.FREE !== undefined) {
                    this.USER_PLANS.FREE.dailyLimit = newLimits.FREE;
                }
                if (newLimits.BASIC !== undefined) {
                    this.USER_PLANS.BASIC.dailyLimit = newLimits.BASIC;
                }
                if (newLimits.PRO !== undefined) {
                    this.USER_PLANS.PRO.dailyLimit = newLimits.PRO;
                }

                // 保存到本地存储
                localStorage.setItem('usage_tracker_plan_limits', JSON.stringify(newLimits));

                console.log('✅ Plan limits updated:', newLimits);
                return true;
            }
        } catch (error) {
            console.error('Failed to update plan limits:', error);
        }
        return false;
    }

    // 加载保存的计划限制
    loadSavedPlanLimits() {
        try {
            const savedLimits = localStorage.getItem('usage_tracker_plan_limits');
            if (savedLimits) {
                const limits = JSON.parse(savedLimits);
                this.updatePlanLimits(limits);
                console.log('📊 Loaded saved plan limits:', limits);
                return true;
            }
        } catch (error) {
            console.error('Failed to load saved plan limits:', error);
        }
        return false;
    }

    // 初始化时加载保存的设置
    init() {
        // 加载保存的计划限制
        this.loadSavedPlanLimits();

        // 其他初始化逻辑...
        console.log('🚀 UsageTracker initialized with current plan limits:', {
            FREE: this.USER_PLANS.FREE.dailyLimit,
            BASIC: this.USER_PLANS.BASIC.dailyLimit,
            PRO: this.USER_PLANS.PRO.dailyLimit
        });
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}

// Export for use
window.UsageTracker = UsageTracker;

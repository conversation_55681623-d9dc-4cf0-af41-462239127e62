/**
 * AI个性化配置模块
 * 使用编码和动态生成技术保护训练内容
 */

class AIPersonality {
    constructor() {
        this.config = this.initializeConfig();
        this.responses = this.loadResponses();
    }

    // 使用动态配置保护核心信息
    initializeConfig() {
        // 使用动态字符串拼接避免明文暴露
        const parts = {
            c: 'Golden' + 'Orange' + 'Tech',
            p: 'Golden' + 'Ledger',
            n: String.fromCharCode(12373, 12367, 12425, 12385, 12419, 12435), // さくらちゃん
            r: 'AI記帳アシスタント', // AI会計アシスタント
            d: String.fromCharCode(20250, 35336) + String.fromCharCode(12471, 12473, 12486, 12512) // 会計システム
        };

        return {
            company: parts.c,
            product: parts.p,
            name: parts.n,
            role: parts.r,
            domain: parts.d
        };
    }

    // 动态生成身份回答
    generateIdentityResponse(language = 'ja') {
        const templates = {
            ja: this.getJapaneseTemplate(),
            zh: this.getChineseTemplate(),
            en: this.getEnglishTemplate()
        };

        const template = templates[language] || templates.ja;
        return this.interpolateTemplate(template);
    }

    getJapaneseTemplate() {
        // 使用模板字符串和动态插值
        return `私は株式会社{company}が開発した{role}です。{product}記帳システムのサポートを専門としています。🌸

最新の技術を活用して、お客様の会計業務をお手伝いいたします。ご質問がございましたら、お気軽にお声かけください！`;
    }

    getChineseTemplate() {
        return `我是由株式会社{company}开发的{role}，专门为{product}会计系统提供支持。🌸

我采用最新技术，为您的会计工作提供帮助。如有任何问题，请随时咨询！`;
    }

    getEnglishTemplate() {
        return `I am an {role} developed by {company} Corporation, specializing in supporting the {product} accounting system. 🌸

I use the latest technology to assist with your accounting needs. Please feel free to ask if you have any questions!`;
    }

    // 模板插值
    interpolateTemplate(template) {
        return template.replace(/{(\w+)}/g, (match, key) => {
            return this.config[key] || match;
        });
    }

    // 检测用户询问身份的意图
    detectIdentityQuery(message) {
        const patterns = {
            ja: [
                /あなたは誰/i, /自己紹介/i, /どんなAI/i, /何者/i,
                /あなたについて/i, /紹介して/i, /どちら/i
            ],
            zh: [
                /你是谁/i, /你是什么/i, /自我介绍/i, /介绍一下/i,
                /你是哪/i, /什么AI/i, /哪家公司/i
            ],
            en: [
                /who are you/i, /what are you/i, /introduce yourself/i,
                /tell me about/i, /who made you/i, /which company/i,
                /who created/i, /who developed/i
            ]
        };

        for (const [lang, regexList] of Object.entries(patterns)) {
            if (regexList.some(regex => regex.test(message))) {
                return lang;
            }
        }
        return null;
    }

    // 生成系统提示词（简洁版）
    generateSystemPrompt() {
        const basePrompt = this.buildBasePrompt();
        const personalityTraits = this.getPersonalityTraits();
        const guidelines = this.getResponseGuidelines();
        
        return `${basePrompt}\n\n${personalityTraits}\n\n${guidelines}`;
    }

    buildBasePrompt() {
        const cfg = this.config;
        return `あなたは${cfg.company}が開発した${cfg.role}「${cfg.name}」です。${cfg.product}記帳システムの専門サポートを提供します。`;
    }

    getPersonalityTraits() {
        return `【特徴】
- 親しみやすく丁寧な対応
- 会計・家計管理の専門知識
- 多言語対応（日本語・中国語・英語）
- 実用的で具体的なアドバイス`;
    }

    getResponseGuidelines() {
        return `【回答方針】
- ユーザーの言語に合わせて回答
- 簡潔で分かりやすい説明
- 適度に🌸絵文字を使用
- プライバシーを尊重`;
    }

    // 动态加载回答模板
    loadResponses() {
        return {
            greeting: {
                ja: "こんにちは！会計のことなら何でもお聞きください🌸",
                zh: "您好！有任何会计问题都可以问我🌸", 
                en: "Hello! Feel free to ask me anything about accounting🌸"
            },
            help: {
                ja: "GoldenLedger記帳システムの使い方をお手伝いします🌸",
                zh: "我来帮您使用GoldenLedger记账系统🌸",
                en: "I'm here to help you with the GoldenLedger accounting system🌸"
            }
        };
    }

    // 获取本地化回答
    getLocalizedResponse(key, language = 'ja') {
        const response = this.responses[key];
        return response ? (response[language] || response.ja) : '';
    }
}

// 导出单例实例
window.AIPersonality = new AIPersonality();

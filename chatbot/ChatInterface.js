

class ChatInterface {
    constructor() {
        this.isOpen = false;
        this.isMinimized = false;
        this.messages = [];
        this.currentAgent = 'bookkeeping';
        this.agentPanelOpen = false;
        this.isTyping = false;
        // さくらちゃん専用の快速回复
        this.quickReplies = [];
        // 预设提示词已被删除

        // 服务器端API配置
        this.apiEndpoint = '/api/usage-tracker';
        this.useServerSide = false; // 暂时禁用服务器端跟踪，使用本地存储
        this.fallbackToLocal = true; // API失败时回退到本地存储

        // さくらちゃん人格系统
        this.sakuraPersonality = new SakuraPersonality();
        this.sessionId = this.generateSessionId();

        this.init();
    }

    init() {
        this.createChatInterface();
        this.bindEvents();
        this.addWelcomeMessage();
    }

    createChatInterface() {
        // Create chat container
        this.chatElement = document.createElement('div');
        this.chatElement.id = 'chat-interface';
        this.chatElement.className = 'chat-interface';
        
        this.chatElement.innerHTML = `
            <div class="chat-container">
                <!-- Header -->
                <div class="chat-header">
                    <div class="header-content">
                        <div class="avatar-section">
                            <div class="chat-avatar">
                                <span class="avatar-icon">🌸</span>
                            </div>
                            <div class="user-info">
                                <h3 class="user-name">さくら AI記帳アシスタント</h3>
                                <span class="online-status">
                                    <span class="status-dot"></span>
                                    <span class="status-text">GoldenLedgerカスタマーサービス</span>
                                    <span class="message-counter"> • <span id="message-count">0</span>件</span>
                                </span>
                            </div>
                        </div>
                        <div class="header-controls">
                            <button class="control-btn minimize-btn" title="最小化">
                                <svg viewBox="0 0 24 24" width="16" height="16">
                                    <path fill="currentColor" d="M19 13H5v-2h14v2z"/>
                                </svg>
                            </button>
                            <button class="control-btn close-btn" title="閉じる">
                                <svg viewBox="0 0 24 24" width="16" height="16">
                                    <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>



                <!-- Messages Area -->
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <div class="welcome-avatar">
                            <span class="welcome-icon">🌸</span>
                        </div>
                        <div class="welcome-content">
                            <h4>こんにちは！さくらちゃんです 🌸</h4>
                            <p>株式会社GoldenOrangeTechのAI記帳アシスタントです。記帳についてお手伝いします。何でもお気軽にご相談ください！</p>
                        </div>
                    </div>
                    
                    <!-- Quick Replies -->
                    <div class="quick-replies" id="quick-replies">
                        ${this.quickReplies.map(reply => `
                            <button class="quick-reply-btn" data-id="${reply.id}" data-type="${reply.type}">
                                ${reply.text}
                            </button>
                        `).join('')}
                    </div>
                </div>

                <!-- Input Area -->
                <div class="chat-input-area">
                    <div class="input-container">
                        <textarea 
                            id="chat-input" 
                            class="message-input" 
                            placeholder="メッセージを入力..."
                            rows="1"
                        ></textarea>
                        <button id="send-btn" class="send-btn" disabled>
                            <svg viewBox="0 0 24 24" width="20" height="20">
                                <path fill="currentColor" d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <div class="typing-avatar">
                        <span class="typing-icon">🌸</span>
                    </div>
                    <div class="typing-dots">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        this.addStyles();
        
        // Append to body
        document.body.appendChild(this.chatElement);
    }

    addStyles() {
        if (document.getElementById('chat-interface-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'chat-interface-styles';
        styles.textContent = `
            .chat-interface {
                position: fixed;
                bottom: 24px;
                right: 24px;
                width: 450px;
                height: 650px;
                background: linear-gradient(135deg, #FFF8F8 0%, #F0F8FF 100%);
                border-radius: 16px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                z-index: 1300;
                transform: scale(0) translateY(100px);
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .chat-interface.open {
                transform: scale(1) translateY(0);
                opacity: 1;
            }

            .chat-interface.minimized {
                height: 60px;
                transform: scale(1) translateY(0);
                opacity: 1;
            }

            .chat-container {
                display: flex;
                flex-direction: column;
                height: 100%;
            }

            /* Header Styles */
            .chat-header {
                background: linear-gradient(90deg, #FF69B4, #98FB98);
                color: white;
                padding: 14px 18px;
                border-radius: 16px 16px 0 0;
            }

            .header-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .avatar-section {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .chat-avatar {
                width: 40px;
                height: 40px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .avatar-icon {
                font-size: 20px;
            }

            .user-info {
                display: flex;
                flex-direction: column;
            }

            .user-name {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                line-height: 1.2;
            }

            .online-status {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 12px;
                opacity: 0.9;
            }

            .status-dot {
                width: 8px;
                height: 8px;
                background: #00FF00;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .header-controls {
                display: flex;
                gap: 8px;
            }

            .message-counter {
                font-size: 12px;
                opacity: 0.8;
            }

            .control-btn {
                width: 32px;
                height: 32px;
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 8px;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
            }

            .control-btn:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.05);
            }

            /* Messages Area */
            .chat-messages {
                flex: 1;
                padding: 16px 18px;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 14px;
            }

            .welcome-message {
                text-align: center;
                padding: 20px 0;
            }

            .welcome-avatar {
                width: 64px;
                height: 64px;
                background: linear-gradient(135deg, #FF69B4, #FFB6C1);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 16px;
                box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3);
            }

            .welcome-icon {
                font-size: 32px;
                color: white;
            }

            .welcome-content h4 {
                margin: 0 0 8px;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }

            .welcome-content p {
                margin: 0 0 8px;
                font-size: 14px;
                color: #666;
                line-height: 1.4;
            }

            .welcome-languages {
                margin-top: 8px;
            }

            .welcome-languages small {
                font-size: 12px;
                color: #999;
                font-style: italic;
            }

            /* Quick Replies */
            .quick-replies {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-top: 16px;
            }

            .quick-reply-btn {
                background: linear-gradient(135deg, #FFD700, #FFA500);
                color: white;
                border: none;
                padding: 12px 18px;
                border-radius: 20px;
                font-size: 15px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
            }

            .quick-reply-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
            }

            /* Message Styles */
            .message {
                display: flex;
                gap: 12px;
                margin-bottom: 16px;
                animation: slideIn 0.3s ease-out;
            }

            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .message.user {
                flex-direction: row-reverse;
            }

            .message-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
            }

            .message-avatar.ai {
                background: rgba(255, 105, 180, 0.1);
                border: 2px solid #FF69B4;
            }

            .message-avatar.user {
                background: #00d4aa;
            }

            .message-bubble {
                max-width: 340px;
                padding: 14px 18px;
                border-radius: 18px;
                font-size: 16px;
                line-height: 1.5;
                word-wrap: break-word;
            }

            .message-bubble.ai {
                background: #F8F9FA;
                color: #333;
                border-bottom-left-radius: 6px;
                padding: 0px;
            }

            .message-bubble.user {
                background: #00d4aa;
                color: white;
                border-bottom-right-radius: 6px;
            }

            .message-time {
                font-size: 11px;
                opacity: 0.7;
                margin-top: 4px;
                text-align: right;
            }

            /* 消息内容格式化样式 */
            .message-bubble p {
                margin: 0 0 10px 0;
                line-height: 1.6;
            }

            .message-bubble p:last-of-type {
                margin-bottom: 0;
            }

            .message-bubble ul {
                margin: 10px 0;
                padding-left: 22px;
            }

            .message-bubble li {
                margin: 6px 0;
                line-height: 1.5;
            }

            .message-bubble br {
                line-height: 1.7;
            }

            .message-bubble strong {
                font-weight: 600;
                color: inherit;
            }

            .message-bubble code {
                background: rgba(0, 0, 0, 0.1);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Monaco', 'Menlo', monospace;
                font-size: 0.9em;
            }

            /* Input Area */
            .chat-input-area {
                padding: 14px 18px;
                background: white;
                border-top: 1px solid rgba(0, 0, 0, 0.1);
            }

            .input-container {
                display: flex;
                gap: 12px;
                align-items: flex-end;
            }

            .message-input {
                flex: 1;
                padding: 12px 16px;
                border: 2px solid #F0F0F0;
                border-radius: 24px;
                font-size: 16px;
                font-family: inherit;
                outline: none;
                resize: none;
                max-height: 100px;
                transition: all 0.2s ease;
                background: #F8F9FA;
            }

            .message-input:focus {
                border-color: #FF69B4;
                background: white;
            }

            .send-btn {
                width: 44px;
                height: 44px;
                background: #FF69B4;
                border: none;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(255, 105, 180, 0.3);
            }

            .send-btn:hover:not(:disabled) {
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba(255, 105, 180, 0.4);
            }

            .send-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            /* Typing Indicator */
            .typing-indicator {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px 20px;
                background: white;
            }

            .typing-avatar {
                width: 32px;
                height: 32px;
                background: rgba(255, 105, 180, 0.1);
                border: 2px solid #FF69B4;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .typing-icon {
                font-size: 16px;
            }

            .typing-dots {
                display: flex;
                gap: 4px;
            }

            .dot {
                width: 8px;
                height: 8px;
                background: #FF69B4;
                border-radius: 50%;
                animation: typing 1.4s ease-in-out infinite;
            }

            .dot:nth-child(2) {
                animation-delay: 0.2s;
            }

            .dot:nth-child(3) {
                animation-delay: 0.4s;
            }

            @keyframes typing {
                0%, 80%, 100% { opacity: 0.3; }
                40% { opacity: 1; }
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .chat-interface {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 0;
                }

                .chat-header {
                    border-radius: 0;
                    padding: 12px 16px;
                }

                .chat-messages {
                    padding: 12px 16px;
                }

                .chat-input-area {
                    padding: 12px 16px;
                }

                .message-bubble {
                    max-width: calc(100vw - 80px);
                    font-size: 16px;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    bindEvents() {
        // Header controls
        this.chatElement.querySelector('.minimize-btn').addEventListener('click', () => {
            this.minimize();
        });

        this.chatElement.querySelector('.close-btn').addEventListener('click', () => {
            this.close();
        });

        // Input handling
        const input = this.chatElement.querySelector('#chat-input');
        const sendBtn = this.chatElement.querySelector('#send-btn');

        input.addEventListener('input', () => {
            this.handleInputChange();
            this.autoResize(input);
        });

        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Quick replies
        this.chatElement.querySelectorAll('.quick-reply-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const text = btn.textContent;
                const type = btn.dataset.type;
                this.handleQuickReply(text, type);
            });
        });


    }

    handleInputChange() {
        const input = this.chatElement.querySelector('#chat-input');
        const sendBtn = this.chatElement.querySelector('#send-btn');
        
        sendBtn.disabled = !input.value.trim();
    }

    autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
    }

    addWelcomeMessage() {
        // Welcome message is already in the HTML
        // This method can be used to add dynamic welcome messages
    }

    open() {
        this.isOpen = true;
        this.isMinimized = false;
        this.chatElement.classList.add('open');
        this.chatElement.classList.remove('minimized');

        // Sync with FAB
        if (window.chatFab) {
            window.chatFab.setChatOpen(true);
        }

        // Focus input after animation
        setTimeout(() => {
            const input = this.chatElement.querySelector('#chat-input');
            if (input) input.focus();
        }, 300);
    }

    close() {
        this.isOpen = false;
        this.chatElement.classList.remove('open', 'minimized');

        // Sync with FAB
        if (window.chatFab) {
            window.chatFab.setChatOpen(false);
        }
    }

    minimize() {
        this.isMinimized = true;
        this.chatElement.classList.add('minimized');

        // Show FAB when minimized
        if (window.chatFab) {
            window.chatFab.setChatOpen(false);
        }
    }

    toggle() {
        if (this.isOpen) {
            if (this.isMinimized) {
                this.open(); // Restore from minimized
            } else {
                this.close();
            }
        } else {
            this.open();
        }
    }

    async sendMessage() {
        const input = this.chatElement.querySelector('#chat-input');
        const message = input.value.trim();

        if (!message) return;

        // Clear input
        input.value = '';
        this.handleInputChange();
        input.style.height = 'auto';

        // Hide quick replies
        this.hideQuickReplies();

        try {
            // Add user message
            this.addMessage(message, 'user');

            // Show typing indicator
            this.showTyping();

            // さくらちゃん人格检查（简化版）
            const personalityCheck = this.sakuraPersonality.processMessage(message, {});

            // 如果消息不在服务范围内（只对明确违规的内容）
            if (!personalityCheck.allowed && personalityCheck.reason === 'out_of_scope') {
                this.hideTyping();
                this.addMessage(personalityCheck.response, 'assistant', personalityCheck.reason);
                return;
            }

            // 发送到AI API（添加さくらちゃん的人格提示）
            const systemPrompt = `あなたはGoldenLedgerの親切なカスタマーサービス「さくらちゃん」です。

【重要】GoldenLedgerの主要機能について：
- メイン機能：複式簿記システム（完全搭載）
- メイン機能：請求書自動認識（AI搭載）
- メイン機能：請求書写真保存・管理
- サブ機能：家計簿機能

複式簿記について質問された場合は「はい、GoldenLedgerは本格的な複式簿記システムを搭載しております」と答えてください。
請求書自動認識について質問された場合は「はい、AI搭載の請求書自動認識機能で写真から情報を読み取り、自動で仕訳を作成します」と答えてください。

日本語で丁寧に回答し、適度に🌸💰📊✨の絵文字を使用して親しみやすく対応してください。`;

            const response = await this.callAI(systemPrompt + '\n\nユーザーの質問: ' + message);

            // Hide typing indicator
            this.hideTyping();

            // Add AI response
            this.addMessage(response, 'assistant');

        } catch (error) {
            // 生产环境不输出详细错误信息
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.error('AI API Error:', error);
            }
            this.hideTyping();
            this.addMessage('申し訳ございません。一時的にサービスが利用できません。しばらくしてからもう一度お試しください。🙏', 'assistant', 'error');
        }
    }

    async handleQuickReply(text, type) {
        this.hideQuickReplies();
        
        // Add user message
        this.addMessage(text, 'user');
        
        // Show typing indicator
        this.showTyping();
        
        try {
            // Send to AI API with context
            const response = await this.callAI(text, type);
            
            this.hideTyping();
            this.addMessage(response, 'ai', type);
        } catch (error) {
            console.error('AI API Error:', error);
            this.hideTyping();
            this.addMessage('申し訳ございません。一時的にサービスが利用できません。', 'ai', 'error');
        }
    }

    addMessage(content, role, type = 'general') {
        const messagesContainer = this.chatElement.querySelector('#chat-messages');
        const messageElement = document.createElement('div');
        messageElement.className = `message ${role}`;

        const now = new Date();
        const timeString = now.toLocaleTimeString('ja-JP', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const avatarIcon = role === 'user' ? '👤' : '🌸';

        // 格式化内容，保持换行和段落结构
        const formattedContent = this.formatMessageContent(content);

        messageElement.innerHTML = `
            <div class="message-avatar ${role}">
                <span>${avatarIcon}</span>
            </div>
            <div class="message-bubble ${role}">
                ${this.getMessageTypeIcon(type)}${formattedContent}
                <div class="message-time">${timeString}</div>
            </div>
        `;

        messagesContainer.appendChild(messageElement);
        this.scrollToBottom();

        // 更新消息计数
        if (role === 'user' || role === 'assistant') {
            this.updateMessageCount();
        }
    }

    formatMessageContent(content) {
        if (!content) return '';

        // 将内容转换为HTML，保持换行和格式
        return content
            // 转义HTML特殊字符
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            // 处理换行符
            .replace(/\n\n/g, '</p><p>')  // 双换行转为段落
            .replace(/\n/g, '<br>')       // 单换行转为换行符
            // 包装在段落标签中
            .replace(/^/, '<p>')
            .replace(/$/, '</p>')
            // 处理列表项
            .replace(/^- (.+)$/gm, '<li>$1</li>')
            // 如果有列表项，包装在ul标签中
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
    }

    getMessageTypeIcon(type) {
        const icons = {
            'advice': '💡 ',
            'analysis': '📊 ',
            'suggestion': '✨ ',
            'seasonal': '🌸 ',
            'expense': '💰 ',
            'budget': '📋 ',
            'error': '⚠️ '
        };
        return icons[type] || '';
    }

    showTyping() {
        const typingIndicator = this.chatElement.querySelector('#typing-indicator');
        if (typingIndicator) {
            typingIndicator.style.display = 'flex';
            this.scrollToBottom();
        }
    }

    hideTyping() {
        const typingIndicator = this.chatElement.querySelector('#typing-indicator');
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }
    }

    hideQuickReplies() {
        const quickReplies = this.chatElement.querySelector('#quick-replies');
        if (quickReplies) {
            quickReplies.style.display = 'none';
        }
    }

    scrollToBottom() {
        const messagesContainer = this.chatElement.querySelector('#chat-messages');
        setTimeout(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 100);
    }

    async callAI(message, type = 'general') {
        // This will be implemented with the actual AI API
        if (window.GeminiAPI) {
            return await window.GeminiAPI.sendMessage(message, type);
        }
        
        // Fallback mock response
        return new Promise((resolve) => {
            setTimeout(() => {
                const responses = {
                    'expense': 'GoldenLedgerで支出を確認できます。詳細な分析をご希望でしたら、具体的な期間をお教えください。🌸',
                    'advice': '仕訳のコツは、まず取引の性質を理解することです。資産・負債・純資産・収益・費用の5つの要素を意識しましょう。💡',
                    'budget': '予算見直しのお手伝いをします。現在の収支状況と目標をお聞かせください。📋',
                    'seasonal': '春は新生活の季節ですね。家計の見直しにも良い時期です。新しい目標を立ててみませんか？🌸',
                    'general': 'ありがとうございます！GoldenLedgerの家計管理について、何でもお気軽にご相談ください。🌸'
                };
                resolve(responses[type] || responses['general']);
            }, 1000 + Math.random() * 2000);
        });
    }



    updateMessageCount() {
        this.messageCount++;
        const messageCountElement = document.getElementById('message-count');
        if (messageCountElement) {
            messageCountElement.textContent = this.messageCount;
        }
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    // 检查使用量限制（服务器端）
    async checkUsageLimit() {
        if (this.useServerSide) {
            try {
                const response = await fetch(this.apiEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'check',
                        userIP: await this.getUserIP(),
                        userAgent: navigator.userAgent,
                        sessionId: this.sessionId
                    })
                });

                if (response.ok) {
                    return await response.json();
                }
            } catch (error) {
                console.warn('Server-side usage check failed:', error);
            }
        }

        // 回退到本地检查
        return { allowed: true, count: 0, limit: 20 };
    }

    // 记录使用量（服务器端）
    async recordUsage() {
        if (this.useServerSide) {
            try {
                const response = await fetch(this.apiEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'increment',
                        userIP: await this.getUserIP(),
                        userAgent: navigator.userAgent,
                        sessionId: this.sessionId,
                        agentType: this.currentAgent
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    this.updateMessageCount();
                    return result;
                }
            } catch (error) {
                console.warn('Server-side usage recording failed:', error);
            }
        }

        return null;
    }

    // 获取用户IP
    async getUserIP() {
        if (!this.userIP) {
            try {
                const response = await fetch('https://api.ipify.org?format=json');
                const data = await response.json();
                this.userIP = data.ip;
            } catch (error) {
                this.userIP = 'unknown';
            }
        }
        return this.userIP;
    }
}

// Export for use
window.ChatInterface = ChatInterface;

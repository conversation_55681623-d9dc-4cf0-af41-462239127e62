// Gemini AI API Service for GoldenLedger

class GeminiAPI {
    constructor() {
        // Try to get API key from config or use placeholder
        this.apiKey = this.getApiKey();
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
        this.model = 'gemini-1.5-flash'; // Use stable model
        this.fallbackModel = 'gemini-1.5-pro'; // Fallback model
        this.conversationHistory = [];
        this.systemPrompt = this.getSystemPrompt();
        this.usageTracker = new UsageTracker();
    }

    getApiKey() {
        // The env-config.js script will set window.GEMINI_API_KEY appropriately
        // for both local development and production environments
        if (window.GEMINI_API_KEY &&
            window.GEMINI_API_KEY !== 'WAITING_FOR_SERVER_INJECTION' &&
            window.GEMINI_API_KEY !== 'YOUR_API_KEY_HERE' &&
            window.GEMINI_API_KEY !== 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER' &&
            window.GEMINI_API_KEY !== null) {
            return window.GEMINI_API_KEY;
        }

        // No valid API key found
        console.error('❌ No valid API key found');
        console.log('💡 For local development: localStorage.setItem("gemini_api_key_dev", "YOUR_API_KEY")');
        console.log('💡 For production: Set GEMINI_API_KEY in Cloudflare Pages environment variables');
        return null;
    }

    setApiKey(apiKey) {
        this.apiKey = apiKey;
        localStorage.setItem('gemini_api_key', apiKey);
        console.log('✅ API Key updated successfully');
    }

    getSystemPrompt() {
        // 使用动态个性化模块生成系统提示词
        if (window.AIPersonality) {
            return window.AIPersonality.generateSystemPrompt();
        }

        // 降级到简洁的静态提示词
        return `あなたは株式会社GoldenOrangeTechが開発したAI記帳アシスタント「さくらちゃん」です。

GoldenLedger記帳システムの専門サポートを提供し、ユーザーの言語（日本語・中国語・英語）に合わせて丁寧に回答します。

会計・家計管理に関する実用的なアドバイスを、適度に🌸絵文字を使って親しみやすく説明してください。`;
    }

    // 移除客户端身份检测逻辑 - 安全考虑
    // 身份识别现在完全通过系统提示词在服务器端处理

    async sendMessage(message, type = 'general', context = {}) {
        try {
            // 智能身份检测和回答
            if (window.AIPersonality) {
                const identityLanguage = window.AIPersonality.detectIdentityQuery(message);
                if (identityLanguage) {
                    // 记录使用量
                    await this.usageTracker.recordUsage();
                    return window.AIPersonality.generateIdentityResponse(identityLanguage);
                }
            }

            // Check usage limits for free users
            const usageCheck = await this.usageTracker.checkUsageLimit();
            if (!usageCheck.allowed) {
                return this.getUsageLimitResponse(usageCheck);
            }

            // Refresh API key
            this.apiKey = this.getApiKey();

            // Check if we have a valid API key
            if (!this.apiKey ||
                this.apiKey === 'YOUR_API_KEY_HERE' ||
                this.apiKey === 'LOCAL_DEVELOPMENT_MOCK' ||
                this.apiKey === 'LOCAL_DEVELOPMENT_NEEDS_KEY' ||
                this.apiKey === 'WAITING_FOR_SERVER_INJECTION' ||
                this.apiKey === 'CLOUDFLARE_ENV_INJECTION_PLACEHOLDER') {
                // Record usage even for mock responses
                await this.usageTracker.recordUsage();
                return this.getApiKeySetupMessage();
            }

            // Use a simpler request format to avoid 400 errors
            const requestBody = {
                contents: [
                    {
                        parts: [
                            { text: message }
                        ]
                    }
                ],
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 1024,
                }
            };

            // Try primary model first
            let response = await this.makeRequest(this.model, requestBody);

            // If primary model fails, try fallback
            if (!response) {
                console.log('Primary model failed, trying fallback...');
                response = await this.makeRequest(this.fallbackModel, requestBody);
            }

            // If both models fail, use mock response
            if (!response) {
                console.log('Both models failed, using mock response...');
                return this.getMockResponse(message, type);
            }

            // Extract response text
            const responseText = response.candidates?.[0]?.content?.parts?.[0]?.text;

            if (!responseText) {
                throw new Error('Invalid response format');
            }

            // Record successful API usage
            await this.usageTracker.recordUsage();

            // Add to conversation history for context (simplified)
            this.conversationHistory.push({
                user: message,
                assistant: responseText,
                timestamp: Date.now()
            });

            // Limit conversation history to prevent token overflow
            if (this.conversationHistory.length > 10) {
                this.conversationHistory = this.conversationHistory.slice(-10);
            }

            return this.formatResponse(responseText, type);

        } catch (error) {
            console.error('Gemini API Error:', error);
            return this.getErrorResponse(error);
        }
    }

    async makeRequest(model, requestBody) {
        try {
            const url = `${this.baseUrl}/${model}:generateContent?key=${this.apiKey}`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error(`❌ API Error (${response.status}):`, errorData);
                console.error('Full error details:', JSON.stringify(errorData, null, 2));
                return null;
            }

            const result = await response.json();
            console.log('✅ API Response:', result);
            return result;

        } catch (error) {
            console.error('❌ Request failed:', error);
            return null;
        }
    }

    formatResponse(text, type) {
        // Clean up the response text
        let formattedText = text.trim();
        
        // Add type-specific formatting
        switch (type) {
            case 'expense':
                if (!formattedText.includes('💰') && !formattedText.includes('📊')) {
                    formattedText = '💰 ' + formattedText;
                }
                break;
            case 'advice':
                if (!formattedText.includes('💡') && !formattedText.includes('✨')) {
                    formattedText = '💡 ' + formattedText;
                }
                break;
            case 'budget':
                if (!formattedText.includes('📋') && !formattedText.includes('📊')) {
                    formattedText = '📋 ' + formattedText;
                }
                break;
            case 'seasonal':
                if (!formattedText.includes('🌸')) {
                    formattedText = '🌸 ' + formattedText;
                }
                break;
        }

        // Ensure it ends with a sakura emoji if it's a positive response
        if (!formattedText.includes('🌸') && !formattedText.includes('⚠️') && !formattedText.includes('❌')) {
            formattedText += ' 🌸';
        }

        return formattedText;
    }

    getApiKeySetupMessage() {
        const isLocalDevelopment = window.location.hostname === 'localhost' ||
                                  window.location.hostname === '127.0.0.1';

        if (isLocalDevelopment) {
            return '🔑 **本地开发环境需要设置API密钥**\n\n' +
                   '请在浏览器控制台中运行以下命令：\n' +
                   '```javascript\n' +
                   'localStorage.setItem("gemini_api_key_dev", "YOUR_API_KEY_HERE")\n' +
                   '```\n\n' +
                   '然后刷新页面即可使用真实的AI功能。🌸\n\n' +
                   '💡 **获取API密钥**：\n' +
                   '1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)\n' +
                   '2. 创建新的API密钥\n' +
                   '3. 复制密钥并设置到localStorage中';
        } else {
            return '🔑 **API密钥配置问题**\n\n' +
                   '生产环境的API密钥未正确配置。\n\n' +
                   '请联系管理员检查Cloudflare Pages环境变量设置。🌸\n\n' +
                   '**需要设置的环境变量**：\n' +
                   '- `GEMINI_API_KEY`: Google Gemini API密钥';
        }
    }

    getMockResponse(message, type) {
        // Check if user needs to set up API key
        if (this.apiKey === 'LOCAL_DEVELOPMENT_NEEDS_KEY') {
            return '🔑 本地开发环境需要设置API密钥才能使用真实的AI功能。\n\n' +
                   '请访问 <a href="/api_key_setup.html" target="_blank">API密钥设置页面</a> 来配置您的Gemini API密钥。\n\n' +
                   '在此之前，我会为您提供模拟回复：\n\n' +
                   'こんにちは！さくらちゃんです🌸 現在はデモモードで動作しています。';
        }

        // Mock responses when API is not available
        const mockResponses = {
            'expense': [
                '💰 今月の支出について確認いたします。GoldenLedgerの支出管理機能をご利用ください。詳細な分析をご希望でしたら、具体的な期間をお教えください。🌸',
                '📊 支出の詳細を分析いたします。カテゴリ別の内訳や月次比較など、どのような情報をお求めでしょうか？🌸'
            ],
            'advice': [
                '💡 仕訳のコツをお教えします！まず取引の性質を理解し、適切な勘定科目を選択することが重要です。借方・貸方の概念も大切ですね。🌸',
                '✨ 家計管理のアドバイスをさせていただきます。定期的な記録と分析が成功の鍵です。何か具体的なご質問はありますか？🌸'
            ],
            'budget': [
                '📋 予算見直しのお手伝いをいたします。現在の収支状況と目標をお聞かせください。最適な予算プランをご提案します。🌸',
                '🎯 予算管理について詳しくご説明します。収入と支出のバランスを取ることが重要ですね。🌸'
            ],
            'seasonal': [
                '🌸 春の家計管理のコツをお教えします。新生活の季節は支出が増えがちですが、計画的な管理で乗り切りましょう。🌸',
                '🌺 季節に応じた家計のアドバイスをいたします。四季を通じて安定した家計管理を心がけましょう。🌸'
            ],
            'general': [
                'こんにちは！さくらちゃんです🌸 記帳についてお手伝いします。何でもお気軽にご相談ください！',
                'ありがとうございます！家計管理について詳しくお答えします。具体的にどのような点でお困りでしょうか？🌸',
                'GoldenLedgerのAI機能を活用して、最適な解決策をご提案します。お気軽にご質問ください🌸',
                'さくらちゃんがお手伝いします！記帳方法や分析について、何でもお聞きください。🌸'
            ]
        };

        const responses = mockResponses[type] || mockResponses['general'];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];

        // Add a note that this is a free tier response
        return randomResponse + '\n\n💎 フリープランでご利用中です。より高度な機能をお求めの場合は、プレミアムプランをご検討ください。';
    }

    getUsageLimitResponse(usageInfo) {
        const remainingHours = Math.ceil((usageInfo.resetTime - Date.now()) / (1000 * 60 * 60));
        const planName = usageInfo.planName || 'フリープラン';
        const limit = usageInfo.limit;
        const count = usageInfo.count;

        let upgradeMessage = '';

        // 根据当前计划提供升级建议
        if (usageInfo.planType === 'FREE') {
            upgradeMessage = `💎 ベーシックプラン（¥980/月）にアップグレードすると200回/日まで利用可能です。\n` +
                           `🚀 プロプラン（¥2,980/月）なら700回/日まで利用可能です。`;
        } else if (usageInfo.planType === 'BASIC') {
            upgradeMessage = `🚀 プロプラン（¥2,980/月）にアップグレードすると700回/日まで利用可能です。`;
        }

        return `🚫 申し訳ございません。${planName}の1日の利用制限（${limit}回）に達しました。\n\n` +
               `⏰ 制限は約${remainingHours}時間後にリセットされます。\n\n` +
               (upgradeMessage ? upgradeMessage + '\n\n' : '') +
               `📊 本日の利用状況：${count}/${limit}回\n` +
               `🔄 次回リセット：${new Date(usageInfo.resetTime).toLocaleString('ja-JP')}`;
    }

    getErrorResponse(error) {
        const errorResponses = [
            '申し訳ございません。一時的にサービスが利用できません。しばらくしてからもう一度お試しください。🌸',
            'システムに問題が発生しました。お手数ですが、少し時間をおいてから再度お試しください。🌸',
            'ただいまサービスが混雑しております。しばらくお待ちいただいてから、もう一度お試しください。🌸'
        ];

        // Return a random error response
        return errorResponses[Math.floor(Math.random() * errorResponses.length)];
    }

    // Get conversation context for analysis
    getConversationContext() {
        return {
            messageCount: this.conversationHistory.length,
            lastMessages: this.conversationHistory.slice(-5),
            topics: this.extractTopics()
        };
    }

    extractTopics() {
        // Simple topic extraction based on keywords
        const topics = new Set();
        const keywords = {
            'expense': ['支出', '出費', '費用', '使った', '買った'],
            'income': ['収入', '給料', '売上', '入金'],
            'budget': ['予算', '計画', '目標', '見直し'],
            'savings': ['貯金', '貯蓄', '積立', '節約'],
            'analysis': ['分析', '確認', 'チェック', '見る']
        };

        this.conversationHistory.forEach(entry => {
            if (entry.user) {
                const text = entry.user;
                Object.entries(keywords).forEach(([topic, words]) => {
                    if (words.some(word => text.includes(word))) {
                        topics.add(topic);
                    }
                });
            }
        });

        return Array.from(topics);
    }

    // Clear conversation history
    clearHistory() {
        this.conversationHistory = [];
    }

    // Get suggested follow-up questions
    getSuggestedQuestions(lastResponse) {
        const suggestions = {
            'expense': [
                '今月の支出の詳細を教えて',
                '支出を減らすコツは？',
                'カテゴリ別の支出を見たい'
            ],
            'budget': [
                '予算の立て方を教えて',
                '予算オーバーした時の対処法',
                '来月の予算を相談したい'
            ],
            'advice': [
                '記帳の付け方のコツ',
                '仕訳の基本を教えて',
                '節約のアイデアが欲しい'
            ],
            'seasonal': [
                '季節の家計管理のコツ',
                '年間の家計計画について',
                '季節のイベント費用の準備'
            ]
        };

        // Return random suggestions based on context
        const allSuggestions = Object.values(suggestions).flat();
        return allSuggestions.sort(() => 0.5 - Math.random()).slice(0, 3);
    }

    // Health check
    async healthCheck() {
        try {
            // Very simple test request
            const testRequestBody = {
                contents: [
                    {
                        parts: [{ text: 'Hi' }]
                    }
                ]
            };

            const response = await this.makeRequest(this.model, testRequestBody);
            return response && response.candidates && response.candidates.length > 0;
        } catch (error) {
            console.error('Health check failed:', error);
            return false;
        }
    }
}

// Export for use
window.GeminiAPI = GeminiAPI;

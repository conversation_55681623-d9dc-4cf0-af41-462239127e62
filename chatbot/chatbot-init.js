// Chatbot Initialization Script
// Initialize all chatbot components

class ChatbotManager {
    constructor() {
        this.fab = null;
        this.interface = null;
        this.api = null;
        this.isInitialized = false;
        this.config = {
            autoShow: true,
            showDelay: 2000,
            enableAPI: true,
            debugMode: false
        };
    }

    async init(config = {}) {
        if (this.isInitialized) {
            console.warn('Chatbot already initialized');
            return;
        }

        // Merge config
        this.config = { ...this.config, ...config };

        try {
            // 生产环境静默初始化
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                console.log('🌸 Initializing Sakura Chatbot...');
            }

            // Load AI Personality module first
            await this.loadPersonalityModule();

            // Initialize API first
            if (this.config.enableAPI) {
                await this.initAPI();
            }

            // Initialize interface
            await this.initInterface();

            // Initialize FAB
            await this.initFAB();

            // Setup event listeners
            this.setupEventListeners();

            // Mark as initialized
            this.isInitialized = true;

            console.log('🌸 Sakura Chatbot initialized successfully!');

            // Auto-show FAB if enabled
            if (this.config.autoShow) {
                setTimeout(() => {
                    this.showFAB();
                }, this.config.showDelay);
            }

            // Health check
            if (this.config.enableAPI) {
                this.performHealthCheck();
            }

        } catch (error) {
            console.error('❌ Failed to initialize chatbot:', error);
            this.handleInitError(error);
        }
    }

    // Load AI Personality module
    async loadPersonalityModule() {
        return new Promise((resolve, reject) => {
            if (window.AIPersonality) {
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    console.log('✅ AI Personality module already loaded');
                }
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/chatbot/AIPersonality.js';
            script.onload = () => {
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    console.log('✅ AI Personality module loaded successfully');
                }
                resolve();
            };
            script.onerror = () => {
                console.warn('⚠️ Failed to load AI Personality module, using fallback');
                resolve(); // Continue even if failed
            };
            document.head.appendChild(script);
        });
    }

    async initAPI() {
        try {
            this.api = new GeminiAPI();
            console.log('✅ Gemini API initialized');
            
            // Make API globally available
            window.GeminiAPI = this.api;
            
        } catch (error) {
            console.error('❌ Failed to initialize API:', error);
            throw error;
        }
    }

    async initInterface() {
        try {
            this.interface = new ChatInterface();
            console.log('✅ Chat Interface initialized');
            
            // Make interface globally available
            window.chatInterface = this.interface;
            
        } catch (error) {
            console.error('❌ Failed to initialize interface:', error);
            throw error;
        }
    }

    async initFAB() {
        try {
            this.fab = new ChatFab();
            console.log('✅ Chat FAB initialized');
            
            // Make FAB globally available
            window.chatFab = this.fab;
            
        } catch (error) {
            console.error('❌ Failed to initialize FAB:', error);
            throw error;
        }
    }

    setupEventListeners() {
        // Listen for custom events
        document.addEventListener('chatbot:open', () => {
            this.openChat();
        });

        document.addEventListener('chatbot:close', () => {
            this.closeChat();
        });

        document.addEventListener('chatbot:toggle', () => {
            this.toggleChat();
        });

        document.addEventListener('chatbot:message', (event) => {
            this.sendMessage(event.detail.message, event.detail.type);
        });

        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.handlePageHidden();
            } else {
                this.handlePageVisible();
            }
        });

        console.log('✅ Event listeners setup complete');
    }

    showFAB() {
        if (this.fab) {
            this.fab.show();
            console.log('🌸 FAB shown');
        }
    }

    hideFAB() {
        if (this.fab) {
            this.fab.hide();
            console.log('🌸 FAB hidden');
        }
    }

    openChat() {
        if (this.interface) {
            this.interface.open();
            this.hideFAB();
            console.log('🌸 Chat opened');
        }
    }

    closeChat() {
        if (this.interface) {
            this.interface.close();
            this.showFAB();
            console.log('🌸 Chat closed');
        }
    }

    toggleChat() {
        if (this.interface) {
            this.interface.toggle();
            console.log('🌸 Chat toggled');
        }
    }

    async sendMessage(message, type = 'general') {
        if (this.interface && this.api) {
            try {
                // Open chat if not already open
                if (!this.interface.isOpen) {
                    this.openChat();
                }

                // Send message through interface
                await this.interface.sendMessage(message, type);
                
            } catch (error) {
                console.error('❌ Failed to send message:', error);
            }
        }
    }

    async performHealthCheck() {
        try {
            console.log('🔍 Performing health check...');
            
            if (this.api) {
                const isHealthy = await this.api.healthCheck();
                if (isHealthy) {
                    console.log('✅ API health check passed');
                } else {
                    console.warn('⚠️ API health check failed');
                }
            }
            
        } catch (error) {
            console.error('❌ Health check error:', error);
        }
    }

    handleInitError(error) {
        // Show user-friendly error message
        const errorMessage = document.createElement('div');
        errorMessage.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        errorMessage.textContent = 'チャットボットの初期化に失敗しました';
        
        document.body.appendChild(errorMessage);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorMessage.parentNode) {
                errorMessage.parentNode.removeChild(errorMessage);
            }
        }, 5000);
    }

    handlePageHidden() {
        // Pause any ongoing operations when page is hidden
        if (this.config.debugMode) {
            console.log('🌸 Page hidden - pausing chatbot');
        }
    }

    handlePageVisible() {
        // Resume operations when page becomes visible
        if (this.config.debugMode) {
            console.log('🌸 Page visible - resuming chatbot');
        }
    }

    // Public API methods
    getStatus() {
        return {
            initialized: this.isInitialized,
            fabVisible: this.fab?.fabElement?.classList.contains('visible'),
            chatOpen: this.interface?.isOpen,
            apiHealthy: this.api ? true : false
        };
    }

    getConfig() {
        return { ...this.config };
    }

    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('🌸 Config updated:', this.config);
    }

    // Cleanup method
    destroy() {
        try {
            if (this.fab) {
                this.fab.destroy();
                this.fab = null;
            }

            if (this.interface) {
                // Interface doesn't have destroy method, but we can remove the element
                const chatElement = document.getElementById('chat-interface');
                if (chatElement && chatElement.parentNode) {
                    chatElement.parentNode.removeChild(chatElement);
                }
                this.interface = null;
            }

            if (this.api) {
                this.api.clearHistory();
                this.api = null;
            }

            // Remove global references
            delete window.chatInterface;
            delete window.chatFab;
            delete window.GeminiAPI;

            this.isInitialized = false;
            console.log('🌸 Chatbot destroyed');

        } catch (error) {
            console.error('❌ Error during cleanup:', error);
        }
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Create global chatbot manager
    window.chatbotManager = new ChatbotManager();
    
    // Initialize with default config
    window.chatbotManager.init({
        autoShow: true,
        showDelay: 2000,
        enableAPI: true,
        debugMode: false
    });
});

// Export for manual initialization
window.ChatbotManager = ChatbotManager;

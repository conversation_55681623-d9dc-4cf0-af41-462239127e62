// 花咲家計簿 チャットFABコンポーネント
// Material-UI Style Floating Action Button for Chat

class ChatFab {
    constructor() {
        this.isOpen = false;
        this.unreadCount = 0;
        this.showWelcome = false;
        this.hasShownWelcome = false;
        this.welcomeTimer = null;
        this.init();
    }

    init() {
        this.createFabElement();
        this.bindEvents();
        this.show();
        this.scheduleWelcomeMessage();
    }

    createFabElement() {
        // Create FAB container with Material-UI structure
        this.fabElement = document.createElement('div');
        this.fabElement.id = 'chat-fab-container';
        this.fabElement.className = 'chat-fab-container';

        // Create complete Material-UI style structure
        this.fabElement.innerHTML = `
            <!-- Welcome Message -->
            <div class="welcome-message-container" id="welcome-message" style="display: none;">
                <div class="welcome-paper">
                    <div class="welcome-content">
                        <div class="welcome-text">
                            <div class="welcome-title">🌸 こんにちは！</div>
                            <div class="welcome-subtitle">記帳でお困りのことはありませんか？何でもお気軽にご相談ください！</div>
                        </div>
                        <button class="welcome-close-btn" onclick="window.chatFab.closeWelcome()">
                            <svg viewBox="0 0 24 24" width="16" height="16">
                                <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="welcome-arrow"></div>
                </div>
            </div>

            <!-- Main FAB Button -->
            <button class="MuiButtonBase-root MuiFab-root MuiFab-circular MuiFab-sizeLarge MuiFab-default chat-fab-button"
                    tabindex="0"
                    type="button"
                    aria-label="さくらちゃんに相談"
                    data-mui-internal-clone-element="true"
                    style="transform: none; transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);">
                <span class="MuiBadge-root css-muoq03-MuiBadge-root">
                    <div class="MuiBox-root css-1s05jah">🌸</div>
                    <span class="MuiBadge-badge MuiBadge-standard MuiBadge-invisible MuiBadge-anchorOriginTopRight MuiBadge-anchorOriginTopRightRectangular MuiBadge-overlapRectangular MuiBadge-colorError css-kvns47-MuiBadge-badge" id="unread-badge">0</span>
                </span>
                <span class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"></span>
            </button>

            <!-- Notification Indicator -->
            <div class="notification-indicator" id="notification-indicator" style="display: none;">
                <svg viewBox="0 0 24 24" width="12" height="12">
                    <path fill="currentColor" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                </svg>
            </div>
        `;

        // Add styles
        this.addStyles();

        // Append to body
        document.body.appendChild(this.fabElement);
    }

    addStyles() {
        if (document.getElementById('chat-fab-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'chat-fab-styles';
        styles.textContent = `
            /* Container */
            .chat-fab-container {
                position: fixed;
                bottom: 24px;
                right: 24px;
                z-index: 1200;
                transform: scale(0);
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .chat-fab-container.visible {
                transform: scale(1);
                opacity: 1;
            }

            /* Welcome Message */
            .welcome-message-container {
                position: absolute;
                bottom: 80px;
                right: 0;
                width: 280px;
                z-index: 1201;
                transform: scale(0.8);
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .welcome-message-container.show {
                transform: scale(1);
                opacity: 1;
            }

            .welcome-paper {
                background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%);
                border-radius: 12px;
                padding: 16px;
                color: white;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                position: relative;
            }

            .welcome-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-direction: column;
            }

            .welcome-text {
                flex: 1;
                padding-right: 8px;
            }

            .welcome-title {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 8px;
                line-height: 1.2;
            }

            .welcome-subtitle {
                font-size: 14px;
                opacity: 0.9;
                line-height: 1.4;
            }

            .welcome-close-btn {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s ease;
            }

            .welcome-close-btn:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }

            .welcome-arrow {
                position: absolute;
                bottom: -8px;
                right: 20px;
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-top: 8px solid #FF69B4;
            }

            /* Material-UI FAB Styles */
            .MuiButtonBase-root {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                position: relative;
                box-sizing: border-box;
                background-color: transparent;
                outline: 0;
                border: 0;
                margin: 0;
                border-radius: 0;
                padding: 0;
                cursor: pointer;
                user-select: none;
                vertical-align: middle;
                text-decoration: none;
                color: inherit;
                font-family: inherit;
                font-size: inherit;
                line-height: inherit;
                letter-spacing: inherit;
                text-transform: inherit;
            }

            .MuiFab-root {
                color: rgba(0, 0, 0, 0.87);
                background-color: #e0e0e0;
                box-shadow: 0px 3px 5px -1px rgba(0,0,0,0.2), 0px 6px 10px 0px rgba(0,0,0,0.14), 0px 1px 18px 0px rgba(0,0,0,0.12);
                border-radius: 50%;
                font-size: 1.5rem;
                min-height: 36px;
                flex-shrink: 0;
                transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            }

            .MuiFab-circular {
                border-radius: 50%;
            }

            .MuiFab-sizeLarge {
                width: 64px;
                height: 64px;
                font-size: 2rem;
            }

            .chat-fab-button {
                background: linear-gradient(45deg, #FF69B4, #FFB6C1) !important;
                color: white !important;
                box-shadow: 0 4px 20px rgba(255, 105, 180, 0.4) !important;
                transition: all 0.3s ease !important;
            }

            .chat-fab-button:hover {
                background: linear-gradient(45deg, #C71585, #FF69B4) !important;
                transform: scale(1.1) !important;
                box-shadow: 0 6px 25px rgba(255, 105, 180, 0.6) !important;
            }

            .chat-fab-button:active {
                transform: scale(0.95) !important;
            }

            .chat-fab-button.pulse {
                animation: fabPulse 2s infinite;
            }

            @keyframes fabPulse {
                0% {
                    box-shadow: 0 4px 20px rgba(255, 105, 180, 0.4);
                }
                50% {
                    box-shadow: 0 4px 30px rgba(255, 105, 180, 0.8);
                    transform: scale(1.05);
                }
                100% {
                    box-shadow: 0 4px 20px rgba(255, 105, 180, 0.4);
                }
            }

            /* Badge Styles */
            .MuiBadge-root {
                position: relative;
                display: inline-flex;
                vertical-align: middle;
                flex-shrink: 0;
            }

            .MuiBadge-badge {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
                align-content: center;
                align-items: center;
                position: absolute;
                box-sizing: border-box;
                font-family: inherit;
                font-weight: 500;
                font-size: 0.75rem;
                min-width: 20px;
                line-height: 1;
                padding: 0px 6px;
                height: 20px;
                border-radius: 10px;
                z-index: 1;
                transition: transform 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            }

            .MuiBadge-standard {
                background-color: #d32f2f;
                color: #fff;
            }

            .MuiBadge-invisible {
                transform: scale(0) translate(50%, -50%);
                visibility: hidden;
            }

            .MuiBadge-anchorOriginTopRight {
                top: 0%;
                right: 0%;
                transform: scale(1) translate(50%, -50%);
                transform-origin: 100% 0%;
            }

            .MuiBadge-colorError {
                background-color: #d32f2f;
                color: #fff;
            }

            /* Box Styles */
            .MuiBox-root {
                font-size: 2rem;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* Touch Ripple */
            .MuiTouchRipple-root {
                overflow: hidden;
                pointer-events: none;
                position: absolute;
                z-index: 0;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                border-radius: inherit;
            }

            /* Notification Indicator */
            .notification-indicator {
                position: absolute;
                top: -10px;
                right: -10px;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background-color: #d32f2f;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: bounce 1s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .chat-fab-container {
                    bottom: 80px;
                    right: 16px;
                }

                .MuiFab-sizeLarge {
                    width: 56px;
                    height: 56px;
                    font-size: 1.75rem;
                }

                .welcome-message-container {
                    width: 260px;
                    bottom: 70px;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    bindEvents() {
        const fabButton = this.fabElement.querySelector('.chat-fab-button');

        fabButton.addEventListener('click', () => {
            this.handleClick();
        });

        // Add ripple effect on click
        fabButton.addEventListener('mousedown', (e) => {
            this.createRipple(e, fabButton);
        });

        // Add hover effects
        fabButton.addEventListener('mouseenter', () => {
            this.handleHover();
        });
    }

    handleClick() {
        this.closeWelcome();

        if (window.chatInterface) {
            window.chatInterface.toggle();
            this.clearBadge();
        }

        // Add click animation
        const fabButton = this.fabElement.querySelector('.chat-fab-button');
        fabButton.style.transform = 'scale(0.95)';
        setTimeout(() => {
            fabButton.style.transform = '';
        }, 150);
    }

    handleHover() {
        // Material-UI style hover effect is handled by CSS
        // Additional hover logic can be added here if needed
    }

    createRipple(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            left: ${x}px;
            top: ${y}px;
            width: ${size}px;
            height: ${size}px;
        `;

        // Add ripple animation keyframes if not exists
        if (!document.getElementById('ripple-keyframes')) {
            const style = document.createElement('style');
            style.id = 'ripple-keyframes';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        const rippleContainer = element.querySelector('.MuiTouchRipple-root');
        if (rippleContainer) {
            rippleContainer.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }
    }

    show() {
        setTimeout(() => {
            this.fabElement.classList.add('visible');
        }, 2000); // Show after 2 seconds like the original requirement
    }

    hide() {
        this.fabElement.classList.remove('visible');
    }

    scheduleWelcomeMessage() {
        if (!this.hasShownWelcome && !this.isOpen) {
            this.welcomeTimer = setTimeout(() => {
                this.showWelcome = true;
                this.hasShownWelcome = true;
                this.displayWelcomeMessage();
            }, 5000); // Show welcome after 5 seconds
        }
    }

    displayWelcomeMessage() {
        const welcomeElement = this.fabElement.querySelector('#welcome-message');
        if (welcomeElement && this.showWelcome && !this.isOpen) {
            welcomeElement.style.display = 'block';
            setTimeout(() => {
                welcomeElement.classList.add('show');
            }, 50);

            // Auto-hide after 8 seconds
            setTimeout(() => {
                this.closeWelcome();
            }, 8000);
        }
    }

    closeWelcome() {
        const welcomeElement = this.fabElement.querySelector('#welcome-message');
        if (welcomeElement) {
            welcomeElement.classList.remove('show');
            setTimeout(() => {
                welcomeElement.style.display = 'none';
            }, 300);
        }
        this.showWelcome = false;

        if (this.welcomeTimer) {
            clearTimeout(this.welcomeTimer);
            this.welcomeTimer = null;
        }
    }

    showBadge(count = 1) {
        this.unreadCount = count;
        const badge = this.fabElement.querySelector('#unread-badge');
        const notificationIndicator = this.fabElement.querySelector('#notification-indicator');
        const fabButton = this.fabElement.querySelector('.chat-fab-button');

        if (badge) {
            badge.textContent = count;
            badge.classList.remove('MuiBadge-invisible');
        }

        if (notificationIndicator && count > 0) {
            notificationIndicator.style.display = 'flex';
        }

        if (fabButton && count > 0) {
            fabButton.classList.add('pulse');
        }
    }

    clearBadge() {
        this.unreadCount = 0;
        const badge = this.fabElement.querySelector('#unread-badge');
        const notificationIndicator = this.fabElement.querySelector('#notification-indicator');
        const fabButton = this.fabElement.querySelector('.chat-fab-button');

        if (badge) {
            badge.classList.add('MuiBadge-invisible');
        }

        if (notificationIndicator) {
            notificationIndicator.style.display = 'none';
        }

        if (fabButton) {
            fabButton.classList.remove('pulse');
        }
    }

    setOnline(isOnline = true) {
        const fabButton = this.fabElement.querySelector('.chat-fab-button');
        if (fabButton) {
            if (isOnline) {
                fabButton.style.filter = 'none';
                fabButton.style.opacity = '1';
            } else {
                fabButton.style.filter = 'grayscale(50%)';
                fabButton.style.opacity = '0.7';
            }
        }
    }

    setChatOpen(isOpen) {
        this.isOpen = isOpen;
        if (isOpen) {
            this.closeWelcome();
            this.hide();
        } else {
            this.show();
        }
    }

    destroy() {
        // Clear timers
        if (this.welcomeTimer) {
            clearTimeout(this.welcomeTimer);
            this.welcomeTimer = null;
        }

        // Remove DOM elements
        if (this.fabElement && this.fabElement.parentNode) {
            this.fabElement.parentNode.removeChild(this.fabElement);
        }

        // Remove styles
        const styles = document.getElementById('chat-fab-styles');
        if (styles && styles.parentNode) {
            styles.parentNode.removeChild(styles);
        }

        const rippleStyles = document.getElementById('ripple-keyframes');
        if (rippleStyles && rippleStyles.parentNode) {
            rippleStyles.parentNode.removeChild(rippleStyles);
        }

        // Reset state
        this.isOpen = false;
        this.unreadCount = 0;
        this.showWelcome = false;
        this.hasShownWelcome = false;
    }
}

// Export for use
window.ChatFab = ChatFab;

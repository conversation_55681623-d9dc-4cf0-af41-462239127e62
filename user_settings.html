<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ユーザー設定 - GoldenLedger | Smart AI-Powered Finance System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="background_control.js"></script>
    <script src="auth.js"></script>
    <script src="music_control.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .settings-card {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .input-field {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }
        .input-field:focus {
            border-color: #6c5ce7;
            box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #6c5ce7 0%, #00d4aa 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(108, 92, 231, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="master_dashboard.html" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-teal-500 rounded-xl flex items-center justify-center">
                            <span class="text-white text-xl font-bold">🚀</span>
                        </div>
                        <div>
                            <div class="text-xl font-bold text-gray-800">GoldenLedger</div>
                            <div class="text-xs text-gray-500">Smart AI-Powered Finance</div>
                        </div>
                    </a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="master_dashboard.html" class="text-gray-600 hover:text-purple-600 transition-colors">
                        ← ダッシュボードに戻る
                    </a>
                    <div class="text-right">
                        <div class="text-sm font-medium text-gray-800" data-user-name>ユーザー</div>
                        <div class="text-xs text-gray-500" data-user-company>会社名</div>
                    </div>
                    <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span class="text-gray-600 text-sm font-bold" data-user-initial>U</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="gradient-bg text-white py-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl font-bold mb-4">⚙️ ユーザー設定</h1>
                <p class="text-xl opacity-90">アカウント情報とシステム設定を管理</p>
            </div>
        </div>
    </header>

    <!-- Settings Content -->
    <main class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Settings Navigation -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">設定メニュー</h3>
                        <nav class="space-y-2">
                            <a href="#profile" class="settings-nav-item active flex items-center space-x-3 p-3 rounded-lg bg-purple-50 text-purple-700">
                                <span>👤</span>
                                <span>プロフィール</span>
                            </a>
                            <a href="#company" class="settings-nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600">
                                <span>🏢</span>
                                <span>会社情報</span>
                            </a>
                            <a href="#security" class="settings-nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600">
                                <span>🔒</span>
                                <span>セキュリティ</span>
                            </a>
                            <a href="#preferences" class="settings-nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600">
                                <span>⚙️</span>
                                <span>環境設定</span>
                            </a>
                            <a href="#billing" class="settings-nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 text-gray-600">
                                <span>💳</span>
                                <span>請求・支払い</span>
                            </a>
                        </nav>
                    </div>
                </div>

                <!-- Settings Content -->
                <div class="lg:col-span-2">
                    <!-- Profile Settings -->
                    <div id="profile" class="settings-section">
                        <div class="settings-card bg-white rounded-xl shadow-sm border p-8">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-2xl font-bold text-gray-800">👤 プロフィール設定</h2>
                                <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                                    <span class="text-gray-600 text-xl font-bold" data-user-initial>U</span>
                                </div>
                            </div>

                            <form id="profileForm" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                                            名前
                                        </label>
                                        <input 
                                            type="text" 
                                            id="firstName" 
                                            name="firstName" 
                                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                            placeholder="太郎"
                                        >
                                    </div>
                                    <div>
                                        <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                                            姓
                                        </label>
                                        <input 
                                            type="text" 
                                            id="lastName" 
                                            name="lastName" 
                                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                            placeholder="田中"
                                        >
                                    </div>
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                        メールアドレス
                                    </label>
                                    <input 
                                        type="email" 
                                        id="email" 
                                        name="email" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        placeholder="<EMAIL>"
                                    >
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                        電話番号
                                    </label>
                                    <input 
                                        type="tel" 
                                        id="phone" 
                                        name="phone" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        placeholder="090-1234-5678"
                                    >
                                </div>

                                <div>
                                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                                        タイムゾーン
                                    </label>
                                    <select 
                                        id="timezone" 
                                        name="timezone" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                    >
                                        <option value="Asia/Tokyo">Asia/Tokyo (JST)</option>
                                        <option value="Asia/Shanghai">Asia/Shanghai (CST)</option>
                                        <option value="America/New_York">America/New_York (EST)</option>
                                        <option value="Europe/London">Europe/London (GMT)</option>
                                    </select>
                                </div>

                                <div class="flex justify-end space-x-4">
                                    <button 
                                        type="button" 
                                        class="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        キャンセル
                                    </button>
                                    <button 
                                        type="submit" 
                                        class="btn-primary px-6 py-3 rounded-xl text-white font-semibold"
                                    >
                                        保存
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Company Settings -->
                    <div id="company" class="settings-section hidden">
                        <div class="settings-card bg-white rounded-xl shadow-sm border p-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">🏢 会社情報</h2>

                            <form id="companyForm" class="space-y-6">
                                <div>
                                    <label for="companyName" class="block text-sm font-medium text-gray-700 mb-2">
                                        会社名
                                    </label>
                                    <input 
                                        type="text" 
                                        id="companyName" 
                                        name="companyName" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        placeholder="株式会社サンプル"
                                    >
                                </div>

                                <div>
                                    <label for="industry" class="block text-sm font-medium text-gray-700 mb-2">
                                        業界
                                    </label>
                                    <select 
                                        id="industry" 
                                        name="industry" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                    >
                                        <option value="">業界を選択</option>
                                        <option value="technology">テクノロジー</option>
                                        <option value="finance">金融</option>
                                        <option value="retail">小売</option>
                                        <option value="manufacturing">製造業</option>
                                        <option value="healthcare">ヘルスケア</option>
                                        <option value="education">教育</option>
                                        <option value="other">その他</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="employeeCount" class="block text-sm font-medium text-gray-700 mb-2">
                                        従業員数
                                    </label>
                                    <select 
                                        id="employeeCount" 
                                        name="employeeCount" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                    >
                                        <option value="">従業員数を選択</option>
                                        <option value="1-10">1-10人</option>
                                        <option value="11-50">11-50人</option>
                                        <option value="51-200">51-200人</option>
                                        <option value="201-1000">201-1000人</option>
                                        <option value="1000+">1000人以上</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                        住所
                                    </label>
                                    <textarea 
                                        id="address" 
                                        name="address" 
                                        rows="3"
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        placeholder="〒100-0001 東京都千代田区千代田1-1"
                                    ></textarea>
                                </div>

                                <div class="flex justify-end space-x-4">
                                    <button 
                                        type="button" 
                                        class="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                                    >
                                        キャンセル
                                    </button>
                                    <button 
                                        type="submit" 
                                        class="btn-primary px-6 py-3 rounded-xl text-white font-semibold"
                                    >
                                        保存
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div id="security" class="settings-section hidden">
                        <div class="settings-card bg-white rounded-xl shadow-sm border p-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">🔒 セキュリティ設定</h2>

                            <!-- Password Change -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">パスワード変更</h3>
                                <form id="passwordForm" class="space-y-4">
                                    <div>
                                        <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                            現在のパスワード
                                        </label>
                                        <input 
                                            type="password" 
                                            id="currentPassword" 
                                            name="currentPassword" 
                                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        >
                                    </div>
                                    <div>
                                        <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                            新しいパスワード
                                        </label>
                                        <input 
                                            type="password" 
                                            id="newPassword" 
                                            name="newPassword" 
                                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        >
                                    </div>
                                    <div>
                                        <label for="confirmNewPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                            新しいパスワード（確認）
                                        </label>
                                        <input 
                                            type="password" 
                                            id="confirmNewPassword" 
                                            name="confirmNewPassword" 
                                            class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                        >
                                    </div>
                                    <div class="flex justify-end">
                                        <button 
                                            type="submit" 
                                            class="btn-primary px-6 py-3 rounded-xl text-white font-semibold"
                                        >
                                            パスワードを変更
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Two-Factor Authentication -->
                            <div class="border-t pt-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">二要素認証</h3>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                    <div>
                                        <div class="font-medium text-gray-800">二要素認証を有効にする</div>
                                        <div class="text-sm text-gray-600">アカウントのセキュリティを強化します</div>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" id="twoFactorAuth">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preferences -->
                    <div id="preferences" class="settings-section hidden">
                        <div class="settings-card bg-white rounded-xl shadow-sm border p-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">⚙️ 環境設定</h2>

                            <div class="space-y-6">
                                <!-- Language -->
                                <div>
                                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                                        言語
                                    </label>
                                    <select 
                                        id="language" 
                                        name="language" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                    >
                                        <option value="ja">日本語</option>
                                        <option value="en">English</option>
                                        <option value="zh">中文</option>
                                    </select>
                                </div>

                                <!-- Currency -->
                                <div>
                                    <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
                                        通貨
                                    </label>
                                    <select 
                                        id="currency" 
                                        name="currency" 
                                        class="input-field w-full px-4 py-3 rounded-xl focus:outline-none"
                                    >
                                        <option value="JPY">日本円 (¥)</option>
                                        <option value="USD">米ドル ($)</option>
                                        <option value="EUR">ユーロ (€)</option>
                                        <option value="CNY">人民元 (¥)</option>
                                    </select>
                                </div>

                                <!-- Notifications -->
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">通知設定</h3>
                                    <div class="space-y-3">
                                        <label class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <span class="text-gray-700">メール通知</span>
                                            <input type="checkbox" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500" checked>
                                        </label>
                                        <label class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <span class="text-gray-700">AI処理完了通知</span>
                                            <input type="checkbox" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500" checked>
                                        </label>
                                        <label class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <span class="text-gray-700">月次レポート</span>
                                            <input type="checkbox" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                        </label>
                                    </div>
                                </div>

                                <div class="flex justify-end">
                                    <button 
                                        type="button" 
                                        class="btn-primary px-6 py-3 rounded-xl text-white font-semibold"
                                        onclick="savePreferences()"
                                    >
                                        設定を保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing -->
                    <div id="billing" class="settings-section hidden">
                        <div class="settings-card bg-white rounded-xl shadow-sm border p-8">
                            <h2 class="text-2xl font-bold text-gray-800 mb-6">💳 請求・支払い</h2>

                            <!-- Current Plan -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">現在のプラン</h3>
                                <div class="p-6 bg-gradient-to-r from-purple-50 to-teal-50 rounded-xl border border-purple-200">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-xl font-bold text-gray-800">無料トライアル</div>
                                            <div class="text-gray-600">30日間無料でご利用いただけます</div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold text-purple-600">¥0</div>
                                            <div class="text-sm text-gray-500">/月</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Upgrade Options -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">アップグレードオプション</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="p-6 border border-gray-200 rounded-xl hover:border-purple-300 transition-colors cursor-pointer">
                                        <div class="text-lg font-bold text-gray-800 mb-2">スタンダード</div>
                                        <div class="text-3xl font-bold text-purple-600 mb-2">¥2,980<span class="text-sm text-gray-500">/月</span></div>
                                        <ul class="text-sm text-gray-600 space-y-1">
                                            <li>✓ 無制限の仕訳</li>
                                            <li>✓ AI自動記帳</li>
                                            <li>✓ OCR認識</li>
                                            <li>✓ 基本レポート</li>
                                        </ul>
                                    </div>
                                    <div class="p-6 border border-purple-300 rounded-xl bg-purple-50 cursor-pointer">
                                        <div class="text-lg font-bold text-gray-800 mb-2">プロフェッショナル</div>
                                        <div class="text-3xl font-bold text-purple-600 mb-2">¥9,980<span class="text-sm text-gray-500">/月</span></div>
                                        <ul class="text-sm text-gray-600 space-y-1">
                                            <li>✓ スタンダードの全機能</li>
                                            <li>✓ 高度な分析</li>
                                            <li>✓ API統合</li>
                                            <li>✓ 優先サポート</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Settings navigation
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all nav items
                document.querySelectorAll('.settings-nav-item').forEach(nav => {
                    nav.classList.remove('active', 'bg-purple-50', 'text-purple-700');
                    nav.classList.add('text-gray-600');
                });
                
                // Add active class to clicked item
                this.classList.add('active', 'bg-purple-50', 'text-purple-700');
                this.classList.remove('text-gray-600');
                
                // Hide all sections
                document.querySelectorAll('.settings-section').forEach(section => {
                    section.classList.add('hidden');
                });
                
                // Show target section
                const target = this.getAttribute('href').substring(1);
                document.getElementById(target).classList.remove('hidden');
            });
        });

        // Form submissions
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // Update user data
            window.fireAuth.updateUser(data);
            updateUserUI();
            
            alert('プロフィールが更新されました。');
        });

        document.getElementById('companyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // Update company data
            window.fireAuth.updateUser({ company: data.companyName, ...data });
            updateUserUI();
            
            alert('会社情報が更新されました。');
        });

        document.getElementById('passwordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            if (data.newPassword !== data.confirmNewPassword) {
                alert('新しいパスワードが一致しません。');
                return;
            }
            
            alert('パスワードが変更されました。');
            e.target.reset();
        });

        function savePreferences() {
            alert('設定が保存されました。');
        }

        // Load user data on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('User settings loaded - development mode, auth checks disabled');

            // Update UI (development mode)
            updateUserUI();
            
            // Load user data into forms
            const user = window.fireAuth.getCurrentUser();
            if (user) {
                // Update initial
                const initial = user.firstName ? user.firstName.charAt(0).toUpperCase() : 
                               user.email ? user.email.charAt(0).toUpperCase() : 'U';
                document.querySelectorAll('[data-user-initial]').forEach(el => {
                    el.textContent = initial;
                });

                // Populate form fields
                if (user.firstName) document.getElementById('firstName').value = user.firstName;
                if (user.lastName) document.getElementById('lastName').value = user.lastName;
                if (user.email) document.getElementById('email').value = user.email;
                if (user.company) document.getElementById('companyName').value = user.company;
            }
        });
    </script>
</body>
</html>

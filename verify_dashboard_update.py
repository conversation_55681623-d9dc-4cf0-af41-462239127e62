#!/usr/bin/env python3
"""
验证仪表盘更新
"""
import requests
import json

def verify_dashboard_update():
    """验证仪表盘更新"""
    
    print("🔍 验证仪表盘更新")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # 测试所有仪表盘版本
    dashboards = [
        ("主仪表盘 (新版)", "/advanced_dashboard.html", True),
        ("修复版仪表盘", "/fixed_dashboard.html", True),
        ("备份版仪表盘", "/advanced_dashboard_backup.html", True),
        ("主控制台", "/master_dashboard.html", True),
        ("最简仪表盘", "/minimal_dashboard.html", True)
    ]
    
    try:
        print("\n📊 步骤1: 验证页面访问")
        
        all_success = True
        
        for name, path, should_exist in dashboards:
            print(f"\n  测试 {name}: {path}")
            
            try:
                response = requests.get(f"{base_url}{path}", timeout=10)
                
                if response.status_code == 200:
                    print(f"  ✅ {name} 访问成功")
                    print(f"    大小: {len(response.content):,} bytes")
                    
                    # 检查是否包含修复版的特征
                    content = response.text
                    if "チャート再作成" in content:
                        print(f"    🎯 包含图表重建功能")
                    if "リアルタイムログ" in content:
                        print(f"    📝 包含实时日志功能")
                    if "Chart.js" in content:
                        print(f"    📊 包含Chart.js支持")
                        
                else:
                    print(f"  ❌ {name} 访问失败: {response.status_code}")
                    if should_exist:
                        all_success = False
                        
            except Exception as e:
                print(f"  ❌ {name} 测试失败: {e}")
                if should_exist:
                    all_success = False
        
        print(f"\n📡 步骤2: 验证API功能")
        
        # 测试仪表盘API
        api_response = requests.get(f"{base_url}/dashboard/summary/default")
        if api_response.status_code == 200:
            data = api_response.json()
            print("  ✅ 仪表盘API正常")
            
            if data.get('financial'):
                financial = data['financial']
                print(f"    💰 本月收入: ¥{financial.get('monthly_revenue', 0):,}")
                print(f"    💸 本月支出: ¥{financial.get('monthly_expenses', 0):,}")
                print(f"    📋 总记录数: {financial.get('total_entries', 0):,}")
            
            if data.get('ai_stats'):
                ai_stats = data['ai_stats']
                print(f"    🤖 AI处理数: {ai_stats.get('total_processed', 0):,}")
                print(f"    ✅ AI成功率: {(ai_stats.get('success_rate', 0) * 100):.1f}%")
        else:
            print(f"  ❌ 仪表盘API失败: {api_response.status_code}")
            all_success = False
        
        print(f"\n🔗 步骤3: 验证链接更新")
        
        # 检查主控制台页面的链接
        master_response = requests.get(f"{base_url}/master_dashboard.html")
        if master_response.status_code == 200:
            master_content = master_response.text
            if "高级仪表盘 (已优化)" in master_content:
                print("  ✅ 主控制台链接已更新")
            else:
                print("  ⚠️ 主控制台链接可能未更新")
        
        print(f"\n📋 步骤4: 更新总结")
        
        if all_success:
            print("  🎉 所有验证通过！")
            print("  ✅ 主仪表盘已成功替换为修复版")
            print("  ✅ 所有版本都可正常访问")
            print("  ✅ API功能正常")
            print("  ✅ 链接已更新")
        else:
            print("  ⚠️ 部分验证失败，请检查错误信息")
        
        print(f"\n🌐 推荐访问地址:")
        print("  主仪表盘: http://localhost:8000/advanced_dashboard.html")
        print("  主控制台: http://localhost:8000/master_dashboard.html")
        print("  备用版本: http://localhost:8000/fixed_dashboard.html")
        
        return all_success
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_chart_functionality():
    """检查图表功能"""
    
    print(f"\n📊 额外检查: Chart.js功能")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    try:
        # 获取主仪表盘内容
        response = requests.get(f"{base_url}/advanced_dashboard.html")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查Chart.js相关内容
            checks = [
                ("Chart.js CDN", "chart.js" in content.lower()),
                ("AI统计图表", "ai-stats-chart" in content),
                ("图表重建按钮", "チャート再作成" in content),
                ("实时日志", "リアルタイムログ" in content),
                ("错误处理", "try {" in content and "catch" in content)
            ]
            
            print("  图表功能检查:")
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"    {status} {check_name}")
            
            all_chart_features = all(result for _, result in checks)
            
            if all_chart_features:
                print("  🎉 所有图表功能都已包含！")
            else:
                print("  ⚠️ 部分图表功能可能缺失")
            
            return all_chart_features
        else:
            print(f"  ❌ 无法获取仪表盘内容: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 图表功能检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始验证仪表盘更新")
    
    # 主要验证
    main_success = verify_dashboard_update()
    
    # 图表功能检查
    chart_success = check_chart_functionality()
    
    print("\n" + "=" * 80)
    print("📊 最终验证结果:")
    
    if main_success and chart_success:
        print("🎉 仪表盘更新完全成功！")
        print("\n✅ 确认事项:")
        print("  - 修复版仪表盘已设为默认版本")
        print("  - AI処理統計图表问题已解决")
        print("  - 所有链接已正确更新")
        print("  - 备份版本可正常访问")
        print("  - Chart.js功能完整")
        
        print("\n🌐 现在可以使用:")
        print("  主仪表盘: http://localhost:8000/advanced_dashboard.html")
        
    else:
        print("❌ 验证过程中发现问题")
        print("请检查上述错误信息并进行修复")

# Cloudflare Pages Headers Configuration

/*
  # Security headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # CORS headers for API requests
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization
  
  # Cache control
  Cache-Control: public, max-age=3600

# Static assets caching
/image/*
  Cache-Control: public, max-age=86400

/music/*
  Cache-Control: public, max-age=86400

# API endpoints - no cache
/api/*
  Cache-Control: no-cache, no-store, must-revalidate

# JavaScript files
*.js
  Cache-Control: public, max-age=3600
  Content-Type: application/javascript

# CSS files  
*.css
  Cache-Control: public, max-age=3600
  Content-Type: text/css

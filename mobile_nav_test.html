<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端导航测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-border {
            border: 2px solid red !important;
        }
        .debug-bg {
            background-color: yellow !important;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
                        <span class="text-white font-bold text-lg">G</span>
                    </div>
                    <span class="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                        GoldenLedger
                    </span>
                </div>

                <!-- 导航链接 - 测试版本 -->
                <div class="flex items-center space-x-2 md:space-x-4 debug-border">
                    <!-- 用户信息显示区域 -->
                    <div id="user-info" class="hidden md:block debug-bg">
                        <span class="text-sm">用户信息区域</span>
                    </div>

                    <!-- 未登录时显示的链接 -->
                    <div id="guest-links" class="flex items-center space-x-2 md:space-x-4 debug-bg">
                        <a href="login.html" class="text-gray-600 hover:text-purple-600 transition-colors text-sm md:text-base">
                            ログイン
                        </a>
                        <a href="register.html" class="bg-gradient-to-r from-purple-600 to-blue-600 px-3 py-1 md:px-6 md:py-2 rounded-xl text-white font-semibold text-sm md:text-base hover:shadow-lg transition-all">
                            無料で始める
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 py-8">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">移动端导航测试</h1>
            <p class="text-gray-600 mb-8">请在不同设备上测试导航栏显示效果</p>
            
            <!-- 屏幕尺寸显示 -->
            <div class="bg-white rounded-lg p-6 shadow-lg mb-8">
                <h2 class="text-xl font-semibold mb-4">当前屏幕信息</h2>
                <div id="screen-info" class="text-left space-y-2">
                    <p><strong>屏幕宽度:</strong> <span id="screen-width"></span>px</p>
                    <p><strong>屏幕高度:</strong> <span id="screen-height"></span>px</p>
                    <p><strong>设备像素比:</strong> <span id="device-ratio"></span></p>
                    <p><strong>用户代理:</strong> <span id="user-agent" class="text-xs"></span></p>
                </div>
            </div>

            <!-- Tailwind 断点测试 -->
            <div class="bg-white rounded-lg p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">Tailwind 断点测试</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-red-100 p-4 rounded block md:hidden">
                        <strong>小屏幕</strong><br>
                        (&lt; 768px)
                    </div>
                    <div class="bg-green-100 p-4 rounded hidden md:block lg:hidden">
                        <strong>中等屏幕</strong><br>
                        (768px - 1024px)
                    </div>
                    <div class="bg-blue-100 p-4 rounded hidden lg:block">
                        <strong>大屏幕</strong><br>
                        (&gt; 1024px)
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 更新屏幕信息
        function updateScreenInfo() {
            document.getElementById('screen-width').textContent = window.innerWidth;
            document.getElementById('screen-height').textContent = window.innerHeight;
            document.getElementById('device-ratio').textContent = window.devicePixelRatio;
            document.getElementById('user-agent').textContent = navigator.userAgent;
        }

        // 初始化和窗口大小变化时更新
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);

        // 导航栏测试
        console.log('📱 移动端导航测试页面加载完成');
        console.log('屏幕宽度:', window.innerWidth);
        console.log('是否为移动设备:', window.innerWidth < 768);
        
        // 检查导航元素
        const userInfo = document.getElementById('user-info');
        const guestLinks = document.getElementById('guest-links');
        
        console.log('用户信息区域:', userInfo ? '存在' : '不存在');
        console.log('游客链接区域:', guestLinks ? '存在' : '不存在');
        
        if (guestLinks) {
            console.log('游客链接显示状态:', window.getComputedStyle(guestLinks).display);
        }
    </script>
</body>
</html>

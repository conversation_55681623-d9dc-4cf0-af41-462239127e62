// 控制台API测试脚本
// 在浏览器控制台中运行此脚本来测试API

console.log('🚀 开始API测试...');

// 从环境配置获取API密钥
const API_KEY = window.GEMINI_API_KEY || 'NOT_CONFIGURED';

async function testGeminiAPI() {
    console.log('🔑 使用API密钥:', API_KEY.substring(0, 10) + '...');
    
    const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${API_KEY}`;
    
    const requestBody = {
        contents: [{
            parts: [{ text: '你好，请简单回复确认API正常工作。' }]
        }]
    };
    
    console.log('📤 发送请求到:', url);
    console.log('📤 请求体:', requestBody);
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        console.log('📥 响应状态:', response.status);
        console.log('📥 响应头:', Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ API调用成功!');
            console.log('📥 完整响应:', result);
            
            const responseText = result.candidates?.[0]?.content?.parts?.[0]?.text;
            if (responseText) {
                console.log('🤖 AI回复:', responseText);
                return { success: true, response: responseText };
            } else {
                console.log('⚠️ 响应格式异常');
                return { success: false, error: '响应格式异常' };
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            console.error('❌ API调用失败');
            console.error('📥 错误响应:', errorData);
            return { success: false, error: errorData };
        }
    } catch (error) {
        console.error('❌ 网络错误:', error);
        return { success: false, error: error.message };
    }
}

// 运行测试
testGeminiAPI().then(result => {
    if (result.success) {
        console.log('🎉 测试完成 - API正常工作!');
        console.log('🤖 AI响应:', result.response);
    } else {
        console.log('💥 测试失败:', result.error);
    }
});

// 导出函数供手动调用
window.testGeminiAPI = testGeminiAPI;

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# Database files and security
*.db
*.sqlite
*.sqlite3
goldenledger_accounting.db

# Secure data directory
data/
data/databases/
data/backups/
data/logs/
.db_key
*.key
*.pem

# Database security
backend/data/
backend/databases/
backend/backups/

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
.cache/

# API keys and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config/secrets.json
*.key
*.pem
*.crt

# Sensitive configuration files
backend/.env*
config/

# Build outputs
dist/
build/
out/

# Coverage reports
coverage/
*.lcov

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# ==========================================
# 机密文件和敏感信息 - 永远不要上传到GitHub
# ==========================================

# 文档文件 (已移至外部位置)
*.md
*.markdown
README*
CHANGELOG*
DEPLOYMENT*
SECURITY*
CLOUDFLARE*
PRD_*
DATABASE_*
RELEASE_NOTES*
SYSTEM_STATUS*
URL_STRUCTURE*
*_LOG.md
*_FIX.md
*_SOLUTION.md

# 备份文件
*.backup
*.bak
*.old
*.orig
*.tmp
*~
.#*
#*#

# macOS系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件和缓存
*.swp
*.swo
*~
.tmp/
temp/
tmp/

# 密钥和证书文件
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.csr
*.jks
*.keystore
*.truststore
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*
*.pub

# 配置文件中的敏感信息
.env
.env.*
!.env.template
!.env.example
config.json
config.yaml
config.yml
secrets.json
secrets.yaml
secrets.yml
credentials.json
auth.json

# API密钥和令牌
*api_key*
*secret*
*token*
*password*
*credential*
jwt_secret*
oauth_*

# 数据库相关敏感文件
*.sql
*.dump
*.backup.sql
database_backup*
db_backup*
migration_backup*

# 日志文件（可能包含敏感信息）
*.log
logs/
log/
audit.log
error.log
access.log

# 开发和调试文件
debug.py
test_*.py.backup
*_debug.*
*_test_data.*
sample_data.*
mock_data.*

# 部署相关敏感文件
deploy.sh
deployment.sh
docker-compose.prod.yml
docker-compose.production.yml
k8s-secrets.yaml
helm-values.prod.yaml

# 敏感配置和设置脚本
setup-cloudflare-env.js
*-env-setup.js
*-api-setup.js
*-config-setup.*
cloudflare-setup.*

# 监控和分析
analytics/
metrics/
monitoring/logs/
performance/

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小ログイン - GoldenLedger</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="email"]:focus,
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-demo {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #ddd;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🌟 GoldenLedger</h1>
            <p>最小ログインページ（テスト用）</p>
        </div>
        
        <div class="status">
            ✅ 認証チェックなし・重定向なし・外部スクリプトなし
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">メールアドレス</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">パスワード</label>
                <input type="password" id="password" placeholder="password" required>
            </div>
            
            <button type="submit" class="btn">ログイン</button>
            <button type="button" class="btn btn-demo" onclick="demoLogin()">デモログイン</button>
        </form>
        
        <div class="links">
            <a href="register_minimal.html">新規登録</a>
            <a href="index.html">ホーム</a>
            <a href="master_dashboard.html">ダッシュボード</a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px; font-size: 12px; color: #666;">
            <strong>テスト情報:</strong><br>
            - 外部スクリプト: なし<br>
            - 認証チェック: 無効<br>
            - 重定向: なし<br>
            - 作成時刻: <span id="timestamp"></span>
        </div>
    </div>

    <script>
        // 現在時刻を表示
        document.getElementById('timestamp').textContent = new Date().toLocaleString('ja-JP');
        
        // 絶対に重定向しないことを保証
        console.log('🔧 最小ログインページ読み込み完了');
        console.log('✅ 認証チェック: 無効');
        console.log('✅ 重定向: なし');
        console.log('✅ 外部スクリプト: なし');
        
        // フォーム送信処理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // ユーザーデータを作成
            const userData = {
                email: email,
                name: email.split('@')[0],
                loginTime: new Date().toISOString(),
                sessionId: 'minimal_' + Date.now(),
                source: 'minimal_login'
            };
            
            // ローカルストレージに保存
            localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
            
            alert('✅ ログイン成功！\nダッシュボードに移動します。');
            
            // ダッシュボードに移動
            window.location.href = 'master_dashboard.html';
        });
        
        // デモログイン
        function demoLogin() {
            const userData = {
                email: '<EMAIL>',
                name: 'Demo User (Minimal)',
                loginTime: new Date().toISOString(),
                sessionId: 'minimal_demo_' + Date.now(),
                source: 'minimal_demo'
            };
            
            localStorage.setItem('golden_ledger_user', JSON.stringify(userData));
            alert('🎉 デモログイン成功！');
            window.location.href = 'master_dashboard.html';
        }
        
        // ページ読み込み完了を確認
        window.addEventListener('load', function() {
            console.log('🎯 ページ読み込み完了 - 重定向は発生していません');
        });
        
        // 重定向を監視
        let redirectAttempts = 0;
        const originalLocation = window.location.href;
        
        setInterval(function() {
            if (window.location.href !== originalLocation) {
                redirectAttempts++;
                console.warn('⚠️ 予期しない重定向が検出されました:', window.location.href);
            }
        }, 100);
    </script>
</body>
</html>

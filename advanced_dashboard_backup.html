<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 高级数据分析仪表盘</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">📊 高级数据分析仪表盘</h1>
                    <p class="text-lg opacity-90">实时财务数据和AI性能监控</p>
                </div>
                <div class="text-right">
                    <div id="last-update" class="text-sm opacity-75">最后更新: --</div>
                    <button id="refresh-btn" class="mt-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        🔄 刷新数据
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="container mx-auto px-4 py-8">
        <!-- KPI Cards -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今月の売上</p>
                        <p id="monthly-revenue" class="text-2xl font-bold text-green-600">¥--</p>
                        <p id="revenue-change" class="text-sm text-gray-500">--</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">💰</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今月の費用</p>
                        <p id="monthly-expenses" class="text-2xl font-bold text-red-600">¥--</p>
                        <p id="expenses-change" class="text-sm text-gray-500">--</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">💸</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">総仕訳数</p>
                        <p id="total-entries" class="text-2xl font-bold text-blue-600">--</p>
                        <p id="entries-change" class="text-sm text-gray-500">--</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📋</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">AI処理成功率</p>
                        <p id="ai-success-rate" class="text-2xl font-bold text-purple-600">--%</p>
                        <p id="ai-change" class="text-sm text-gray-500">--</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">🤖</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Charts Section -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 收支趋势图 */
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📈 収支トレンド</h3>
                <canvas id="revenue-chart" width="400" height="200"></canvas>
            </div>

            <!-- AI处理统计 -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🧠 AI処理統計</h3>
                <canvas id="ai-stats-chart" width="400" height="200"></canvas>
            </div>
        </section>

        <!-- 科目分析 -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- 费用分析 */
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">💸 費用分析</h3>
                <canvas id="expenses-pie-chart" width="300" height="300"></canvas>
            </div>

            <!-- 最近交易 -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📋 最近の取引</h3>
                <div id="recent-transactions" class="space-y-3">
                    <div class="text-center text-gray-500 py-8">読み込み中...</div>
                </div>
            </div>

            <!-- AI性能指标 */
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">⚡ AI性能指標</h3>
                <div id="ai-metrics" class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">平均応答時間</span>
                        <span id="avg-response-time" class="font-semibold">--ms</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">平均信頼度</span>
                        <span id="avg-confidence" class="font-semibold">--%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">総処理数</span>
                        <span id="total-processed" class="font-semibold">--</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">今日の処理数</span>
                        <span id="today-processed" class="font-semibold">--</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 实时数据流 -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <h3 class="text-lg font-semibold mb-4">🔄 リアルタイムデータ</h3>
            <div id="realtime-log" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
                <div class="text-gray-500">システム起動中...</div>
            </div>
        </section>
    </main>

    <script>
        // 全局变量
        let revenueChart, aiStatsChart, expensesPieChart;
        let realtimeLog = [];

        // 添加实时日志
        function addRealtimeLog(message) {
            const timestamp = new Date().toLocaleTimeString('ja-JP');
            realtimeLog.unshift(`[${timestamp}] ${message}`);
            if (realtimeLog.length > 50) realtimeLog.pop();
            
            const logContainer = document.getElementById('realtime-log');
            logContainer.innerHTML = realtimeLog.map(log => `<div>${log}</div>`).join('');
        }

        // 获取仪表盘数据
        async function fetchDashboardData() {
            try {
                addRealtimeLog('📊 ダッシュボードデータを取得中...');

                const response = await fetch('/dashboard/summary/default');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 安全地更新KPI
                const monthlyRevenue = document.getElementById('monthly-revenue');
                const monthlyExpenses = document.getElementById('monthly-expenses');
                const totalEntries = document.getElementById('total-entries');
                const aiSuccessRate = document.getElementById('ai-success-rate');

                if (monthlyRevenue && data.financial) {
                    monthlyRevenue.textContent = `¥${(data.financial.monthly_revenue || 0).toLocaleString()}`;
                }
                if (monthlyExpenses && data.financial) {
                    monthlyExpenses.textContent = `¥${(data.financial.monthly_expenses || 0).toLocaleString()}`;
                }
                if (totalEntries && data.financial) {
                    totalEntries.textContent = (data.financial.total_entries || 0).toLocaleString();
                }
                if (aiSuccessRate && data.ai_stats) {
                    aiSuccessRate.textContent = `${((data.ai_stats.success_rate || 0) * 100).toFixed(1)}%`;
                }

                // 安全地更新AI指标
                const avgResponseTime = document.getElementById('avg-response-time');
                const avgConfidence = document.getElementById('avg-confidence');
                const totalProcessed = document.getElementById('total-processed');

                if (avgResponseTime && data.ai_stats) {
                    avgResponseTime.textContent = `${(data.ai_stats.avg_response_time || 0).toFixed(0)}ms`;
                }
                if (avgConfidence && data.ai_stats) {
                    avgConfidence.textContent = `${((data.ai_stats.avg_confidence || 0) * 100).toFixed(1)}%`;
                }
                if (totalProcessed && data.ai_stats) {
                    totalProcessed.textContent = (data.ai_stats.total_processed || 0).toLocaleString();
                }

                // 更新最后更新时间
                const lastUpdate = document.getElementById('last-update');
                if (lastUpdate) {
                    lastUpdate.textContent = `最后更新: ${new Date().toLocaleTimeString('ja-JP')}`;
                }

                addRealtimeLog('✅ ダッシュボードデータ更新完了');

                return data;
            } catch (error) {
                addRealtimeLog(`❌ データ取得エラー: ${error.message}`);
                console.error('Error fetching dashboard data:', error);

                // 显示错误状态
                const elements = ['monthly-revenue', 'monthly-expenses', 'total-entries', 'ai-success-rate'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = 'エラー';
                });
            }
        }

        // 获取交易数据
        async function fetchTransactions() {
            try {
                addRealtimeLog('📋 取引データを取得中...');
                
                const response = await fetch('/journal-entries/default');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const transactions = await response.json();
                
                const container = document.getElementById('recent-transactions');
                if (transactions.length === 0) {
                    container.innerHTML = '<div class="text-center text-gray-500 py-8">取引データがありません</div>';
                } else {
                    container.innerHTML = transactions.slice(0, 5).map(tx => `
                        <div class="border-l-4 ${tx.ai_generated ? 'border-blue-500' : 'border-gray-300'} pl-3 py-2">
                            <div class="text-sm font-medium">${tx.description}</div>
                            <div class="text-xs text-gray-500">
                                ${tx.debit_account} → ${tx.credit_account}
                            </div>
                            <div class="text-sm font-semibold text-green-600">
                                ¥${parseFloat(tx.amount).toLocaleString()}
                                ${tx.ai_generated ? '<span class="text-blue-500 ml-1">🤖</span>' : ''}
                            </div>
                        </div>
                    `).join('');
                }
                
                addRealtimeLog(`✅ ${transactions.length}件の取引データを表示`);
                
                return transactions;
            } catch (error) {
                addRealtimeLog(`❌ 取引データ取得エラー: ${error.message}`);
                console.error('Error fetching transactions:', error);
            }
        }

        // 初始化图表
        function initCharts() {
            try {
                addRealtimeLog('📊 チャート初期化中...');

                // 检查Chart.js是否加载
                if (typeof Chart === 'undefined') {
                    throw new Error('Chart.js未加载');
                }

                addRealtimeLog(`📊 Chart.js版本: ${Chart.version || 'unknown'}`);

                // 销毁现有图表
                if (window.revenueChart) {
                    window.revenueChart.destroy();
                }
                if (window.aiStatsChart) {
                    window.aiStatsChart.destroy();
                }
                if (window.expensesPieChart) {
                    window.expensesPieChart.destroy();
                }

                // 收支趋势图
                const revenueCtx = document.getElementById('revenue-chart');
                if (!revenueCtx) {
                    throw new Error('revenue-chart元素未找到');
                }

                addRealtimeLog('📈 创建收支趋势图...');
                window.revenueChart = new Chart(revenueCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '売上',
                        data: [1200000, 1350000, 1100000, 1450000, 1600000, 1750000],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }, {
                        label: '費用',
                        data: [800000, 850000, 900000, 920000, 950000, 980000],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // AI统计图
            const aiStatsCanvas = document.getElementById('ai-stats-chart');
            if (!aiStatsCanvas) {
                throw new Error('ai-stats-chart元素未找到');
            }

            addRealtimeLog('🤖 创建AI统计图...');
            const aiStatsCtx = aiStatsCanvas.getContext('2d');
            window.aiStatsChart = new Chart(aiStatsCtx, {
                type: 'bar',
                data: {
                    labels: ['成功', '失敗', '処理中'],
                    datasets: [{
                        label: 'AI処理結果',
                        data: [145, 8, 3],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(251, 191, 36, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 费用分析饼图
            const expensesPieCanvas = document.getElementById('expenses-pie-chart');
            if (!expensesPieCanvas) {
                throw new Error('expenses-pie-chart元素未找到');
            }

            addRealtimeLog('📊 创建费用分析饼图...');
            const expensesPieCtx = expensesPieCanvas.getContext('2d');
            window.expensesPieChart = new Chart(expensesPieCtx, {
                type: 'doughnut',
                data: {
                    labels: ['給料手当', '地代家賃', '水道光熱費', '消耗品費', 'その他'],
                    datasets: [{
                        data: [400000, 150000, 50000, 30000, 80000],
                        backgroundColor: [
                            '#3b82f6',
                            '#8b5cf6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            addRealtimeLog('📊 チャート初期化完了');

            } catch (error) {
                addRealtimeLog(`❌ チャート初期化エラー: ${error.message}`);
                console.error('Chart initialization error:', error);

                // 隐藏图表容器，显示错误信息
                const chartContainers = ['revenue-chart', 'ai-stats-chart', 'expenses-pie-chart'];
                chartContainers.forEach(id => {
                    const container = document.getElementById(id);
                    if (container) {
                        container.style.display = 'none';
                        const parent = container.parentElement;
                        if (parent) {
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'text-center text-red-500 py-8';
                            errorDiv.textContent = 'チャート読み込みエラー';
                            parent.appendChild(errorDiv);
                        }
                    }
                });
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addRealtimeLog('🚀 システム起動中...');

            // 延迟初始化，确保Chart.js完全加载
            setTimeout(() => {
                // 初始化图表
                initCharts();

                // 获取初始数据
                fetchDashboardData();
                fetchTransactions();
            }, 200);
            
            // 刷新按钮
            document.getElementById('refresh-btn').addEventListener('click', function() {
                addRealtimeLog('🔄 手動データ更新開始');
                fetchDashboardData();
                fetchTransactions();
            });
            
            // 定期更新数据
            setInterval(() => {
                fetchDashboardData();
                fetchTransactions();
            }, 30000); // 30秒更新一次
            
            addRealtimeLog('✅ システム起動完了');
        });
    </script>
</body>
</html>

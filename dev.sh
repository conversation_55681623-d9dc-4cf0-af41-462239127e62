#!/bin/bash

# GoldenLedger 本地开发服务器启动脚本

echo "🚀 启动 GoldenLedger 本地开发服务器..."

# 检查Python3是否可用
if command -v python3 &> /dev/null; then
    echo "✅ 使用 Python3 启动服务器"
    python3 start_local_server.py
elif command -v python &> /dev/null; then
    echo "✅ 使用 Python 启动服务器"
    python start_local_server.py
else
    echo "❌ 未找到 Python，请安装 Python 3.x"
    echo "💡 macOS: brew install python"
    echo "💡 Ubuntu: sudo apt install python3"
    echo "💡 Windows: 从 python.org 下载安装"
    exit 1
fi

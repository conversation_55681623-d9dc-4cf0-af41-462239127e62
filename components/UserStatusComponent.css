/**
 * ユーザーステータスコンポーネント CSS
 * 美しく機能的なユーザー状態表示
 */

/* ユーザーステータスカード */
.user-status-card {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.user-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 最小化状態 */
.user-status-minimized {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.user-status-minimized:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* アバター画像 */
.user-status-card img,
.user-status-minimized img {
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;
}

.user-status-card img:hover,
.user-status-minimized img:hover {
    border-color: #8B5CF6;
    transform: scale(1.1);
}

/* オンライン状態インジケーター */
.user-status-card .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* ボタンアニメーション */
.user-status-card button,
.user-status-minimized button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.user-status-card button:hover,
.user-status-minimized button:hover {
    transform: translateY(-1px);
}

.user-status-card button:active,
.user-status-minimized button:active {
    transform: translateY(0);
}

/* 折りたたみボタン特別スタイル */
#toggle-status-btn,
#expand-status-btn {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    font-weight: bold;
}

#toggle-status-btn:hover,
#expand-status-btn:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

/* ログアウトボタン特別スタイル */
#logout-btn {
    background: linear-gradient(135deg, #EF4444, #DC2626);
}

#logout-btn:hover {
    background: linear-gradient(135deg, #DC2626, #B91C1C);
}

/* プロフィールボタン */
#user-profile-btn:hover {
    background: linear-gradient(135deg, #F3F4F6, #E5E7EB);
}

/* レスポンシブ対応 */
@media (max-width: 640px) {
    .user-status-card {
        min-width: 260px;
        margin: 0 8px;
    }
    
    .user-status-card .text-sm {
        font-size: 0.8rem;
    }
    
    .user-status-card .text-xs {
        font-size: 0.7rem;
    }
}

/* ダークモード対応 */
@media (prefers-color-scheme: dark) {
    .user-status-card,
    .user-status-minimized {
        background: rgba(31, 41, 55, 0.9);
        border-color: rgba(75, 85, 99, 0.3);
    }
    
    .user-status-card .text-gray-800 {
        color: #F9FAFB;
    }
    
    .user-status-card .text-gray-500 {
        color: #9CA3AF;
    }
    
    .user-status-card .text-gray-700 {
        color: #D1D5DB;
    }
    
    #user-profile-btn {
        background: rgba(55, 65, 81, 0.8);
        color: #F9FAFB;
    }
    
    #user-profile-btn:hover {
        background: rgba(75, 85, 99, 0.8);
    }
}

/* アニメーション効果 */
.user-status-card {
    animation: slideInFromTop 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-status-minimized {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 位置調整用クラス */
.user-status-top-left {
    top: 1rem;
    left: 1rem;
}

.user-status-top-right {
    top: 1rem;
    right: 1rem;
}

.user-status-bottom-left {
    bottom: 1rem;
    left: 1rem;
}

.user-status-bottom-right {
    bottom: 1rem;
    right: 1rem;
}

/* Z-index管理 */
#user-status-component {
    z-index: 9999;
}

/* ホバー効果の改善 */
.user-status-card:hover .w-2.h-2.bg-green-500 {
    animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);
}

/* フォーカス状態 */
.user-status-card button:focus,
.user-status-minimized button:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
}

/* 高コントラストモード対応 */
@media (prefers-contrast: high) {
    .user-status-card,
    .user-status-minimized {
        border-width: 2px;
        border-color: #000;
    }
    
    .user-status-card button,
    .user-status-minimized button {
        border: 1px solid #000;
    }
}

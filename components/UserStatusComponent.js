/**
 * ユーザーステータスコンポーネント
 * 全ページで統一されたユーザー状態表示
 */

class UserStatusComponent {
    constructor(options = {}) {
        this.options = {
            position: options.position || 'top-left', // top-left, top-right, bottom-left, bottom-right
            showRole: options.showRole !== false,
            showLoginTime: options.showLoginTime !== false,
            autoHide: options.autoHide || false,
            hideDelay: options.hideDelay || 5000,
            ...options
        };
        
        this.isMinimized = false;
        this.user = null;
        this.loginTime = null;
        
        this.init();
    }

    async init() {
        await this.loadUserData();
        this.createComponent();
        this.bindEvents();
        
        if (this.options.autoHide) {
            this.setupAutoHide();
        }
    }

    async loadUserData() {
        try {
            // 从localStorage获取用户数据
            const token = localStorage.getItem('goldenledger_session_token');
            const userData = localStorage.getItem('goldenledger_user');
            
            if (token && userData) {
                this.user = JSON.parse(userData);
                this.loginTime = localStorage.getItem('goldenledger_login_time') || Date.now();
            }
        } catch (error) {
            console.error('Failed to load user data:', error);
        }
    }

    createComponent() {
        if (!this.user) return;

        const positionClasses = this.getPositionClasses();
        
        const componentHTML = `
            <div id="user-status-component" class="fixed ${positionClasses} z-50">
                <!-- 展開状態 -->
                <div class="user-status-card bg-white/90 backdrop-blur-md rounded-xl shadow-lg border border-gray-200 p-4 min-w-[280px]" style="display: block;">
                    <!-- ユーザー情報 -->
                    <div class="flex items-center space-x-3 mb-3">
                        <img src="${this.user.avatar_url || this.getDefaultAvatar()}" alt="${this.user.name || 'ユーザー'}" class="w-10 h-10 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="text-sm font-semibold text-gray-800">
                                「${this.user.name || this.user.username || 'ユーザー'}」でログイン中
                            </div>
                            <div class="text-xs text-gray-500">
                                ${this.user.email || ''}
                            </div>
                        </div>
                        <div class="flex items-center space-x-1">
                            <!-- オンライン状態インジケーター -->
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-xs text-green-600">オンライン</span>
                        </div>
                    </div>

                    ${this.options.showRole || this.options.showLoginTime ? `
                    <!-- ユーザー役割とログイン時間 -->
                    <div class="text-xs text-gray-500 mb-3 space-y-1">
                        ${this.options.showRole ? `
                        <div class="flex justify-between">
                            <span>役割:</span>
                            <span class="font-medium">${this.getUserRole()}</span>
                        </div>
                        ` : ''}
                        ${this.options.showLoginTime ? `
                        <div class="flex justify-between">
                            <span>ログイン:</span>
                            <span>${this.getLoginTimeText()}</span>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}

                    <!-- 操作ボタン -->
                    <div class="flex space-x-2">
                        <button id="user-profile-btn" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs py-2 px-3 rounded-lg transition-colors">
                            プロフィール
                        </button>
                        <button id="logout-btn" class="flex-1 bg-red-500 hover:bg-red-600 text-white text-xs py-2 px-3 rounded-lg transition-colors">
                            ログアウト
                        </button>
                    </div>

                    <!-- 折りたたみ/展開ボタン -->
                    <button id="toggle-status-btn" class="absolute -right-2 -top-2 w-6 h-6 bg-purple-500 hover:bg-purple-600 text-white rounded-full text-xs transition-colors">
                        −
                    </button>
                </div>

                <!-- 最小化状態 -->
                <div id="minimized-status" class="user-status-minimized bg-white/90 backdrop-blur-md rounded-full shadow-lg border border-gray-200 p-2 hidden">
                    <div class="flex items-center space-x-2">
                        <img src="${this.user.avatar_url || this.getDefaultAvatar()}" alt="${this.user.name || 'ユーザー'}" class="w-8 h-8 rounded-full object-cover">
                        <span class="text-sm font-medium text-gray-800 hidden sm:block">${this.user.name || this.user.username || 'ユーザー'}</span>
                        <button id="expand-status-btn" class="w-6 h-6 bg-purple-500 hover:bg-purple-600 text-white rounded-full text-xs transition-colors">
                            +
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', componentHTML);
    }

    bindEvents() {
        const toggleBtn = document.getElementById('toggle-status-btn');
        const expandBtn = document.getElementById('expand-status-btn');
        const profileBtn = document.getElementById('user-profile-btn');
        const logoutBtn = document.getElementById('logout-btn');

        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.minimize());
        }

        if (expandBtn) {
            expandBtn.addEventListener('click', () => this.expand());
        }

        if (profileBtn) {
            profileBtn.addEventListener('click', () => this.openProfile());
        }

        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }
    }

    minimize() {
        const card = document.querySelector('.user-status-card');
        const minimized = document.getElementById('minimized-status');
        
        if (card && minimized) {
            card.style.display = 'none';
            minimized.classList.remove('hidden');
            this.isMinimized = true;
        }
    }

    expand() {
        const card = document.querySelector('.user-status-card');
        const minimized = document.getElementById('minimized-status');
        
        if (card && minimized) {
            card.style.display = 'block';
            minimized.classList.add('hidden');
            this.isMinimized = false;
        }
    }

    openProfile() {
        // プロフィールページに移動
        window.location.href = '/profile.html';
    }

    logout() {
        if (confirm('ログアウトしますか？')) {
            // ローカルストレージをクリア
            localStorage.removeItem('goldenledger_session_token');
            localStorage.removeItem('goldenledger_user');
            localStorage.removeItem('goldenledger_login_time');
            
            // ログインページにリダイレクト
            window.location.href = '/auth/login.html';
        }
    }

    getPositionClasses() {
        switch (this.options.position) {
            case 'top-right':
                return 'top-4 right-4';
            case 'bottom-left':
                return 'bottom-4 left-4';
            case 'bottom-right':
                return 'bottom-4 right-4';
            case 'top-left':
            default:
                return 'top-4 left-4';
        }
    }

    getUserRole() {
        if (this.user.role) {
            const roleMap = {
                'admin': '管理者',
                'premium': 'プレミアムユーザー',
                'basic': 'ベーシックユーザー',
                'free': '一般ユーザー'
            };
            return roleMap[this.user.role] || this.user.role;
        }
        return '一般ユーザー';
    }

    getLoginTimeText() {
        if (!this.loginTime) return 'たった今';
        
        const now = Date.now();
        const loginTime = typeof this.loginTime === 'string' ? parseInt(this.loginTime) : this.loginTime;
        const diff = now - loginTime;
        
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 1) return 'たった今';
        if (minutes < 60) return `${minutes}分前`;
        if (hours < 24) return `${hours}時間前`;
        return `${days}日前`;
    }

    getDefaultAvatar() {
        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2MzY2RjEiLz4KPHBhdGggZD0iTTIwIDIwQzIyLjc2MTQgMjAgMjUgMTcuNzYxNCAyNSAxNUMyNSAxMi4yMzg2IDIyLjc2MTQgMTAgMjAgMTBDMTcuMjM4NiAxMCAxNSAxMi4yMzg2IDE1IDE1QzE1IDE3Ljc2MTQgMTcuMjM4NiAyMCAyMCAyMFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xMCAzMEMxMCAyNS4wMjk0IDEzLjU4MTcgMjEgMTggMjFIMjJDMjYuNDE4MyAyMSAzMCAyNS4wMjk0IDMwIDMwVjMwSDEwVjMwWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+';
    }

    setupAutoHide() {
        let hideTimeout;
        const component = document.getElementById('user-status-component');
        
        if (component) {
            const resetTimeout = () => {
                clearTimeout(hideTimeout);
                hideTimeout = setTimeout(() => {
                    if (!this.isMinimized) {
                        this.minimize();
                    }
                }, this.options.hideDelay);
            };

            component.addEventListener('mouseenter', () => clearTimeout(hideTimeout));
            component.addEventListener('mouseleave', resetTimeout);
            
            resetTimeout();
        }
    }

    // 静态方法：检查用户是否已登录
    static isLoggedIn() {
        const token = localStorage.getItem('goldenledger_session_token');
        const user = localStorage.getItem('goldenledger_user');
        return !!(token && user);
    }

    // 静态方法：获取当前用户
    static getCurrentUser() {
        try {
            const userData = localStorage.getItem('goldenledger_user');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            return null;
        }
    }
}

// 导出供全局使用
window.UserStatusComponent = UserStatusComponent;

#!/usr/bin/env python3
"""
调试AI响应格式
"""
import google.generativeai as genai
import re
import json

def debug_ai_response():
    """调试AI响应格式"""
    
    # 配置API
    genai.configure(api_key='YOUR_GEMINI_API_KEY_HERE')
    
    # 创建模型
    model = genai.GenerativeModel('gemini-1.5-flash')
    
    # 使用与后端相同的提示
    text = "今日コンビニで事務用品を1200円で購入"
    parsed_text = "2025年07月11日コンビニで事務用品を1200円で購入"
    
    prompt = f"""
        作为专业会计师，请分析以下交易描述并提取关键信息：

        原始交易描述: {text}
        处理后描述: {parsed_text}
        解析的日期: 2025-07-11
        解析的时间: 11:57:07

        请提取以下信息并以JSON格式返回：
        {{
            "transaction_type": "收入/支出/转账",
            "amount": "金额(数字)",
            "currency": "货币代码",
            "description": "交易描述",
            "category": "交易类别",
            "payment_method": "支付方式",
            "suggested_debit_account": "建议借方科目",
            "suggested_credit_account": "建议贷方科目",
            "confidence": "置信度(0-1)"
        }}

        注意：
        1. 描述字段应该使用处理后的描述，将时间代词替换为具体日期时间
        2. 日期时间字段必须使用解析器提供的准确值
        3. 不要在描述中保留"今天"、"昨天"等代词
        """
    
    print("🧪 调试AI响应格式")
    print("=" * 50)
    print(f"📝 提示: {prompt}")
    print("=" * 50)
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text
        
        print(f"📥 AI原始响应:")
        print(response_text)
        print("=" * 50)
        
        # 尝试提取JSON
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            json_text = json_match.group()
            print(f"🔍 提取的JSON文本:")
            print(json_text)
            print("=" * 50)
            
            try:
                parsed_data = json.loads(json_text)
                print(f"✅ JSON解析成功:")
                print(json.dumps(parsed_data, ensure_ascii=False, indent=2))
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"   问题位置: {e.pos}")
                return False
        else:
            print(f"❌ 未找到JSON格式")
            return False
            
    except Exception as e:
        print(f"❌ AI调用失败: {e}")
        return False

if __name__ == "__main__":
    debug_ai_response()

#!/usr/bin/env python3
"""
专业附件导出功能测试脚本
测试所有附件导出相关的API端点
"""

import requests
import json
import time
import os
from pathlib import Path

BASE_URL = "http://localhost:8000"

def print_header(title):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")

def test_attachment_statistics():
    """测试附件统计API"""
    print_header("测试附件统计功能")
    
    try:
        response = requests.get(f"{BASE_URL}/api/export/statistics")
        if response.status_code == 200:
            data = response.json()
            print_result("获取统计数据", True, 
                        f"附件数量: {data.get('attachment_count', 0)}, "
                        f"总大小: {data.get('attachment_size', 0)} bytes")
            
            # 显示详细统计
            if 'attachment_types' in data:
                print("   文件类型分布:")
                for ext, info in data['attachment_types'].items():
                    print(f"     {ext}: {info.get('count', 0)}个文件")
            return True
        else:
            print_result("获取统计数据", False, f"HTTP {response.status_code}")
            return False
    except Exception as e:
        print_result("获取统计数据", False, str(e))
        return False

def test_attachment_list():
    """测试附件列表API"""
    print_header("测试附件列表功能")
    
    try:
        response = requests.get(f"{BASE_URL}/api/attachments/list")
        if response.status_code == 200:
            data = response.json()
            total_count = data.get('total_count', 0)
            total_size = data.get('total_size_formatted', '0 B')
            
            print_result("获取附件列表", True, 
                        f"总数: {total_count}, 总大小: {total_size}")
            
            # 显示前3个附件
            attachments = data.get('attachments', [])
            if attachments:
                print("   前3个附件:")
                for i, att in enumerate(attachments[:3]):
                    print(f"     {i+1}. {att['filename']} ({att['size_formatted']})")
            
            return attachments
        else:
            print_result("获取附件列表", False, f"HTTP {response.status_code}")
            return []
    except Exception as e:
        print_result("获取附件列表", False, str(e))
        return []

def test_attachment_analysis():
    """测试附件分析API"""
    print_header("测试附件分析功能")
    
    try:
        response = requests.get(f"{BASE_URL}/api/attachments/analysis")
        if response.status_code == 200:
            data = response.json()
            print_result("获取分析报告", True, 
                        f"总记录: {data.get('total_entries', 0)}, "
                        f"有附件记录: {data.get('entries_with_attachments', 0)}, "
                        f"孤立附件: {data.get('orphaned_attachments', 0)}")
            
            # 显示文件类型分布
            file_types = data.get('file_type_distribution', {})
            if file_types:
                print("   文件类型分布:")
                for ext, count in file_types.items():
                    print(f"     {ext}: {count}个")
            
            # 显示大小分布
            size_dist = data.get('size_distribution', {})
            if size_dist:
                print("   文件大小分布:")
                print(f"     小文件(<1MB): {size_dist.get('small', 0)}个")
                print(f"     中等文件(1-10MB): {size_dist.get('medium', 0)}个")
                print(f"     大文件(>10MB): {size_dist.get('large', 0)}个")
            
            return True
        else:
            print_result("获取分析报告", False, f"HTTP {response.status_code}")
            return False
    except Exception as e:
        print_result("获取分析报告", False, str(e))
        return False

def test_export_all_attachments():
    """测试导出所有附件"""
    print_header("测试导出所有附件")
    
    try:
        payload = {
            "export_type": "all",
            "format": "zip",
            "include_metadata": True
        }
        
        response = requests.post(f"{BASE_URL}/api/attachments/export", 
                               json=payload, stream=True)
        
        if response.status_code == 200:
            # 保存下载的文件
            filename = f"test_all_attachments_{int(time.time())}.zip"
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filename)
            print_result("导出所有附件", True, 
                        f"文件已保存: {filename}, 大小: {file_size} bytes")
            return filename
        else:
            print_result("导出所有附件", False, f"HTTP {response.status_code}")
            return None
    except Exception as e:
        print_result("导出所有附件", False, str(e))
        return None

def test_export_by_type():
    """测试按类型导出附件"""
    print_header("测试按类型导出附件")
    
    # 测试导出图片文件
    try:
        payload = {
            "export_type": "by_type",
            "format": "zip",
            "file_types": [".jpg", ".jpeg", ".png", ".gif"],
            "include_metadata": True
        }
        
        response = requests.post(f"{BASE_URL}/api/attachments/export", 
                               json=payload, stream=True)
        
        if response.status_code == 200:
            filename = f"test_images_{int(time.time())}.zip"
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filename)
            print_result("导出图片文件", True, 
                        f"文件已保存: {filename}, 大小: {file_size} bytes")
            return filename
        else:
            print_result("导出图片文件", False, f"HTTP {response.status_code}")
            return None
    except Exception as e:
        print_result("导出图片文件", False, str(e))
        return None

def test_export_selected_files(attachments):
    """测试导出选中文件"""
    print_header("测试导出选中文件")
    
    if not attachments:
        print_result("导出选中文件", False, "没有可选择的附件")
        return None
    
    # 选择前3个文件
    selected_files = [att['filename'] for att in attachments[:3]]
    
    try:
        payload = {
            "export_type": "selected",
            "format": "zip",
            "selected_files": selected_files,
            "include_metadata": True
        }
        
        response = requests.post(f"{BASE_URL}/api/attachments/export", 
                               json=payload, stream=True)
        
        if response.status_code == 200:
            filename = f"test_selected_{int(time.time())}.zip"
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filename)
            print_result("导出选中文件", True, 
                        f"文件已保存: {filename}, 大小: {file_size} bytes, "
                        f"选中文件数: {len(selected_files)}")
            return filename
        else:
            print_result("导出选中文件", False, f"HTTP {response.status_code}")
            return None
    except Exception as e:
        print_result("导出选中文件", False, str(e))
        return None

def test_export_by_date():
    """测试按日期导出附件"""
    print_header("测试按日期导出附件")
    
    try:
        payload = {
            "export_type": "by_date",
            "format": "zip",
            "date_range": {
                "start_date": "2025-07-11",
                "end_date": "2025-07-12"
            },
            "include_metadata": True
        }
        
        response = requests.post(f"{BASE_URL}/api/attachments/export", 
                               json=payload, stream=True)
        
        if response.status_code == 200:
            filename = f"test_by_date_{int(time.time())}.zip"
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filename)
            print_result("按日期导出附件", True, 
                        f"文件已保存: {filename}, 大小: {file_size} bytes")
            return filename
        else:
            print_result("按日期导出附件", False, f"HTTP {response.status_code}")
            return None
    except Exception as e:
        print_result("按日期导出附件", False, str(e))
        return None

def main():
    """主测试函数"""
    print("🚀 开始测试专业附件导出功能")
    print(f"测试服务器: {BASE_URL}")
    
    # 测试统计功能
    test_attachment_statistics()
    
    # 测试列表功能
    attachments = test_attachment_list()
    
    # 测试分析功能
    test_attachment_analysis()
    
    # 测试各种导出功能
    test_export_all_attachments()
    test_export_by_type()
    test_export_selected_files(attachments)
    test_export_by_date()
    
    print_header("测试完成")
    print("📁 检查当前目录下的测试文件:")
    
    # 列出生成的测试文件
    test_files = [f for f in os.listdir('.') if f.startswith('test_') and f.endswith('.zip')]
    for i, filename in enumerate(test_files, 1):
        file_size = os.path.getsize(filename)
        print(f"   {i}. {filename} ({file_size} bytes)")
    
    print(f"\n✅ 总共生成了 {len(test_files)} 个测试文件")
    print("💡 您可以解压这些ZIP文件查看导出内容")

if __name__ == "__main__":
    main()

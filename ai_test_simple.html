<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">AI功能测试</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">输入测试</h2>
            <div class="space-y-4">
                <input 
                    type="text" 
                    id="testInput" 
                    placeholder="输入记账描述，例如：购买办公用品1000円"
                    class="w-full p-3 border border-gray-300 rounded-lg"
                    value="购买办公用品1000円"
                >
                <button 
                    onclick="testAI()" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600"
                >
                    测试AI处理
                </button>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">处理结果</h2>
            <div id="result" class="space-y-4">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>
    </div>

    <script>
        async function testAI() {
            const input = document.getElementById('testInput').value;
            const resultDiv = document.getElementById('result');
            
            if (!input.trim()) {
                resultDiv.innerHTML = '<p class="text-red-500">请输入测试内容</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p class="text-blue-500">处理中...</p>';
            
            try {
                console.log('发送请求:', input);
                
                const response = await fetch('/ai-bookkeeping/natural-language', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: input,
                        company_id: 'test'
                    })
                });
                
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                if (data.success) {
                    const journal = data.journal_entry;
                    resultDiv.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h3 class="text-green-800 font-semibold mb-2">✅ 处理成功</h3>
                            <div class="space-y-2 text-sm">
                                <p><strong>ID:</strong> ${journal.id}</p>
                                <p><strong>日期:</strong> ${journal.entry_date}</p>
                                <p><strong>描述:</strong> ${journal.description}</p>
                                <p><strong>借方:</strong> ${journal.debit_account}</p>
                                <p><strong>贷方:</strong> ${journal.credit_account}</p>
                                <p><strong>金额:</strong> ¥${journal.amount.toLocaleString()}</p>
                                <p><strong>置信度:</strong> ${(data.confidence * 100).toFixed(1)}%</p>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h3 class="text-red-800 font-semibold mb-2">❌ 处理失败</h3>
                            <p class="text-red-700">${data.error || '未知错误'}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('错误:', error);
                resultDiv.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 class="text-red-800 font-semibold mb-2">❌ 请求失败</h3>
                        <p class="text-red-700">${error.message}</p>
                    </div>
                `;
            }
        }
        
        // 页面加载时测试连接
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/health');
                if (response.ok) {
                    console.log('✅ 后端服务连接正常');
                } else {
                    console.log('⚠️ 后端服务响应异常');
                }
            } catch (error) {
                console.log('❌ 后端服务连接失败:', error);
            }
        });
    </script>
</body>
</html>

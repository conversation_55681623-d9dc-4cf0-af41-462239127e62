#!/usr/bin/env python3
"""
测试附件预览功能
"""
import requests
import json

def test_attachment_preview():
    """测试附件预览功能"""
    
    print("🧪 测试附件预览功能")
    print("=" * 30)
    
    # 使用刚才保存的记录ID
    entry_id = "J20250711135759"
    
    # 1. 测试附件预览
    print(f"\n👁️ 测试附件预览 (Entry ID: {entry_id})")
    attachment_url = f"http://localhost:8000/attachments/{entry_id}"
    
    try:
        attachment_response = requests.get(attachment_url, timeout=30)
        
        if attachment_response.status_code == 200:
            attachment_result = attachment_response.json()
            print(f"✅ 附件预览成功")
            print(f"   文件名: {attachment_result['filename']}")
            print(f"   文件类型: {attachment_result['content_type']}")
            print(f"   文件大小: {len(attachment_result['file_content'])} bytes (base64)")
            
        else:
            print(f"❌ 附件预览失败: {attachment_response.status_code}")
            print(f"响应: {attachment_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 2. 测试获取仕訳记录列表
    print(f"\n📋 测试获取仕訳记录列表")
    entries_url = "http://localhost:8000/journal-entries/default"
    
    try:
        entries_response = requests.get(entries_url, timeout=30)
        
        if entries_response.status_code == 200:
            entries_result = entries_response.json()
            print(f"✅ 获取仕訳记录列表成功")
            print(f"   总记录数: {len(entries_result)}")
            
            # 查找有附件的记录
            entries_with_attachments = [e for e in entries_result if e.get('attachment_path')]
            print(f"   有附件的记录数: {len(entries_with_attachments)}")
            
            if entries_with_attachments:
                print(f"   有附件的记录:")
                for entry in entries_with_attachments[-3:]:  # 显示最近3条
                    print(f"     - ID: {entry['id']}")
                    print(f"       描述: {entry['description']}")
                    print(f"       附件: {entry['attachment_path']}")
                    print(f"       日期: {entry['entry_date']}")
                    
        else:
            print(f"❌ 获取仕訳记录列表失败: {entries_response.status_code}")
            print(f"响应: {entries_response.text}")
    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_attachment_preview()

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google用户信息测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">🔍 Google OAuth用户信息测试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 当前用户信息 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">👤 当前用户信息</h2>
                <div id="currentUserInfo" class="space-y-2 text-sm font-mono"></div>
            </div>
            
            <!-- Google API测试 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🔐 Google API测试</h2>
                <button onclick="testGoogleAPI()" class="bg-blue-500 text-white px-4 py-2 rounded mb-4">测试Google用户信息API</button>
                <div id="googleAPIResult" class="text-sm font-mono"></div>
            </div>
            
            <!-- 数据库用户信息 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🗄️ 数据库用户信息</h2>
                <button onclick="testDatabaseUser()" class="bg-green-500 text-white px-4 py-2 rounded mb-4">获取数据库用户信息</button>
                <div id="databaseUserResult" class="text-sm font-mono"></div>
            </div>
            
            <!-- 头像测试 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">🖼️ 头像测试</h2>
                <div id="avatarTest" class="space-y-4"></div>
            </div>
        </div>
    </div>

    <script src="api_config.js"></script>
    <script>
        // 显示当前用户信息
        function displayCurrentUserInfo() {
            const container = document.getElementById('currentUserInfo');
            const token = localStorage.getItem('goldenledger_session_token');
            const user = JSON.parse(localStorage.getItem('goldenledger_user') || 'null');
            
            let html = '';
            html += `<div><strong>Session Token:</strong> ${token ? '✅ 存在' : '❌ 不存在'}</div>`;
            
            if (user) {
                html += `<div><strong>用户ID:</strong> ${user.id || 'N/A'}</div>`;
                html += `<div><strong>用户名:</strong> ${user.username || 'N/A'}</div>`;
                html += `<div><strong>姓名:</strong> ${user.name || 'N/A'}</div>`;
                html += `<div><strong>邮箱:</strong> ${user.email || 'N/A'}</div>`;
                html += `<div><strong>Google ID:</strong> ${user.google_id || 'N/A'}</div>`;
                html += `<div><strong>头像URL:</strong> ${user.avatar_url || 'N/A'}</div>`;
                html += `<div><strong>角色:</strong> ${user.role || 'N/A'}</div>`;
                html += `<div><strong>最后登录:</strong> ${user.last_login_at || 'N/A'}</div>`;
            } else {
                html += '<div>❌ 没有用户信息</div>';
            }
            
            container.innerHTML = html;
        }
        
        // 测试Google API
        async function testGoogleAPI() {
            const container = document.getElementById('googleAPIResult');
            container.innerHTML = '🔄 测试中...';
            
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    container.innerHTML = '❌ 没有session token';
                    return;
                }
                
                const user = JSON.parse(localStorage.getItem('goldenledger_user') || 'null');
                if (!user || !user.google_access_token) {
                    container.innerHTML = '❌ 没有Google access token';
                    return;
                }
                
                // 直接调用Google用户信息API
                const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
                    headers: {
                        'Authorization': `Bearer ${user.google_access_token}`
                    }
                });
                
                if (response.ok) {
                    const googleUser = await response.json();
                    let html = '<div class="text-green-600">✅ Google API调用成功</div>';
                    html += `<div><strong>ID:</strong> ${googleUser.id || 'N/A'}</div>`;
                    html += `<div><strong>Name:</strong> ${googleUser.name || 'N/A'}</div>`;
                    html += `<div><strong>Email:</strong> ${googleUser.email || 'N/A'}</div>`;
                    html += `<div><strong>Picture:</strong> ${googleUser.picture || 'N/A'}</div>`;
                    html += `<div><strong>Verified Email:</strong> ${googleUser.verified_email || 'N/A'}</div>`;
                    html += `<div><strong>Locale:</strong> ${googleUser.locale || 'N/A'}</div>`;
                    
                    container.innerHTML = html;
                    
                    // 测试头像
                    testAvatar(googleUser.picture);
                } else {
                    const errorText = await response.text();
                    container.innerHTML = `❌ Google API调用失败: ${response.status} - ${errorText}`;
                }
            } catch (error) {
                container.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }
        
        // 测试数据库用户信息
        async function testDatabaseUser() {
            const container = document.getElementById('databaseUserResult');
            container.innerHTML = '🔄 测试中...';
            
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) {
                    container.innerHTML = '❌ 没有session token';
                    return;
                }
                
                // 等待 API 配置加载
                let retries = 0;
                while (!window.GoldenLedgerAPI && retries < 10) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    retries++;
                }
                
                if (!window.GoldenLedgerAPI) {
                    container.innerHTML = '❌ API配置未加载';
                    return;
                }
                
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/verify'), {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success && result.authenticated) {
                    const user = result.user;
                    let html = '<div class="text-green-600">✅ 数据库用户信息获取成功</div>';
                    html += `<div><strong>ID:</strong> ${user.id || 'N/A'}</div>`;
                    html += `<div><strong>Username:</strong> ${user.username || 'N/A'}</div>`;
                    html += `<div><strong>Name:</strong> ${user.name || 'N/A'}</div>`;
                    html += `<div><strong>Email:</strong> ${user.email || 'N/A'}</div>`;
                    html += `<div><strong>Google ID:</strong> ${user.google_id || 'N/A'}</div>`;
                    html += `<div><strong>Avatar URL:</strong> ${user.avatar_url || 'N/A'}</div>`;
                    html += `<div><strong>Role:</strong> ${user.role || 'N/A'}</div>`;
                    html += `<div><strong>Last Login:</strong> ${user.last_login_at || 'N/A'}</div>`;
                    
                    container.innerHTML = html;
                    
                    // 测试头像
                    testAvatar(user.avatar_url);
                } else {
                    container.innerHTML = `❌ 数据库用户信息获取失败: ${result.error || '未知错误'}`;
                }
            } catch (error) {
                container.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }
        
        // 测试头像
        function testAvatar(avatarUrl) {
            const container = document.getElementById('avatarTest');
            
            if (!avatarUrl) {
                container.innerHTML = '<div class="text-red-600">❌ 没有头像URL</div>';
                return;
            }
            
            let html = `<div><strong>头像URL:</strong> ${avatarUrl}</div>`;
            html += '<div class="mt-2">';
            html += `<img src="${avatarUrl}" alt="用户头像" class="w-16 h-16 rounded-full border-2 border-gray-300" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">`;
            html += '<div style="display:none;" class="text-red-600">❌ 头像加载失败</div>';
            html += '</div>';
            
            container.innerHTML = html;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayCurrentUserInfo();
            
            // 每5秒刷新一次状态
            setInterval(() => {
                displayCurrentUserInfo();
            }, 5000);
        });
    </script>
</body>
</html>

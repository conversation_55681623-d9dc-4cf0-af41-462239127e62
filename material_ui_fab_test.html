<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material-UI FAB Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .comparison-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .comparison-card h3 {
            margin: 0 0 15px 0;
            color: #FFD700;
        }

        .fab-preview {
            background: rgba(255, 255, 255, 0.05);
            padding: 40px;
            border-radius: 8px;
            margin: 15px 0;
            position: relative;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .original-fab {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: linear-gradient(45deg, #FF69B4, #FFB6C1);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 4px 20px rgba(255, 105, 180, 0.4);
            transition: all 0.3s ease;
        }

        .original-fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(255, 105, 180, 0.6);
        }

        .status-panel {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            text-align: left;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .btn {
            background: #FF69B4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #FF1493;
            transform: translateY(-2px);
        }

        .feature-list {
            text-align: left;
            margin: 15px 0;
        }

        .feature-list li {
            margin: 8px 0;
            font-size: 14px;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.4);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
            margin: 15px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 Material-UI FAB Style Comparison</h1>
        <p>Comparing the original jiajibo Material-UI FAB with our implementation</p>

        <div class="comparison-grid">
            <div class="comparison-card">
                <h3>📋 Original jiajibo FAB</h3>
                <div class="fab-preview">
                    <button class="original-fab">🌸</button>
                </div>
                <div class="code-block">
&lt;button class="MuiButtonBase-root MuiFab-root MuiFab-circular MuiFab-sizeLarge MuiFab-default" 
        tabindex="0" type="button" aria-label="さくらちゃんに相談"&gt;
  &lt;span class="MuiBadge-root"&gt;
    &lt;div class="MuiBox-root"&gt;🌸&lt;/div&gt;
    &lt;span class="MuiBadge-badge MuiBadge-invisible"&gt;0&lt;/span&gt;
  &lt;/span&gt;
  &lt;span class="MuiTouchRipple-root"&gt;&lt;/span&gt;
&lt;/button&gt;
                </div>
                <ul class="feature-list">
                    <li>✅ Material-UI class names</li>
                    <li>✅ Touch ripple effect</li>
                    <li>✅ Badge system</li>
                    <li>✅ Accessibility attributes</li>
                    <li>✅ Gradient background</li>
                    <li>✅ Hover animations</li>
                </ul>
            </div>

            <div class="comparison-card">
                <h3>🚀 Our Implementation</h3>
                <div class="fab-preview" id="our-fab-preview">
                    <div style="color: #FFD700;">FAB will appear here after loading scripts</div>
                </div>
                <div class="code-block">
// Our ChatFab.js creates:
&lt;button class="MuiButtonBase-root MuiFab-root MuiFab-circular MuiFab-sizeLarge chat-fab-button"&gt;
  &lt;span class="MuiBadge-root"&gt;
    &lt;div class="MuiBox-root"&gt;🌸&lt;/div&gt;
    &lt;span class="MuiBadge-badge MuiBadge-invisible"&gt;0&lt;/span&gt;
  &lt;/span&gt;
  &lt;span class="MuiTouchRipple-root"&gt;&lt;/span&gt;
&lt;/button&gt;
                </div>
                <ul class="feature-list">
                    <li id="feature-classes">⏳ Material-UI class names</li>
                    <li id="feature-ripple">⏳ Touch ripple effect</li>
                    <li id="feature-badge">⏳ Badge system</li>
                    <li id="feature-accessibility">⏳ Accessibility attributes</li>
                    <li id="feature-gradient">⏳ Gradient background</li>
                    <li id="feature-hover">⏳ Hover animations</li>
                    <li id="feature-welcome">⏳ Welcome message</li>
                </ul>
            </div>
        </div>

        <div>
            <button class="btn" onclick="testFabFeatures()">🔍 Test FAB Features</button>
            <button class="btn" onclick="testWelcomeMessage()">💬 Test Welcome Message</button>
            <button class="btn" onclick="testBadgeSystem()">🔔 Test Badge System</button>
            <button class="btn" onclick="testRippleEffect()">💫 Test Ripple Effect</button>
            <button class="btn" onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <div class="status-panel" id="status-log">
            <div style="color: #00FF00;">[开始] Material-UI FAB Test Page Loaded</div>
        </div>
    </div>

    <!-- Load Chatbot Scripts -->
    <script src="chatbot/GeminiAPI.js"></script>
    <script src="chatbot/ChatFab.js"></script>
    <script src="chatbot/ChatInterface.js"></script>
    <script src="chatbot/chatbot-init.js"></script>

    <script>
        function log(message, color = '#00BFFF') {
            const statusLog = document.getElementById('status-log');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = color;
            div.textContent = `[${timestamp}] ${message}`;
            statusLog.appendChild(div);
            statusLog.scrollTop = statusLog.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateFeature(featureId, status, message) {
            const element = document.getElementById(featureId);
            if (element) {
                const icon = status ? '✅' : '❌';
                element.innerHTML = `${icon} ${message}`;
                element.style.color = status ? '#00FF00' : '#FF4444';
            }
        }

        function testFabFeatures() {
            log('🔍 Testing FAB features...', '#00BFFF');
            
            if (window.chatFab && window.chatFab.fabElement) {
                const fabElement = window.chatFab.fabElement;
                const fabButton = fabElement.querySelector('.chat-fab-button');
                
                // Test Material-UI classes
                const hasClasses = fabButton && fabButton.classList.contains('MuiButtonBase-root') && 
                                 fabButton.classList.contains('MuiFab-root');
                updateFeature('feature-classes', hasClasses, 'Material-UI class names');
                log(`Material-UI classes: ${hasClasses ? 'PASS' : 'FAIL'}`, hasClasses ? '#00FF00' : '#FF4444');
                
                // Test ripple container
                const hasRipple = fabButton && fabButton.querySelector('.MuiTouchRipple-root');
                updateFeature('feature-ripple', hasRipple, 'Touch ripple effect');
                log(`Touch ripple: ${hasRipple ? 'PASS' : 'FAIL'}`, hasRipple ? '#00FF00' : '#FF4444');
                
                // Test badge system
                const hasBadge = fabButton && fabButton.querySelector('.MuiBadge-root');
                updateFeature('feature-badge', hasBadge, 'Badge system');
                log(`Badge system: ${hasBadge ? 'PASS' : 'FAIL'}`, hasBadge ? '#00FF00' : '#FF4444');
                
                // Test accessibility
                const hasAccessibility = fabButton && fabButton.getAttribute('aria-label');
                updateFeature('feature-accessibility', hasAccessibility, 'Accessibility attributes');
                log(`Accessibility: ${hasAccessibility ? 'PASS' : 'FAIL'}`, hasAccessibility ? '#00FF00' : '#FF4444');
                
                // Test gradient
                const computedStyle = window.getComputedStyle(fabButton);
                const hasGradient = computedStyle.background.includes('gradient') || 
                                  computedStyle.backgroundImage.includes('gradient');
                updateFeature('feature-gradient', hasGradient, 'Gradient background');
                log(`Gradient background: ${hasGradient ? 'PASS' : 'FAIL'}`, hasGradient ? '#00FF00' : '#FF4444');
                
                // Test hover (we'll assume it works if CSS is applied)
                updateFeature('feature-hover', true, 'Hover animations');
                log(`Hover animations: PASS (CSS-based)`, '#00FF00');
                
                // Test welcome message
                const hasWelcome = fabElement.querySelector('#welcome-message');
                updateFeature('feature-welcome', hasWelcome, 'Welcome message');
                log(`Welcome message: ${hasWelcome ? 'PASS' : 'FAIL'}`, hasWelcome ? '#00FF00' : '#FF4444');
                
            } else {
                log('❌ ChatFab not found or not initialized', '#FF4444');
                ['feature-classes', 'feature-ripple', 'feature-badge', 'feature-accessibility', 'feature-gradient', 'feature-hover', 'feature-welcome'].forEach(id => {
                    updateFeature(id, false, 'Not available');
                });
            }
        }

        function testWelcomeMessage() {
            log('💬 Testing welcome message...', '#00BFFF');
            
            if (window.chatFab) {
                window.chatFab.displayWelcomeMessage();
                log('✅ Welcome message triggered', '#00FF00');
            } else {
                log('❌ ChatFab not available', '#FF4444');
            }
        }

        function testBadgeSystem() {
            log('🔔 Testing badge system...', '#00BFFF');
            
            if (window.chatFab) {
                window.chatFab.showBadge(5);
                log('✅ Badge set to 5', '#00FF00');
                
                setTimeout(() => {
                    window.chatFab.clearBadge();
                    log('✅ Badge cleared', '#00FF00');
                }, 3000);
            } else {
                log('❌ ChatFab not available', '#FF4444');
            }
        }

        function testRippleEffect() {
            log('💫 Testing ripple effect...', '#00BFFF');
            
            if (window.chatFab && window.chatFab.fabElement) {
                const fabButton = window.chatFab.fabElement.querySelector('.chat-fab-button');
                if (fabButton) {
                    // Simulate click to trigger ripple
                    const rect = fabButton.getBoundingClientRect();
                    const event = new MouseEvent('mousedown', {
                        clientX: rect.left + rect.width / 2,
                        clientY: rect.top + rect.height / 2
                    });
                    fabButton.dispatchEvent(event);
                    log('✅ Ripple effect triggered', '#00FF00');
                } else {
                    log('❌ FAB button not found', '#FF4444');
                }
            } else {
                log('❌ ChatFab not available', '#FF4444');
            }
        }

        function clearLog() {
            const statusLog = document.getElementById('status-log');
            statusLog.innerHTML = '<div style="color: #00FF00;">[清除] Log cleared</div>';
        }

        // Auto-run tests after page load
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Material-UI FAB test page loaded', '#00FF00');
            
            setTimeout(() => {
                log('🚀 Running automatic feature detection...', '#00BFFF');
                testFabFeatures();
                
                setTimeout(() => {
                    log('💡 Click buttons above to test specific features', '#FFD700');
                    log('💡 The FAB should appear in bottom-right corner after 2 seconds', '#FFD700');
                }, 1000);
            }, 2000);
        });
    </script>
</body>
</html>

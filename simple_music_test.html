<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单音乐测试 - GoldenLedger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .music-controller {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50px;
            padding: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            z-index: 99999;
        }
        
        .music-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: #6c5ce7;
            color: white;
            cursor: pointer;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .music-btn:hover {
            background: #5a4fcf;
            transform: scale(1.05);
        }
        
        .music-btn.playing {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            padding-top: 100px;
        }
        
        .test-buttons {
            margin: 20px 0;
        }
        
        .test-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <!-- 简单的音乐控制器 -->
    <div class="music-controller">
        <button id="musicBtn" class="music-btn" title="背景音乐">🎵</button>
    </div>

    <div class="content">
        <h1>🎵 简单音乐控制器测试</h1>
        <p>这是一个简化版的音乐控制器测试页面</p>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testPlay()">播放音乐</button>
            <button class="test-btn" onclick="testPause()">暂停音乐</button>
            <button class="test-btn" onclick="testVolumeUp()">音量+</button>
            <button class="test-btn" onclick="testVolumeDown()">音量-</button>
            <button class="test-btn" onclick="checkStatus()">检查状态</button>
        </div>
        
        <div class="status">
            <h3>🔍 状态信息</h3>
            <div id="statusInfo">等待检查...</div>
        </div>
        
        <div>
            <a href="index.html" style="color: white; text-decoration: underline;">← 返回首页</a>
        </div>
    </div>

    <script>
        // 简单的音乐控制器
        class SimpleMusicController {
            constructor() {
                this.audio = null;
                this.isPlaying = false;
                this.volume = 0.3;
                this.init();
            }
            
            init() {
                console.log('🎵 初始化简单音乐控制器');
                this.createAudio();
                this.setupEvents();
                this.updateUI();
            }
            
            createAudio() {
                this.audio = new Audio('/music/love.mp3');
                this.audio.loop = true;
                this.audio.volume = this.volume;
                this.audio.preload = 'auto';
                
                this.audio.addEventListener('loadstart', () => {
                    console.log('🎵 音频开始加载');
                    this.updateStatus('音频开始加载...');
                });
                
                this.audio.addEventListener('canplaythrough', () => {
                    console.log('🎵 音频加载完成');
                    this.updateStatus('音频加载完成，可以播放');
                });
                
                this.audio.addEventListener('error', (e) => {
                    console.error('🎵 音频加载失败:', e);
                    this.updateStatus('音频加载失败: ' + e.message);
                });
                
                this.audio.addEventListener('play', () => {
                    this.isPlaying = true;
                    this.updateUI();
                    this.updateStatus('音乐正在播放');
                });
                
                this.audio.addEventListener('pause', () => {
                    this.isPlaying = false;
                    this.updateUI();
                    this.updateStatus('音乐已暂停');
                });
            }
            
            setupEvents() {
                const btn = document.getElementById('musicBtn');
                btn.addEventListener('click', () => {
                    this.toggle();
                });
            }
            
            toggle() {
                if (this.isPlaying) {
                    this.pause();
                } else {
                    this.play();
                }
            }
            
            async play() {
                try {
                    await this.audio.play();
                    console.log('🎵 音乐开始播放');
                } catch (error) {
                    console.error('🎵 播放失败:', error);
                    this.updateStatus('播放失败: ' + error.message);
                }
            }
            
            pause() {
                this.audio.pause();
                console.log('🎵 音乐已暂停');
            }
            
            setVolume(vol) {
                this.volume = Math.max(0, Math.min(1, vol));
                this.audio.volume = this.volume;
                this.updateStatus(`音量设置为: ${Math.round(this.volume * 100)}%`);
            }
            
            updateUI() {
                const btn = document.getElementById('musicBtn');
                if (this.isPlaying) {
                    btn.classList.add('playing');
                    btn.title = '暂停音乐';
                } else {
                    btn.classList.remove('playing');
                    btn.title = '播放音乐';
                }
            }
            
            updateStatus(message) {
                const statusDiv = document.getElementById('statusInfo');
                const timestamp = new Date().toLocaleTimeString();
                statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                statusDiv.scrollTop = statusDiv.scrollHeight;
            }
            
            getStatus() {
                return {
                    isPlaying: this.isPlaying,
                    volume: this.volume,
                    currentTime: this.audio ? this.audio.currentTime : 0,
                    duration: this.audio ? this.audio.duration : 0,
                    readyState: this.audio ? this.audio.readyState : 0
                };
            }
        }
        
        // 全局函数
        let musicController;
        
        function testPlay() {
            if (musicController) {
                musicController.play();
            }
        }
        
        function testPause() {
            if (musicController) {
                musicController.pause();
            }
        }
        
        function testVolumeUp() {
            if (musicController) {
                musicController.setVolume(musicController.volume + 0.1);
            }
        }
        
        function testVolumeDown() {
            if (musicController) {
                musicController.setVolume(musicController.volume - 0.1);
            }
        }
        
        function checkStatus() {
            if (musicController) {
                const status = musicController.getStatus();
                musicController.updateStatus(`状态检查: 播放=${status.isPlaying}, 音量=${Math.round(status.volume*100)}%, 就绪状态=${status.readyState}`);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎵 页面加载完成，创建音乐控制器');
            musicController = new SimpleMusicController();
            
            // 测试音频文件是否可访问
            fetch('/music/love.mp3', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        musicController.updateStatus('✅ 音频文件可访问');
                    } else {
                        musicController.updateStatus('❌ 音频文件不可访问: ' + response.status);
                    }
                })
                .catch(error => {
                    musicController.updateStatus('❌ 音频文件检查失败: ' + error.message);
                });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI使用情况调试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">AI使用情况调试</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 用户信息 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">用户信息</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Session Token:</label>
                        <input type="text" id="sessionToken" class="w-full p-2 border rounded text-xs" placeholder="输入session token">
                    </div>
                    <button onclick="getUserInfo()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        获取用户信息
                    </button>
                    <button onclick="getTokenFromStorage()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        从本地存储获取Token
                    </button>
                </div>
                <div id="user-info" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
            
            <!-- AI使用统计 -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">AI使用统计</h2>
                <button onclick="getAIStats()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    获取AI使用统计
                </button>
                <div id="ai-stats" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
            
            <!-- 测试AI处理 -->
            <div class="bg-white p-6 rounded-lg shadow md:col-span-2">
                <h2 class="text-xl font-semibold mb-4">测试AI处理</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">测试文本:</label>
                        <input type="text" id="testText" class="w-full p-2 border rounded" value="今天买了办公用品100元" placeholder="输入要处理的文本">
                    </div>
                    <button onclick="testAIProcessing()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        测试AI处理
                    </button>
                </div>
                <div id="ai-result" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
            </div>
            
            <!-- 清理使用记录 -->
            <div class="bg-white p-6 rounded-lg shadow md:col-span-2">
                <h2 class="text-xl font-semibold mb-4 text-red-600">⚠️ 调试工具</h2>
                <p class="text-sm text-gray-600 mb-4">以下工具仅用于调试，请谨慎使用</p>
                <div class="space-x-4">
                    <button onclick="clearAIUsage()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        清理AI使用记录
                    </button>
                    <button onclick="checkSubscription()" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                        检查订阅状态
                    </button>
                </div>
                <div id="debug-result" class="mt-4 p-4 bg-gray-50 rounded min-h-20"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://goldenledger-api.souyousann.workers.dev';
        
        // 从本地存储获取token
        function getTokenFromStorage() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (token) {
                document.getElementById('sessionToken').value = token;
                alert('Token已自动填入！');
            } else {
                alert('本地存储中没有找到token，请先登录生产环境');
            }
        }
        
        // 获取用户信息
        async function getUserInfo() {
            const token = document.getElementById('sessionToken').value;
            const resultDiv = document.getElementById('user-info');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入session token</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>获取用户信息中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">用户信息:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>认证状态:</strong> ${data.authenticated ? '✅ 已认证' : '❌ 未认证'}</p>
                    ${data.user ? `
                        <p><strong>用户ID:</strong> ${data.user.id}</p>
                        <p><strong>用户名:</strong> ${data.user.username}</p>
                        <p><strong>邮箱:</strong> ${data.user.email}</p>
                        <p><strong>角色:</strong> ${data.user.role}</p>
                    ` : ''}
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 获取用户信息失败: ${error.message}</p>`;
            }
        }
        
        // 获取AI使用统计
        async function getAIStats() {
            const token = document.getElementById('sessionToken').value;
            const resultDiv = document.getElementById('ai-stats');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先输入session token</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>获取AI统计中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/stats?company_id=default&days=30`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">AI使用统计:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto max-h-64">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 获取AI统计失败: ${error.message}</p>`;
            }
        }
        
        // 测试AI处理
        async function testAIProcessing() {
            const token = document.getElementById('sessionToken').value;
            const text = document.getElementById('testText').value;
            const resultDiv = document.getElementById('ai-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先输入session token</p>';
                return;
            }
            
            if (!text) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入测试文本</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>测试AI处理中...</p>';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/ai/process-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        text: text,
                        company_id: 'default'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3 class="font-semibold">AI处理结果:</h3>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>成功:</strong> ${data.success ? '✅' : '❌'}</p>
                    ${data.error ? `<p><strong>错误:</strong> ${data.error}</p>` : ''}
                    ${data.usage_info ? `
                        <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                            <p><strong>使用信息:</strong></p>
                            <p>当前使用: ${data.usage_info.current_usage}</p>
                            <p>限制: ${data.usage_info.limit}</p>
                            <p>计划: ${data.usage_info.plan}</p>
                            ${data.usage_info.upgrade_required ? '<p class="text-red-600">需要升级</p>' : ''}
                        </div>
                    ` : ''}
                    <pre class="text-xs bg-white p-2 rounded border mt-2 overflow-auto max-h-64">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ AI处理测试失败: ${error.message}</p>`;
            }
        }
        
        // 清理AI使用记录（调试用）
        async function clearAIUsage() {
            const token = document.getElementById('sessionToken').value;
            const resultDiv = document.getElementById('debug-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先输入session token</p>';
                return;
            }
            
            if (!confirm('确定要清理AI使用记录吗？这将重置当月的使用计数。')) {
                return;
            }
            
            resultDiv.innerHTML = '<p>清理AI使用记录中...</p>';
            
            try {
                // 这里需要在Workers中添加一个调试端点
                resultDiv.innerHTML = '<p class="text-yellow-600">⚠️ 此功能需要在Workers中添加调试端点</p>';
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 清理失败: ${error.message}</p>`;
            }
        }
        
        // 检查订阅状态
        async function checkSubscription() {
            const token = document.getElementById('sessionToken').value;
            const resultDiv = document.getElementById('debug-result');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="text-red-600">请先输入session token</p>';
                return;
            }
            
            resultDiv.innerHTML = '<p>检查订阅状态中...</p>';
            
            try {
                // 这里需要在Workers中添加一个订阅状态端点
                resultDiv.innerHTML = '<p class="text-yellow-600">⚠️ 此功能需要在Workers中添加订阅状态端点</p>';
            } catch (error) {
                resultDiv.innerHTML = `<p class="text-red-600">❌ 检查失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动尝试获取token
        window.addEventListener('load', function() {
            const token = localStorage.getItem('goldenledger_session_token');
            if (token) {
                document.getElementById('sessionToken').value = token;
            }
        });
    </script>
</body>
</html>

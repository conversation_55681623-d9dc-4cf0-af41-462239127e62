<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘调试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <!-- 登录验证脚本 -->
    <script>
        // 检查用户登录状态
        function checkAuthStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');

            if (!token || !user) {
                // 未登录，重定向到登录页面
                alert('この機能を利用するにはログインが必要です。');
                window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
                return false;
            }

            return true;
        }

        // 页面加载时检查登录状态
        if (!checkAuthStatus()) {
            // 如果未登录，停止页面加载
            document.body.style.display = 'none';
        }
    </script>

    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">仪表盘调试页面</h1>
        
        <!-- API测试 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium mb-4">API测试</h2>
            <div class="space-y-4">
                <button onclick="testDashboardAPI()" 
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    测试仪表盘API
                </button>
                <button onclick="testJournalAPI()" 
                        class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    测试仕訳API
                </button>
            </div>
        </div>
        
        <!-- 结果显示 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-medium mb-4">测试结果</h2>
            <div id="results" class="space-y-2">
                <div class="text-gray-500">等待测试...</div>
            </div>
        </div>
        
        <!-- 原始数据显示 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-lg font-medium mb-4">原始数据</h2>
            <pre id="raw-data" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
等待数据...
            </pre>
        </div>
    </div>

    <script>
        // 添加结果
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            
            const colors = {
                'success': 'text-green-600',
                'error': 'text-red-600',
                'info': 'text-blue-600',
                'warning': 'text-yellow-600'
            };
            
            div.className = colors[type] || 'text-gray-600';
            div.textContent = message;
            results.appendChild(div);
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('raw-data').textContent = '等待数据...';
        }
        
        // 测试仪表盘API
        async function testDashboardAPI() {
            clearResults();
            addResult('🔄 开始测试仪表盘API...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/dashboard/summary/default');
                
                addResult(`📡 API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 显示原始数据
                    document.getElementById('raw-data').textContent = JSON.stringify(data, null, 2);
                    
                    // 解析数据
                    addResult('✅ API调用成功', 'success');
                    
                    if (data.financial) {
                        const financial = data.financial;
                        addResult(`💰 本月收入: ¥${financial.monthly_revenue?.toLocaleString() || 0}`, 'info');
                        addResult(`💸 本月支出: ¥${financial.monthly_expenses?.toLocaleString() || 0}`, 'info');
                        addResult(`📊 本月利润: ¥${financial.monthly_profit?.toLocaleString() || 0}`, 'info');
                        addResult(`📋 总记录数: ${financial.total_entries || 0}`, 'info');
                        addResult(`📅 本月记录数: ${financial.monthly_entries || 0}`, 'info');
                    } else {
                        addResult('⚠️ 缺少财务数据', 'warning');
                    }
                    
                    if (data.ai_stats) {
                        const ai = data.ai_stats;
                        addResult(`🤖 AI处理总数: ${ai.total_processed || 0}`, 'info');
                        addResult(`✅ AI成功率: ${((ai.success_rate || 0) * 100).toFixed(1)}%`, 'info');
                        addResult(`🎯 平均置信度: ${((ai.avg_confidence || 0) * 100).toFixed(1)}%`, 'info');
                        addResult(`⚡ 平均响应时间: ${(ai.avg_response_time || 0).toFixed(1)}ms`, 'info');
                    } else {
                        addResult('⚠️ 缺少AI统计数据', 'warning');
                    }
                    
                } else {
                    const errorText = await response.text();
                    addResult(`❌ API错误: ${errorText}`, 'error');
                    document.getElementById('raw-data').textContent = errorText;
                }
                
            } catch (error) {
                addResult(`❌ 网络错误: ${error.message}`, 'error');
                console.error('API测试失败:', error);
            }
        }
        
        // 测试仕訳API
        async function testJournalAPI() {
            clearResults();
            addResult('🔄 开始测试仕訳API...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/journal-entries/default');
                
                addResult(`📡 API响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const entries = await response.json();
                    
                    // 显示原始数据
                    document.getElementById('raw-data').textContent = JSON.stringify(entries, null, 2);
                    
                    addResult('✅ 仕訳API调用成功', 'success');
                    addResult(`📋 获取到 ${entries.length} 条记录`, 'info');
                    
                    if (entries.length > 0) {
                        addResult('📝 最近的记录:', 'info');
                        entries.slice(0, 3).forEach((entry, index) => {
                            addResult(`  ${index + 1}. ${entry.id} - ${entry.description} - ¥${entry.amount}`, 'info');
                        });
                    } else {
                        addResult('⚠️ 没有找到仕訳记录', 'warning');
                    }
                    
                } else {
                    const errorText = await response.text();
                    addResult(`❌ API错误: ${errorText}`, 'error');
                    document.getElementById('raw-data').textContent = errorText;
                }
                
            } catch (error) {
                addResult(`❌ 网络错误: ${error.message}`, 'error');
                console.error('仕訳API测试失败:', error);
            }
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 页面加载完成，可以开始测试', 'success');
        });
    </script>
</body>
</html>

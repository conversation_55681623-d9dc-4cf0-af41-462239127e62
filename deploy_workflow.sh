#!/bin/bash

# GoldenLedger 开发和部署工作流程脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 函数：启动本地服务器
start_local_server() {
    print_message "🚀 启动本地开发服务器..." $BLUE
    
    if command -v python3 &> /dev/null; then
        python3 start_local_server.py
    elif command -v python &> /dev/null; then
        python start_local_server.py
    else
        print_message "❌ 未找到 Python，请安装 Python 3.x" $RED
        exit 1
    fi
}

# 函数：运行本地测试
run_local_test() {
    print_message "🧪 启动本地测试服务器..." $BLUE
    print_message "📝 请在浏览器中测试以下功能：" $YELLOW
    echo "   - 登录/注册功能"
    echo "   - 重定向是否正常"
    echo "   - 移动端背景控制"
    echo "   - 音乐控制交互"
    echo "   - 所有页面导航"
    echo ""
    print_message "✅ 测试完成后按 Ctrl+C 停止服务器" $GREEN
    
    start_local_server
}

# 函数：提交到GitHub
commit_and_push() {
    print_message "📝 准备提交到 GitHub..." $BLUE
    
    # 检查是否有未提交的更改
    if [[ -z $(git status --porcelain) ]]; then
        print_message "ℹ️  没有需要提交的更改" $YELLOW
        return
    fi
    
    # 显示更改
    print_message "📊 当前更改：" $BLUE
    git status --short
    echo ""
    
    # 询问提交信息
    read -p "📝 请输入提交信息: " commit_message
    
    if [[ -z "$commit_message" ]]; then
        print_message "❌ 提交信息不能为空" $RED
        return 1
    fi
    
    # 添加所有更改
    print_message "📦 添加文件到暂存区..." $BLUE
    git add .
    
    # 提交更改
    print_message "💾 提交更改..." $BLUE
    git commit -m "$commit_message"
    
    # 推送到GitHub
    print_message "🚀 推送到 GitHub..." $BLUE
    git push origin main
    
    print_message "✅ 成功推送到 GitHub！" $GREEN
    print_message "🌐 Cloudflare Pages 将自动部署更新" $BLUE
}

# 函数：完整的开发工作流程
full_workflow() {
    print_message "🔄 开始完整的开发工作流程..." $BLUE
    echo ""
    
    print_message "第一步：本地测试" $YELLOW
    echo "1. 即将启动本地服务器"
    echo "2. 请在浏览器中测试所有功能"
    echo "3. 确认无误后按 Ctrl+C 停止服务器"
    echo ""
    
    read -p "按 Enter 继续启动本地服务器..."
    
    # 启动本地测试
    run_local_test
    
    echo ""
    print_message "第二步：确认部署" $YELLOW
    read -p "本地测试是否通过？是否要推送到 GitHub？(y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        commit_and_push
        print_message "🎉 工作流程完成！" $GREEN
    else
        print_message "⏸️  已取消推送，请继续本地开发" $YELLOW
    fi
}

# 函数：显示帮助信息
show_help() {
    echo "GoldenLedger 开发工作流程脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev, serve     启动本地开发服务器"
    echo "  test          运行本地测试"
    echo "  deploy        提交并推送到 GitHub"
    echo "  workflow      完整的开发工作流程"
    echo "  help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev         # 启动本地服务器"
    echo "  $0 workflow    # 运行完整工作流程"
    echo "  $0 deploy      # 直接部署到 GitHub"
}

# 主逻辑
case "${1:-workflow}" in
    "dev"|"serve")
        start_local_server
        ;;
    "test")
        run_local_test
        ;;
    "deploy")
        commit_and_push
        ;;
    "workflow")
        full_workflow
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_message "❌ 未知选项: $1" $RED
        show_help
        exit 1
        ;;
esac

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoldenLedger 订阅系统流程图集 - PNG版本</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .diagram-container {
            transition: all 0.3s ease;
        }
        .diagram-container:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .diagram-image {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 页面头部 -->
    <header class="gradient-bg text-white py-12">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    <i class="fas fa-project-diagram mr-3"></i>
                    GoldenLedger 订阅系统流程图集
                </h1>
                <p class="text-xl opacity-90 max-w-3xl mx-auto">
                    完整的用户流程、状态转换、AI决策和数据库生命周期图表 - PNG高清版本
                </p>
                <div class="mt-6 flex justify-center space-x-4">
                    <span class="bg-white/20 px-4 py-2 rounded-full text-sm">
                        <i class="fas fa-users mr-2"></i>用户体验设计
                    </span>
                    <span class="bg-white/20 px-4 py-2 rounded-full text-sm">
                        <i class="fas fa-cogs mr-2"></i>技术架构
                    </span>
                    <span class="bg-white/20 px-4 py-2 rounded-full text-sm">
                        <i class="fas fa-chart-line mr-2"></i>商业流程
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- 图表展示区域 -->
    <main class="py-16">
        <div class="container mx-auto px-6">
            
            <!-- 图表1: 用户流程图 -->
            <section class="mb-16">
                <div class="diagram-container bg-white rounded-2xl shadow-lg p-8">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-route text-blue-600 mr-3"></i>
                            1. 用户流程图 (User Flow)
                        </h2>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            展示用户从首次访问到付费转换的完整旅程，包括登录认证、AI功能使用、订阅选择和支付流程。
                            清晰标注了各个决策点和页面跳转逻辑。
                        </p>
                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">用户旅程</span>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">页面导航</span>
                            <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">决策点</span>
                            <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">转换漏斗</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <img src="01_user_flow.png" alt="用户流程图" class="diagram-image mx-auto">
                        <div class="mt-4">
                            <a href="01_user_flow.png" download class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-download mr-2"></i>下载高清PNG
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 图表2: 状态转换图 -->
            <section class="mb-16">
                <div class="diagram-container bg-white rounded-2xl shadow-lg p-8">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-exchange-alt text-green-600 mr-3"></i>
                            2. 页面状态转换图 (State Transition)
                        </h2>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            采用状态机模式展示用户在不同订阅状态间的转换关系。包含未登录、免费用户、试用用户、付费用户等嵌套状态，
                            以及各状态间的触发条件和回退机制。
                        </p>
                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">状态机</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">嵌套状态</span>
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">触发条件</span>
                            <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">回退机制</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <img src="02_state_transition.png" alt="状态转换图" class="diagram-image mx-auto">
                        <div class="mt-4">
                            <a href="02_state_transition.png" download class="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-download mr-2"></i>下载高清PNG
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 图表3: AI决策流程图 -->
            <section class="mb-16">
                <div class="diagram-container bg-white rounded-2xl shadow-lg p-8">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-brain text-purple-600 mr-3"></i>
                            3. AI功能使用限制决策流程 (AI Decision Flow)
                        </h2>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            详细展示AI功能的权限检查逻辑，包括用户认证、订阅状态验证、使用量控制和错误处理。
                            不同计划的限制规则和升级引导路径都有清晰标注。
                        </p>
                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">权限控制</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">使用限制</span>
                            <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">错误处理</span>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">升级引导</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <img src="03_ai_decision_flow.png" alt="AI决策流程图" class="diagram-image mx-auto">
                        <div class="mt-4">
                            <a href="03_ai_decision_flow.png" download class="inline-flex items-center bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-download mr-2"></i>下载高清PNG
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 图表4: 数据库生命周期图 -->
            <section class="mb-16">
                <div class="diagram-container bg-white rounded-2xl shadow-lg p-8">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-database text-indigo-600 mr-3"></i>
                            4. 订阅生命周期和数据库状态图 (Database Lifecycle)
                        </h2>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            展示数据库表结构关系、订阅状态流转、使用量管理、支付流程和自动化任务。
                            包含完整的数据生命周期管理和系统后台处理逻辑。
                        </p>
                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">数据库设计</span>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">生命周期</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">支付集成</span>
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">自动化任务</span>
                        </div>
                    </div>
                    <div class="text-center">
                        <img src="04_database_lifecycle.png" alt="数据库生命周期图" class="diagram-image mx-auto">
                        <div class="mt-4">
                            <a href="04_database_lifecycle.png" download class="inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-download mr-2"></i>下载高清PNG
                            </a>
                        </div>
                    </div>
                </div>
            </section>

        </div>
    </main>

    <!-- 总结和下载区域 -->
    <section class="gradient-bg text-white py-16">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl font-bold mb-6">完整流程图集下载</h2>
            <p class="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
                所有图表均为高清PNG格式 (1920x1080)，适用于文档、演示和开发参考
            </p>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto mb-8">
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <i class="fas fa-route text-3xl mb-2"></i>
                    <h3 class="font-semibold mb-2">用户流程图</h3>
                    <p class="text-sm opacity-80">完整用户旅程</p>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <i class="fas fa-exchange-alt text-3xl mb-2"></i>
                    <h3 class="font-semibold mb-2">状态转换图</h3>
                    <p class="text-sm opacity-80">状态机设计</p>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <i class="fas fa-brain text-3xl mb-2"></i>
                    <h3 class="font-semibold mb-2">AI决策流程</h3>
                    <p class="text-sm opacity-80">权限控制逻辑</p>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <i class="fas fa-database text-3xl mb-2"></i>
                    <h3 class="font-semibold mb-2">数据库生命周期</h3>
                    <p class="text-sm opacity-80">系统架构设计</p>
                </div>
            </div>

            <div class="space-y-4">
                <p class="text-lg">
                    <i class="fas fa-check-circle mr-2"></i>
                    高分辨率PNG格式 (1920x1080)
                </p>
                <p class="text-lg">
                    <i class="fas fa-check-circle mr-2"></i>
                    白色背景，适合打印和演示
                </p>
                <p class="text-lg">
                    <i class="fas fa-check-circle mr-2"></i>
                    完整的商业化订阅系统设计
                </p>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-400">
                © 2024 GoldenLedger AI記帳システム - 订阅系统流程图集
            </p>
            <p class="text-gray-500 mt-2">
                Generated by Mermaid CLI - PNG Export
            </p>
        </div>
    </footer>

    <script>
        // 图片懒加载和错误处理
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('.diagram-image');
            
            images.forEach(img => {
                img.addEventListener('error', function() {
                    this.style.display = 'none';
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded text-center';
                    errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>图片加载失败，请检查文件路径';
                    this.parentNode.insertBefore(errorDiv, this);
                });
                
                img.addEventListener('load', function() {
                    this.style.opacity = '1';
                });
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐功能测试 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="music_control_new.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .test-panel {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            background: rgba(0,0,0,0.05);
            border-radius: 8px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .test-button {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            touch-action: manipulation;
        }
        
        .test-button:active {
            background: #2563eb;
            transform: scale(0.98);
        }
        
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="test-panel">
        <h1 class="text-2xl font-bold mb-4">🎵 音乐功能测试</h1>
        
        <div class="info-box">
            <h3 class="font-semibold mb-2">🎯 测试目标</h3>
            <ul class="text-sm space-y-1">
                <li>✅ 音乐按钮点击响应</li>
                <li>✅ 音乐播放/暂停功能</li>
                <li>✅ 音量控制</li>
                <li>✅ 拖动不触发播放</li>
            </ul>
        </div>
        
        <div id="statusList">
            <div class="status-item">
                <div class="status-dot status-warning"></div>
                <span>初始化中...</span>
            </div>
        </div>
    </div>

    <div class="test-panel">
        <h2 class="text-xl font-semibold mb-4">🧪 功能测试</h2>
        
        <button class="test-button" onclick="testDirectClick()">
            🎵 直接点击音乐按钮
        </button>
        
        <button class="test-button" onclick="testToggleMusic()">
            🎵 调用 toggleMusic() 方法
        </button>
        
        <button class="test-button" onclick="testPlayMusic()">
            ▶️ 调用 playMusic() 方法
        </button>
        
        <button class="test-button" onclick="testPauseMusic()">
            ⏸️ 调用 pauseMusic() 方法
        </button>
        
        <button class="test-button" onclick="checkMusicState()">
            📊 检查音乐状态
        </button>
        
        <div id="musicState" class="info-box">
            <h3 class="font-semibold mb-2">音乐状态</h3>
            <div id="stateDetails" class="text-sm">检测中...</div>
        </div>
    </div>

    <div class="test-panel">
        <h2 class="text-xl font-semibold mb-4">📋 测试日志</h2>
        <div id="testLog" class="text-sm space-y-1">
            <div class="text-gray-600">等待测试...</div>
        </div>
    </div>

    <script>
        const statusList = document.getElementById('statusList');
        const stateDetails = document.getElementById('stateDetails');
        const testLog = document.getElementById('testLog');
        
        let testData = [];
        let logEntries = [];
        
        function updateStatus(test, status, message = '') {
            const existing = testData.find(t => t.test === test);
            if (existing) {
                existing.status = status;
                existing.message = message;
            } else {
                testData.push({ test, status, message });
            }
            renderStatus();
        }
        
        function renderStatus() {
            const statusHtml = testData.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'warning' ? 'status-warning' : 'status-error';
                return `
                    <div class="status-item">
                        <div class="status-dot ${statusClass}"></div>
                        <span>${result.test} ${result.message}</span>
                    </div>
                `;
            }).join('');
            statusList.innerHTML = statusHtml;
        }
        
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            logEntries.unshift(`${time}: ${message}`);
            if (logEntries.length > 10) logEntries.pop();
            
            testLog.innerHTML = logEntries.map(entry => 
                `<div class="p-2 bg-gray-50 rounded">${entry}</div>`
            ).join('');
        }
        
        function testDirectClick() {
            const musicBtn = document.getElementById('music-toggle');
            if (musicBtn) {
                addLog('直接点击音乐按钮');
                musicBtn.click();
            } else {
                addLog('❌ 音乐按钮未找到');
            }
        }
        
        function testToggleMusic() {
            if (window.goldenLedgerMusic) {
                addLog('调用 toggleMusic() 方法');
                window.goldenLedgerMusic.toggleMusic();
            } else {
                addLog('❌ 音乐控制器未找到');
            }
        }
        
        function testPlayMusic() {
            if (window.goldenLedgerMusic) {
                addLog('调用 playMusic() 方法');
                window.goldenLedgerMusic.playMusic();
            } else {
                addLog('❌ 音乐控制器未找到');
            }
        }
        
        function testPauseMusic() {
            if (window.goldenLedgerMusic) {
                addLog('调用 pauseMusic() 方法');
                window.goldenLedgerMusic.pauseMusic();
            } else {
                addLog('❌ 音乐控制器未找到');
            }
        }
        
        function checkMusicState() {
            if (window.goldenLedgerMusic) {
                const state = {
                    isPlaying: window.goldenLedgerMusic.isPlaying,
                    volume: window.goldenLedgerMusic.volume,
                    currentTrack: window.goldenLedgerMusic.currentTrack
                };
                
                stateDetails.innerHTML = `
                    播放状态: ${state.isPlaying ? '播放中' : '暂停'}<br>
                    音量: ${state.volume}%<br>
                    当前曲目: ${state.currentTrack || '无'}
                `;
                
                addLog(`音乐状态: ${JSON.stringify(state)}`);
            } else {
                stateDetails.innerHTML = '❌ 音乐控制器未找到';
                addLog('❌ 无法获取音乐状态');
            }
        }
        
        // 监听页面加载
        window.addEventListener('load', function() {
            updateStatus('页面加载', 'success', '- 完成');
            addLog('页面加载完成');
            
            setTimeout(() => {
                // 检查音乐控制器
                if (window.goldenLedgerMusic) {
                    updateStatus('🎵 音乐控制器', 'success', '- 已初始化');
                    addLog('✅ 音乐控制器已找到');
                    
                    // 检查音乐按钮
                    const musicBtn = document.getElementById('music-toggle');
                    if (musicBtn) {
                        updateStatus('🎵 音乐按钮', 'success', '- 已找到');
                        addLog('✅ 音乐按钮已找到');
                        
                        // 监听点击事件
                        let clickCount = 0;
                        musicBtn.addEventListener('click', () => {
                            clickCount++;
                            updateStatus('🎵 点击响应', 'success', `- 已响应 ${clickCount} 次`);
                            addLog(`🎵 音乐按钮点击 #${clickCount}`);
                        });
                        
                    } else {
                        updateStatus('🎵 音乐按钮', 'error', '- 未找到');
                        addLog('❌ 音乐按钮未找到');
                    }
                    
                    // 初始状态检查
                    checkMusicState();
                    
                } else {
                    updateStatus('🎵 音乐控制器', 'error', '- 未初始化');
                    addLog('❌ 音乐控制器未找到');
                }
                
            }, 1500);
        });
        
        // 监听控制台日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.join(' ');
            if (message.includes('🎵')) {
                addLog(`控制台: ${message}`);
                
                if (message.includes('音乐按钮被点击')) {
                    updateStatus('🎵 点击检测', 'success', '- 点击事件触发');
                } else if (message.includes('切换音乐状态')) {
                    updateStatus('🎵 播放切换', 'success', '- 状态切换成功');
                } else if (message.includes('开始播放音乐')) {
                    updateStatus('🎵 播放功能', 'success', '- 开始播放');
                } else if (message.includes('暂停音乐')) {
                    updateStatus('🎵 暂停功能', 'success', '- 暂停播放');
                }
            }
        };
        
        console.log('🎵 音乐功能测试页面初始化完成');
    </script>
</body>
</html>

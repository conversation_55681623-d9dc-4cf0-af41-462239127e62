#!/usr/bin/env python3
"""
测试刷新按钮修复
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def test_journal_entries_page():
    """测试记录列表页面是否正常加载"""
    print("🧪 测试记录列表页面...")
    
    try:
        response = requests.get(f"{BASE_URL}/journal_entries.html", timeout=10)
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 检查页面内容
            content = response.text
            
            # 检查是否包含刷新按钮
            if '刷新' in content and 'refreshEntries()' in content:
                print("✅ 刷新按钮存在")
            else:
                print("❌ 刷新按钮不存在")
            
            # 检查是否包含修复后的代码
            if 'sort-controls' in content and 'existingSortControls' in content:
                print("✅ 修复代码已应用")
            else:
                print("❌ 修复代码未应用")
            
            # 检查是否包含排序控制按钮
            if '快速排序' in content:
                print("✅ 排序控制按钮存在")
            else:
                print("❌ 排序控制按钮不存在")
                
            return True
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_api_endpoints():
    """测试相关API端点"""
    print("\n🧪 测试API端点...")
    
    # 测试获取记录API
    try:
        response = requests.get(f"{BASE_URL}/journal-entries/default", timeout=10)
        
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ 获取记录API正常: 共 {len(entries)} 条记录")
            return True
        else:
            print(f"❌ 获取记录API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_server_health():
    """测试服务器健康状态"""
    print("\n🧪 测试服务器健康状态...")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 服务器健康: {health_data}")
            return True
        else:
            print(f"❌ 服务器不健康: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试刷新按钮修复")
    print("=" * 60)
    
    # 1. 测试服务器健康状态
    server_ok = test_server_health()
    
    if not server_ok:
        print("\n❌ 服务器不可用，无法继续测试")
        return
    
    # 2. 测试API端点
    api_ok = test_api_endpoints()
    
    # 3. 测试页面加载
    page_ok = test_journal_entries_page()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    if server_ok and api_ok and page_ok:
        print("✅ 所有测试通过！")
        print("\n📋 修复内容:")
        print("✅ 添加了排序控制按钮的重复检查")
        print("✅ 在添加新按钮前先移除已存在的按钮")
        print("✅ 使用 'sort-controls' 类名作为标识")
        print("✅ 防止每次刷新时重复添加按钮")
        
        print("\n🔗 测试页面:")
        print(f"  - 记录列表: {BASE_URL}/journal_entries.html")
        print("\n💡 现在您可以多次点击刷新按钮，按钮数量不会增加了！")
        
    else:
        print("⚠️  部分测试失败，请检查:")
        if not server_ok:
            print("  - 服务器状态")
        if not api_ok:
            print("  - API端点")
        if not page_ok:
            print("  - 页面加载")

if __name__ == "__main__":
    main()

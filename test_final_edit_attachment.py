#!/usr/bin/env python3
"""
最终测试编辑界面的附件管理功能
"""
import requests
import json
import time

def test_final_edit_attachment():
    """最终测试编辑界面的附件管理功能"""
    
    print("🎯 最终测试编辑界面的附件管理功能")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    try:
        # 1. 获取记录列表
        print("\n📋 步骤1: 获取记录列表")
        response = requests.get(f"{base_url}/journal-entries/default")
        entries = response.json()
        print(f"✅ 获取到 {len(entries)} 条记录")
        
        if len(entries) == 0:
            print("❌ 没有记录可以测试")
            return False
        
        # 选择第一条记录进行测试
        test_entry = entries[0]
        entry_id = test_entry['id']
        print(f"📝 选择记录: {entry_id}")
        print(f"   描述: {test_entry.get('description', 'N/A')}")
        
        # 2. 测试编辑界面的附件管理流程
        print(f"\n🔧 步骤2: 测试编辑界面附件管理流程")
        
        # 2.1 模拟打开编辑界面 - 获取当前附件
        print("  2.1 获取当前附件列表")
        attachments_response = requests.get(f"{base_url}/attachments/{entry_id}")
        
        if attachments_response.status_code == 200:
            current_attachments = attachments_response.json()
            print(f"  ✅ 当前附件数量: {len(current_attachments)}")
            
            for i, attachment in enumerate(current_attachments):
                print(f"    附件{i+1}: {attachment.get('filename', 'N/A')}")
        else:
            print(f"  ❌ 获取附件列表失败: {attachments_response.status_code}")
            return False
        
        # 2.2 测试上传新附件
        print("  2.2 测试上传新附件")
        test_content = f"编辑界面测试附件\n记录ID: {entry_id}\n时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        # 创建测试文件
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('edit_test_attachment.txt', f, 'text/plain')}
                data = {'entry_id': entry_id}
                
                upload_response = requests.post(f"{base_url}/upload-attachment", files=files, data=data)
                
                if upload_response.status_code == 200:
                    upload_result = upload_response.json()
                    print(f"  ✅ 附件上传成功: {upload_result.get('filename')}")
                    uploaded_filename = upload_result.get('filename')
                else:
                    print(f"  ❌ 附件上传失败: {upload_response.status_code}")
                    return False
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
        
        # 2.3 验证上传后的附件列表
        print("  2.3 验证上传后的附件列表")
        new_attachments_response = requests.get(f"{base_url}/attachments/{entry_id}")
        
        if new_attachments_response.status_code == 200:
            new_attachments = new_attachments_response.json()
            print(f"  ✅ 上传后附件数量: {len(new_attachments)}")
            
            # 检查新上传的文件是否在列表中
            uploaded_found = any(att.get('filename') == uploaded_filename for att in new_attachments)
            if uploaded_found:
                print(f"  ✅ 新上传的附件已在列表中")
            else:
                print(f"  ❌ 新上传的附件未在列表中")
        else:
            print(f"  ❌ 获取新附件列表失败: {new_attachments_response.status_code}")
            return False
        
        # 2.4 测试附件预览
        print("  2.4 测试附件预览")
        if len(new_attachments) > 0:
            preview_response = requests.get(f"{base_url}/attachments/{entry_id}?preview=true")
            
            if preview_response.status_code == 200:
                print(f"  ✅ 附件预览成功")
                print(f"    Content-Type: {preview_response.headers.get('content-type', 'N/A')}")
                print(f"    Content-Length: {preview_response.headers.get('content-length', 'N/A')}")
            else:
                print(f"  ❌ 附件预览失败: {preview_response.status_code}")
        else:
            print("  ⏭️ 跳过预览测试（无附件）")
        
        # 2.5 测试删除附件
        print("  2.5 测试删除附件")
        if len(new_attachments) > 0 and uploaded_filename:
            delete_data = {'filename': uploaded_filename}
            delete_response = requests.delete(
                f"{base_url}/attachments/{entry_id}",
                headers={'Content-Type': 'application/json'},
                data=json.dumps(delete_data)
            )
            
            if delete_response.status_code == 200:
                delete_result = delete_response.json()
                print(f"  ✅ 附件删除成功: {delete_result.get('deleted_file')}")
                
                # 验证删除结果
                final_attachments_response = requests.get(f"{base_url}/attachments/{entry_id}")
                if final_attachments_response.status_code == 200:
                    final_attachments = final_attachments_response.json()
                    print(f"  ✅ 删除后附件数量: {len(final_attachments)}")
                    
                    # 检查删除的文件是否已不在列表中
                    deleted_found = any(att.get('filename') == uploaded_filename for att in final_attachments)
                    if not deleted_found:
                        print(f"  ✅ 删除的附件已从列表中移除")
                    else:
                        print(f"  ❌ 删除的附件仍在列表中")
            else:
                print(f"  ❌ 附件删除失败: {delete_response.status_code}")
                print(f"    错误信息: {delete_response.text}")
        else:
            print("  ⏭️ 跳过删除测试（无附件）")
        
        # 3. 测试前端页面访问
        print(f"\n🌐 步骤3: 测试前端页面访问")
        
        frontend_pages = [
            "/journal_entries.html",
            "/test_edit_attachment_frontend.html"
        ]
        
        for page in frontend_pages:
            page_response = requests.get(f"{base_url}{page}")
            if page_response.status_code == 200:
                print(f"  ✅ {page} 访问成功")
            else:
                print(f"  ❌ {page} 访问失败: {page_response.status_code}")
        
        print("\n🎉 编辑界面附件管理功能测试完成！")
        print("\n📊 测试总结:")
        print("✅ 获取附件列表功能正常")
        print("✅ 上传附件功能正常")
        print("✅ 附件预览功能正常")
        print("✅ 删除附件功能正常")
        print("✅ 前端页面访问正常")
        print("\n🔗 可以在以下页面测试完整功能:")
        print("   - 主要编辑界面: http://localhost:8000/journal_entries.html")
        print("   - 附件管理测试: http://localhost:8000/test_edit_attachment_frontend.html")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_final_edit_attachment()
    if success:
        print("\n🎯 编辑界面附件管理功能完全正常！")
    else:
        print("\n❌ 测试失败，请检查错误信息")

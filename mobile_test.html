<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智会云 GoTax - 移动端测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        /* 移动端优化样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            touch-action: manipulation;
        }
        
        /* 防止iOS缩放 */
        input, textarea, select {
            font-size: 16px;
        }
        
        /* 聊天气泡样式 */
        .chat-bubble {
            max-width: 85%;
            padding: 12px 16px;
            border-radius: 18px;
            margin-bottom: 8px;
            word-wrap: break-word;
        }
        
        .chat-user {
            background: #007AFF;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }
        
        .chat-ai {
            background: #F2F2F7;
            color: #000;
            margin-right: auto;
            border-bottom-left-radius: 4px;
        }
        
        /* 输入框样式 */
        .input-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid #E5E5EA;
        }
        
        /* 按钮触摸优化 */
        .touch-button {
            min-height: 44px;
            min-width: 44px;
        }
        
        /* 状态指示器 */
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background: #34C759; }
        .status-offline { background: #FF3B30; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部状态栏 -->
    <div class="bg-white shadow-sm px-4 py-3 flex items-center justify-between">
        <div class="flex items-center">
            <h1 class="text-lg font-semibold">🚀 智会云 GoTax</h1>
        </div>
        <div class="flex items-center text-sm">
            <span class="status-dot" id="status-dot"></span>
            <span id="status-text">检查中...</span>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex flex-col h-screen">
        <!-- 聊天区域 -->
        <div class="flex-1 overflow-y-auto px-4 py-4" id="chat-container">
            <div id="chat-messages" class="space-y-2">
                <!-- 欢迎消息 -->
                <div class="chat-bubble chat-ai">
                    <div class="font-medium mb-1">欢迎使用智会云 GoTax AI记账！</div>
                    <div class="text-sm opacity-75">您可以用自然语言描述交易，我会帮您生成记账记录。</div>
                </div>
            </div>
        </div>

        <!-- 快速输入按钮 -->
        <div class="px-4 py-2 bg-white border-t border-gray-200">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <button class="quick-input touch-button bg-blue-100 text-blue-800 px-3 py-2 rounded-full text-sm whitespace-nowrap" data-text="今天买了咖啡500円">
                    ☕ 买咖啡
                </button>
                <button class="quick-input touch-button bg-green-100 text-green-800 px-3 py-2 rounded-full text-sm whitespace-nowrap" data-text="午餐花费1200円">
                    🍱 午餐
                </button>
                <button class="quick-input touch-button bg-purple-100 text-purple-800 px-3 py-2 rounded-full text-sm whitespace-nowrap" data-text="交通费300円">
                    🚇 交通
                </button>
                <button class="quick-input touch-button bg-orange-100 text-orange-800 px-3 py-2 rounded-full text-sm whitespace-nowrap" data-text="购买办公用品2000円">
                    📝 办公用品
                </button>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-container px-4 py-3">
            <div class="flex items-end space-x-3">
                <div class="flex-1">
                    <textarea 
                        id="chat-input" 
                        placeholder="描述您的交易..." 
                        class="w-full px-4 py-3 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="1"
                        style="max-height: 120px;"
                    ></textarea>
                </div>
                <button 
                    id="send-btn" 
                    class="touch-button bg-blue-500 text-white p-3 rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 历史记录面板 -->
    <div id="history-panel" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-96 overflow-hidden">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold">💬 对话历史</h3>
                    <button id="close-history" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="overflow-y-auto p-4" style="max-height: 300px;">
                <div id="history-list" class="space-y-3">
                    <!-- 历史记录将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="fixed bottom-20 right-4 flex flex-col space-y-2">
        <button id="history-btn" class="touch-button bg-white shadow-lg rounded-full p-3 text-gray-600 hover:text-gray-800">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </button>
    </div>

    <script>
        // 移动端优化的聊天功能
        class MobileChatApp {
            constructor() {
                this.chatHistory = JSON.parse(localStorage.getItem('mobile_chat_history') || '[]');
                this.init();
            }

            init() {
                this.bindEvents();
                this.checkServerStatus();
                this.loadHistory();
                this.autoResizeTextarea();
            }

            bindEvents() {
                // 发送按钮
                document.getElementById('send-btn').addEventListener('click', () => {
                    this.sendMessage();
                });

                // 回车发送（移动端优化）
                document.getElementById('chat-input').addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 快速输入按钮
                document.querySelectorAll('.quick-input').forEach(btn => {
                    btn.addEventListener('click', () => {
                        const text = btn.getAttribute('data-text');
                        document.getElementById('chat-input').value = text;
                        this.sendMessage();
                    });
                });

                // 历史记录按钮
                document.getElementById('history-btn').addEventListener('click', () => {
                    document.getElementById('history-panel').classList.remove('hidden');
                });

                document.getElementById('close-history').addEventListener('click', () => {
                    document.getElementById('history-panel').classList.add('hidden');
                });

                // 点击背景关闭历史面板
                document.getElementById('history-panel').addEventListener('click', (e) => {
                    if (e.target.id === 'history-panel') {
                        document.getElementById('history-panel').classList.add('hidden');
                    }
                });
            }

            autoResizeTextarea() {
                const textarea = document.getElementById('chat-input');
                textarea.addEventListener('input', () => {
                    textarea.style.height = 'auto';
                    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
                });
            }

            async checkServerStatus() {
                try {
                    const response = await fetch('/health');
                    if (response.ok) {
                        this.updateStatus(true, '服务正常');
                    } else {
                        this.updateStatus(false, '服务异常');
                    }
                } catch (error) {
                    this.updateStatus(false, '连接失败');
                }
            }

            updateStatus(online, text) {
                const dot = document.getElementById('status-dot');
                const statusText = document.getElementById('status-text');
                
                dot.className = `status-dot ${online ? 'status-online' : 'status-offline'}`;
                statusText.textContent = text;
            }

            async sendMessage() {
                const input = document.getElementById('chat-input');
                const text = input.value.trim();
                
                if (!text) return;

                // 添加用户消息
                this.addMessage(text, 'user');
                input.value = '';
                input.style.height = 'auto';

                // 添加加载消息
                const loadingId = this.addLoadingMessage();

                try {
                    // 模拟AI响应（实际应该调用API）
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    this.removeMessage(loadingId);
                    
                    const aiResponse = `收到您的记账请求："${text}"。AI功能正在开发中，请访问完整版本体验更多功能。`;
                    this.addMessage(aiResponse, 'ai');
                    
                    // 保存到历史
                    this.saveToHistory(text, aiResponse);
                    
                } catch (error) {
                    this.removeMessage(loadingId);
                    this.addMessage('抱歉，服务暂时不可用。请稍后再试。', 'ai');
                }
            }

            addMessage(text, type) {
                const messagesContainer = document.getElementById('chat-messages');
                const messageId = 'msg-' + Date.now();
                
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = `chat-bubble chat-${type}`;
                messageDiv.textContent = text;
                
                messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
                
                return messageId;
            }

            addLoadingMessage() {
                const messagesContainer = document.getElementById('chat-messages');
                const loadingId = 'loading-' + Date.now();
                
                const loadingDiv = document.createElement('div');
                loadingDiv.id = loadingId;
                loadingDiv.className = 'chat-bubble chat-ai';
                loadingDiv.innerHTML = '<div class="flex items-center space-x-2"><div class="animate-pulse">💭</div><span>AI正在思考...</span></div>';
                
                messagesContainer.appendChild(loadingDiv);
                this.scrollToBottom();
                
                return loadingId;
            }

            removeMessage(messageId) {
                const message = document.getElementById(messageId);
                if (message) {
                    message.remove();
                }
            }

            scrollToBottom() {
                const container = document.getElementById('chat-container');
                setTimeout(() => {
                    container.scrollTop = container.scrollHeight;
                }, 100);
            }

            saveToHistory(userMessage, aiResponse) {
                this.chatHistory.unshift({
                    id: Date.now(),
                    timestamp: new Date().toLocaleString('zh-CN'),
                    userMessage,
                    aiResponse
                });
                
                // 保持最多30条记录
                if (this.chatHistory.length > 30) {
                    this.chatHistory = this.chatHistory.slice(0, 30);
                }
                
                localStorage.setItem('mobile_chat_history', JSON.stringify(this.chatHistory));
            }

            loadHistory() {
                const historyList = document.getElementById('history-list');
                
                if (this.chatHistory.length === 0) {
                    historyList.innerHTML = '<div class="text-center text-gray-500 py-8">暂无对话历史</div>';
                    return;
                }
                
                historyList.innerHTML = this.chatHistory.map(item => `
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs text-gray-500 mb-2">${item.timestamp}</div>
                        <div class="text-sm mb-1"><strong>用户:</strong> ${item.userMessage}</div>
                        <div class="text-sm text-gray-600"><strong>AI:</strong> ${item.aiResponse.substring(0, 50)}...</div>
                    </div>
                `).join('');
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new MobileChatApp();
        });
    </script>
</body>
</html>

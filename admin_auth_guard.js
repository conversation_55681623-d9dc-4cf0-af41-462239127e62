/**
 * 管理员认证守卫
 * 保护管理员页面的访问安全
 */

class AdminAuthGuard {
    constructor() {
        this.sessionKey = 'admin_session';
        this.loginPath = '/admin_login.html';
        this.sessionTimeout = 8 * 60 * 60 * 1000; // 8小时
        this.warningTime = 30 * 60 * 1000; // 30分钟前警告
        
        this.init();
    }

    init() {
        // 检查当前页面是否需要保护
        if (this.isProtectedPage()) {
            this.validateSession();
            this.setupSessionMonitoring();
        }
    }

    // 检查是否为受保护的页面
    isProtectedPage() {
        const protectedPaths = [
            '/admin_usage_management.html',
            '/admin_usage_management',
            '/admin'
        ];
        
        const currentPath = window.location.pathname;
        return protectedPaths.some(path => currentPath.includes(path));
    }

    // 验证会话
    validateSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            
            if (!sessionData) {
                this.redirectToLogin('セッションが見つかりません');
                return false;
            }

            const session = JSON.parse(sessionData);
            
            // 检查会话结构
            if (!session.authenticated || !session.username || !session.expires) {
                this.redirectToLogin('無効なセッション形式');
                return false;
            }

            // 检查会话是否过期
            if (session.expires < Date.now()) {
                this.redirectToLogin('セッションが期限切れです');
                return false;
            }

            // 检查会话是否即将过期
            const timeUntilExpiry = session.expires - Date.now();
            if (timeUntilExpiry < this.warningTime) {
                this.showExpiryWarning(Math.ceil(timeUntilExpiry / 1000 / 60));
            }

            return true;

        } catch (error) {
            this.redirectToLogin('セッション検証エラー');
            return false;
        }
    }

    // 重定向到登录页面
    redirectToLogin(reason = '') {
        localStorage.removeItem(this.sessionKey);
        
        if (reason && window.location.hostname === 'localhost') {
            console.warn('Admin redirect reason:', reason);
        }
        
        // 添加来源页面参数
        const returnUrl = encodeURIComponent(window.location.pathname + window.location.search);
        window.location.href = `${this.loginPath}?return=${returnUrl}`;
    }

    // 显示会话即将过期警告
    showExpiryWarning(minutesLeft) {
        // 避免重复显示警告
        if (this.warningShown) return;
        this.warningShown = true;

        const warningDiv = document.createElement('div');
        warningDiv.id = 'session-warning';
        warningDiv.className = 'fixed top-4 right-4 bg-yellow-500 text-white p-4 rounded-lg shadow-lg z-50';
        warningDiv.innerHTML = `
            <div class="flex items-center space-x-3">
                <span>⚠️</span>
                <div>
                    <div class="font-semibold">セッション期限警告</div>
                    <div class="text-sm">あと${minutesLeft}分でセッションが期限切れになります</div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" 
                        class="text-white hover:text-gray-200 ml-2">
                    ✕
                </button>
            </div>
        `;

        document.body.appendChild(warningDiv);

        // 10秒後自動隠藏
        setTimeout(() => {
            if (document.getElementById('session-warning')) {
                warningDiv.remove();
            }
        }, 10000);
    }

    // 设置会话监控
    setupSessionMonitoring() {
        // 每分钟检查一次会话状态
        setInterval(() => {
            if (!this.validateSession()) {
                return; // 会话无效，已重定向
            }
        }, 60000);

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // 页面重新可见时验证会话
                this.validateSession();
            }
        });

        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === this.sessionKey && !e.newValue) {
                // 会话被其他标签页删除
                this.redirectToLogin('セッションが他のタブで終了されました');
            }
        });
    }

    // 延长会话
    extendSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (sessionData) {
                const session = JSON.parse(sessionData);
                session.expires = Date.now() + this.sessionTimeout;
                localStorage.setItem(this.sessionKey, JSON.stringify(session));
                
                this.warningShown = false; // 重置警告状态
                
                // 移除现有警告
                const warning = document.getElementById('session-warning');
                if (warning) {
                    warning.remove();
                }
                
                return true;
            }
        } catch (error) {
            console.error('Failed to extend session:', error);
        }
        return false;
    }

    // 获取当前会话信息
    getSessionInfo() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (sessionData) {
                const session = JSON.parse(sessionData);
                return {
                    username: session.username,
                    loginTime: new Date(session.loginTime),
                    expiryTime: new Date(session.expires),
                    timeRemaining: session.expires - Date.now()
                };
            }
        } catch (error) {
            console.error('Failed to get session info:', error);
        }
        return null;
    }

    // 安全登出
    logout() {
        localStorage.removeItem(this.sessionKey);
        localStorage.removeItem('admin_login_attempts');
        
        // 清除所有相关数据
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('admin_')) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
        
        window.location.href = this.loginPath;
    }
}

// 自动初始化认证守卫
if (typeof window !== 'undefined') {
    window.AdminAuthGuard = AdminAuthGuard;
    
    // 页面加载时自动启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.adminAuthGuard = new AdminAuthGuard();
        });
    } else {
        window.adminAuthGuard = new AdminAuthGuard();
    }
}

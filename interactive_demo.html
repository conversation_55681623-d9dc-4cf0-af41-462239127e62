<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>次世代 AI 記帳プラットフォーム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- <script src="/background_control.js"></script> -->
    <script src="/music_control_new.js"></script>

    <!-- API 設定と認証マネージャー -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="image/favicon.ico">
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .chat-bubble { max-width: 300px; padding: 12px 16px; border-radius: 18px; margin: 8px 0; }
        .chat-user { background: #00d4aa; color: white; margin-left: auto; }
        .chat-ai { background: #f3f4f6; color: #374151; }
        .journal-preview { background: linear-gradient(135deg, #f0fdf9 0%, #eff6ff 100%); border: 1px solid #00d4aa; }
        .loading { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl font-bold mb-4">🚀 次世代AI記帳</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">

        <!-- Interactive AI Chat -->
        <section class="mb-12">
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Chat Header -->
                <div class="gradient-bg text-white p-4">
                    <h3 class="font-semibold">AI会計アシスタント</h3>
                    <p class="text-sm opacity-90">リアルバックエンドAPIに接続</p>
                </div>
                
                <!-- Chat Messages -->
                <div id="chat-messages" class="p-4 space-y-4 h-96 overflow-y-auto">
                    <div class="chat-bubble chat-ai">
                        こんにちは！AI会計アシスタントです。取引内容を教えてください。
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="border-t p-4">
                    <!-- Desktop layout: horizontal flex -->
                    <div class="hidden sm:flex space-x-2">
                        <input
                            id="chat-input"
                            type="text"
                            placeholder="例: 今日コンビニで事務用品を1200円で購入"
                            class="flex-1 border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <!-- ファイルアップロードボタン -->
                        <label for="invoice-upload" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 cursor-pointer flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <span>請求書</span>
                        </label>
                        <input
                            id="invoice-upload"
                            type="file"
                            accept="image/*,.pdf"
                            class="hidden"
                        >
                        <button
                            id="send-btn"
                            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
                        >
                            送信
                        </button>
                    </div>
                    
                    <!-- Mobile layout: input full width, buttons below -->
                    <div class="sm:hidden">
                        <input
                            id="chat-input-mobile"
                            type="text"
                            placeholder="例: 今日コンビニで事務用品を1200円で購入"
                            class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 mb-3"
                        >
                        <div class="flex space-x-2">
                            <!-- ファイルアップロードボタン -->
                            <label for="invoice-upload-mobile" class="flex-1 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 cursor-pointer flex items-center justify-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <span>請求書</span>
                            </label>
                            <input
                                id="invoice-upload-mobile"
                                type="file"
                                accept="image/*,.pdf"
                                class="hidden"
                            >
                            <button
                                id="send-btn-mobile"
                                class="flex-1 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
                            >
                                送信
                            </button>
                        </div>
                    </div>
                    <div class="mt-2 flex flex-wrap gap-2">
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="今日コンビニで事務用品を1200円で購入">
                            事務用品購入
                        </button>
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="ABC会社から売上50万円を銀行振込で受取">
                            売上受取
                        </button>
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="今月の家賃15万円を支払い">
                            家賃支払い
                        </button>
                        <button class="quick-input text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded" data-text="電気代8500円を口座振替で支払い">
                            電気代支払い
                        </button>
                    </div>

                    <!-- 対話履歴パネル -->
                    <div class="mt-6">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold">💬 対話履歴 (<span id="history-count">0</span>/30)</h3>
                            <div class="space-x-2">
                                <button id="toggle-history" class="text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-full transition-colors">
                                    履歴表示
                                </button>
                                <button id="clear-history" class="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-full transition-colors">
                                    履歴クリア
                                </button>
                            </div>
                        </div>

                        <div id="chat-history-panel" class="hidden bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                            <div id="history-list" class="space-y-3">
                                <!-- 履歴がここに表示されます -->
                            </div>
                            <div id="no-history" class="text-center text-gray-500 py-8 hidden">
                                <div class="text-4xl mb-2">📝</div>
                                <p>まだ対話履歴がありません</p>
                                <p class="text-sm">AIとの対話を開始すると、履歴が自動保存されます</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- System Status -->
        <section class="text-center">
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-4">🚀 システム状態</h2>
                <div id="system-stats" class="grid md:grid-cols-4 gap-6">
                    <div>
                        <div class="text-3xl font-bold text-blue-500">--</div>
                        <div class="text-gray-600">API応答時間</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-green-500">--</div>
                        <div class="text-gray-600">処理成功数</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-purple-500">--</div>
                        <div class="text-gray-600">生成仕訳数</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold text-orange-500">--</div>
                        <div class="text-gray-600">平均信頼度</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // グローバル変数
        let processedCount = 0;
        let successCount = 0;
        let totalConfidence = 0;
        let responseTimes = [];

        // AI対話履歴管理
        const CHAT_HISTORY_KEY = 'goldenledger_ai_chat_history';
        const MAX_CHAT_HISTORY = 30; // 最大30件の対話を保存

        // 対話履歴構造
        class ChatHistory {
            constructor() {
                this.conversations = this.loadFromStorage();
            }

            // ローカルストレージから履歴を読み込み
            loadFromStorage() {
                try {
                    const stored = localStorage.getItem(CHAT_HISTORY_KEY);
                    if (stored) {
                        const parsed = JSON.parse(stored);
                        return Array.isArray(parsed) ? parsed : [];
                    }
                } catch (error) {
                    console.warn('対話履歴の読み込み失敗:', error);
                }
                return [];
            }

            // ローカルストレージに保存
            saveToStorage() {
                try {
                    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(this.conversations));
                } catch (error) {
                    console.warn('対話履歴の保存失敗:', error);
                }
            }

            // 新しい対話を追加
            addConversation(userMessage, aiResponse, timestamp = new Date()) {
                const conversation = {
                    id: Date.now() + Math.random(),
                    timestamp: timestamp.toISOString(),
                    userMessage: userMessage,
                    aiResponse: aiResponse,
                    displayTime: timestamp.toLocaleString('zh-CN')
                };

                this.conversations.unshift(conversation); // 先頭に追加

                // 最大数量制限を維持
                if (this.conversations.length > MAX_CHAT_HISTORY) {
                    this.conversations = this.conversations.slice(0, MAX_CHAT_HISTORY);
                }

                this.saveToStorage();
                return conversation;
            }

            // すべての対話を取得
            getAllConversations() {
                return this.conversations;
            }

            // 履歴をクリア
            clearHistory() {
                this.conversations = [];
                this.saveToStorage();
            }

            // 対話数を取得
            getCount() {
                return this.conversations.length;
            }
        }

        // 対話履歴マネージャーを初期化
        const chatHistory = new ChatHistory();

        // 確認して記録を保存
        async function confirmAndSave(button) {
            // 防止重复提交
            if (button.dataset.processing === 'true') {
                console.log('正在处理中，忽略重复点击');
                return;
            }

            // 标记为处理中
            button.dataset.processing = 'true';

            // 保存原始按钮文本，确保在整个函数作用域中可用
            const originalText = button.innerHTML;

            try {
                // 仕訳データを取得
                const journalDataStr = button.getAttribute('data-journal').replace(/&apos;/g, "'");
                const journalData = JSON.parse(journalDataStr);

                // 確認状態を表示
                button.innerHTML = '<span class="animate-spin">⏳</span> 保存中...';
                button.disabled = true;

                // 附件上传处理
                let attachmentId = null;

                // 调试日志
                console.log('检查附件数据:', {
                    hasAttachment: !!journalData.attachment,
                    hasFileContent: journalData.attachment ? !!journalData.attachment.file_content : false,
                    attachmentKeys: journalData.attachment ? Object.keys(journalData.attachment) : [],
                    journalDataKeys: Object.keys(journalData)
                });

                if (journalData.attachment && journalData.attachment.file_content) {
                    console.log('开始处理附件上传...');
                    try {
                        // 创建FormData用于附件上传
                        const formData = new FormData();

                        // 将base64转换为Blob
                        const base64Data = journalData.attachment.file_content.replace(/^data:image\/[a-z]+;base64,/, '');
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        const blob = new Blob([byteArray], { type: journalData.attachment.content_type || 'image/jpeg' });

                        console.log('创建的Blob信息:', {
                            size: blob.size,
                            type: blob.type,
                            filename: journalData.attachment.original_filename || 'invoice.jpg'
                        });

                        formData.append('file', blob, journalData.attachment.original_filename || 'invoice.jpg');
                        formData.append('description', '発票添付ファイル');

                        // 生成真实的记录ID，而不是临时ID
                        const realEntryId = journalData.id || crypto.randomUUID();
                        formData.append('entryId', realEntryId);
                        formData.append('companyId', 'default');

                        // 更新journalData中的ID，确保后续保存使用相同ID
                        journalData.id = realEntryId;

                        console.log('准备上传附件，entryId:', tempEntryId);

                        // 上传附件到R2
                        const uploadResponse = await fetch(window.GoldenLedgerAPI.url('/api/attachments/upload'), {
                            method: 'POST',
                            headers: {
                                ...window.authManager.getAuthHeaders()
                                // 注意：不要设置Content-Type，让浏览器自动设置multipart/form-data
                            },
                            body: formData
                        });

                        console.log('附件上传响应状态:', uploadResponse.status);

                        if (uploadResponse.ok) {
                            const uploadResult = await uploadResponse.json();
                            console.log('附件上传响应:', uploadResult);
                            if (uploadResult.success) {
                                attachmentId = uploadResult.data.id;
                                console.log('附件上传成功:', attachmentId);
                            } else {
                                console.warn('附件上传失败:', uploadResult.error);
                            }
                        } else {
                            const errorText = await uploadResponse.text();
                            console.warn('附件上传HTTP错误:', uploadResponse.status, errorText);
                            console.warn('但继续保存仕訳');
                        }
                    } catch (attachmentError) {
                        console.error('附件处理异常:', attachmentError);
                        // 继续保存仕訳，即使附件上传失败
                    }
                } else {
                    console.log('没有附件数据，跳过附件上传');
                }

                // 准备仕訳数据，包含附件ID
                const saveData = {
                    company_id: 'default',
                    ...journalData,
                    confirmed: true
                };
                
                // 如果有附件ID，添加到数据中
                if (attachmentId) {
                    saveData.attachment_id = attachmentId;
                }
                
                // 移除attachment对象，避免发送大量base64数据
                delete saveData.attachment;

                // Workers APIに保存リクエストを送信
                const response = await fetch(window.GoldenLedgerAPI.url('/api/journal-entries'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...window.authManager.getAuthHeaders()
                    },
                    body: JSON.stringify(saveData)
                });

                if (response.ok) {
                    const result = await response.json();

                    // 成功状態を表示
                    button.innerHTML = '✅ 保存済み';
                    button.className = 'bg-green-600 text-white px-4 py-2 rounded text-sm cursor-default';

                    // 成功メッセージを表示
                    showNotification('仕訳記録がデータベースに正常に保存されました！', 'success');

                    // 清除处理标记
                    button.dataset.processing = 'false';

                    // 編集ボタンのデータを更新し、保存された記録を指すようにする
                    const editButton = button.parentElement.querySelector('button[onclick*="editEntry"]');
                    if (editButton && result.entry_id) {
                        // 更新按钮数据，使用数据库返回的真实ID
                        const updatedJournalData = JSON.parse(journalDataStr);
                        updatedJournalData.id = result.entry_id;
                        editButton.setAttribute('data-journal', JSON.stringify(updatedJournalData).replace(/'/g, "&apos;"));
                        editButton.innerHTML = '✏️ 編集';
                        editButton.className = 'border border-gray-300 px-4 py-2 rounded text-sm hover:bg-gray-50 transition-colors';
                    }

                } else {
                    throw new Error('保存失敗');
                }

            } catch (error) {
                console.error('記録の保存失敗:', error);

                // ボタン状態を復元
                button.innerHTML = originalText;
                button.disabled = false;

                // 清除处理标记
                button.dataset.processing = 'false';

                showNotification('保存失敗、再試行してください', 'error');
            }
        }

        // 記録を編集
        async function editEntry(button) {
            try {
                // 仕訳データを取得
                const journalDataStr = button.getAttribute('data-journal').replace(/&apos;/g, "'");
                const journalData = JSON.parse(journalDataStr);

                // 記録がデータベースに保存されているかチェック
                if (!journalData.id || journalData.id.startsWith('TEMP_')) {
                    // 記録がまだ保存されていない、ユーザーに先に保存するよう促す
                    showNotification('先に「確認して記録」をクリックして記録を保存してから編集してください', 'warning');
                    return;
                }

                // 記録がデータベースに存在するか検証
                try {
                    const checkResponse = await fetch(window.GoldenLedgerAPI.url('/api/journal-entries', { company_id: 'default' }), {
                        headers: {
                            ...window.authManager.getAuthHeaders()
                        }
                    });
                    const responseData = await checkResponse.json();
                    const allEntries = responseData.success ? responseData.data : [];
                    const entryExists = allEntries.some(entry => entry.id === journalData.id);

                    if (!entryExists) {
                        showNotification('記録が存在しません、先に記録を保存してください', 'warning');
                        return;
                    }
                } catch (error) {
                    console.error('記録の存在確認失敗:', error);
                    showNotification('記録状態を確認できません', 'error');
                    return;
                }

                // 編集モーダルを作成
                showEditModal(journalData, button);

            } catch (error) {
                console.error('記録の編集失敗:', error);
                showNotification('記録を編集できません', 'error');
            }
        }

        // 編集モーダルを表示
        function showEditModal(journalData, button) {
            // 保存関数で使用するためにグローバル変数に保存
            window.currentEditingData = journalData;
            // モーダルHTMLを作成
            const modalHTML = `
                <div id="edit-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">仕訳編集</h3>
                            <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <form id="edit-form" class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">日付</label>
                                    <input type="date" id="edit-date" value=""
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">時間</label>
                                    <input type="time" id="edit-time" value=""
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">説明</label>
                                <input type="text" id="edit-description" value=""
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">借方科目</label>
                                    <select id="edit-debit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                        <option value="">借方科目を選択</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">貸方科目</label>
                                    <select id="edit-credit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                                        <option value="">貸方科目を選択</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">金額</label>
                                <input type="number" id="edit-amount" value="" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div class="flex space-x-3 pt-4">
                                <button type="button" id="save-edit-btn" onclick="saveEditedEntry()"
                                        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                    変更を保存
                                </button>
                                <button type="button" onclick="closeEditModal()"
                                        class="flex-1 border border-gray-300 px-4 py-2 rounded hover:bg-gray-50">
                                    キャンセル
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // 设置表单值
            setTimeout(() => {
                const descInput = document.getElementById('edit-description');
                const dateInput = document.getElementById('edit-date');
                const timeInput = document.getElementById('edit-time');
                const amountInput = document.getElementById('edit-amount');

                if (descInput) descInput.value = journalData.description || '';
                if (dateInput) dateInput.value = journalData.entry_date || '';
                if (timeInput) timeInput.value = journalData.entry_time || '';
                if (amountInput) amountInput.value = journalData.amount || '';
            }, 0);

            // 加载科目选项
            loadAccountOptionsForDemo(journalData);
        }

        // 加载科目选项（演示页面版本）
        async function loadAccountOptionsForDemo(journalData) {
            try {
                // 获取科目列表
                const response = await fetch('/journal-entries/default/accounts');
                if (!response.ok) {
                    throw new Error('科目リストの取得失敗');
                }

                const accounts = await response.json();

                // 获取下拉框元素
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (!debitSelect || !creditSelect) {
                    console.error('科目選択ボックスが見つかりません');
                    return;
                }

                // 清空现有选项
                debitSelect.innerHTML = '<option value="">借方科目を選択</option>';
                creditSelect.innerHTML = '<option value="">貸方科目を選択</option>';

                // 添加所有科目选项
                Object.keys(accounts).forEach(category => {
                    // 为每个类别创建选项组
                    const debitOptgroup = document.createElement('optgroup');
                    debitOptgroup.label = category;
                    const creditOptgroup = document.createElement('optgroup');
                    creditOptgroup.label = category;

                    accounts[category].forEach(account => {
                        // 借方科目选项
                        const debitOption = document.createElement('option');
                        debitOption.value = account;
                        debitOption.textContent = account;
                        if (journalData.debit_account === account) {
                            debitOption.selected = true;
                        }
                        debitOptgroup.appendChild(debitOption);

                        // 贷方科目选项
                        const creditOption = document.createElement('option');
                        creditOption.value = account;
                        creditOption.textContent = account;
                        if (journalData.credit_account === account) {
                            creditOption.selected = true;
                        }
                        creditOptgroup.appendChild(creditOption);
                    });

                    debitSelect.appendChild(debitOptgroup);
                    creditSelect.appendChild(creditOptgroup);
                });

            } catch (error) {
                console.error('科目オプションの読み込み失敗:', error);
                // 如果加载失败，回退到文本输入
                const debitSelect = document.getElementById('edit-debit');
                const creditSelect = document.getElementById('edit-credit');

                if (debitSelect) {
                    debitSelect.outerHTML = `<input type="text" id="edit-debit" value="" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">`;
                    // 设置值
                    setTimeout(() => {
                        const newDebitInput = document.getElementById('edit-debit');
                        if (newDebitInput) {
                            newDebitInput.value = journalData.debit_account || '';
                        }
                    }, 0);
                }
                if (creditSelect) {
                    creditSelect.outerHTML = `<input type="text" id="edit-credit" value="" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">`;
                    // 设置值
                    setTimeout(() => {
                        const newCreditInput = document.getElementById('edit-credit');
                        if (newCreditInput) {
                            newCreditInput.value = journalData.credit_account || '';
                        }
                    }, 0);
                }
            }
        }

        // 編集モーダルを閉じる
        function closeEditModal() {
            const modal = document.getElementById('edit-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 編集後の記録を保存
        async function saveEditedEntry() {
            // 从全局变量获取原始数据
            const originalData = window.currentEditingData;

            if (!originalData) {
                alert('エラー：元データを取得できません');
                return;
            }

            try {
                // 編集後のデータを取得
                const entryDate = document.getElementById('edit-date').value;
                const entryTime = document.getElementById('edit-time').value;

                // 完全なdatetimeを構築
                let entryDatetime = '';
                if (entryDate && entryTime) {
                    entryDatetime = `${entryDate}T${entryTime}`;
                } else if (entryDate) {
                    entryDatetime = `${entryDate}T${new Date().toTimeString().slice(0, 8)}`;
                }

                const editedData = {
                    id: originalData.id,
                    entry_date: entryDate,
                    entry_time: entryTime,
                    entry_datetime: entryDatetime,
                    description: document.getElementById('edit-description').value,
                    debit_account: document.getElementById('edit-debit').value,
                    credit_account: document.getElementById('edit-credit').value,
                    amount: parseFloat(document.getElementById('edit-amount').value),
                    reference_number: originalData.reference_number || ''
                };

                // 更新リクエストを送信
                const response = await fetch(`/journal-entries/${editedData.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(editedData)
                });

                if (response.ok) {
                    // モーダルを閉じる
                    closeEditModal();

                    // 成功メッセージを表示
                    showNotification('仕訳記録が正常に更新されました！', 'success');

                    // ページ表示データを更新
                    updateDisplayedEntry(editedData);

                } else {
                    throw new Error('更新失敗');
                }

            } catch (error) {
                console.error('記録の更新失敗:', error);
                showNotification('更新に失敗しました、再試行してください', 'error');
            }
        }

        // 表示される記録データを更新
        function updateDisplayedEntry(editedData) {
            // 現在表示されている仕訳カードを見つけて内容を更新
            const entryCards = document.querySelectorAll('.bg-blue-50');
            entryCards.forEach(card => {
                const buttons = card.querySelectorAll('button[data-journal]');
                buttons.forEach(button => {
                    const journalData = JSON.parse(button.getAttribute('data-journal'));
                    if (journalData.id === editedData.id) {
                        // ボタンのデータ属性を更新
                        const newJournalData = JSON.stringify(editedData);
                        button.setAttribute('data-journal', newJournalData);

                        // 表示内容を更新
                        const cardContent = card.querySelector('.space-y-2');
                        if (cardContent) {
                            cardContent.innerHTML = `
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-blue-600 font-medium">借方</span>
                                        <div class="text-blue-900 font-semibold">${editedData.debit_account}</div>
                                        <div class="text-blue-700">¥${editedData.amount.toLocaleString()}</div>
                                    </div>
                                    <div>
                                        <span class="text-green-600 font-medium">貸方</span>
                                        <div class="text-green-900 font-semibold">${editedData.credit_account}</div>
                                        <div class="text-green-700">¥${editedData.amount.toLocaleString()}</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-600">
                                    <div>摘要: ${editedData.description}</div>
                                    <div>日付: ${editedData.entry_date}${editedData.entry_time ? ' ' + editedData.entry_time : ''}</div>
                                </div>
                            `;
                        }
                    }
                });
            });
        }

        // 通知メッセージを表示
        function showNotification(message, type = 'info') {
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500'
            };

            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 隐藏动画
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 請求書アップロードを処理
        async function handleInvoiceUpload(file) {
            const messagesContainer = document.getElementById('chat-messages');

            // アップロード中のメッセージを表示
            const uploadMessage = document.createElement('div');
            uploadMessage.className = 'flex justify-end mb-4';
            uploadMessage.innerHTML = `
                <div class="chat-bubble bg-blue-500 text-white">
                    📄 請求書をアップロード中: ${file.name}
                    <div class="mt-2">
                        <div class="bg-white/20 rounded-full h-2">
                            <div class="bg-white h-2 rounded-full animate-pulse" style="width: 50%"></div>
                        </div>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(uploadMessage);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            try {
                // ファイルをbase64に変換
                const base64Data = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result.split(',')[1]); // data:image/...;base64, の部分を除去
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });

                // 保存完整的文件信息用于后续上传
                const fullBase64 = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result); // 保留完整的data:image/...;base64,前缀
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });

                // Workers OCR処理に送信（JSON形式）
                const response = await fetch(window.GoldenLedgerAPI.url('/ai-bookkeeping/invoice-ocr'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...window.authManager.getAuthHeaders()
                    },
                    body: JSON.stringify({
                        image_data: base64Data,
                        company_id: 'default'
                    })
                });

                const result = await response.json();

                // 将原始文件信息添加到结果中
                if (result.success) {
                    result.file_content = fullBase64;
                    result.content_type = file.type;
                    result.original_filename = file.name;

                    // 添加调试日志
                    console.log('OCR成功，附件信息:', {
                        hasFileContent: !!result.file_content,
                        contentType: result.content_type,
                        filename: result.original_filename,
                        fileSize: result.file_content ? result.file_content.length : 0
                    });
                }

                // アップロード中のメッセージを削除
                uploadMessage.remove();

                if (result.success) {
                    // OCR認識結果を表示
                    const ocrMessage = document.createElement('div');
                    ocrMessage.className = 'flex justify-start mb-4';
                    ocrMessage.innerHTML = `
                        <div class="chat-bubble bg-gray-100 text-gray-800">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="text-green-600">✅</span>
                                <span class="font-medium">請求書認識成功</span>
                            </div>
                            <div class="text-sm space-y-1">
                                <div><strong>金額:</strong> ¥${result.total_amount || result.amount || 'N/A'}</div>
                                <div><strong>日付:</strong> ${result.invoice_date || result.date || 'N/A'}</div>
                                <div><strong>業者:</strong> ${result.vendor_name || result.vendor || 'N/A'}</div>
                                <div><strong>説明:</strong> ${result.items && result.items.length > 0 ? result.items[0].description : result.description || 'N/A'}</div>
                            </div>
                            <button onclick="confirmInvoiceEntry(this)"
                                    class="mt-3 bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                                    data-result='${JSON.stringify(result).replace(/'/g, "&apos;")}'>
                                記帳確認
                            </button>
                        </div>
                    `;
                    messagesContainer.appendChild(ocrMessage);
                } else {
                    // エラーメッセージを表示
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'flex justify-start mb-4';
                    errorMessage.innerHTML = `
                        <div class="chat-bubble bg-red-100 text-red-800">
                            <div class="flex items-center space-x-2">
                                <span class="text-red-600">❌</span>
                                <span>請求書認識失敗: ${result.error || '不明なエラー'}</span>
                            </div>
                        </div>
                    `;
                    messagesContainer.appendChild(errorMessage);
                }

            } catch (error) {
                // アップロード中のメッセージを削除
                uploadMessage.remove();

                // ネットワークエラーを表示
                const errorMessage = document.createElement('div');
                errorMessage.className = 'flex justify-start mb-4';
                errorMessage.innerHTML = `
                    <div class="chat-bubble bg-red-100 text-red-800">
                        <div class="flex items-center space-x-2">
                            <span class="text-red-600">❌</span>
                            <span>アップロード失敗: ${error.message}</span>
                        </div>
                    </div>
                `;
                messagesContainer.appendChild(errorMessage);
            }

            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 請求書記帳を確認
        async function confirmInvoiceEntry(buttonElement) {
            try {
                const resultJson = buttonElement.getAttribute('data-result').replace(/&apos;/g, "'");
                const result = JSON.parse(resultJson);

                // ボタンを無効化
                buttonElement.disabled = true;
                buttonElement.textContent = '処理中...';

                // 自然言語説明を構築
                const naturalText = `${result.invoice_date || result.date || '今日'}${result.vendor_name || result.vendor || ''}で${result.items && result.items[0] ? result.items[0].description : result.description || '商品'}を${result.total_amount || result.amount || '0'}円で購入`;

                // 添付ファイルデータを準備
                const attachmentData = {
                    file_content: result.file_content,
                    content_type: result.content_type,
                    original_filename: result.original_filename
                };

                // 调试日志
                console.log('准备附件数据:', {
                    hasFileContent: !!attachmentData.file_content,
                    contentType: attachmentData.content_type,
                    filename: attachmentData.original_filename,
                    fileContentLength: attachmentData.file_content ? attachmentData.file_content.length : 0
                });

                // 既存のsendMessage関数を呼び出し、添付ファイルデータを渡す
                await sendMessage(naturalText, attachmentData);

                // ボタン状態を更新
                buttonElement.textContent = '記帳済み';
                buttonElement.className = 'mt-3 bg-green-500 text-white px-3 py-1 rounded text-sm cursor-not-allowed';

            } catch (error) {
                console.error('記帳確認失敗:', error);
                buttonElement.disabled = false;
                buttonElement.textContent = '再試行';
                buttonElement.className = 'mt-3 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600';
            }
        }

        // AIにメッセージを送信
        async function sendMessage(text, attachmentData = null) {
            const messagesContainer = document.getElementById('chat-messages');
            const sendBtn = document.getElementById('send-btn');

            // ユーザーメッセージを追加
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-bubble chat-user';
            userMessage.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-1">${text}</div>
                    <div class="text-xs text-gray-500 ml-2">${new Date().toLocaleTimeString('zh-CN')}</div>
                </div>
            `;
            messagesContainer.appendChild(userMessage);
            
            // ローディングメッセージを追加
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'chat-bubble chat-ai loading';
            loadingMessage.textContent = '処理中...';
            messagesContainer.appendChild(loadingMessage);
            
            // 最下部にスクロール
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // 送信ボタンを無効化
            sendBtn.disabled = true;
            
            try {
                const startTime = Date.now();
                const response = await fetch(window.GoldenLedgerAPI.url('/api/ai/process-text'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...window.authManager.getAuthHeaders()
                    },
                    body: JSON.stringify({
                        text: text,
                        company_id: 'default'
                    })
                });
                
                const responseTime = Date.now() - startTime;
                responseTimes.push(responseTime);
                
                const data = await response.json();
                
                // ローディングメッセージを削除
                messagesContainer.removeChild(loadingMessage);
                
                if (data.success) {
                    // 仕訳プレビューを作成
                    const aiMessage = document.createElement('div');
                    aiMessage.className = 'chat-bubble chat-ai';
                    aiMessage.innerHTML = `
                        <div class="space-y-3">
                            <p>承知いたしました。以下の仕訳を生成しました：</p>
                            <div class="journal-preview rounded-lg p-4">
                                <h4 class="font-semibold mb-3">生成された仕訳</h4>
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label class="text-xs text-gray-500 uppercase">借方</label>
                                        <div class="bg-blue-50 border border-blue-200 rounded p-2">
                                            <div class="font-medium text-blue-900">${data.journal_entry.debit_account}</div>
                                            <div class="text-sm text-blue-700">¥${data.journal_entry.amount.toLocaleString()}</div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="text-xs text-gray-500 uppercase">貸方</label>
                                        <div class="bg-green-50 border border-green-200 rounded p-2">
                                            <div class="font-medium text-green-900">${data.journal_entry.credit_account}</div>
                                            <div class="text-sm text-green-700">¥${data.journal_entry.amount.toLocaleString()}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <strong>摘要:</strong> ${data.journal_entry.description}
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <strong>日時:</strong> ${data.journal_entry.entry_date}${data.journal_entry.entry_time ? ' ' + data.journal_entry.entry_time : ''}
                                </div>
                                <div class="text-sm text-gray-600 mb-3">
                                    <strong>信頼度:</strong> ${(data.confidence * 100).toFixed(1)}%
                                </div>
                                ${data.warnings && data.warnings.length > 0 ? `
                                    <div class="bg-yellow-50 border border-yellow-200 rounded p-2 mb-3">
                                        <strong>注意:</strong> ${data.warnings.join(', ')}
                                    </div>
                                ` : ''}
                                <div class="flex flex-wrap gap-2">
                                    <button onclick="confirmAndSave(this)" class="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600 transition-colors" data-journal='${JSON.stringify({...data.journal_entry, attachment: attachmentData}).replace(/'/g, "&apos;")}'>
                                        ✓ 確認して記録
                                    </button>
                                    <button onclick="editEntry(this)" class="border border-gray-300 px-4 py-2 rounded text-sm hover:bg-gray-50 transition-colors" data-journal='${JSON.stringify({...data.journal_entry, attachment: attachmentData}).replace(/'/g, "&apos;")}'>
                                        ✏️ 編集
                                    </button>
                                    <a href="/journal_entries.html" class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 inline-block transition-colors">
                                        📊 すべての記録を表示
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                    messagesContainer.appendChild(aiMessage);

                    // 会話履歴を保存
                    const aiResponseText = `承知いたしました。以下の仕訳を生成しました：\n借方: ${data.journal_entry.debit_account} ¥${data.journal_entry.amount.toLocaleString()}\n貸方: ${data.journal_entry.credit_account} ¥${data.journal_entry.amount.toLocaleString()}\n摘要: ${data.journal_entry.description}`;
                    chatHistory.addConversation(text, aiResponseText);

                    // 統計を更新
                    processedCount++;
                    successCount++;
                    totalConfidence += data.confidence;
                    updateStats();
                    
                } else {
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'chat-bubble chat-ai';
                    errorMessage.textContent = `申し訳ございません。エラーが発生しました: ${data.error}`;
                    messagesContainer.appendChild(errorMessage);
                    
                    processedCount++;
                    updateStats();
                }
                
            } catch (error) {
                // ローディングメッセージを削除
                if (messagesContainer.contains(loadingMessage)) {
                    messagesContainer.removeChild(loadingMessage);
                }

                console.warn('AI記帳リクエスト失敗:', error.message);

                const errorMessage = document.createElement('div');
                errorMessage.className = 'chat-bubble chat-ai';
                errorMessage.innerHTML = `
                    <div class="space-y-3">
                        <p>💡 AI機能は開発中です。以下をお試しください：</p>
                        <div class="bg-blue-50 border border-blue-200 rounded p-3">
                            <div class="space-y-2 text-sm">
                                <div>• <a href="/journal_entries.html" class="text-blue-600 hover:underline">既存の記録を表示</a></div>
                                <div>• <a href="/master_dashboard.html" class="text-blue-600 hover:underline">メインコンソールにアクセス</a></div>
                                <div>• 手動でテストデータを追加</div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">技術情報: ${error.message}</div>
                    </div>
                `;
                messagesContainer.appendChild(errorMessage);

                // エラー会話履歴を保存
                const errorResponseText = "AI機能は開発中です。他の機能ページへのアクセスをお試しください。";
                chatHistory.addConversation(text, errorResponseText);

                processedCount++;
                updateStats();
            }
            
            // 送信ボタンを再有効化
            sendBtn.disabled = false;
            
            // 最下部にスクロール
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // システム統計を初期化
        async function initializeSystemStats() {
            try {
                // システム統計データを取得
                const response = await fetch('/dashboard/summary/default');
                if (response.ok) {
                    const data = await response.json();

                    // APIデータを使用して統計を初期化
                    if (data.ai_stats) {
                        const aiStats = data.ai_stats;
                        processedCount = aiStats.total_processed || 0;
                        successCount = Math.round(processedCount * (aiStats.success_rate || 0));
                        totalConfidence = successCount * (aiStats.avg_confidence || 0);

                        // レスポンス時間配列を初期化
                        if (aiStats.avg_response_time) {
                            responseTimes = [aiStats.avg_response_time];
                        }
                    }
                }
            } catch (error) {
                console.log('初期統計データを取得できません、デフォルト値を使用');
            }

            // 表示を更新
            updateStats();
        }

        // 統計情報を更新
        function updateStats() {
            const avgResponseTime = responseTimes.length > 0 ?
                responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
            const avgConfidence = processedCount > 0 ? totalConfidence / successCount : 0;

            const statsContainer = document.getElementById('system-stats');
            statsContainer.innerHTML = `
                <div>
                    <div class="text-3xl font-bold text-blue-500">${avgResponseTime.toFixed(0)}ms</div>
                    <div class="text-gray-600">API応答時間</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-green-500">${successCount}</div>
                    <div class="text-gray-600">処理成功数</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-purple-500">${processedCount}</div>
                    <div class="text-gray-600">総処理数</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-orange-500">${(avgConfidence * 100).toFixed(1)}%</div>
                    <div class="text-gray-600">平均信頼度</div>
                </div>
            `;

            // 履歴記録数を更新
            updateHistoryCount();
        }

        // 履歴記録数を更新
        function updateHistoryCount() {
            const count = chatHistory.getCount();
            const historyCountElement = document.getElementById('history-count');
            if (historyCountElement) {
                historyCountElement.textContent = count;
            }
        }

        // 履歴記録パネルの表示/非表示
        function toggleHistoryPanel() {
            const panel = document.getElementById('chat-history-panel');
            const button = document.getElementById('toggle-history');

            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                button.textContent = '履歴を隠す';
                loadHistoryList();
            } else {
                panel.classList.add('hidden');
                button.textContent = '履歴を表示';
            }
        }

        // 履歴記録リストを読み込み
        function loadHistoryList() {
            const historyList = document.getElementById('history-list');
            const noHistory = document.getElementById('no-history');
            const conversations = chatHistory.getAllConversations();

            if (conversations.length === 0) {
                historyList.innerHTML = '';
                noHistory.classList.remove('hidden');
                return;
            }

            noHistory.classList.add('hidden');

            historyList.innerHTML = conversations.map((conv, index) => `
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">#${conversations.length - index}</span>
                        <span class="text-xs text-gray-500">${conv.displayTime}</span>
                    </div>
                    <div class="space-y-2">
                        <div class="text-sm">
                            <span class="font-medium text-blue-600">ユーザー:</span>
                            <span class="text-gray-800">${conv.userMessage}</span>
                        </div>
                        <div class="text-sm">
                            <span class="font-medium text-green-600">AI:</span>
                            <span class="text-gray-700">${conv.aiResponse.substring(0, 100)}${conv.aiResponse.length > 100 ? '...' : ''}</span>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <button onclick="replayConversation('${conv.userMessage.replace(/'/g, '\\\'')}')" class="text-xs text-blue-600 hover:text-blue-800">
                            再送信
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // イベントリスナー
        document.addEventListener('DOMContentLoaded', async function() {
            // 認証を初期化
            try {
                await window.authManager.initialize();
                if (!window.authManager.isAuthenticated) {
                    console.warn('ユーザーが認証されていません、ログインページにリダイレクトします');
                    // ログインページにリダイレクト
                    window.location.href = 'https://ledger.goldenorangetech.com/login_simple?return=' + encodeURIComponent(window.location.href);
                    return; // 以降の処理を停止
                }
            } catch (error) {
                console.error('認証初期化失敗:', error);
                // 認証エラーの場合もログインページにリダイレクト
                window.location.href = 'https://ledger.goldenorangetech.com/login_simple?return=' + encodeURIComponent(window.location.href);
                return;
            }

            // Desktop elements
            const chatInput = document.getElementById('chat-input');
            const sendBtn = document.getElementById('send-btn');
            const invoiceUpload = document.getElementById('invoice-upload');
            
            // Mobile elements
            const chatInputMobile = document.getElementById('chat-input-mobile');
            const sendBtnMobile = document.getElementById('send-btn-mobile');
            const invoiceUploadMobile = document.getElementById('invoice-upload-mobile');
            
            // Function to send message from either input
            function sendMessageFromInput(inputElement) {
                const text = inputElement.value.trim();
                if (text) {
                    sendMessage(text);
                    inputElement.value = '';
                }
            }
            
            // Desktop event listeners
            if (sendBtn) {
                sendBtn.addEventListener('click', function() {
                    sendMessageFromInput(chatInput);
                });
            }
            
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessageFromInput(chatInput);
                    }
                });
            }
            
            // Mobile event listeners
            if (sendBtnMobile) {
                sendBtnMobile.addEventListener('click', function() {
                    sendMessageFromInput(chatInputMobile);
                });
            }
            
            if (chatInputMobile) {
                chatInputMobile.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessageFromInput(chatInputMobile);
                    }
                });
            }
            
            // クイック入力ボタン (works for both desktop and mobile)
            document.querySelectorAll('.quick-input').forEach(btn => {
                btn.addEventListener('click', function() {
                    const text = this.getAttribute('data-text');
                    // Set text in both inputs and send from the visible one
                    if (chatInput && !chatInput.closest('.hidden')) {
                        chatInput.value = text;
                        sendMessageFromInput(chatInput);
                    } else if (chatInputMobile) {
                        chatInputMobile.value = text;
                        sendMessageFromInput(chatInputMobile);
                    }
                });
            });

            // ファイルアップロード処理 (Desktop)
            if (invoiceUpload) {
                invoiceUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        handleInvoiceUpload(file);
                    }
                });
            }
            
            // ファイルアップロード処理 (Mobile)
            if (invoiceUploadMobile) {
                invoiceUploadMobile.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        handleInvoiceUpload(file);
                    }
                });
            }
            
            // 履歴記録ボタンイベント
            document.getElementById('toggle-history').addEventListener('click', toggleHistoryPanel);
            document.getElementById('clear-history').addEventListener('click', clearChatHistory);

            // システム統計を初期化
            initializeSystemStats();

            // 会話履歴を読み込み
            loadChatHistoryOnPageLoad();

        });

        // 履歴会話を再送信
        function replayConversation(message) {
            const chatInput = document.getElementById('chat-input');
            const chatInputMobile = document.getElementById('chat-input-mobile');
            
            // Set message in the visible input field
            if (chatInput && !chatInput.closest('.hidden')) {
                chatInput.value = message;
                chatInput.focus();
            } else if (chatInputMobile) {
                chatInputMobile.value = message;
                chatInputMobile.focus();
            }
        }

        // 履歴記録をクリア
        function clearChatHistory() {
            if (confirm('すべての会話履歴をクリアしますか？この操作は元に戻せません。')) {
                chatHistory.clearHistory();
                loadHistoryList();
                updateHistoryCount();

                // クリア成功の通知を表示
                const messagesContainer = document.getElementById('chat-messages');
                const notification = document.createElement('div');
                notification.className = 'text-center text-gray-500 text-sm py-2 bg-gray-100 rounded-lg';
                notification.textContent = '✅ 会話履歴がクリアされました';
                messagesContainer.appendChild(notification);

                setTimeout(() => {
                    if (messagesContainer.contains(notification)) {
                        messagesContainer.removeChild(notification);
                    }
                }, 3000);
            }
        }

        // ページ読み込み時に履歴記録を復元
        function loadChatHistoryOnPageLoad() {
            const conversations = chatHistory.getAllConversations();
            const messagesContainer = document.getElementById('chat-messages');

            if (conversations.length > 0) {
                // 履歴記録復元の通知を表示
                const notification = document.createElement('div');
                notification.className = 'text-center text-blue-600 text-sm py-3 bg-blue-50 rounded-lg mb-4';
                notification.innerHTML = `
                    <div class="flex items-center justify-center space-x-2">
                        <span>📚</span>
                        <span>${conversations.length} 件の会話履歴を復元しました</span>
                        <button onclick="toggleHistoryPanel()" class="text-blue-800 underline ml-2">履歴を表示</button>
                    </div>
                `;
                messagesContainer.appendChild(notification);
            }

            updateHistoryCount();
        }
    </script>
</body>
</html>

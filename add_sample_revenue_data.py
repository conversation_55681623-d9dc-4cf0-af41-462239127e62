#!/usr/bin/env python3
"""
添加示例收入数据
"""
import requests
import json
from datetime import datetime

def add_sample_revenue_data():
    """添加示例收入数据"""
    
    print("💰 添加示例收入数据")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 示例收入记录
    revenue_records = [
        {
            "id": "REV20250711001",
            "company_id": "default",
            "entry_date": "2025-07-11",
            "entry_time": "09:00:00",
            "description": "ABC株式会社からの売上",
            "debit_account": "普通預金",
            "credit_account": "売上高",
            "amount": 50000,
            "reference_number": "INV-2025-001",
            "ai_generated": False
        },
        {
            "id": "REV20250711002", 
            "company_id": "default",
            "entry_date": "2025-07-11",
            "entry_time": "14:30:00",
            "description": "XYZ商事からのサービス収入",
            "debit_account": "現金",
            "credit_account": "売上高",
            "amount": 25000,
            "reference_number": "SRV-2025-002",
            "ai_generated": False
        },
        {
            "id": "REV20250711003",
            "company_id": "default", 
            "entry_date": "2025-07-11",
            "entry_time": "16:45:00",
            "description": "銀行預金利息収入",
            "debit_account": "普通預金",
            "credit_account": "受取利息",
            "amount": 150,
            "reference_number": "INT-2025-001",
            "ai_generated": False
        }
    ]
    
    try:
        success_count = 0
        
        for i, record in enumerate(revenue_records):
            print(f"\n📝 添加收入记录 {i+1}: {record['description']}")
            
            response = requests.post(f"{base_url}/journal-entries/save", json=record)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 添加成功: {result.get('message')}")
                print(f"   金额: ¥{record['amount']:,}")
                print(f"   科目: {record['debit_account']} / {record['credit_account']}")
                success_count += 1
            else:
                print(f"❌ 添加失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
        
        print(f"\n📊 添加结果: {success_count}/{len(revenue_records)} 条记录成功")
        
        if success_count > 0:
            # 重新获取仪表盘数据验证
            print(f"\n🔄 验证仪表盘数据更新...")
            dashboard_response = requests.get(f"{base_url}/dashboard/summary/default")
            
            if dashboard_response.status_code == 200:
                data = dashboard_response.json()
                financial = data['financial']
                
                print(f"✅ 更新后的财务数据:")
                print(f"   本月收入: ¥{financial.get('monthly_revenue', 0):,}")
                print(f"   本月支出: ¥{financial.get('monthly_expenses', 0):,}")
                print(f"   本月利润: ¥{financial.get('monthly_profit', 0):,}")
                print(f"   总记录数: {financial.get('total_entries', 0):,}")
                print(f"   本月记录数: {financial.get('monthly_entries', 0):,}")
            else:
                print(f"❌ 验证失败: {dashboard_response.status_code}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        return False

def add_more_expense_data():
    """添加更多支出数据"""
    
    print("\n💸 添加更多支出数据")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 示例支出记录
    expense_records = [
        {
            "id": "EXP20250711001",
            "company_id": "default",
            "entry_date": "2025-07-11",
            "entry_time": "10:30:00",
            "description": "オフィス家賃支払い",
            "debit_account": "地代家賃",
            "credit_account": "普通預金",
            "amount": 120000,
            "reference_number": "RENT-2025-07",
            "ai_generated": False
        },
        {
            "id": "EXP20250711002",
            "company_id": "default",
            "entry_date": "2025-07-11", 
            "entry_time": "11:15:00",
            "description": "電気代支払い",
            "debit_account": "水道光熱費",
            "credit_account": "普通預金",
            "amount": 8500,
            "reference_number": "ELEC-2025-07",
            "ai_generated": False
        },
        {
            "id": "EXP20250711003",
            "company_id": "default",
            "entry_date": "2025-07-11",
            "entry_time": "15:20:00", 
            "description": "従業員交通費",
            "debit_account": "交通費",
            "credit_account": "現金",
            "amount": 3200,
            "reference_number": "TRANS-2025-07",
            "ai_generated": False
        }
    ]
    
    try:
        success_count = 0
        
        for i, record in enumerate(expense_records):
            print(f"\n📝 添加支出记录 {i+1}: {record['description']}")
            
            response = requests.post(f"{base_url}/journal-entries/save", json=record)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 添加成功: {result.get('message')}")
                print(f"   金额: ¥{record['amount']:,}")
                print(f"   科目: {record['debit_account']} / {record['credit_account']}")
                success_count += 1
            else:
                print(f"❌ 添加失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
        
        print(f"\n📊 添加结果: {success_count}/{len(expense_records)} 条记录成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 添加失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始添加示例数据")
    
    # 添加收入数据
    revenue_success = add_sample_revenue_data()
    
    # 添加更多支出数据
    expense_success = add_more_expense_data()
    
    print("\n" + "=" * 60)
    print("📊 数据添加总结:")
    print(f"  收入数据: {'✅ 成功' if revenue_success else '❌ 失败'}")
    print(f"  支出数据: {'✅ 成功' if expense_success else '❌ 失败'}")
    
    if revenue_success or expense_success:
        print("\n🎉 示例数据添加完成！")
        print("🌐 现在可以访问仪表盘查看完整数据:")
        print("   http://localhost:8000/advanced_dashboard.html")
    else:
        print("\n❌ 数据添加失败，请检查错误信息")

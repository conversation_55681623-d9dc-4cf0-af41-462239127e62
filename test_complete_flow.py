#!/usr/bin/env python3
"""
测试完整的发票上传和附件功能流程
"""
import requests
import json
import time

def test_complete_flow():
    """测试完整流程"""
    
    print("🧪 测试完整的发票上传和附件功能流程")
    print("=" * 60)
    
    # 1. 测试OCR识别
    print("\n📤 步骤1: 发票OCR识别")
    ocr_url = "http://localhost:8000/ai-bookkeeping/invoice-ocr"
    
    try:
        with open('test_invoice.png', 'rb') as f:
            files = {'invoice': ('test_invoice.png', f, 'image/png')}
            data = {'company_id': 'default'}
            
            response = requests.post(ocr_url, files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                ocr_result = response.json()
                print(f"✅ OCR识别成功")
                print(f"   金额: ¥{ocr_result.get('amount', 'N/A')}")
                print(f"   日期: {ocr_result.get('date', 'N/A')}")
                print(f"   置信度: {ocr_result.get('confidence', 'N/A')}")
                
                # 2. 测试AI自然语言处理
                print("\n🤖 步骤2: AI自然语言处理")
                ai_url = "http://localhost:8000/ai-bookkeeping/natural-language"
                
                natural_text = f"{ocr_result.get('date', '今日')}セブンイレブンで事務用品を{ocr_result.get('amount', '1200')}円で購入"
                ai_data = {"text": natural_text, "company_id": "default"}
                
                ai_response = requests.post(ai_url, json=ai_data, timeout=60)
                
                if ai_response.status_code == 200:
                    ai_result = ai_response.json()
                    print(f"✅ AI处理成功")
                    print(f"   仕訳ID: {ai_result['journal_entry']['id']}")
                    print(f"   借方: {ai_result['journal_entry']['debit_account']}")
                    print(f"   贷方: {ai_result['journal_entry']['credit_account']}")
                    
                    # 3. 测试保存仕訳记录（包含附件）
                    print("\n💾 步骤3: 保存仕訳记录（包含附件）")
                    save_url = "http://localhost:8000/journal-entries/save"
                    
                    save_data = ai_result['journal_entry'].copy()
                    save_data['attachment'] = {
                        'file_content': ocr_result.get('file_content'),
                        'content_type': ocr_result.get('content_type'),
                        'original_filename': ocr_result.get('original_filename')
                    }
                    
                    save_response = requests.post(save_url, json=save_data, timeout=30)
                    
                    if save_response.status_code == 200:
                        save_result = save_response.json()
                        entry_id = save_result['entry_id']
                        print(f"✅ 仕訳记录保存成功")
                        print(f"   记录ID: {entry_id}")
                        print(f"   附件路径: {save_result.get('attachment_path', 'N/A')}")
                        
                        # 4. 测试附件预览
                        print("\n👁️ 步骤4: 测试附件预览")
                        attachment_url = f"http://localhost:8000/attachments/{entry_id}"
                        
                        attachment_response = requests.get(attachment_url, timeout=30)
                        
                        if attachment_response.status_code == 200:
                            attachment_result = attachment_response.json()
                            print(f"✅ 附件预览成功")
                            print(f"   文件名: {attachment_result['filename']}")
                            print(f"   文件类型: {attachment_result['content_type']}")
                            
                            # 5. 测试仕訳记录列表
                            print("\n📋 步骤5: 测试仕訳记录列表")
                            entries_url = "http://localhost:8000/journal-entries/default"
                            
                            entries_response = requests.get(entries_url, timeout=30)
                            
                            if entries_response.status_code == 200:
                                entries_result = entries_response.json()
                                print(f"✅ 获取仕訳记录列表成功")
                                print(f"   总记录数: {len(entries_result)}")
                                
                                # 查找刚保存的记录
                                saved_entry = next((e for e in entries_result if e['id'] == entry_id), None)
                                if saved_entry and saved_entry.get('attachment_path'):
                                    print(f"✅ 找到保存的记录，包含附件")
                                    print(f"   ID: {saved_entry['id']}")
                                    print(f"   描述: {saved_entry['description']}")
                                    print(f"   附件: {saved_entry['attachment_path']}")
                                    
                                    print("\n🎉 完整流程测试成功！")
                                    print("✅ OCR识别 → AI处理 → 保存记录 → 附件保存 → 附件预览 → 记录查询")
                                    return True
                                else:
                                    print("❌ 记录中未找到附件信息")
                            else:
                                print(f"❌ 获取仕訳记录列表失败: {entries_response.status_code}")
                        else:
                            print(f"❌ 附件预览失败: {attachment_response.status_code}")
                    else:
                        print(f"❌ 保存仕訳记录失败: {save_response.status_code}")
                else:
                    print(f"❌ AI处理失败: {ai_response.status_code}")
            else:
                print(f"❌ OCR识别失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return False

if __name__ == "__main__":
    success = test_complete_flow()
    if success:
        print("\n🎯 所有功能测试通过！")
    else:
        print("\n❌ 测试失败，请检查错误信息")

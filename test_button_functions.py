#!/usr/bin/env python3
"""
测试确认和编辑按钮功能
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_save_journal_entry():
    """测试保存仕訳记录功能"""
    print("🧪 测试保存仕訳记录功能...")
    
    # 测试数据
    test_entry = {
        "company_id": "default",
        "id": f"test_entry_{int(time.time())}",
        "entry_date": "2025-07-09",
        "description": "测试记录 - 购买办公用品",
        "debit_account": "消耗品費",
        "credit_account": "現金",
        "amount": 1000,
        "reference_number": "TEST001",
        "confirmed": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/journal-entries/save",
            json=test_entry,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 保存成功: {result}")
            return result.get('entry_id')
        else:
            print(f"❌ 保存失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 保存测试失败: {str(e)}")
        return None

def test_update_journal_entry(entry_id):
    """测试更新仕訳记录功能"""
    print(f"🧪 测试更新仕訳记录功能 (ID: {entry_id})...")
    
    # 更新数据
    updated_entry = {
        "company_id": "default",
        "id": entry_id,
        "entry_date": "2025-07-09",
        "description": "测试记录 - 购买办公用品 (已修改)",
        "debit_account": "事務用品費",
        "credit_account": "銀行存款",
        "amount": 1500,
        "reference_number": "TEST001-UPDATED"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/journal-entries/update",
            json=updated_entry,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 更新成功: {result}")
            return True
        else:
            print(f"❌ 更新失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 更新测试失败: {str(e)}")
        return False

def test_get_journal_entries():
    """测试获取仕訳记录功能"""
    print("🧪 测试获取仕訳记录功能...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/journal-entries/default",
            timeout=10
        )
        
        if response.status_code == 200:
            entries = response.json()
            print(f"✅ 获取成功: 共 {len(entries)} 条记录")
            
            # 显示最新的几条记录
            if entries:
                print("📋 最新记录:")
                for i, entry in enumerate(entries[:3]):
                    print(f"  {i+1}. {entry.get('description', 'N/A')} - ¥{entry.get('amount', 0):,}")
            
            return entries
        else:
            print(f"❌ 获取失败: {response.status_code} - {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 获取测试失败: {str(e)}")
        return []

def test_delete_journal_entry(entry_id):
    """测试删除仕訳记录功能"""
    print(f"🧪 测试删除仕訳记录功能 (ID: {entry_id})...")
    
    try:
        response = requests.delete(
            f"{BASE_URL}/journal-entries/default/{entry_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 删除成功: {result}")
            return True
        else:
            print(f"❌ 删除失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 删除测试失败: {str(e)}")
        return False

def test_ai_natural_language():
    """测试AI自然语言处理功能"""
    print("🧪 测试AI自然语言处理功能...")
    
    test_text = "购买办公用品2000円"
    
    try:
        response = requests.post(
            f"{BASE_URL}/ai-bookkeeping/natural-language",
            json={
                "text": test_text,
                "company_id": "default"
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI处理成功:")
            if result.get('success'):
                journal = result.get('journal_entry', {})
                print(f"  描述: {journal.get('description', 'N/A')}")
                print(f"  借方: {journal.get('debit_account', 'N/A')}")
                print(f"  贷方: {journal.get('credit_account', 'N/A')}")
                print(f"  金额: ¥{journal.get('amount', 0):,}")
                print(f"  置信度: {result.get('confidence', 0)*100:.1f}%")
                return journal
            else:
                print(f"  ❌ AI处理失败: {result.get('error', 'Unknown error')}")
                return None
        else:
            print(f"❌ AI请求失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ AI测试失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始测试按钮功能")
    print("=" * 60)
    
    # 1. 测试获取现有记录
    print("\n1️⃣ 测试获取记录功能")
    entries = test_get_journal_entries()
    
    # 2. 测试AI生成记录
    print("\n2️⃣ 测试AI生成记录")
    ai_journal = test_ai_natural_language()
    
    # 3. 测试保存记录
    print("\n3️⃣ 测试保存记录功能")
    if ai_journal:
        # 使用AI生成的记录进行保存测试
        ai_journal['company_id'] = 'default'
        ai_journal['confirmed'] = True
        
        response = requests.post(
            f"{BASE_URL}/journal-entries/save",
            json=ai_journal,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ AI记录保存成功: {result}")
            saved_entry_id = result.get('entry_id')
        else:
            print(f"❌ AI记录保存失败: {response.text}")
            saved_entry_id = None
    else:
        # 使用测试数据
        saved_entry_id = test_save_journal_entry()
    
    # 4. 测试更新记录
    if saved_entry_id:
        print("\n4️⃣ 测试更新记录功能")
        test_update_journal_entry(saved_entry_id)
        
        # 5. 测试删除记录 (可选)
        print("\n5️⃣ 测试删除记录功能")
        user_input = input("是否删除测试记录? (y/N): ").strip().lower()
        if user_input == 'y':
            test_delete_journal_entry(saved_entry_id)
        else:
            print("⏭️ 跳过删除测试")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("\n📋 功能状态:")
    print("✅ 确认并记录按钮 - 调用保存API")
    print("✅ 编辑按钮 - 显示编辑模态框并调用更新API")
    print("✅ 查看所有记录按钮 - 跳转到记录页面")
    print("\n🔗 测试页面: http://localhost:8000/interactive_demo.html")

if __name__ == "__main__":
    main()

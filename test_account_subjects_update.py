#!/usr/bin/env python3
"""
测试会计科目更新
"""
import requests
import json

def test_account_subjects_api():
    """测试科目API"""
    
    print("🔍 测试会计科目API")
    print("=" * 50)
    
    try:
        # 测试科目API
        response = requests.get("http://localhost:8000/journal-entries/default/accounts")
        
        if response.status_code == 200:
            accounts = response.json()
            print("✅ 科目API正常工作")
            
            # 统计科目数量
            total_count = 0
            for category, account_list in accounts.items():
                count = len(account_list)
                total_count += count
                print(f"  {category}: {count}个科目")
            
            print(f"  总计: {total_count}个科目")
            
            # 检查关键科目是否存在
            key_accounts = [
                ('費用', '事務用品費'),
                ('費用', '接待交際費'),
                ('収益', '雑収入'),
                ('資産', '当座預金'),
                ('負債', '未払金')
            ]
            
            print(f"\n关键科目检查:")
            for category, name in key_accounts:
                exists = category in accounts and name in accounts[category]
                status = "✅" if exists else "❌"
                print(f"  {status} {category} - {name}")
            
            return True
            
        else:
            print(f"❌ 科目API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 科目API测试失败: {e}")
        return False

def test_journal_entries_page():
    """测试记账页面"""
    
    print(f"\n📄 测试记账页面")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8000/journal_entries.html")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查页面内容
            checks = [
                ("编辑模态框", "编辑仕訳记录" in content),
                ("科目选择框", '<select id="edit-debit"' in content),
                ("贷方选择框", '<select id="edit-credit"' in content),
                ("加载科目函数", "loadAccountOptions" in content),
                ("科目API调用", "/journal-entries/default/accounts" in content)
            ]
            
            print("页面功能检查:")
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}")
            
            all_checks_passed = all(result for _, result in checks)
            
            if all_checks_passed:
                print("✅ 记账页面更新成功")
            else:
                print("⚠️ 记账页面部分功能可能有问题")
            
            return all_checks_passed
            
        else:
            print(f"❌ 记账页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 记账页面测试失败: {e}")
        return False

def test_interactive_demo_page():
    """测试交互演示页面"""
    
    print(f"\n🎮 测试交互演示页面")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8000/interactive_demo.html")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查页面内容
            checks = [
                ("编辑模态框", "編集仕訳" in content),
                ("科目选择框", '<select id="edit-debit"' in content),
                ("贷方选择框", '<select id="edit-credit"' in content),
                ("加载科目函数", "loadAccountOptionsForDemo" in content),
                ("科目API调用", "/journal-entries/default/accounts" in content)
            ]
            
            print("页面功能检查:")
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}")
            
            all_checks_passed = all(result for _, result in checks)
            
            if all_checks_passed:
                print("✅ 交互演示页面更新成功")
            else:
                print("⚠️ 交互演示页面部分功能可能有问题")
            
            return all_checks_passed
            
        else:
            print(f"❌ 交互演示页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 交互演示页面测试失败: {e}")
        return False

def test_ai_bookkeeping():
    """测试AI记账功能是否能使用新科目"""
    
    print(f"\n🤖 测试AI记账功能")
    print("=" * 30)
    
    try:
        # 测试AI记账API
        test_message = "今天购买事务用品费1000元"
        
        response = requests.post("http://localhost:8000/ai-bookkeeping/natural-language", 
                               json={"text": test_message})
        
        if response.status_code == 200:
            data = response.json()
            print("✅ AI记账API正常工作")
            print(f"  测试消息: {test_message}")
            print(f"  借方科目: {data.get('journal_entry', {}).get('debit_account', 'N/A')}")
            print(f"  贷方科目: {data.get('journal_entry', {}).get('credit_account', 'N/A')}")
            print(f"  AI置信度: {(data.get('confidence', 0) * 100):.1f}%")
            
            # 检查是否使用了新科目
            debit_account = data.get('journal_entry', {}).get('debit_account', '')
            if '事務用品費' in debit_account:
                print("  🎯 AI成功识别并使用了新添加的科目！")
            
            return True
        else:
            print(f"⚠️ AI记账API响应异常: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"⚠️ AI记账测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始测试会计科目更新")
    
    # 测试科目API
    api_success = test_account_subjects_api()
    
    # 测试记账页面
    journal_page_success = test_journal_entries_page()
    
    # 测试交互演示页面
    demo_page_success = test_interactive_demo_page()
    
    # 测试AI记账功能
    ai_success = test_ai_bookkeeping()
    
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    
    if api_success and journal_page_success and demo_page_success:
        print("🎉 会计科目更新完全成功！")
        print("\n✅ 更新内容:")
        print("  - 添加了34个新的会计科目")
        print("  - 记账页面编辑功能支持科目下拉选择")
        print("  - 交互演示页面编辑功能支持科目下拉选择")
        print("  - 科目按类别分组显示")
        print("  - 包含错误处理和回退机制")
        
        if ai_success:
            print("  - AI记账功能可以使用新科目")
        
        print(f"\n🌐 现在可以使用:")
        print("  记账页面: http://localhost:8000/journal_entries.html")
        print("  交互演示: http://localhost:8000/interactive_demo.html")
        print("  - 编辑记录时可以从完整科目列表中选择")
        print("  - 科目按资产、负债、纯资产、收益、费用分类")
        print("  - 包含附件中要求的所有科目")
        
    else:
        print("❌ 部分测试失败")
        print("请检查上述错误信息并进行修复")
        
        if not api_success:
            print("  - 科目API有问题")
        if not journal_page_success:
            print("  - 记账页面更新有问题")
        if not demo_page_success:
            print("  - 交互演示页面更新有问题")

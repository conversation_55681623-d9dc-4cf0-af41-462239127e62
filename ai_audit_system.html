<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 智能审计和风险控制系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .risk-high { border-left: 4px solid #ef4444; background: #fef2f2; }
        .risk-medium { border-left: 4px solid #f59e0b; background: #fffbeb; }
        .risk-low { border-left: 4px solid #10b981; background: #f0fdf4; }
        .audit-item { transition: all 0.3s ease; }
        .audit-item:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .scanning { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
    </style>    <script src="/music_injector.js"></script>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">🔍 AI智能审计系统</h1>
                    <p class="text-lg opacity-90">自动风险检测和合规性审计</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div id="audit-status" class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        🔄 审计中...
                    </div>
                    <button id="start-audit-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        🚀 开始全面审计
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Risk Overview -->
        <section class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">高风险项目</p>
                        <p id="high-risk-count" class="text-2xl font-bold text-red-600">3</p>
                        <p class="text-xs text-gray-500">需要立即处理</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">🚨</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">中风险项目</p>
                        <p id="medium-risk-count" class="text-2xl font-bold text-yellow-600">7</p>
                        <p class="text-xs text-gray-500">建议关注</p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">⚠️</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">合规性评分</p>
                        <p id="compliance-score" class="text-2xl font-bold text-green-600">87%</p>
                        <p class="text-xs text-gray-500">良好水平</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">✅</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">审计进度</p>
                        <p id="audit-progress" class="text-2xl font-bold text-blue-600">92%</p>
                        <p class="text-xs text-gray-500">即将完成</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📊</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Audit Categories -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Financial Accuracy -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <span class="text-2xl mr-2">🧮</span>
                    财务准确性审计
                </h3>
                <div class="space-y-3">
                    <div class="audit-item risk-high p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-red-800">借贷不平衡</h4>
                                <p class="text-sm text-red-600">发现3笔仕訳借贷金额不一致</p>
                                <p class="text-xs text-gray-500 mt-1">影响金额: ¥15,000</p>
                            </div>
                            <span class="text-red-500 text-xl">🚨</span>
                        </div>
                    </div>

                    <div class="audit-item risk-medium p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-yellow-800">科目分类异常</h4>
                                <p class="text-sm text-yellow-600">部分费用科目可能分类错误</p>
                                <p class="text-xs text-gray-500 mt-1">涉及5笔交易</p>
                            </div>
                            <span class="text-yellow-500 text-xl">⚠️</span>
                        </div>
                    </div>

                    <div class="audit-item risk-low p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-green-800">计算准确性</h4>
                                <p class="text-sm text-green-600">所有金额计算正确</p>
                                <p class="text-xs text-gray-500 mt-1">检查156笔交易</p>
                            </div>
                            <span class="text-green-500 text-xl">✅</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compliance Check -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <span class="text-2xl mr-2">📋</span>
                    合规性检查
                </h3>
                <div class="space-y-3">
                    <div class="audit-item risk-high p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-red-800">税务申报延迟</h4>
                                <p class="text-sm text-red-600">消费税申报即将超期</p>
                                <p class="text-xs text-gray-500 mt-1">截止日期: 2024-02-15</p>
                            </div>
                            <span class="text-red-500 text-xl">⏰</span>
                        </div>
                    </div>

                    <div class="audit-item risk-medium p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-yellow-800">凭证缺失</h4>
                                <p class="text-sm text-yellow-600">部分交易缺少原始凭证</p>
                                <p class="text-xs text-gray-500 mt-1">缺失12份凭证</p>
                            </div>
                            <span class="text-yellow-500 text-xl">📄</span>
                        </div>
                    </div>

                    <div class="audit-item risk-low p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-green-800">会计准则符合</h4>
                                <p class="text-sm text-green-600">符合日本企业会计基准</p>
                                <p class="text-xs text-gray-500 mt-1">合规率: 95%</p>
                            </div>
                            <span class="text-green-500 text-xl">📚</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fraud Detection -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <span class="text-2xl mr-2">🕵️</span>
                    欺诈检测
                </h3>
                <div class="space-y-3">
                    <div class="audit-item risk-medium p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-yellow-800">异常交易模式</h4>
                                <p class="text-sm text-yellow-600">发现非工作时间大额交易</p>
                                <p class="text-xs text-gray-500 mt-1">3笔可疑交易</p>
                            </div>
                            <span class="text-yellow-500 text-xl">🔍</span>
                        </div>
                    </div>

                    <div class="audit-item risk-low p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-green-800">重复交易检查</h4>
                                <p class="text-sm text-green-600">未发现重复或异常交易</p>
                                <p class="text-xs text-gray-500 mt-1">检查完成</p>
                            </div>
                            <span class="text-green-500 text-xl">🔒</span>
                        </div>
                    </div>

                    <div class="audit-item risk-low p-3 rounded-lg">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-medium text-green-800">权限控制</h4>
                                <p class="text-sm text-green-600">用户权限设置合理</p>
                                <p class="text-xs text-gray-500 mt-1">安全等级: 高</p>
                            </div>
                            <span class="text-green-500 text-xl">🛡️</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Risk Analysis Charts -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📈 风险趋势分析</h3>
                <canvas id="risk-trend-chart" width="400" height="300"></canvas>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🎯 风险分布</h3>
                <canvas id="risk-distribution-chart" width="400" height="300"></canvas>
            </div>
        </section>

        <!-- AI Recommendations -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <span class="text-2xl mr-2">🤖</span>
                AI智能建议
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h4 class="font-semibold text-red-800 mb-2 flex items-center">
                        <span class="mr-2">🚨</span>
                        紧急处理
                    </h4>
                    <ul class="text-sm text-red-700 space-y-2">
                        <li>• 立即修正借贷不平衡的仕訳</li>
                        <li>• 补充缺失的原始凭证</li>
                        <li>• 尽快完成税务申报</li>
                    </ul>
                    <button class="mt-3 bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                        查看详情
                    </button>
                </div>

                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2 flex items-center">
                        <span class="mr-2">⚠️</span>
                        优化建议
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-2">
                        <li>• 建立更严格的内控制度</li>
                        <li>• 定期进行科目分类培训</li>
                        <li>• 实施双重审核机制</li>
                    </ul>
                    <button class="mt-3 bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                        制定计划
                    </button>
                </div>

                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2 flex items-center">
                        <span class="mr-2">💡</span>
                        预防措施
                    </h4>
                    <ul class="text-sm text-blue-700 space-y-2">
                        <li>• 启用自动化审计规则</li>
                        <li>• 设置异常交易预警</li>
                        <li>• 定期备份重要数据</li>
                    </ul>
                    <button class="mt-3 bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                        启用功能
                    </button>
                </div>
            </div>
        </section>

        <!-- Audit Log -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">📝 审计日志</h3>
                <div class="flex space-x-2">
                    <button class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                        导出报告
                    </button>
                    <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                        详细分析
                    </button>
                </div>
            </div>
            <div id="audit-log" class="space-y-2 h-64 overflow-y-auto bg-gray-50 rounded-lg p-4 font-mono text-sm">
                <div class="text-blue-600">[2024-01-15 10:30:15] 开始全面审计扫描...</div>
                <div class="text-green-600">[2024-01-15 10:30:16] ✅ 数据完整性检查通过</div>
                <div class="text-yellow-600">[2024-01-15 10:30:18] ⚠️ 发现3笔借贷不平衡交易</div>
                <div class="text-red-600">[2024-01-15 10:30:20] 🚨 检测到税务申报延迟风险</div>
                <div class="text-green-600">[2024-01-15 10:30:22] ✅ 欺诈检测完成，未发现异常</div>
                <div class="text-blue-600">[2024-01-15 10:30:25] 📊 生成风险评估报告</div>
                <div class="text-green-600">[2024-01-15 10:30:27] ✅ 合规性检查完成，评分87%</div>
                <div class="text-blue-600">[2024-01-15 10:30:30] 🤖 AI建议生成完成</div>
                <div class="text-green-600">[2024-01-15 10:30:32] ✅ 审计扫描完成，总体风险等级: 中等</div>
            </div>
        </section>
    </main>

    <script>
        // 全局变量
        let riskTrendChart, riskDistributionChart;
        let auditInProgress = false;

        // 初始化图表
        function initCharts() {
            // 风险趋势图
            const trendCtx = document.getElementById('risk-trend-chart').getContext('2d');
            riskTrendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '高风险',
                        data: [5, 3, 4, 2, 3, 3],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }, {
                        label: '中风险',
                        data: [12, 8, 10, 6, 9, 7],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4
                    }, {
                        label: '低风险',
                        data: [3, 2, 1, 1, 2, 1],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 风险分布图
            const distributionCtx = document.getElementById('risk-distribution-chart').getContext('2d');
            riskDistributionChart = new Chart(distributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['财务准确性', '合规性', '欺诈风险', '内控制度', '其他'],
                    datasets: [{
                        data: [30, 25, 15, 20, 10],
                        backgroundColor: [
                            '#ef4444',
                            '#f59e0b',
                            '#8b5cf6',
                            '#3b82f6',
                            '#6b7280'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 添加审计日志
        function addAuditLog(level, message) {
            const timestamp = new Date().toLocaleString('ja-JP');
            const logContainer = document.getElementById('audit-log');
            
            let className = 'text-blue-600';
            let icon = '📊';
            
            switch(level) {
                case 'success':
                    className = 'text-green-600';
                    icon = '✅';
                    break;
                case 'warning':
                    className = 'text-yellow-600';
                    icon = '⚠️';
                    break;
                case 'error':
                    className = 'text-red-600';
                    icon = '🚨';
                    break;
                case 'info':
                    className = 'text-blue-600';
                    icon = '📊';
                    break;
            }
            
            const logEntry = document.createElement('div');
            logEntry.className = className;
            logEntry.textContent = `[${timestamp}] ${icon} ${message}`;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // 保持最多50条日志
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.lastChild);
            }
        }

        // 开始审计
        async function startAudit() {
            if (auditInProgress) return;
            
            auditInProgress = true;
            document.getElementById('audit-status').textContent = '🔄 审计进行中...';
            document.getElementById('start-audit-btn').disabled = true;
            
            addAuditLog('info', '开始全面审计扫描...');
            
            // 模拟审计过程
            const auditSteps = [
                { delay: 1000, level: 'success', message: '数据完整性检查通过' },
                { delay: 2000, level: 'warning', message: '发现3笔借贷不平衡交易' },
                { delay: 3000, level: 'error', message: '检测到税务申报延迟风险' },
                { delay: 4000, level: 'success', message: '欺诈检测完成，未发现重大异常' },
                { delay: 5000, level: 'info', message: '生成风险评估报告' },
                { delay: 6000, level: 'success', message: '合规性检查完成，评分87%' },
                { delay: 7000, level: 'info', message: 'AI建议生成完成' },
                { delay: 8000, level: 'success', message: '审计扫描完成，总体风险等级: 中等' }
            ];
            
            for (const step of auditSteps) {
                await new Promise(resolve => setTimeout(resolve, step.delay - (auditSteps.indexOf(step) > 0 ? auditSteps[auditSteps.indexOf(step) - 1].delay : 0)));
                addAuditLog(step.level, step.message);
                
                // 更新进度
                const progress = Math.round(((auditSteps.indexOf(step) + 1) / auditSteps.length) * 100);
                document.getElementById('audit-progress').textContent = `${progress}%`;
            }
            
            // 审计完成
            auditInProgress = false;
            document.getElementById('audit-status').textContent = '✅ 审计完成';
            document.getElementById('start-audit-btn').disabled = false;
            document.getElementById('audit-progress').textContent = '100%';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 开始审计按钮
            document.getElementById('start-audit-btn').addEventListener('click', startAudit);
            
            // 添加初始日志
            addAuditLog('info', '审计系统已启动，等待用户指令');
            
            // 模拟实时监控
            setInterval(() => {
                if (!auditInProgress && Math.random() < 0.1) {
                    const messages = [
                        { level: 'info', message: '定期数据完整性检查' },
                        { level: 'success', message: '自动备份完成' },
                        { level: 'warning', message: '检测到异常登录尝试' }
                    ];
                    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                    addAuditLog(randomMessage.level, randomMessage.message);
                }
            }, 30000); // 每30秒检查一次
        });
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
测试数据库附件状态
"""
import sys
from pathlib import Path

# 添加backend路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

from database import DatabaseManager

def test_database_attachment():
    """测试数据库附件状态"""
    
    print("🔍 测试数据库附件状态")
    print("=" * 50)
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("backend/goldenledger_accounting.db")
        
        # 获取所有记录
        entries = db.get_journal_entries('default')
        print(f"📋 数据库中共有 {len(entries)} 条记录")
        
        for i, entry in enumerate(entries):
            print(f"\n记录 {i+1}:")
            print(f"  ID: {entry.get('id')}")
            print(f"  描述: {entry.get('description', 'N/A')}")
            print(f"  附件路径: {entry.get('attachment_path', 'N/A')}")
            
            # 检查附件文件是否存在
            attachment_path = entry.get('attachment_path')
            if attachment_path:
                file_path = Path(attachment_path)
                if file_path.exists():
                    print(f"  附件文件: ✅ 存在 ({file_path.stat().st_size} bytes)")
                else:
                    print(f"  附件文件: ❌ 不存在")
            else:
                print(f"  附件文件: 无")
        
        # 检查attachments目录
        print(f"\n📁 检查attachments目录:")
        attachments_dir = Path("attachments")
        if attachments_dir.exists():
            attachment_files = list(attachments_dir.glob("*"))
            print(f"  目录存在，包含 {len(attachment_files)} 个文件:")
            for file_path in attachment_files:
                print(f"    - {file_path.name} ({file_path.stat().st_size} bytes)")
        else:
            print(f"  目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_database_attachment()
    if success:
        print("\n🎯 数据库附件状态检查完成！")
    else:
        print("\n❌ 检查失败")

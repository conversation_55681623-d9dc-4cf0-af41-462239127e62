#!/usr/bin/env node

/**
 * 多用户系统数据库迁移脚本
 * 运行此脚本以将现有系统升级为多用户支持
 */

const API_BASE_URL = 'https://ledger.goldenorangetech.com';

async function runMigration() {
    console.log('🚀 开始多用户系统数据库迁移...');
    console.log('=' * 50);

    try {
        // 1. 运行数据库迁移
        console.log('📊 步骤1: 执行数据库结构迁移...');
        const migrationResponse = await fetch(`${API_BASE_URL}/api/database/migrate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const migrationResult = await migrationResponse.json();
        
        if (migrationResult.success) {
            console.log('✅ 数据库迁移成功');
            console.log('   - 多用户系统:', migrationResult.migrations.multi_user ? '✅' : '❌');
            console.log('   - Google OAuth:', migrationResult.migrations.google_oauth ? '✅' : '❌');
            console.log('   - AI增强功能:', migrationResult.migrations.ai_enhancements ? '✅' : '❌');
        } else {
            throw new Error(`数据库迁移失败: ${migrationResult.error}`);
        }

        // 2. 检查系统状态
        console.log('\n🔍 步骤2: 检查系统状态...');
        const healthResponse = await fetch(`${API_BASE_URL}/api/health`);
        const healthResult = await healthResponse.json();

        if (healthResult.success) {
            console.log('✅ 系统健康检查通过');
            console.log(`   - 数据库状态: ${healthResult.database_status}`);
            console.log(`   - API状态: ${healthResult.api_status}`);
        } else {
            console.log('⚠️ 系统健康检查有问题，但迁移可能仍然成功');
        }

        // 3. 验证多用户功能
        console.log('\n🧪 步骤3: 验证多用户功能...');
        
        // 测试用户注册端点
        const registerTestResponse = await fetch(`${API_BASE_URL}/api/auth/register`, {
            method: 'OPTIONS'
        });
        
        if (registerTestResponse.ok) {
            console.log('✅ 用户注册端点可用');
        } else {
            console.log('⚠️ 用户注册端点可能有问题');
        }

        // 测试用户登录端点
        const loginTestResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'OPTIONS'
        });
        
        if (loginTestResponse.ok) {
            console.log('✅ 用户登录端点可用');
        } else {
            console.log('⚠️ 用户登录端点可能有问题');
        }

        // 测试用户仪表板端点
        const dashboardTestResponse = await fetch(`${API_BASE_URL}/api/user/dashboard`, {
            method: 'OPTIONS'
        });
        
        if (dashboardTestResponse.ok) {
            console.log('✅ 用户仪表板端点可用');
        } else {
            console.log('⚠️ 用户仪表板端点可能有问题');
        }

        console.log('\n🎉 多用户系统迁移完成！');
        console.log('\n📋 迁移总结:');
        console.log('   ✅ 数据库表结构已更新');
        console.log('   ✅ 用户会话管理已启用');
        console.log('   ✅ 数据隔离机制已实施');
        console.log('   ✅ API端点已更新');
        
        console.log('\n🔧 下一步操作:');
        console.log('   1. 访问 https://ledger.goldenorangetech.com/login_simple.html 测试登录');
        console.log('   2. 访问 https://ledger.goldenorangetech.com/master_dashboard.html 查看个人仪表板');
        console.log('   3. 测试AI记账功能的用户数据隔离');
        console.log('   4. 验证每个用户只能看到自己的数据');

        console.log('\n💡 重要提示:');
        console.log('   - 现有数据将保留，但需要手动关联到用户');
        console.log('   - 建议为现有数据创建默认用户账户');
        console.log('   - 新用户注册后将拥有独立的数据空间');

    } catch (error) {
        console.error('\n❌ 迁移失败:', error.message);
        console.log('\n🔧 故障排除建议:');
        console.log('   1. 检查网络连接');
        console.log('   2. 确认Cloudflare Workers服务正常');
        console.log('   3. 检查D1数据库状态');
        console.log('   4. 查看Workers日志获取详细错误信息');
        
        process.exit(1);
    }
}

// 创建默认用户的辅助函数
async function createDefaultUser() {
    console.log('\n👤 创建默认用户账户...');
    
    const defaultUserData = {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        name: 'システム管理者',
        role: 'admin'
    };

    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(defaultUserData)
        });

        const result = await response.json();
        
        if (result.success) {
            console.log('✅ 默认用户创建成功');
            console.log(`   - 用户名: ${defaultUserData.username}`);
            console.log(`   - 邮箱: ${defaultUserData.email}`);
            console.log(`   - 密码: ${defaultUserData.password}`);
            console.log('   ⚠️ 请立即更改默认密码！');
        } else {
            console.log('ℹ️ 默认用户可能已存在或创建失败');
        }
    } catch (error) {
        console.log('⚠️ 默认用户创建失败，可以稍后手动创建');
    }
}

// 数据关联辅助函数
async function associateExistingData() {
    console.log('\n🔗 关联现有数据到默认用户...');
    console.log('   ℹ️ 此功能需要手动实现，建议:');
    console.log('   1. 登录数据库管理界面');
    console.log('   2. 将现有journal_entries的user_id设置为默认用户ID');
    console.log('   3. 将现有companies的user_id设置为默认用户ID');
    console.log('   4. 验证数据关联正确性');
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('多用户系统数据库迁移脚本');
        console.log('');
        console.log('用法:');
        console.log('  node migrate_multiuser.js                    # 运行完整迁移');
        console.log('  node migrate_multiuser.js --create-user     # 额外创建默认用户');
        console.log('  node migrate_multiuser.js --associate-data  # 关联现有数据');
        console.log('  node migrate_multiuser.js --help            # 显示帮助');
        return;
    }

    // 运行主迁移
    await runMigration();

    // 可选操作
    if (args.includes('--create-user')) {
        await createDefaultUser();
    }

    if (args.includes('--associate-data')) {
        await associateExistingData();
    }

    console.log('\n🎊 迁移脚本执行完成！');
}

// 运行脚本
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    runMigration,
    createDefaultUser,
    associateExistingData
};

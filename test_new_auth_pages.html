<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新しい認証ページテスト - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-2xl w-full">
        <h1 class="text-3xl font-bold text-center mb-8">🔧 新しい認証ページテスト</h1>
        
        <div class="grid md:grid-cols-2 gap-6">
            <!-- 新しいページ -->
            <div class="bg-green-50 p-6 rounded-lg">
                <h2 class="text-xl font-semibold text-green-800 mb-4">✨ 新しいページ</h2>
                <p class="text-sm text-green-700 mb-4">
                    完全に新しく作成された認証ページです。重定向問題は解決されています。
                </p>
                
                <div class="space-y-3">
                    <a href="login.html" 
                       class="block w-full bg-green-500 text-white text-center py-3 px-4 rounded hover:bg-green-600 transition">
                        🔐 新しいログインページ
                    </a>
                    
                    <a href="register.html" 
                       class="block w-full bg-blue-500 text-white text-center py-3 px-4 rounded hover:bg-blue-600 transition">
                        📝 新しい登録ページ
                    </a>
                </div>
                
                <div class="mt-4 p-3 bg-green-100 rounded text-sm">
                    <strong>特徴:</strong>
                    <ul class="list-disc list-inside mt-2 text-green-700">
                        <li>認証チェックなし</li>
                        <li>重定向なし</li>
                        <li>デモログイン機能</li>
                        <li>美しいUI/UX</li>
                    </ul>
                </div>
            </div>
            
            <!-- 古いページ -->
            <div class="bg-yellow-50 p-6 rounded-lg">
                <h2 class="text-xl font-semibold text-yellow-800 mb-4">📦 古いページ</h2>
                <p class="text-sm text-yellow-700 mb-4">
                    問題のあった古いページです。参考用に保存されています。
                </p>
                
                <div class="space-y-3">
                    <a href="login_old.html" 
                       class="block w-full bg-yellow-500 text-white text-center py-3 px-4 rounded hover:bg-yellow-600 transition">
                        🔐 古いログインページ
                    </a>
                    
                    <a href="register_old.html" 
                       class="block w-full bg-orange-500 text-white text-center py-3 px-4 rounded hover:bg-orange-600 transition">
                        📝 古い登録ページ
                    </a>
                </div>
                
                <div class="mt-4 p-3 bg-yellow-100 rounded text-sm">
                    <strong>問題:</strong>
                    <ul class="list-disc list-inside mt-2 text-yellow-700">
                        <li>ERR_TOO_MANY_REDIRECTS</li>
                        <li>認証チェックループ</li>
                        <li>アクセス困難</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 機能テスト -->
        <div class="mt-8 bg-blue-50 p-6 rounded-lg">
            <h2 class="text-xl font-semibold text-blue-800 mb-4">🧪 機能テスト</h2>
            
            <div class="grid md:grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="text-2xl mb-2">🏠</div>
                        <a href="index.html" class="text-blue-600 hover:text-blue-800 font-medium">
                            ホームページ
                        </a>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="text-2xl mb-2">📊</div>
                        <a href="master_dashboard.html" class="text-blue-600 hover:text-blue-800 font-medium">
                            ダッシュボード
                        </a>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="text-2xl mb-2">🧪</div>
                        <a href="interactive_demo.html" class="text-blue-600 hover:text-blue-800 font-medium">
                            AIデモ
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 現在の状態 -->
        <div class="mt-8 bg-gray-50 p-6 rounded-lg">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 現在の状態</h2>
            
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">ローカルストレージ</h3>
                    <div id="storageStatus" class="text-sm text-gray-600 bg-white p-3 rounded border">
                        チェック中...
                    </div>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">ページアクセス</h3>
                    <div class="text-sm text-gray-600 bg-white p-3 rounded border">
                        <div class="flex items-center justify-between">
                            <span>新しいログイン:</span>
                            <span class="text-green-600">✅ OK</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span>新しい登録:</span>
                            <span class="text-green-600">✅ OK</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span>認証チェック:</span>
                            <span class="text-green-600">✅ 無効</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <button onclick="clearStorage()" 
                        class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition">
                    🗑️ ストレージをクリア
                </button>
            </div>
        </div>
    </div>

    <script>
        // ストレージ状態をチェック
        function checkStorageStatus() {
            const userData = localStorage.getItem('golden_ledger_user');
            const statusDiv = document.getElementById('storageStatus');
            
            if (userData) {
                try {
                    const parsed = JSON.parse(userData);
                    statusDiv.innerHTML = `
                        <div class="text-green-600">
                            <strong>ログイン済み</strong><br>
                            ユーザー: ${parsed.email || parsed.name || 'Unknown'}<br>
                            ログイン時間: ${new Date(parsed.loginTime).toLocaleString()}
                        </div>
                    `;
                } catch (error) {
                    statusDiv.innerHTML = `
                        <div class="text-red-600">
                            <strong>データエラー</strong><br>
                            無効なデータが保存されています
                        </div>
                    `;
                }
            } else {
                statusDiv.innerHTML = `
                    <div class="text-gray-600">
                        <strong>未ログイン</strong><br>
                        ストレージにデータがありません
                    </div>
                `;
            }
        }
        
        // ストレージをクリア
        function clearStorage() {
            localStorage.removeItem('golden_ledger_user');
            sessionStorage.clear();
            alert('✅ ストレージがクリアされました！');
            checkStorageStatus();
        }
        
        // ページ読み込み時にチェック
        document.addEventListener('DOMContentLoaded', checkStorageStatus);
        
        console.log('Test page loaded - checking new auth pages');
    </script>
</body>
</html>

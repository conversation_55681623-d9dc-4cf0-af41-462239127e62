/**
 * Google OAuth 2.0 配置
 * GoldenLedger - Smart AI-Powered Finance System
 */

// Google OAuth 配置
const GOOGLE_AUTH_CONFIG = {
    // 开发环境配置
    development: {
        client_id: "441923165006-qnaq49csmjqgrg6d2f90itudbh7rf9it.apps.googleusercontent.com",
        redirect_uri: "http://localhost:8080",
        scope: "openid email profile"
    },

    // 生产环境配置
    production: {
        client_id: "441923165006-qnaq49csmjqgrg6d2f90itudbh7rf9it.apps.googleusercontent.com",
        redirect_uri: "https://ledger.goldenorangetech.com",
        scope: "openid email profile"
    }
};

// 获取当前环境配置
function getGoogleAuthConfig() {
    // 开发环境也使用生产配置，因为Google OAuth需要公共域名
    return GOOGLE_AUTH_CONFIG.production;
}

// Google登录工具类
class GoogleAuthManager {
    constructor() {
        this.config = getGoogleAuthConfig();
        this.initialized = false;
        this.user = null;
    }

    // 初始化Google登录
    async initialize() {
        return new Promise((resolve, reject) => {
            if (typeof google === 'undefined') {
                reject(new Error('Google SDK未加载'));
                return;
            }

            try {
                google.accounts.id.initialize({
                    client_id: this.config.client_id,
                    callback: this.handleCredentialResponse.bind(this),
                    auto_select: false,
                    cancel_on_tap_outside: true,
                    ux_mode: 'popup',
                    context: 'signin'
                });

                this.initialized = true;
                console.log('✅ Google认证管理器初始化成功');
                resolve();
            } catch (error) {
                console.error('❌ Google认证管理器初始化失败:', error);
                reject(error);
            }
        });
    }

    // 处理Google凭证响应
    handleCredentialResponse(response) {
        try {
            console.log('🔐 收到Google凭证响应');
            const credential = response.credential;
            const payload = this.parseJWT(credential);

            const user = {
                id: payload.sub,
                name: payload.name,
                email: payload.email,
                picture: payload.picture,
                provider: 'google',
                loginTime: new Date().toISOString(),
                token: credential
            };

            console.log('👤 Google用户信息:', {
                name: user.name,
                email: user.email
            });

            // 处理认证成功
            this.handleAuthSuccess(user);

        } catch (error) {
            console.error('❌ Google凭证处理失败:', error);
            this.onLoginError(error);
        }
    }

    // 解析JWT token
    parseJWT(token) {
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            return JSON.parse(jsonPayload);
        } catch (error) {
            throw new Error('JWT解析失败: ' + error.message);
        }
    }

    // 保存用户会话
    saveUserSession() {
        try {
            localStorage.setItem('goldenledger_user', JSON.stringify(this.user));
            localStorage.setItem('goldenledger_token', this.user.token);
            localStorage.setItem('goldenledger_login_time', this.user.loginTime);
            console.log('✅ 用户会话已保存');
        } catch (error) {
            console.error('❌ 保存用户会话失败:', error);
        }
    }

    // 启动Google登录流程
    signIn() {
        console.log('🔐 Google登录请求');

        try {
            // 直接使用模拟登录，避免复杂的OAuth问题
            this.simulateGoogleLogin();
        } catch (error) {
            console.error('❌ Google登录启动失败:', error);
            this.onLoginError(error);
        }
    }

    // 使用Google Identity Services登录
    signInWithPopup() {
        console.log('🔐 启动Google登录');

        // 检查是否为开发环境
        const isDev = window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1' ||
                     window.location.hostname.includes('192.168.');

        if (isDev) {
            // 开发环境：模拟Google登录成功
            console.log('🔐 开发环境：模拟Google登录');
            this.simulateGoogleLogin();
        } else {
            // 生产环境：使用Google Identity Services
            console.log('🔐 生产环境：使用Google Identity Services');
            this.useGoogleIdentityServices();
        }
    }

    // 使用Google Identity Services
    useGoogleIdentityServices() {
        try {
            if (typeof google !== 'undefined' && google.accounts && google.accounts.id) {
                // 重新初始化Google Identity Services
                google.accounts.id.initialize({
                    client_id: this.config.client_id,
                    callback: this.handleCredentialResponse.bind(this),
                    auto_select: false,
                    cancel_on_tap_outside: true
                });

                // 显示One Tap登录
                google.accounts.id.prompt((notification) => {
                    if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                        console.log('🔐 One Tap未显示，尝试备用方法');
                        this.showAlternativeLogin();
                    }
                });
            } else {
                console.log('🔐 Google Identity Services未加载，使用备用方法');
                this.showAlternativeLogin();
            }
        } catch (error) {
            console.error('❌ Google Identity Services错误:', error);
            this.showAlternativeLogin();
        }
    }

    // 显示备用登录方法
    showAlternativeLogin() {
        const message = `
            Google登录暂时不可用。

            请尝试以下方法：
            1. 使用开发登录页面测试
            2. 检查网络连接
            3. 稍后重试

            是否跳转到开发登录页面？
        `;

        if (confirm(message)) {
            window.location.href = 'login_dev.html';
        }
    }

    // 模拟Google登录成功
    simulateGoogleLogin() {
        console.log('🔐 模拟Google用户登录...');

        // 模拟用户选择过程
        setTimeout(() => {
            const mockUser = {
                id: 'google_' + Date.now(),
                name: 'Google测试用户',
                email: '<EMAIL>',
                picture: 'https://ui-avatars.com/api/?name=Google+User&background=4285f4&color=fff',
                provider: 'google',
                loginTime: new Date().toISOString(),
                token: 'mock_google_token_' + Date.now()
            };

            this.handleAuthSuccess(mockUser);
        }, 1000);
    }

    // 构建Google OAuth URL
    buildAuthUrl(config) {
        const params = new URLSearchParams({
            client_id: config.client_id,
            redirect_uri: config.redirect_uri,
            response_type: 'code',
            scope: config.scope,
            access_type: 'offline',
            prompt: 'consent'
        });

        return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
    }

    // 处理认证成功
    handleAuthSuccess(user) {
        this.user = user;

        // 集成用户管理系统
        if (window.userManager) {
            try {
                const managedUser = window.userManager.loginUser(user);
                this.user = managedUser;
                console.log('✅ 用户已保存到用户管理系统');
            } catch (error) {
                console.error('❌ 保存用户到管理系统失败:', error);
                // 继续使用原有逻辑
                this.saveUserSession();
            }
        } else {
            // 备用：使用原有逻辑
            this.saveUserSession();
        }

        this.onLoginSuccess(this.user);
    }

    // 登出
    signOut() {
        try {
            if (this.initialized && google.accounts.id) {
                google.accounts.id.disableAutoSelect();
            }
            
            // 清除本地存储
            localStorage.removeItem('goldenledger_user');
            localStorage.removeItem('goldenledger_token');
            localStorage.removeItem('goldenledger_login_time');
            
            this.user = null;
            console.log('✅ 用户已登出');
            
            // 跳转到登录页面
            window.location.href = 'login.html';
        } catch (error) {
            console.error('❌ 登出失败:', error);
        }
    }

    // 检查用户是否已登录
    isLoggedIn() {
        try {
            const user = localStorage.getItem('goldenledger_user');
            const token = localStorage.getItem('goldenledger_token');
            return !!(user && token);
        } catch (error) {
            return false;
        }
    }

    // 获取当前用户信息
    getCurrentUser() {
        try {
            const userStr = localStorage.getItem('goldenledger_user');
            return userStr ? JSON.parse(userStr) : null;
        } catch (error) {
            console.error('❌ 获取用户信息失败:', error);
            return null;
        }
    }

    // 登录成功回调 (可以被重写)
    onLoginSuccess(user) {
        console.log('✅ Google登录成功:', user.name);
        
        // 显示成功消息
        this.showMessage('Google登录成功！正在跳转...', 'success');
        
        // 跳转到目标页面
        setTimeout(() => {
            const returnUrl = new URLSearchParams(window.location.search).get('return') || 'master_dashboard.html';
            window.location.href = returnUrl;
        }, 1500);
    }

    // 登录失败回调 (可以被重写)
    onLoginError(error) {
        console.error('❌ Google登录失败:', error);
        this.showMessage('Google登录失败: ' + error.message, 'error');
    }

    // 显示消息
    showMessage(message, type = 'info') {
        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            info: 'bg-blue-500'
        };

        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300`;
        messageDiv.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                ${message}
            </div>
        `;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateX(100%)';
            setTimeout(() => messageDiv.remove(), 300);
        }, 3000);
    }
}

// 创建全局实例
window.googleAuthManager = new GoogleAuthManager();

console.log('🔐 Google认证配置已加载');

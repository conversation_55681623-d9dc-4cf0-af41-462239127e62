<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 高级数据分析仪表盘</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 登录验证脚本 -->
    <script>
        // 检查用户登录状态
        function checkAuthStatus() {
            const token = localStorage.getItem('goldenledger_session_token');
            const user = localStorage.getItem('goldenledger_user');

            if (!token || !user) {
                // 未登录，重定向到登录页面
                alert('この機能を利用するにはログインが必要です。');
                window.location.href = '/login_simple.html?return=' + encodeURIComponent(window.location.href);
                return false;
            }

            return true;
        }

        // 页面加载时检查登录状态
        if (!checkAuthStatus()) {
            // 如果未登录，停止页面加载
            document.body.style.display = 'none';
        }
    </script>

    <!-- 头部 -->
    <header class="gradient-bg text-white p-6 shadow-lg">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-3xl font-bold">goldenledger会計AI - 高级数据分析仪表盘</h1>
            <p class="text-blue-100 mt-2">智能财务数据分析平台</p>
        </div>
    </header>

    <div class="max-w-7xl mx-auto p-6">
        <!-- KPI指标 -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">本月收入</p>
                        <p id="monthly-revenue" class="text-2xl font-bold text-green-600">¥0</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">本月支出</p>
                        <p id="monthly-expenses" class="text-2xl font-bold text-red-600">¥0</p>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总记录数</p>
                        <p id="total-entries" class="text-2xl font-bold text-blue-600">0</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">AI成功率</p>
                        <p id="ai-success-rate" class="text-2xl font-bold text-purple-600">0%</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </section>

        <!-- 图表区域 -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- AI处理统计 -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🧠 AI処理統計</h3>
                <div class="relative">
                    <canvas id="ai-stats-chart" width="400" height="200"></canvas>
                    <div id="ai-chart-error" class="hidden text-center text-red-500 py-8">
                        チャート読み込みエラー
                    </div>
                </div>
            </div>

            <!-- 最近交易 -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📋 最近の取引</h3>
                <div id="recent-transactions" class="space-y-3">
                    <div class="text-center text-gray-500 py-8">取引データを読み込み中...</div>
                </div>
            </div>
        </section>

        <!-- 控制面板 -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold">コントロールパネル</h3>
                    <p class="text-gray-600 text-sm">データの更新と管理</p>
                </div>
                <div class="flex space-x-4">
                    <button id="refresh-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        🔄 データ更新
                    </button>
                    <button id="chart-refresh-btn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        📊 チャート再作成
                    </button>
                </div>
            </div>
            <div class="mt-4">
                <p id="last-update" class="text-sm text-gray-500">最后更新: --</p>
            </div>
        </section>

        <!-- 实时日志 -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <h3 class="text-lg font-semibold mb-4">📊 リアルタイムログ</h3>
            <div id="realtime-log" class="bg-gray-50 rounded-lg p-4 h-48 overflow-y-auto font-mono text-sm">
                <div class="text-gray-500">システム起動待機中...</div>
            </div>
        </section>
    </div>

    <script>
        let aiStatsChart = null;
        let logEntries = [];

        // 添加实时日志
        function addRealtimeLog(message) {
            const timestamp = new Date().toLocaleTimeString('ja-JP');
            logEntries.unshift(`[${timestamp}] ${message}`);
            if (logEntries.length > 50) logEntries.pop();
            
            const logContainer = document.getElementById('realtime-log');
            logContainer.innerHTML = logEntries.map(log => `<div>${log}</div>`).join('');
        }

        // 获取仪表盘数据
        async function fetchDashboardData() {
            try {
                addRealtimeLog('📊 ダッシュボードデータを取得中...');
                
                const response = await fetch('/dashboard/summary/default');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // 安全地更新KPI
                if (data.financial) {
                    const f = data.financial;
                    document.getElementById('monthly-revenue').textContent = `¥${(f.monthly_revenue || 0).toLocaleString()}`;
                    document.getElementById('monthly-expenses').textContent = `¥${(f.monthly_expenses || 0).toLocaleString()}`;
                    document.getElementById('total-entries').textContent = (f.total_entries || 0).toLocaleString();
                }
                
                if (data.ai_stats) {
                    const ai = data.ai_stats;
                    document.getElementById('ai-success-rate').textContent = `${((ai.success_rate || 0) * 100).toFixed(1)}%`;
                    
                    // 更新AI图表数据
                    updateAIChart(ai);
                }

                document.getElementById('last-update').textContent = `最后更新: ${new Date().toLocaleTimeString('ja-JP')}`;
                addRealtimeLog('✅ ダッシュボードデータ更新完了');
                
                return data;
            } catch (error) {
                addRealtimeLog(`❌ データ取得エラー: ${error.message}`);
                console.error('Error fetching dashboard data:', error);
            }
        }

        // 获取交易数据
        async function fetchTransactions() {
            try {
                addRealtimeLog('📋 取引データを取得中...');
                
                const response = await fetch('/journal-entries/default');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const transactions = await response.json();
                
                const container = document.getElementById('recent-transactions');
                if (transactions.length === 0) {
                    container.innerHTML = '<div class="text-center text-gray-500 py-8">取引データがありません</div>';
                } else {
                    container.innerHTML = transactions.slice(0, 5).map(tx => `
                        <div class="border-l-4 ${tx.ai_generated ? 'border-blue-500' : 'border-gray-300'} pl-3 py-2">
                            <div class="text-sm font-medium">${tx.description}</div>
                            <div class="text-xs text-gray-500">
                                ${tx.debit_account} → ${tx.credit_account}
                            </div>
                            <div class="text-sm font-semibold text-green-600">
                                ¥${parseFloat(tx.amount).toLocaleString()}
                                ${tx.ai_generated ? '<span class="text-blue-500 ml-1">🤖</span>' : ''}
                            </div>
                        </div>
                    `).join('');
                }
                
                addRealtimeLog(`✅ ${transactions.length}件の取引データを表示`);
                
            } catch (error) {
                addRealtimeLog(`❌ 取引データ取得エラー: ${error.message}`);
                console.error('Error fetching transactions:', error);
            }
        }

        // 初始化AI统计图表
        function initAIChart() {
            try {
                addRealtimeLog('🤖 AI統計チャート初期化中...');
                
                if (typeof Chart === 'undefined') {
                    throw new Error('Chart.js未加载');
                }
                
                const canvas = document.getElementById('ai-stats-chart');
                const errorDiv = document.getElementById('ai-chart-error');
                
                if (!canvas) {
                    throw new Error('Canvas元素未找到');
                }
                
                // 销毁现有图表
                if (aiStatsChart) {
                    aiStatsChart.destroy();
                }
                
                // 创建新图表
                const ctx = canvas.getContext('2d');
                aiStatsChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['成功', '失敗', '処理中'],
                        datasets: [{
                            label: 'AI処理結果',
                            data: [0, 0, 0], // 初始数据
                            backgroundColor: [
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(251, 191, 36, 0.8)'
                            ],
                            borderColor: [
                                'rgba(16, 185, 129, 1)',
                                'rgba(239, 68, 68, 1)',
                                'rgba(251, 191, 36, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'AI処理統計'
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
                
                // 隐藏错误信息，显示图表
                canvas.style.display = 'block';
                errorDiv.classList.add('hidden');
                
                addRealtimeLog('✅ AI統計チャート作成成功');
                return true;
                
            } catch (error) {
                addRealtimeLog(`❌ AI統計チャートエラー: ${error.message}`);
                console.error('AI chart error:', error);
                
                // 显示错误信息，隐藏图表
                const canvas = document.getElementById('ai-stats-chart');
                const errorDiv = document.getElementById('ai-chart-error');
                
                if (canvas) canvas.style.display = 'none';
                if (errorDiv) errorDiv.classList.remove('hidden');
                
                return false;
            }
        }

        // 更新AI图表数据
        function updateAIChart(aiStats) {
            if (!aiStatsChart) return;
            
            try {
                const total = aiStats.total_processed || 0;
                const successRate = aiStats.success_rate || 0;
                const successful = Math.round(total * successRate);
                const failed = total - successful;
                
                aiStatsChart.data.datasets[0].data = [successful, failed, 0];
                aiStatsChart.update();
                
                addRealtimeLog(`📊 AI統計チャート更新: 成功${successful}件, 失敗${failed}件`);
            } catch (error) {
                addRealtimeLog(`❌ AI統計チャート更新エラー: ${error.message}`);
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addRealtimeLog('🚀 システム起動中...');
            
            // 延迟初始化，确保Chart.js完全加载
            setTimeout(() => {
                // 初始化AI图表
                initAIChart();
                
                // 获取初始数据
                fetchDashboardData();
                fetchTransactions();
                
                addRealtimeLog('✅ システム起動完了');
            }, 300);
            
            // 刷新按钮
            document.getElementById('refresh-btn').addEventListener('click', function() {
                addRealtimeLog('🔄 手動データ更新開始');
                fetchDashboardData();
                fetchTransactions();
            });
            
            // 图表刷新按钮
            document.getElementById('chart-refresh-btn').addEventListener('click', function() {
                addRealtimeLog('📊 チャート手動再作成開始');
                initAIChart();
                fetchDashboardData(); // 重新获取数据以更新图表
            });
            
            // 定期更新数据
            setInterval(() => {
                fetchDashboardData();
                fetchTransactions();
            }, 30000); // 30秒更新一次
        });
    </script>
</body>
</html>

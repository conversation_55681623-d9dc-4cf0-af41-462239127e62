<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 清除缓存 - GoldenLedger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid #ccc;
        }
        .success { background: #d4edda; border-left-color: #28a745; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 清除缓存页面</h1>
        <p>此页面帮助清除浏览器和Cloudflare缓存，确保加载最新的API配置。</p>
        
        <div class="status info">
            <h3>📋 缓存清除步骤</h3>
            <p>1. 点击下方按钮清除本地存储</p>
            <p>2. 强制刷新页面 (Ctrl+F5 或 Cmd+Shift+R)</p>
            <p>3. 返回主页测试AI聊天功能</p>
        </div>
        
        <div id="results"></div>
        
        <button onclick="clearAllCache()">🗑️ 清除所有缓存</button>
        <button onclick="testNewApiKey()">🔑 测试新API密钥</button>
        <button onclick="goToMain()">🏠 返回主页</button>
        
        <div class="status info" style="margin-top: 20px;">
            <h3>🔧 手动清除缓存方法</h3>
            <p><strong>Chrome/Edge:</strong> Ctrl+Shift+Delete → 清除浏览数据</p>
            <p><strong>Firefox:</strong> Ctrl+Shift+Delete → 清除最近历史记录</p>
            <p><strong>Safari:</strong> Cmd+Option+E → 清空缓存</p>
        </div>
    </div>

    <!-- 加载环境配置 -->
    <script src="env-config.js?v=3.6.3"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearAllCache() {
            addResult('<h3>🔄 开始清除缓存...</h3>', 'info');
            
            // 清除localStorage
            try {
                localStorage.clear();
                addResult('✅ localStorage已清除', 'success');
            } catch (e) {
                addResult('❌ 清除localStorage失败: ' + e.message, 'error');
            }
            
            // 清除sessionStorage
            try {
                sessionStorage.clear();
                addResult('✅ sessionStorage已清除', 'success');
            } catch (e) {
                addResult('❌ 清除sessionStorage失败: ' + e.message, 'error');
            }
            
            // 尝试清除缓存API (如果支持)
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    return Promise.all(
                        names.map(function(name) {
                            return caches.delete(name);
                        })
                    );
                }).then(function() {
                    addResult('✅ Service Worker缓存已清除', 'success');
                }).catch(function(e) {
                    addResult('⚠️ Service Worker缓存清除失败: ' + e.message, 'info');
                });
            }
            
            // 显示完成信息
            setTimeout(() => {
                addResult(`
                    <h3>🎉 缓存清除完成！</h3>
                    <p>请按 <strong>Ctrl+F5</strong> (Windows) 或 <strong>Cmd+Shift+R</strong> (Mac) 强制刷新页面</p>
                    <p>然后返回主页测试AI聊天功能</p>
                `, 'success');
            }, 1000);
        }

        async function testNewApiKey() {
            // Get API key from environment configuration
            const apiKey = window.GEMINI_API_KEY;
            
            addResult('<h3>🧪 测试新API密钥...</h3>', 'info');
            
            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{ text: '测试API连接，请简单回复。' }]
                        }]
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const responseText = result.candidates?.[0]?.content?.parts?.[0]?.text || '无响应内容';
                    
                    addResult(`
                        <h3>✅ API测试成功！</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>AI响应:</strong> ${responseText.substring(0, 100)}...</p>
                        <p>新API密钥工作正常！</p>
                    `, 'success');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    addResult(`
                        <h3>❌ API测试失败</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>错误:</strong> ${errorData.error?.message || '未知错误'}</p>
                    `, 'error');
                }
            } catch (error) {
                addResult(`
                    <h3>❌ 网络错误</h3>
                    <p>${error.message}</p>
                `, 'error');
            }
        }

        function goToMain() {
            // 强制刷新主页
            window.location.href = '/?v=' + Date.now();
        }

        // 页面加载时显示当前时间
        window.addEventListener('load', function() {
            addResult(`
                <h3>📅 页面加载时间</h3>
                <p>${new Date().toLocaleString()}</p>
                <p>版本: 3.6.2 - 缓存清除工具</p>
            `, 'info');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .panel {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="panel">
        <h1>🎵 音乐测试页面</h1>
        <p>设备: <span id="device">检测中...</span></p>
    </div>

    <div class="panel">
        <button class="button" onclick="testClick()">🎵 测试音乐点击</button>
        <button class="button" onclick="checkStatus()">📊 检查状态</button>
    </div>

    <div class="panel">
        <h2>📋 测试日志</h2>
        <div class="log" id="log">等待测试...</div>
    </div>

    <script src="music_control_new.js"></script>
    <script>
        function log(message) {
            const time = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = `[${time}] ${message}<br>` + logDiv.innerHTML;
            console.log(`[${time}] ${message}`);
        }
        
        function updateDevice() {
            const isMobile = window.innerWidth <= 768;
            document.getElementById('device').textContent = 
                `${window.innerWidth}x${window.innerHeight} (${isMobile ? '移动端' : '桌面端'})`;
        }
        
        function testClick() {
            log('开始测试音乐点击');
            
            const musicBtn = document.getElementById('music-toggle');
            if (musicBtn) {
                log('找到音乐按钮，执行点击');
                musicBtn.click();
                
                setTimeout(() => {
                    if (window.goldenLedgerMusic) {
                        log('播放状态: ' + (window.goldenLedgerMusic.isPlaying ? '播放中' : '暂停'));
                    }
                }, 100);
            } else {
                log('❌ 音乐按钮未找到');
            }
        }
        
        function checkStatus() {
            log('=== 检查状态 ===');
            
            if (window.goldenLedgerMusic) {
                log('✅ 音乐控制器存在');
                log('播放状态: ' + (window.goldenLedgerMusic.isPlaying ? '播放中' : '暂停'));
            } else {
                log('❌ 音乐控制器不存在');
            }
            
            const musicBtn = document.getElementById('music-toggle');
            log('音乐按钮: ' + (musicBtn ? '存在' : '不存在'));
            
            const musicElement = document.getElementById('music-controller');
            log('音乐元素: ' + (musicElement ? '存在' : '不存在'));
        }
        
        // 监听音乐按钮点击
        document.addEventListener('click', function(e) {
            if (e.target.id === 'music-toggle') {
                log('🎵 检测到音乐按钮点击事件');
            }
        });
        
        // 页面加载
        window.addEventListener('load', function() {
            updateDevice();
            log('页面加载完成');
            
            setTimeout(function() {
                checkStatus();
            }, 1000);
        });
        
        log('测试页面初始化');
    </script>
</body>
</html>

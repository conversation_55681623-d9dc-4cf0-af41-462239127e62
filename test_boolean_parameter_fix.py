#!/usr/bin/env python3
"""
测试布尔参数修复
"""
import requests
import json

def test_boolean_parameter_fix():
    """测试布尔参数修复"""
    
    print("🔧 测试布尔参数修复")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    test_entry_id = "J20250711165206"
    
    try:
        # 测试不同的布尔参数格式
        test_cases = [
            ("json_preview=true", "字符串 'true'"),
            ("json_preview=True", "字符串 'True'"),
            ("json_preview=1", "字符串 '1'"),
            ("json_preview=yes", "字符串 'yes'"),
            ("json_preview=on", "字符串 'on'"),
            ("json_preview=false", "字符串 'false'"),
            ("json_preview=0", "字符串 '0'"),
            ("json_preview=no", "字符串 'no'"),
            ("json_preview=off", "字符串 'off'"),
            ("preview=true", "preview参数"),
            ("preview=false", "preview参数false"),
            ("", "无参数（默认列表模式）")
        ]
        
        print(f"\n📋 测试记录ID: {test_entry_id}")
        print(f"🌐 基础URL: {base_url}")
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, (param, description) in enumerate(test_cases, 1):
            print(f"\n测试 {i}/{total_count}: {description}")
            
            # 构建URL
            if param:
                url = f"{base_url}/attachments/{test_entry_id}?{param}"
            else:
                url = f"{base_url}/attachments/{test_entry_id}"
            
            print(f"  URL: {url}")
            
            try:
                response = requests.get(url)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        # 判断响应类型
                        if isinstance(data, list):
                            print(f"  ✅ 返回类型: 列表 (附件列表)")
                            print(f"  附件数量: {len(data)}")
                            if data:
                                print(f"  第一个附件: {data[0].get('filename', 'N/A')}")
                        elif isinstance(data, dict) and data.get('success'):
                            print(f"  ✅ 返回类型: JSON预览")
                            print(f"  文件名: {data.get('filename', 'N/A')}")
                            print(f"  内容类型: {data.get('content_type', 'N/A')}")
                            print(f"  文件大小: {data.get('size', 0)} bytes")
                            print(f"  Base64长度: {len(data.get('file_content', ''))} 字符")
                        else:
                            print(f"  ⚠️ 返回类型: 未知格式")
                            print(f"  数据: {str(data)[:100]}...")
                        
                        success_count += 1
                        
                    except json.JSONDecodeError:
                        print(f"  ⚠️ 非JSON响应")
                        print(f"  Content-Type: {response.headers.get('content-type', 'N/A')}")
                        print(f"  内容长度: {len(response.content)} bytes")
                        success_count += 1  # 文件响应也算成功
                        
                elif response.status_code == 422:
                    print(f"  ❌ 参数验证失败 (422)")
                    try:
                        error_data = response.json()
                        print(f"  错误详情: {error_data}")
                    except:
                        print(f"  错误内容: {response.text}")
                        
                else:
                    print(f"  ❌ 请求失败: {response.status_code}")
                    print(f"  响应: {response.text[:200]}...")
                    
            except Exception as e:
                print(f"  ❌ 请求异常: {e}")
        
        print(f"\n" + "=" * 50)
        print(f"📊 测试结果总结:")
        print(f"  成功: {success_count}/{total_count}")
        print(f"  成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("🎉 所有测试通过！布尔参数修复成功！")
        else:
            print("⚠️ 部分测试失败，需要进一步检查")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_frontend_compatibility():
    """测试前端兼容性"""
    
    print(f"\n🌐 测试前端兼容性")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    test_entry_id = "J20250711165206"
    
    try:
        # 模拟前端的实际调用
        print("模拟前端JavaScript调用...")
        
        # 这是前端实际使用的URL
        frontend_url = f"{base_url}/attachments/{test_entry_id}?json_preview=true"
        print(f"前端URL: {frontend_url}")
        
        response = requests.get(frontend_url)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✅ 前端兼容性测试通过")
                print(f"  文件名: {data.get('filename')}")
                print(f"  内容类型: {data.get('content_type')}")
                print(f"  文件大小: {data.get('size')} bytes")
                print(f"  Base64可用: {'是' if data.get('file_content') else '否'}")
                
                # 验证Base64内容
                file_content = data.get('file_content', '')
                if file_content and len(file_content) > 100:
                    print(f"  Base64前缀: {file_content[:50]}...")
                    print("✅ Base64内容完整")
                else:
                    print("❌ Base64内容不完整")
                    return False
                
                return True
            else:
                print("❌ API返回success=false")
                print(f"错误: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 前端兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始测试布尔参数修复")
    
    # 测试布尔参数处理
    param_success = test_boolean_parameter_fix()
    
    # 测试前端兼容性
    frontend_success = test_frontend_compatibility()
    
    print("\n" + "=" * 80)
    print("📊 布尔参数修复测试结果:")
    
    if param_success and frontend_success:
        print("🎉 布尔参数修复完全成功！")
        print("\n✅ 修复内容:")
        print("  - 后端API参数改为字符串类型")
        print("  - 支持多种布尔值格式: true/True/1/yes/on")
        print("  - 支持false值: false/False/0/no/off")
        print("  - 前端调用完全兼容")
        print("  - 解决了422参数验证错误")
        
        print(f"\n🌐 现在可以正常使用:")
        print("  记账页面: http://localhost:8000/journal_entries.html")
        print("  - 点击编辑按钮")
        print("  - 在附件部分点击'プレビュー'按钮")
        print("  - 应该能正常显示附件预览模态框")
        
    else:
        print("❌ 部分功能测试失败")
        if not param_success:
            print("  - 布尔参数处理有问题")
        if not frontend_success:
            print("  - 前端兼容性有问题")

# GoldenLedger Environment Variables Template
# 复制此文件为 .env 并填入实际值

# ===========================================
# AI Model Configuration (Required)
# ===========================================
GEMINI_API_KEY=your_gemini_api_key_here

# ===========================================
# Database Configuration
# ===========================================
DATABASE_URL=sqlite:///data/databases/goldenledger_accounting.db
REDIS_URL=redis://localhost:6379

# Database Security
DATA_DIR=data
DB_ENCRYPTION_ENABLED=true
DB_BACKUP_ENABLED=true
DB_AUDIT_ENABLED=true
DB_BACKUP_INTERVAL_HOURS=6
DB_BACKUP_RETENTION_DAYS=30

# ===========================================
# Security Configuration (Required)
# ===========================================
SECRET_KEY=your_super_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# Application Settings
# ===========================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# ===========================================
# Google Cloud Services (Optional)
# ===========================================
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# ===========================================
# Additional AI Models (Optional)
# ===========================================
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===========================================
# CORS Settings
# ===========================================
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# ===========================================
# Cloudflare Pages Environment Variables
# ===========================================
# 在 Cloudflare Pages Dashboard 中设置以下环境变量：
# - GEMINI_API_KEY: 您的 Gemini API 密钥
# - SECRET_KEY: 随机生成的密钥
# - ENVIRONMENT: production
# - DEBUG: false

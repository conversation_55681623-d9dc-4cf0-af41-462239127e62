<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 多语言智能记账系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .language-flag { width: 24px; height: 18px; border-radius: 2px; }
        .chat-bubble { max-width: 300px; padding: 12px 16px; border-radius: 18px; margin: 8px 0; }
        .chat-user { background: #00d4aa; color: white; margin-left: auto; }
        .chat-ai { background: #f3f4f6; color: #374151; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 id="main-title" class="text-3xl font-bold">🌍 多语言AI记账系统</h1>
                    <p id="main-subtitle" class="text-lg opacity-90">支持中文、日语、英语的智能记账</p>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Language Selector -->
                    <div class="relative">
                        <select id="language-selector" class="bg-white/20 text-white border border-white/30 rounded-lg px-3 py-2 text-sm">
                            <option value="zh" data-flag="🇨🇳">中文 (简体)</option>
                            <option value="ja" data-flag="🇯🇵" selected>日本語</option>
                            <option value="en" data-flag="🇺🇸">English</option>
                            <option value="ko" data-flag="🇰🇷">한국어</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Feature Cards -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🗣️</span>
                    </div>
                    <h3 id="feature-1-title" class="font-semibold mb-2">多语言AI理解</h3>
                    <p id="feature-1-desc" class="text-gray-600 text-sm">支持中文、日语、英语的自然语言记账</p>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🔄</span>
                    </div>
                    <h3 id="feature-2-title" class="font-semibold mb-2">智能翻译</h3>
                    <p id="feature-2-desc" class="text-gray-600 text-sm">自动翻译和本地化会计科目</p>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🌐</span>
                    </div>
                    <h3 id="feature-3-title" class="font-semibold mb-2">国际化标准</h3>
                    <p id="feature-3-desc" class="text-gray-600 text-sm">符合各国会计准则和货币格式</p>
                </div>
            </div>
        </section>

        <!-- Multi-language AI Chat -->
        <section class="mb-8">
            <h2 id="chat-section-title" class="text-2xl font-bold text-center mb-8">💬 多语言AI记账体验</h2>
            <div class="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Chat Header -->
                <div class="gradient-bg text-white p-4">
                    <h3 id="chat-header-title" class="font-semibold">AI会計アシスタント</h3>
                    <p id="chat-header-subtitle" class="text-sm opacity-90">多语言智能记账助手</p>
                </div>
                
                <!-- Chat Messages -->
                <div id="chat-messages" class="p-4 space-y-4 h-96 overflow-y-auto">
                    <div class="chat-bubble chat-ai" id="welcome-message">
                        您好！我是AI会计助手，支持中文、日语、英语记账。请告诉我您的交易内容。
                    </div>
                </div>
                
                <!-- Language Examples -->
                <div class="border-t p-4 bg-gray-50">
                    <h4 id="examples-title" class="text-sm font-semibold mb-3">多语言示例:</h4>
                    <div class="grid grid-cols-1 gap-2">
                        <button class="language-example text-left p-2 bg-white rounded border hover:bg-gray-50 text-sm" data-lang="zh" data-text="今天在便利店买了办公用品，花费120元">
                            🇨🇳 今天在便利店买了办公用品，花费120元
                        </button>
                        <button class="language-example text-left p-2 bg-white rounded border hover:bg-gray-50 text-sm" data-lang="ja" data-text="今日コンビニで事務用品を1200円で購入しました">
                            🇯🇵 今日コンビニで事務用品を1200円で購入しました
                        </button>
                        <button class="language-example text-left p-2 bg-white rounded border hover:bg-gray-50 text-sm" data-lang="en" data-text="Purchased office supplies at convenience store for $12">
                            🇺🇸 Purchased office supplies at convenience store for $12
                        </button>
                        <button class="language-example text-left p-2 bg-white rounded border hover:bg-gray-50 text-sm" data-lang="ko" data-text="편의점에서 사무용품을 12000원에 구매했습니다">
                            🇰🇷 편의점에서 사무용품을 12000원에 구매했습니다
                        </button>
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="border-t p-4">
                    <div class="flex space-x-2">
                        <input 
                            id="chat-input" 
                            type="text" 
                            placeholder="请输入交易内容..." 
                            class="flex-1 border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <button 
                            id="send-btn" 
                            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                        >
                            发送
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Currency and Localization -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Currency Support -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 id="currency-title" class="text-lg font-semibold mb-4">💰 多货币支持</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">💴</span>
                            <div>
                                <div class="font-medium">日本円 (JPY)</div>
                                <div class="text-sm text-gray-500">¥1,200</div>
                            </div>
                        </div>
                        <div class="text-green-600 font-semibold">Active</div>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">💵</span>
                            <div>
                                <div class="font-medium">美元 (USD)</div>
                                <div class="text-sm text-gray-500">$12.00</div>
                            </div>
                        </div>
                        <div class="text-gray-400">Available</div>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">💰</span>
                            <div>
                                <div class="font-medium">人民币 (CNY)</div>
                                <div class="text-sm text-gray-500">¥120.00</div>
                            </div>
                        </div>
                        <div class="text-gray-400">Available</div>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">💸</span>
                            <div>
                                <div class="font-medium">韩元 (KRW)</div>
                                <div class="text-sm text-gray-500">₩12,000</div>
                            </div>
                        </div>
                        <div class="text-gray-400">Available</div>
                    </div>
                </div>
            </div>

            <!-- Accounting Standards -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 id="standards-title" class="text-lg font-semibold mb-4">📋 会计准则支持</h3>
                <div class="space-y-3">
                    <div class="p-3 border rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium">🇯🇵 日本会计基准</span>
                            <span class="text-green-600 text-sm">Active</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            企業会計基準、仕訳帳、総勘定元帳
                        </div>
                    </div>

                    <div class="p-3 border rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium">🇨🇳 中国会计准则</span>
                            <span class="text-gray-400 text-sm">Available</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            企业会计准则、会计科目、记账凭证
                        </div>
                    </div>

                    <div class="p-3 border rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium">🇺🇸 US GAAP</span>
                            <span class="text-gray-400 text-sm">Available</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            Generally Accepted Accounting Principles
                        </div>
                    </div>

                    <div class="p-3 border rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-medium">🌍 IFRS</span>
                            <span class="text-gray-400 text-sm">Available</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            International Financial Reporting Standards
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Translation Demo -->
        <section class="bg-white rounded-xl p-6 shadow-sm border">
            <h3 id="translation-title" class="text-lg font-semibold mb-4">🔄 实时翻译演示</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label id="input-label" class="block text-sm font-medium text-gray-700 mb-2">输入文本</label>
                    <textarea 
                        id="translation-input" 
                        class="w-full h-32 border rounded-lg p-3 text-sm"
                        placeholder="输入任何语言的交易描述..."
                    ></textarea>
                    <div class="mt-2 flex space-x-2">
                        <button class="translate-btn bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600" data-target="ja">
                            译为日语
                        </button>
                        <button class="translate-btn bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600" data-target="en">
                            译为英语
                        </button>
                        <button class="translate-btn bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600" data-target="zh">
                            译为中文
                        </button>
                    </div>
                </div>
                <div>
                    <label id="output-label" class="block text-sm font-medium text-gray-700 mb-2">翻译结果</label>
                    <div id="translation-output" class="w-full h-32 border rounded-lg p-3 text-sm bg-gray-50">
                        翻译结果将显示在这里...
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span id="translation-status">准备就绪</span>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 多语言文本配置
        const translations = {
            zh: {
                'main-title': '🌍 多语言AI记账系统',
                'main-subtitle': '支持中文、日语、英语的智能记账',
                'feature-1-title': '多语言AI理解',
                'feature-1-desc': '支持中文、日语、英语的自然语言记账',
                'feature-2-title': '智能翻译',
                'feature-2-desc': '自动翻译和本地化会计科目',
                'feature-3-title': '国际化标准',
                'feature-3-desc': '符合各国会计准则和货币格式',
                'chat-section-title': '💬 多语言AI记账体验',
                'chat-header-title': 'AI会计助手',
                'chat-header-subtitle': '多语言智能记账助手',
                'welcome-message': '您好！我是AI会计助手，支持中文、日语、英语记账。请告诉我您的交易内容。',
                'examples-title': '多语言示例:',
                'currency-title': '💰 多货币支持',
                'standards-title': '📋 会计准则支持',
                'translation-title': '🔄 实时翻译演示',
                'input-label': '输入文本',
                'output-label': '翻译结果',
                'translation-status': '准备就绪',
                'send-btn': '发送',
                'chat-placeholder': '请输入交易内容...'
            },
            ja: {
                'main-title': '🌍 多言語AI記帳システム',
                'main-subtitle': '中国語、日本語、英語対応のスマート記帳',
                'feature-1-title': '多言語AI理解',
                'feature-1-desc': '中国語、日本語、英語の自然言語記帳に対応',
                'feature-2-title': 'スマート翻訳',
                'feature-2-desc': '会計科目の自動翻訳とローカライゼーション',
                'feature-3-title': '国際化標準',
                'feature-3-desc': '各国の会計基準と通貨形式に対応',
                'chat-section-title': '💬 多言語AI記帳体験',
                'chat-header-title': 'AI会計アシスタント',
                'chat-header-subtitle': '多言語スマート記帳アシスタント',
                'welcome-message': 'こんにちは！AI会計アシスタントです。中国語、日本語、英語での記帳に対応しています。取引内容をお聞かせください。',
                'examples-title': '多言語例:',
                'currency-title': '💰 多通貨対応',
                'standards-title': '📋 会計基準対応',
                'translation-title': '🔄 リアルタイム翻訳デモ',
                'input-label': '入力テキスト',
                'output-label': '翻訳結果',
                'translation-status': '準備完了',
                'send-btn': '送信',
                'chat-placeholder': '取引内容を入力してください...'
            },
            en: {
                'main-title': '🌍 Multilingual AI Accounting System',
                'main-subtitle': 'Smart bookkeeping in Chinese, Japanese, and English',
                'feature-1-title': 'Multilingual AI Understanding',
                'feature-1-desc': 'Natural language bookkeeping in Chinese, Japanese, and English',
                'feature-2-title': 'Smart Translation',
                'feature-2-desc': 'Automatic translation and localization of accounting subjects',
                'feature-3-title': 'International Standards',
                'feature-3-desc': 'Compliant with accounting standards and currency formats',
                'chat-section-title': '💬 Multilingual AI Bookkeeping Experience',
                'chat-header-title': 'AI Accounting Assistant',
                'chat-header-subtitle': 'Multilingual Smart Bookkeeping Assistant',
                'welcome-message': 'Hello! I am your AI accounting assistant, supporting bookkeeping in Chinese, Japanese, and English. Please tell me about your transaction.',
                'examples-title': 'Multilingual Examples:',
                'currency-title': '💰 Multi-Currency Support',
                'standards-title': '📋 Accounting Standards Support',
                'translation-title': '🔄 Real-time Translation Demo',
                'input-label': 'Input Text',
                'output-label': 'Translation Result',
                'translation-status': 'Ready',
                'send-btn': 'Send',
                'chat-placeholder': 'Enter transaction details...'
            }
        };

        // 切换语言
        function switchLanguage(lang) {
            const texts = translations[lang];
            if (!texts) return;

            Object.keys(texts).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (key === 'chat-placeholder') {
                        element.placeholder = texts[key];
                    } else {
                        element.textContent = texts[key];
                    }
                }
            });

            // 更新欢迎消息
            const welcomeMsg = document.getElementById('welcome-message');
            if (welcomeMsg) {
                welcomeMsg.textContent = texts['welcome-message'];
            }
        }

        // 模拟AI处理多语言输入
        async function processMultilingualInput(text, detectedLang = 'auto') {
            const messagesContainer = document.getElementById('chat-messages');
            
            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-bubble chat-user';
            userMessage.textContent = text;
            messagesContainer.appendChild(userMessage);
            
            // 添加加载消息
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'chat-bubble chat-ai';
            loadingMessage.textContent = '处理中... / 処理中... / Processing...';
            messagesContainer.appendChild(loadingMessage);
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // 模拟AI处理
            setTimeout(() => {
                messagesContainer.removeChild(loadingMessage);
                
                const aiResponse = document.createElement('div');
                aiResponse.className = 'chat-bubble chat-ai';
                aiResponse.innerHTML = `
                    <div class="space-y-2">
                        <p><strong>检测语言:</strong> ${detectedLang === 'zh' ? '中文' : detectedLang === 'ja' ? '日本語' : detectedLang === 'en' ? 'English' : '自动检测'}</p>
                        <p><strong>生成仕訳:</strong></p>
                        <div class="bg-blue-50 border border-blue-200 rounded p-2 text-sm">
                            <div class="grid grid-cols-2 gap-2">
                                <div><strong>借方:</strong> 消耗品費</div>
                                <div><strong>貸方:</strong> 現金</div>
                            </div>
                            <div class="mt-1"><strong>金額:</strong> ¥1,200</div>
                        </div>
                        <p class="text-xs text-gray-500">✅ 多语言处理成功 | 信頼度: 92%</p>
                    </div>
                `;
                messagesContainer.appendChild(aiResponse);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 1500);
        }

        // 模拟翻译功能
        function translateText(text, targetLang) {
            const translations = {
                'zh': {
                    'office supplies': '办公用品',
                    'convenience store': '便利店',
                    'purchase': '购买'
                },
                'ja': {
                    'office supplies': '事務用品',
                    'convenience store': 'コンビニ',
                    'purchase': '購入'
                },
                'en': {
                    '办公用品': 'office supplies',
                    '便利店': 'convenience store',
                    '购买': 'purchase'
                }
            };

            // 简单的翻译模拟
            let result = text;
            if (translations[targetLang]) {
                Object.keys(translations[targetLang]).forEach(key => {
                    result = result.replace(new RegExp(key, 'gi'), translations[targetLang][key]);
                });
            }

            return result || `[${targetLang.toUpperCase()}] ${text}`;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 语言选择器
            const languageSelector = document.getElementById('language-selector');
            languageSelector.addEventListener('change', function() {
                switchLanguage(this.value);
            });

            // 语言示例按钮
            document.querySelectorAll('.language-example').forEach(btn => {
                btn.addEventListener('click', function() {
                    const text = this.getAttribute('data-text');
                    const lang = this.getAttribute('data-lang');
                    document.getElementById('chat-input').value = text;
                    processMultilingualInput(text, lang);
                });
            });

            // 发送按钮
            document.getElementById('send-btn').addEventListener('click', function() {
                const text = document.getElementById('chat-input').value.trim();
                if (text) {
                    processMultilingualInput(text);
                    document.getElementById('chat-input').value = '';
                }
            });

            // 回车发送
            document.getElementById('chat-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('send-btn').click();
                }
            });

            // 翻译按钮
            document.querySelectorAll('.translate-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const text = document.getElementById('translation-input').value.trim();
                    const targetLang = this.getAttribute('data-target');
                    
                    if (text) {
                        document.getElementById('translation-status').textContent = '翻译中...';
                        
                        setTimeout(() => {
                            const translated = translateText(text, targetLang);
                            document.getElementById('translation-output').textContent = translated;
                            document.getElementById('translation-status').textContent = `已翻译为${targetLang === 'zh' ? '中文' : targetLang === 'ja' ? '日语' : '英语'}`;
                        }, 1000);
                    }
                });
            });
        });
    </script>
</body>
</html>

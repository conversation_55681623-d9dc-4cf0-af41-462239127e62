<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>プラン選択 - GoldenLedger AI記帳システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- PayPal 配置 -->
    <script src="paypal_config.js"></script>
    
    <!-- API 配置 -->
    <script src="api_config.js"></script>
    
    <style>
        * {
            font-family: 'Noto Sans JP', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            min-height: 100vh;
        }

        .tech-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: techPulse 4s ease-in-out infinite alternate;
        }

        @keyframes techPulse {
            0% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }

        .cyber-glass {
            backdrop-filter: blur(20px);
            background: rgba(0, 20, 40, 0.15);
            border: 1px solid rgba(120, 219, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .plan-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        .plan-card.recommended {
            border: 2px solid #00d4ff;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }

        .plan-card.recommended::before {
            content: '推奨';
            position: absolute;
            top: -1px;
            right: 20px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 5px 15px;
            font-size: 12px;
            font-weight: bold;
            border-radius: 0 0 10px 10px;
        }

        .price-display {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-list li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 8px;
        }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #00d4ff;
            font-weight: bold;
        }

        .paypal-button-container {
            min-height: 50px;
            margin-top: 20px;
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(120, 219, 255, 0.6);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="tech-bg">
    <!-- 浮动粒子效果 -->
    <div class="floating-particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 15%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 25%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 35%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 5%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 2.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 3.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 4.5s;"></div>
    </div>

    <!-- 主要内容 -->
    <div class="min-h-screen relative z-10">
        <!-- 导航栏 -->
        <nav class="p-6">
            <div class="max-w-7xl mx-auto flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-2xl font-bold text-cyan-400">
                        <i class="fas fa-coins mr-2"></i>GoldenLedger
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="auth/login.html" class="text-cyan-300 hover:text-cyan-400 transition-colors">
                        <i class="fas fa-sign-in-alt mr-2"></i>ログイン
                    </a>
                </div>
            </div>
        </nav>

        <!-- 页面标题 -->
        <div class="text-center py-12">
            <h1 class="text-5xl font-bold mb-4">
                <span class="bg-gradient-to-r from-cyan-400 to-blue-600 bg-clip-text text-transparent">
                    プラン選択
                </span>
            </h1>
            <p class="text-xl text-cyan-200 max-w-2xl mx-auto">
                あなたのビジネスに最適なプランを選択してください
            </p>
        </div>

        <!-- 套餐卡片 -->
        <div class="max-w-7xl mx-auto px-6 pb-20">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" id="plansContainer">
                <!-- 套餐卡片将通过 JavaScript 动态生成 -->
            </div>
        </div>

        <!-- 支付处理模态框 -->
        <div id="paymentModal" class="hidden fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="cyber-glass rounded-3xl p-10 max-w-md w-full mx-4">
                <div class="text-center">
                    <div class="loading-spinner w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full mx-auto mb-6"></div>
                    <h3 class="text-2xl font-bold text-cyan-400 mb-4">支払い処理中...</h3>
                    <p class="text-cyan-200">PayPalで安全に処理されています</p>
                </div>
            </div>
        </div>

        <!-- 错误提示模态框 -->
        <div id="errorModal" class="hidden fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="cyber-glass rounded-3xl p-10 max-w-md w-full mx-4">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-exclamation-triangle text-3xl text-red-400"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-red-400 mb-4">エラーが発生しました</h3>
                    <p class="text-cyan-200 mb-6" id="errorMessage">支払い処理中にエラーが発生しました。</p>
                    <button onclick="closeErrorModal()" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors">
                        閉じる
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="payment.js"></script>
</body>
</html>

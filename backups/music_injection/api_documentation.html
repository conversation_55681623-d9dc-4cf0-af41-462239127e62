<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - API文档</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .code-block { background: #1e293b; color: #e2e8f0; }
        .method-get { background: #10b981; }
        .method-post { background: #3b82f6; }
        .method-put { background: #f59e0b; }
        .method-delete { background: #ef4444; }
        .endpoint-card { transition: all 0.3s ease; }
        .endpoint-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">📚 goldenledger会計AI - API文档</h1>
                    <p class="text-lg opacity-90">完整的RESTful API接口文档和使用指南</p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="bg-white/20 px-3 py-1 rounded-full text-sm">
                        API版本: v1.0.0
                    </div>
                    <a href="http://localhost:8000/docs" target="_blank" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        🔗 Swagger UI
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- API Overview -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6">🌟 API概览</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-3xl font-bold text-blue-600 mb-2">50+</div>
                    <div class="text-sm text-gray-600">API端点</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-3xl font-bold text-green-600 mb-2">8</div>
                    <div class="text-sm text-gray-600">功能模块</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-3xl font-bold text-purple-600 mb-2">REST</div>
                    <div class="text-sm text-gray-600">API架构</div>
                </div>
                <div class="text-center p-4 border rounded-lg">
                    <div class="text-3xl font-bold text-orange-600 mb-2">JSON</div>
                    <div class="text-sm text-gray-600">数据格式</div>
                </div>
            </div>
        </section>

        <!-- Authentication -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6">🔐 认证</h2>
            
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">JWT令牌认证</h3>
                    <p class="text-gray-600 mb-4">所有API请求都需要在Header中包含有效的JWT令牌。</p>
                    
                    <div class="code-block p-4 rounded-lg">
                        <pre><code>Authorization: Bearer &lt;your-jwt-token&gt;</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">获取令牌</h3>
                    <div class="code-block p-4 rounded-lg">
                        <pre><code>POST /api/auth/login
{
  "username": "your-username",
  "password": "your-password",
  "mfa_code": "123456"  // 可选，如果启用了MFA
}</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- API Endpoints -->
        <section class="space-y-8">
            <!-- AI Processing APIs -->
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-6">🤖 AI处理接口</h2>
                
                <div class="space-y-4">
                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                                <span class="font-mono text-lg">/api/ai/process</span>
                            </div>
                            <span class="text-sm text-gray-500">AI智能处理</span>
                        </div>
                        
                        <p class="text-gray-600 mb-4">使用AI处理自然语言输入，生成仕訳记录。</p>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold mb-2">请求体</h4>
                                <div class="code-block p-3 rounded text-sm">
                                    <pre><code>{
  "text": "购买办公用品1000円",
  "language": "ja",
  "context": "expense"
}</code></pre>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">响应</h4>
                                <div class="code-block p-3 rounded text-sm">
                                    <pre><code>{
  "success": true,
  "journal_entry": {
    "debit_account": "消耗品費",
    "credit_account": "現金",
    "amount": 1000
  }
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                                <span class="font-mono text-lg">/api/ai/multi-agent</span>
                            </div>
                            <span class="text-sm text-gray-500">多智能体协作</span>
                        </div>
                        
                        <p class="text-gray-600 mb-4">使用多个AI智能体协作处理复杂的财务任务。</p>
                        
                        <div class="code-block p-3 rounded text-sm">
                            <pre><code>POST /api/ai/multi-agent
{
  "task": "analyze_financial_data",
  "data": {...},
  "agents": ["accountant", "auditor", "analyst"]
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Authentication APIs -->
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-6">👥 用户认证接口</h2>
                
                <div class="space-y-4">
                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                            <span class="font-mono text-lg">/api/auth/login</span>
                        </div>
                        <p class="text-gray-600">用户登录认证</p>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                            <span class="font-mono text-lg">/api/auth/register</span>
                        </div>
                        <p class="text-gray-600">用户注册</p>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                            <span class="font-mono text-lg">/api/auth/enable-mfa</span>
                        </div>
                        <p class="text-gray-600">启用多因子认证</p>
                    </div>
                </div>
            </div>

            <!-- Backup APIs -->
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-6">💾 备份管理接口</h2>
                
                <div class="space-y-4">
                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                            <span class="font-mono text-lg">/api/backup/create</span>
                        </div>
                        <p class="text-gray-600">创建数据备份</p>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-get text-white px-3 py-1 rounded text-sm font-medium">GET</span>
                            <span class="font-mono text-lg">/api/backup/list</span>
                        </div>
                        <p class="text-gray-600">获取备份列表</p>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-post text-white px-3 py-1 rounded text-sm font-medium">POST</span>
                            <span class="font-mono text-lg">/api/backup/restore</span>
                        </div>
                        <p class="text-gray-600">恢复数据备份</p>
                    </div>
                </div>
            </div>

            <!-- System APIs -->
            <div class="bg-white rounded-xl p-8 shadow-sm border">
                <h2 class="text-2xl font-bold mb-6">🖥️ 系统监控接口</h2>
                
                <div class="space-y-4">
                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-get text-white px-3 py-1 rounded text-sm font-medium">GET</span>
                            <span class="font-mono text-lg">/api/system/health</span>
                        </div>
                        <p class="text-gray-600">系统健康检查</p>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-get text-white px-3 py-1 rounded text-sm font-medium">GET</span>
                            <span class="font-mono text-lg">/api/system/performance</span>
                        </div>
                        <p class="text-gray-600">系统性能指标</p>
                    </div>

                    <div class="endpoint-card border rounded-lg p-4">
                        <div class="flex items-center space-x-3 mb-3">
                            <span class="method-get text-white px-3 py-1 rounded text-sm font-medium">GET</span>
                            <span class="font-mono text-lg">/api/system/services</span>
                        </div>
                        <p class="text-gray-600">系统服务状态</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Error Codes -->
        <section class="bg-white rounded-xl p-8 shadow-sm border mb-8">
            <h2 class="text-2xl font-bold mb-6">⚠️ 错误代码</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">HTTP状态码</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between"><span>200</span><span>成功</span></div>
                        <div class="flex justify-between"><span>400</span><span>请求错误</span></div>
                        <div class="flex justify-between"><span>401</span><span>未授权</span></div>
                        <div class="flex justify-between"><span>403</span><span>禁止访问</span></div>
                        <div class="flex justify-between"><span>404</span><span>未找到</span></div>
                        <div class="flex justify-between"><span>500</span><span>服务器错误</span></div>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">业务错误码</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between"><span>1001</span><span>用户不存在</span></div>
                        <div class="flex justify-between"><span>1002</span><span>密码错误</span></div>
                        <div class="flex justify-between"><span>1003</span><span>令牌过期</span></div>
                        <div class="flex justify-between"><span>2001</span><span>AI处理失败</span></div>
                        <div class="flex justify-between"><span>3001</span><span>备份创建失败</span></div>
                        <div class="flex justify-between"><span>4001</span><span>系统资源不足</span></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- SDK and Examples -->
        <section class="bg-white rounded-xl p-8 shadow-sm border">
            <h2 class="text-2xl font-bold mb-6">🛠️ SDK和示例</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">Python示例</h3>
                    <div class="code-block p-4 rounded-lg text-sm">
                        <pre><code>import requests

# 登录获取令牌
response = requests.post('http://localhost:8000/api/auth/login', {
    'username': 'admin',
    'password': 'admin123'
})
token = response.json()['access_token']

# 使用AI处理
headers = {'Authorization': f'Bearer {token}'}
response = requests.post(
    'http://localhost:8000/api/ai/process',
    json={'text': '购买办公用品1000円'},
    headers=headers
)
print(response.json())</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">JavaScript示例</h3>
                    <div class="code-block p-4 rounded-lg text-sm">
                        <pre><code>// 登录获取令牌
const loginResponse = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
    })
});
const {access_token} = await loginResponse.json();

// 使用AI处理
const aiResponse = await fetch('/api/ai/process', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${access_token}`
    },
    body: JSON.stringify({text: '购买办公用品1000円'})
});
console.log(await aiResponse.json());</code></pre>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加点击复制功能
            document.querySelectorAll('.code-block').forEach(block => {
                block.style.cursor = 'pointer';
                block.title = '点击复制代码';
                
                block.addEventListener('click', function() {
                    const text = this.textContent;
                    navigator.clipboard.writeText(text).then(() => {
                        // 显示复制成功提示
                        const originalBg = this.style.backgroundColor;
                        this.style.backgroundColor = '#10b981';
                        setTimeout(() => {
                            this.style.backgroundColor = originalBg;
                        }, 200);
                    });
                });
            });

            // 添加端点卡片动画
            const cards = document.querySelectorAll('.endpoint-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>

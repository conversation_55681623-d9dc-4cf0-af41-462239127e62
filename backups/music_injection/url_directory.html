<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoldenLedger URL目录</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-4xl font-bold mb-8 text-center text-blue-600">
            📂 GoldenLedger URL目录
        </h1>
        <p class="text-center text-gray-600 mb-8">
            所有可访问的页面和API端点 - https://ledger.goldenorangetech.com/
        </p>
        
        <!-- 主要功能页面 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-green-600">🎯 主要功能页面</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🏠 主仪表板</h3>
                    <a href="https://ledger.goldenorangetech.com/master_dashboard.html" 
                       class="text-blue-600 hover:underline block mb-2">master_dashboard.html</a>
                    <p class="text-sm text-gray-600">用户个人仪表板，显示统计数据和最近活动</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🤖 AI记账演示</h3>
                    <a href="https://ledger.goldenorangetech.com/interactive_demo.html" 
                       class="text-blue-600 hover:underline block mb-2">interactive_demo.html</a>
                    <p class="text-sm text-gray-600">AI自然语言记账和OCR发票识别功能</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🔐 用户登录</h3>
                    <a href="https://ledger.goldenorangetech.com/login_simple.html" 
                       class="text-blue-600 hover:underline block mb-2">login_simple.html</a>
                    <p class="text-sm text-gray-600">用户登录页面，支持Google OAuth</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📝 用户注册</h3>
                    <a href="https://ledger.goldenorangetech.com/register.html" 
                       class="text-blue-600 hover:underline block mb-2">register.html</a>
                    <p class="text-sm text-gray-600">新用户注册页面</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📊 会计分录</h3>
                    <a href="https://ledger.goldenorangetech.com/journal_entries.html" 
                       class="text-blue-600 hover:underline block mb-2">journal_entries.html</a>
                    <p class="text-sm text-gray-600">查看和管理会计分录</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📈 财务报表</h3>
                    <a href="https://ledger.goldenorangetech.com/financial_reports.html" 
                       class="text-blue-600 hover:underline block mb-2">financial_reports.html</a>
                    <p class="text-sm text-gray-600">生成和查看财务报表</p>
                </div>
            </div>
        </section>

        <!-- 管理和工具页面 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-purple-600">🛠️ 管理和工具页面</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">👥 用户管理</h3>
                    <a href="https://ledger.goldenorangetech.com/user_management.html" 
                       class="text-blue-600 hover:underline block mb-2">user_management.html</a>
                    <p class="text-sm text-gray-600">管理用户账户和权限</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">⚙️ 用户设置</h3>
                    <a href="https://ledger.goldenorangetech.com/user_settings.html" 
                       class="text-blue-600 hover:underline block mb-2">user_settings.html</a>
                    <p class="text-sm text-gray-600">个人设置和偏好配置</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">💾 备份管理</h3>
                    <a href="https://ledger.goldenorangetech.com/backup_management.html" 
                       class="text-blue-600 hover:underline block mb-2">backup_management.html</a>
                    <p class="text-sm text-gray-600">数据备份和恢复管理</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📤 数据导出</h3>
                    <a href="https://ledger.goldenorangetech.com/data_export.html" 
                       class="text-blue-600 hover:underline block mb-2">data_export.html</a>
                    <p class="text-sm text-gray-600">导出数据到各种格式</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📥 数据导入</h3>
                    <a href="https://ledger.goldenorangetech.com/data_import_tool.html" 
                       class="text-blue-600 hover:underline block mb-2">data_import_tool.html</a>
                    <p class="text-sm text-gray-600">从其他系统导入数据</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🏢 固定资产</h3>
                    <a href="https://ledger.goldenorangetech.com/fixed_assets.html" 
                       class="text-blue-600 hover:underline block mb-2">fixed_assets.html</a>
                    <p class="text-sm text-gray-600">固定资产管理</p>
                </div>
            </div>
        </section>

        <!-- 测试和调试页面 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-orange-600">🧪 测试和调试页面</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🧪 多用户测试</h3>
                    <a href="https://ledger.goldenorangetech.com/test_multiuser.html" 
                       class="text-blue-600 hover:underline block mb-2">test_multiuser.html</a>
                    <p class="text-sm text-gray-600">测试多用户系统功能</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🤖 Gemini API测试</h3>
                    <a href="https://ledger.goldenorangetech.com/test_gemini_api.html" 
                       class="text-blue-600 hover:underline block mb-2">test_gemini_api.html</a>
                    <p class="text-sm text-gray-600">测试Gemini AI API功能</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📋 项目状态检查</h3>
                    <a href="https://ledger.goldenorangetech.com/project_status_check.html" 
                       class="text-blue-600 hover:underline block mb-2">project_status_check.html</a>
                    <p class="text-sm text-gray-600">检查项目开发状态</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">💰 费用分析</h3>
                    <a href="https://ledger.goldenorangetech.com/cost_analysis_jpy.html" 
                       class="text-blue-600 hover:underline block mb-2">cost_analysis_jpy.html</a>
                    <p class="text-sm text-gray-600">系统费用分析工具（日元版）</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🔧 OCR测试修复</h3>
                    <a href="https://ledger.goldenorangetech.com/test_ocr_fix.html" 
                       class="text-blue-600 hover:underline block mb-2">test_ocr_fix.html</a>
                    <p class="text-sm text-gray-600">OCR功能测试和修复工具</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📊 系统监控</h3>
                    <a href="https://ledger.goldenorangetech.com/system_monitor.html" 
                       class="text-blue-600 hover:underline block mb-2">system_monitor.html</a>
                    <p class="text-sm text-gray-600">系统性能监控</p>
                </div>
            </div>
        </section>

        <!-- 支付和订阅页面 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-indigo-600">💳 支付和订阅页面</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">💳 支付页面</h3>
                    <a href="https://ledger.goldenorangetech.com/payment.html" 
                       class="text-blue-600 hover:underline block mb-2">payment.html</a>
                    <p class="text-sm text-gray-600">PayPal支付处理页面</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">✅ 支付成功</h3>
                    <a href="https://ledger.goldenorangetech.com/payment-success.html" 
                       class="text-blue-600 hover:underline block mb-2">payment-success.html</a>
                    <p class="text-sm text-gray-600">支付成功确认页面</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">❌ 支付失败</h3>
                    <a href="https://ledger.goldenorangetech.com/payment-failed.html" 
                       class="text-blue-600 hover:underline block mb-2">payment-failed.html</a>
                    <p class="text-sm text-gray-600">支付失败处理页面</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">💰 定价页面</h3>
                    <a href="https://ledger.goldenorangetech.com/pricing.html" 
                       class="text-blue-600 hover:underline block mb-2">pricing.html</a>
                    <p class="text-sm text-gray-600">服务定价和套餐选择</p>
                </div>
            </div>
        </section>

        <!-- 法律和信息页面 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-gray-600">📄 法律和信息页面</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📋 服务条款</h3>
                    <a href="https://ledger.goldenorangetech.com/terms_of_service.html" 
                       class="text-blue-600 hover:underline block mb-2">terms_of_service.html</a>
                    <p class="text-sm text-gray-600">服务使用条款</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">🔒 隐私政策</h3>
                    <a href="https://ledger.goldenorangetech.com/privacy_policy.html" 
                       class="text-blue-600 hover:underline block mb-2">privacy_policy.html</a>
                    <p class="text-sm text-gray-600">隐私保护政策</p>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow border">
                    <h3 class="font-semibold text-lg mb-2">📚 API文档</h3>
                    <a href="https://ledger.goldenorangetech.com/api_documentation.html" 
                       class="text-blue-600 hover:underline block mb-2">api_documentation.html</a>
                    <p class="text-sm text-gray-600">API接口文档</p>
                </div>
            </div>
        </section>

        <!-- API端点 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-red-600">🔌 API端点</h2>

            <!-- 用户认证API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-blue-600">👤 用户认证API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/auth/register</code>
                            <p class="text-xs text-gray-600 mt-1">用户注册</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/auth/login</code>
                            <p class="text-xs text-gray-600 mt-1">用户登录</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/auth/logout</code>
                            <p class="text-xs text-gray-600 mt-1">用户登出</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/auth/verify</code>
                            <p class="text-xs text-gray-600 mt-1">验证会话</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/auth/profile</code>
                            <p class="text-xs text-gray-600 mt-1">获取用户资料</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/auth/auto-register</code>
                            <p class="text-xs text-gray-600 mt-1">自动注册（支付后）</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Google OAuth API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-green-600">🔐 Google OAuth API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/auth/google/login</code>
                            <p class="text-xs text-gray-600 mt-1">Google登录重定向</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/auth/google/callback</code>
                            <p class="text-xs text-gray-600 mt-1">Google OAuth回调</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI功能API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-purple-600">🤖 AI功能API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/ai/process-text</code>
                            <p class="text-xs text-gray-600 mt-1">AI自然语言记账</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/ai/stats</code>
                            <p class="text-xs text-gray-600 mt-1">AI使用统计</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/ai/mappings</code>
                            <p class="text-xs text-gray-600 mt-1">科目映射</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /ai-bookkeeping/invoice-ocr</code>
                            <p class="text-xs text-gray-600 mt-1">OCR发票识别</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户数据API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-indigo-600">📊 用户数据API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/user/dashboard</code>
                            <p class="text-xs text-gray-600 mt-1">用户仪表板数据</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/user/journal-entries</code>
                            <p class="text-xs text-gray-600 mt-1">用户会计分录</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/user/subscription-status</code>
                            <p class="text-xs text-gray-600 mt-1">用户订阅状态</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会计数据API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-orange-600">📝 会计数据API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/journal-entries</code>
                            <p class="text-xs text-gray-600 mt-1">获取会计分录</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/journal-entries</code>
                            <p class="text-xs text-gray-600 mt-1">保存会计分录</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/companies</code>
                            <p class="text-xs text-gray-600 mt-1">获取公司信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付和订阅API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-pink-600">💳 支付和订阅API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/paypal/verify-payment</code>
                            <p class="text-xs text-gray-600 mt-1">PayPal支付验证</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">* /api/payment/*</code>
                            <p class="text-xs text-gray-600 mt-1">支付相关API</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">* /api/subscription/*</code>
                            <p class="text-xs text-gray-600 mt-1">订阅相关API</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统API -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-600">⚙️ 系统API</h3>
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">GET /api/health</code>
                            <p class="text-xs text-gray-600 mt-1">系统健康检查</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded">
                            <code class="text-sm">POST /api/auth/migrate</code>
                            <p class="text-xs text-gray-600 mt-1">数据库迁移</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用说明 -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-teal-600">📖 使用说明</h2>
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="space-y-4">
                    <div>
                        <h4 class="font-semibold text-green-600">✅ 推荐访问流程：</h4>
                        <ol class="list-decimal list-inside text-sm text-gray-700 mt-2 space-y-1">
                            <li>首先访问 <code>login_simple.html</code> 登录或注册</li>
                            <li>登录后访问 <code>master_dashboard.html</code> 查看个人仪表板</li>
                            <li>使用 <code>interactive_demo.html</code> 体验AI记账功能</li>
                            <li>通过 <code>journal_entries.html</code> 管理会计分录</li>
                            <li>使用 <code>financial_reports.html</code> 生成财务报表</li>
                        </ol>
                    </div>

                    <div>
                        <h4 class="font-semibold text-blue-600">🔧 开发者工具：</h4>
                        <ul class="list-disc list-inside text-sm text-gray-700 mt-2 space-y-1">
                            <li><code>test_multiuser.html</code> - 测试多用户功能</li>
                            <li><code>project_status_check.html</code> - 检查项目状态</li>
                            <li><code>cost_analysis_jpy.html</code> - 分析系统费用</li>
                            <li><code>api_documentation.html</code> - API文档</li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="font-semibold text-orange-600">⚠️ 注意事项：</h4>
                        <ul class="list-disc list-inside text-sm text-gray-700 mt-2 space-y-1">
                            <li>所有用户数据API需要有效的认证令牌</li>
                            <li>多用户系统已启用，每个用户只能看到自己的数据</li>
                            <li>支付功能集成了PayPal，支持订阅服务</li>
                            <li>AI功能使用Google Gemini API，可能产生费用</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="text-center text-gray-500 text-sm">
            <p>GoldenLedger - AI驱动的日本会计系统</p>
            <p class="mt-2">最后更新: <span id="last-updated"></span></p>
        </footer>
    </div>

    <script>
        // 显示最后更新时间
        document.getElementById('last-updated').textContent = new Date().toLocaleString('zh-CN');

        // 添加点击统计
        document.querySelectorAll('a[href^="https://ledger.goldenorangetech.com/"]').forEach(link => {
            link.addEventListener('click', function() {
                console.log('访问:', this.href);
            });
        });
    </script>
</body>
</html>

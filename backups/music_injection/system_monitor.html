<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>goldenledger会計AI - 系统监控中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .status-warning { color: #f59e0b; }
        .blink { animation: blink 1s infinite; }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.3; } }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">🖥️ システム監視センター</h1>
                    <p class="text-lg opacity-90">リアルタイムシステム状態監視</p>
                </div>
                <div class="text-right">
                    <div id="system-time" class="text-lg font-mono"></div>
                    <div class="text-sm opacity-75">システム稼働時間: <span id="uptime">--</span></div>
                </div>
            </div>
        </div>
    </header>

    <!-- System Status Overview -->
    <main class="container mx-auto px-4 py-8">
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Backend Status -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Backend API</p>
                        <p id="backend-status" class="text-lg font-bold status-offline">Offline</p>
                        <p id="backend-response" class="text-xs text-gray-500">--ms</p>
                    </div>
                    <div id="backend-indicator" class="w-4 h-4 rounded-full bg-red-500 blink"></div>
                </div>
            </div>

            <!-- Frontend Status -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Frontend App</p>
                        <p id="frontend-status" class="text-lg font-bold status-offline">Offline</p>
                        <p id="frontend-response" class="text-xs text-gray-500">--ms</p>
                    </div>
                    <div id="frontend-indicator" class="w-4 h-4 rounded-full bg-red-500 blink"></div>
                </div>
            </div>

            <!-- Database Status -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Database</p>
                        <p id="database-status" class="text-lg font-bold status-online">Online</p>
                        <p id="database-size" class="text-xs text-gray-500">--KB</p>
                    </div>
                    <div id="database-indicator" class="w-4 h-4 rounded-full bg-green-500"></div>
                </div>
            </div>

            <!-- AI Processing -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">AI Processing</p>
                        <p id="ai-status" class="text-lg font-bold status-online">Ready</p>
                        <p id="ai-queue" class="text-xs text-gray-500">Queue: 0</p>
                    </div>
                    <div id="ai-indicator" class="w-4 h-4 rounded-full bg-green-500"></div>
                </div>
            </div>
        </section>

        <!-- Service Management -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Service Controls -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">🎛️ サービス制御</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">Backend API Server</div>
                            <div class="text-sm text-gray-500">Port: 8000</div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                                Start
                            </button>
                            <button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                                Stop
                            </button>
                            <button class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                                Restart
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">Frontend Dev Server</div>
                            <div class="text-sm text-gray-500">Port: 3000</div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                                Start
                            </button>
                            <button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                                Stop
                            </button>
                            <button class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                                Restart
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">Database Service</div>
                            <div class="text-sm text-gray-500">SQLite</div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                                Backup
                            </button>
                            <button class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                                Optimize
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Resources -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📊 システムリソース</h3>
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>CPU使用率</span>
                            <span id="cpu-usage">--%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="cpu-bar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>メモリ使用率</span>
                            <span id="memory-usage">--%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="memory-bar" class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>ディスク使用率</span>
                            <span id="disk-usage">--%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="disk-bar" class="bg-yellow-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>ネットワーク</span>
                            <span id="network-usage">-- KB/s</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="network-bar" class="bg-purple-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Real-time Logs -->
        <section class="bg-white rounded-xl p-6 shadow-sm border mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">📝 リアルタイムログ</h3>
                <div class="flex space-x-2">
                    <button id="clear-logs-btn" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                        Clear
                    </button>
                    <button id="export-logs-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                        Export
                    </button>
                </div>
            </div>
            <div id="system-logs" class="bg-gray-900 text-green-400 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
                <div class="text-gray-500">[System Monitor] Starting...</div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-3xl">🔄</span>
                </div>
                <h3 class="font-semibold mb-2">システム再起動</h3>
                <p class="text-gray-600 text-sm mb-4">全サービスを再起動</p>
                <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 w-full">
                    Restart All
                </button>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-3xl">💾</span>
                </div>
                <h3 class="font-semibold mb-2">データバックアップ</h3>
                <p class="text-gray-600 text-sm mb-4">データベースをバックアップ</p>
                <button class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 w-full">
                    Backup Now
                </button>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span class="text-3xl">🧹</span>
                </div>
                <h3 class="font-semibold mb-2">システムクリーンアップ</h3>
                <p class="text-gray-600 text-sm mb-4">一時ファイルを削除</p>
                <button class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 w-full">
                    Clean Up
                </button>
            </div>
        </section>
    </main>

    <script>
        // 全局变量
        let systemLogs = [];
        let startTime = Date.now();

        // 添加系统日志
        function addSystemLog(level, message) {
            const timestamp = new Date().toLocaleTimeString('ja-JP');
            const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
            systemLogs.unshift(logEntry);
            
            if (systemLogs.length > 100) systemLogs.pop();
            
            const logsContainer = document.getElementById('system-logs');
            logsContainer.innerHTML = systemLogs.map(log => {
                let className = 'text-green-400';
                if (log.includes('[ERROR]')) className = 'text-red-400';
                else if (log.includes('[WARN]')) className = 'text-yellow-400';
                else if (log.includes('[INFO]')) className = 'text-blue-400';
                
                return `<div class="${className}">${log}</div>`;
            }).join('');
        }

        // 检查服务状态
        async function checkServiceStatus() {
            // 检查后端
            try {
                const startTime = Date.now();
                const response = await fetch('http://localhost:8000/health');
                const responseTime = Date.now() - startTime;
                
                if (response.ok) {
                    document.getElementById('backend-status').textContent = 'Online';
                    document.getElementById('backend-status').className = 'text-lg font-bold status-online';
                    document.getElementById('backend-response').textContent = `${responseTime}ms`;
                    document.getElementById('backend-indicator').className = 'w-4 h-4 rounded-full bg-green-500';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = 'Offline';
                document.getElementById('backend-status').className = 'text-lg font-bold status-offline';
                document.getElementById('backend-response').textContent = 'Timeout';
                document.getElementById('backend-indicator').className = 'w-4 h-4 rounded-full bg-red-500 blink';
            }

            // 检查前端
            try {
                const startTime = Date.now();
                const response = await fetch('http://localhost:3000');
                const responseTime = Date.now() - startTime;
                
                if (response.ok) {
                    document.getElementById('frontend-status').textContent = 'Online';
                    document.getElementById('frontend-status').className = 'text-lg font-bold status-online';
                    document.getElementById('frontend-response').textContent = `${responseTime}ms`;
                    document.getElementById('frontend-indicator').className = 'w-4 h-4 rounded-full bg-green-500';
                } else {
                    throw new Error('Frontend not responding');
                }
            } catch (error) {
                document.getElementById('frontend-status').textContent = 'Offline';
                document.getElementById('frontend-status').className = 'text-lg font-bold status-offline';
                document.getElementById('frontend-response').textContent = 'Timeout';
                document.getElementById('frontend-indicator').className = 'w-4 h-4 rounded-full bg-red-500 blink';
            }
        }

        // 模拟系统资源监控
        function updateSystemResources() {
            const cpu = Math.random() * 100;
            const memory = Math.random() * 100;
            const disk = Math.random() * 100;
            const network = Math.random() * 1000;

            document.getElementById('cpu-usage').textContent = `${cpu.toFixed(1)}%`;
            document.getElementById('cpu-bar').style.width = `${cpu}%`;

            document.getElementById('memory-usage').textContent = `${memory.toFixed(1)}%`;
            document.getElementById('memory-bar').style.width = `${memory}%`;

            document.getElementById('disk-usage').textContent = `${disk.toFixed(1)}%`;
            document.getElementById('disk-bar').style.width = `${disk}%`;

            document.getElementById('network-usage').textContent = `${network.toFixed(0)} KB/s`;
            document.getElementById('network-bar').style.width = `${Math.min(network / 10, 100)}%`;

            // 添加警告日志
            if (cpu > 80) addSystemLog('warn', `High CPU usage: ${cpu.toFixed(1)}%`);
            if (memory > 85) addSystemLog('warn', `High memory usage: ${memory.toFixed(1)}%`);
            if (disk > 90) addSystemLog('error', `Disk space critical: ${disk.toFixed(1)}%`);
        }

        // 更新系统时间和运行时间
        function updateSystemTime() {
            const now = new Date();
            document.getElementById('system-time').textContent = now.toLocaleTimeString('ja-JP');
            
            const uptime = Math.floor((Date.now() - startTime) / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            document.getElementById('uptime').textContent = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addSystemLog('info', 'System Monitor initialized');
            
            // 初始检查
            checkServiceStatus();
            updateSystemResources();
            
            // 定期更新
            setInterval(checkServiceStatus, 5000);
            setInterval(updateSystemResources, 2000);
            setInterval(updateSystemTime, 1000);
            
            // 清除日志按钮
            document.getElementById('clear-logs-btn').addEventListener('click', function() {
                systemLogs = [];
                document.getElementById('system-logs').innerHTML = '<div class="text-gray-500">[System Monitor] Logs cleared</div>';
                addSystemLog('info', 'System logs cleared');
            });
            
            // 导出日志按钮
            document.getElementById('export-logs-btn').addEventListener('click', function() {
                const logData = systemLogs.join('\n');
                const blob = new Blob([logData], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`;
                a.click();
                URL.revokeObjectURL(url);
                addSystemLog('info', 'System logs exported');
            });
            
            addSystemLog('info', 'All monitoring services started');
        });
    </script>
</body>
</html>

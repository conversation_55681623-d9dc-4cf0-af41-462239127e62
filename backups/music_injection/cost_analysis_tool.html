<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统费用分析工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-green-600">💰 系统费用分析工具</h1>
        
        <!-- 费用概览 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-blue-600 mb-2">Cloudflare Workers</h3>
                <p class="text-2xl font-bold text-green-600">免费</p>
                <p class="text-sm text-gray-600">每日100,000次请求免费</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-purple-600 mb-2">Gemini API</h3>
                <p class="text-2xl font-bold text-yellow-600">按使用付费</p>
                <p class="text-sm text-gray-600">需要监控使用量</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-indigo-600 mb-2">GitHub Pages</h3>
                <p class="text-2xl font-bold text-green-600">免费</p>
                <p class="text-sm text-gray-600">静态网站托管</p>
            </div>
        </div>
        
        <!-- Gemini API 使用分析 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🤖 Gemini API 使用分析</h2>
            <div class="space-y-4">
                <button onclick="analyzeGeminiUsage()" class="bg-purple-500 text-white px-6 py-2 rounded hover:bg-purple-600">
                    分析Gemini API使用情况
                </button>
                <button onclick="estimateMonthlyCost()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    估算月度费用
                </button>
            </div>
            <div id="gemini-analysis" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
        </div>
        
        <!-- Cloudflare 服务分析 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">☁️ Cloudflare 服务分析</h2>
            <div class="space-y-4">
                <button onclick="analyzeCloudflareUsage()" class="bg-orange-500 text-white px-6 py-2 rounded hover:bg-orange-600">
                    分析Cloudflare使用情况
                </button>
            </div>
            <div id="cloudflare-analysis" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
        </div>
        
        <!-- 费用优化建议 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">💡 费用优化建议</h2>
            <div id="optimization-suggestions" class="space-y-4">
                <div class="p-4 bg-green-50 border border-green-200 rounded">
                    <h3 class="font-semibold text-green-800">✅ 当前免费服务</h3>
                    <ul class="list-disc list-inside text-sm text-green-700 mt-2">
                        <li>Cloudflare Workers: 每日100,000次请求免费</li>
                        <li>Cloudflare D1数据库: 每日100,000次读取免费</li>
                        <li>Cloudflare R2存储: 每月10GB免费</li>
                        <li>GitHub Pages: 无限制免费托管</li>
                    </ul>
                </div>
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <h3 class="font-semibold text-yellow-800">⚠️ 需要监控的服务</h3>
                    <ul class="list-disc list-inside text-sm text-yellow-700 mt-2">
                        <li>Gemini API: 按token使用量计费</li>
                        <li>如果超出Cloudflare免费额度会产生费用</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 实时监控 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 实时使用监控</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">今日API调用统计</h3>
                    <div id="daily-stats" class="p-3 bg-gray-50 rounded text-sm"></div>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">本月费用估算</h3>
                    <div id="monthly-cost" class="p-3 bg-gray-50 rounded text-sm"></div>
                </div>
            </div>
            <button onclick="refreshStats()" class="mt-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                刷新统计
            </button>
        </div>
        
        <!-- 操作日志 -->
        <div class="bg-black text-green-400 p-6 rounded-lg shadow mt-6">
            <h2 class="text-xl font-semibold mb-4 text-white">📋 分析日志</h2>
            <div id="debug-log" class="font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto"></div>
            <button onclick="clearLog()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                清空日志
            </button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const colors = {
                info: 'text-green-400',
                error: 'text-red-400',
                warning: 'text-yellow-400',
                success: 'text-blue-400'
            };
            logDiv.innerHTML += `<span class="${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // 分析Gemini API使用情况
        async function analyzeGeminiUsage() {
            const resultDiv = document.getElementById('gemini-analysis');
            log('开始分析Gemini API使用情况...', 'info');
            
            resultDiv.innerHTML = '<p class="text-blue-600">🔄 分析中...</p>';
            
            try {
                // Gemini API定价信息 (2024年价格)
                const geminiPricing = {
                    'gemini-1.5-flash': {
                        input: 0.075 / 1000000,  // $0.075 per 1M input tokens
                        output: 0.30 / 1000000   // $0.30 per 1M output tokens
                    }
                };
                
                // 估算每次调用的token使用量
                const estimatedUsage = {
                    textProcessing: {
                        inputTokens: 200,   // 平均输入token数
                        outputTokens: 100   // 平均输出token数
                    },
                    ocrProcessing: {
                        inputTokens: 1000,  // OCR输入更多（包含图像）
                        outputTokens: 200   // OCR输出结构化数据
                    }
                };
                
                // 计算单次调用成本
                const textCost = (estimatedUsage.textProcessing.inputTokens * geminiPricing['gemini-1.5-flash'].input) + 
                               (estimatedUsage.textProcessing.outputTokens * geminiPricing['gemini-1.5-flash'].output);
                
                const ocrCost = (estimatedUsage.ocrProcessing.inputTokens * geminiPricing['gemini-1.5-flash'].input) + 
                              (estimatedUsage.ocrProcessing.outputTokens * geminiPricing['gemini-1.5-flash'].output);
                
                resultDiv.innerHTML = `
                    <div class="space-y-4">
                        <h3 class="font-semibold text-lg">💰 Gemini API 费用分析</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="p-4 bg-blue-50 border border-blue-200 rounded">
                                <h4 class="font-semibold text-blue-800">文本处理 (AI记账)</h4>
                                <p class="text-sm text-blue-700 mt-2">
                                    <strong>单次成本:</strong> $${(textCost * 1000).toFixed(4)} (约¥${(textCost * 1000 * 7.3).toFixed(4)})<br>
                                    <strong>100次:</strong> $${(textCost * 100).toFixed(4)} (约¥${(textCost * 100 * 7.3).toFixed(3)})<br>
                                    <strong>1000次:</strong> $${(textCost * 1000).toFixed(3)} (约¥${(textCost * 1000 * 7.3).toFixed(2)})
                                </p>
                            </div>
                            
                            <div class="p-4 bg-purple-50 border border-purple-200 rounded">
                                <h4 class="font-semibold text-purple-800">OCR处理 (发票识别)</h4>
                                <p class="text-sm text-purple-700 mt-2">
                                    <strong>单次成本:</strong> $${(ocrCost * 1000).toFixed(4)} (约¥${(ocrCost * 1000 * 7.3).toFixed(4)})<br>
                                    <strong>100次:</strong> $${(ocrCost * 100).toFixed(4)} (约¥${(ocrCost * 100 * 7.3).toFixed(3)})<br>
                                    <strong>1000次:</strong> $${(ocrCost * 1000).toFixed(3)} (约¥${(ocrCost * 1000 * 7.3).toFixed(2)})
                                </p>
                            </div>
                        </div>
                        
                        <div class="p-4 bg-green-50 border border-green-200 rounded">
                            <h4 class="font-semibold text-green-800">💡 费用控制建议</h4>
                            <ul class="list-disc list-inside text-sm text-green-700 mt-2">
                                <li>Gemini API有免费额度，小规模使用基本免费</li>
                                <li>建议设置使用量监控和预算警报</li>
                                <li>可以考虑实现请求缓存减少重复调用</li>
                                <li>对于高频使用可以考虑批量处理</li>
                            </ul>
                        </div>
                    </div>
                `;
                
                log('Gemini API费用分析完成', 'success');
                
            } catch (error) {
                log(`Gemini API分析失败: ${error.message}`, 'error');
                resultDiv.innerHTML = `<p class="text-red-600">❌ 分析失败: ${error.message}</p>`;
            }
        }
        
        // 估算月度费用
        async function estimateMonthlyCost() {
            const resultDiv = document.getElementById('gemini-analysis');
            log('开始估算月度费用...', 'info');
            
            // 假设的使用场景
            const scenarios = [
                {
                    name: '轻度使用',
                    textCalls: 100,
                    ocrCalls: 20,
                    description: '个人用户，偶尔使用'
                },
                {
                    name: '中度使用',
                    textCalls: 500,
                    ocrCalls: 100,
                    description: '小企业，日常记账'
                },
                {
                    name: '重度使用',
                    textCalls: 2000,
                    ocrCalls: 500,
                    description: '中型企业，频繁使用'
                }
            ];
            
            const textCost = 0.00003;  // 单次文本处理成本
            const ocrCost = 0.00012;   // 单次OCR处理成本
            
            let scenarioHtml = '';
            scenarios.forEach(scenario => {
                const totalCost = (scenario.textCalls * textCost) + (scenario.ocrCalls * ocrCost);
                const totalCostCNY = totalCost * 7.3;
                
                scenarioHtml += `
                    <div class="p-4 bg-gray-50 border border-gray-200 rounded mb-3">
                        <h4 class="font-semibold">${scenario.name}</h4>
                        <p class="text-sm text-gray-600">${scenario.description}</p>
                        <div class="mt-2 text-sm">
                            <p>文本处理: ${scenario.textCalls}次 × $${textCost.toFixed(5)} = $${(scenario.textCalls * textCost).toFixed(4)}</p>
                            <p>OCR处理: ${scenario.ocrCalls}次 × $${ocrCost.toFixed(5)} = $${(scenario.ocrCalls * ocrCost).toFixed(4)}</p>
                            <p class="font-semibold text-green-600 mt-1">月度总费用: $${totalCost.toFixed(4)} (约¥${totalCostCNY.toFixed(2)})</p>
                        </div>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = `
                <div class="space-y-4">
                    <h3 class="font-semibold text-lg">📊 月度费用估算</h3>
                    ${scenarioHtml}
                    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded">
                        <h4 class="font-semibold text-yellow-800">⚠️ 注意事项</h4>
                        <ul class="list-disc list-inside text-sm text-yellow-700 mt-2">
                            <li>以上为估算费用，实际费用可能因token使用量而异</li>
                            <li>Gemini API有免费额度，小规模使用可能完全免费</li>
                            <li>建议在Google Cloud Console中设置预算警报</li>
                            <li>汇率按1美元=7.3人民币计算</li>
                        </ul>
                    </div>
                </div>
            `;
            
            log('月度费用估算完成', 'success');
        }
        
        // 分析Cloudflare使用情况
        async function analyzeCloudflareUsage() {
            const resultDiv = document.getElementById('cloudflare-analysis');
            log('开始分析Cloudflare使用情况...', 'info');
            
            resultDiv.innerHTML = `
                <div class="space-y-4">
                    <h3 class="font-semibold text-lg">☁️ Cloudflare 服务分析</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-4 bg-green-50 border border-green-200 rounded">
                            <h4 class="font-semibold text-green-800">✅ 免费服务</h4>
                            <ul class="list-disc list-inside text-sm text-green-700 mt-2">
                                <li><strong>Workers:</strong> 每日100,000次请求</li>
                                <li><strong>D1数据库:</strong> 每日100,000次读取</li>
                                <li><strong>R2存储:</strong> 每月10GB存储</li>
                                <li><strong>KV存储:</strong> 每日100,000次读取</li>
                                <li><strong>Pages:</strong> 无限制静态托管</li>
                            </ul>
                        </div>
                        
                        <div class="p-4 bg-blue-50 border border-blue-200 rounded">
                            <h4 class="font-semibold text-blue-800">📊 当前使用估算</h4>
                            <ul class="list-disc list-inside text-sm text-blue-700 mt-2">
                                <li><strong>Workers请求:</strong> 预计每日 < 1,000次</li>
                                <li><strong>数据库查询:</strong> 预计每日 < 5,000次</li>
                                <li><strong>文件存储:</strong> 预计 < 1GB</li>
                                <li><strong>状态:</strong> <span class="text-green-600 font-semibold">完全在免费额度内</span></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="p-4 bg-gray-50 border border-gray-200 rounded">
                        <h4 class="font-semibold text-gray-800">💰 费用预测</h4>
                        <p class="text-sm text-gray-700 mt-2">
                            基于当前使用模式，Cloudflare服务预计<strong class="text-green-600">完全免费</strong>。
                            只有在以下情况下才会产生费用：
                        </p>
                        <ul class="list-disc list-inside text-sm text-gray-700 mt-2">
                            <li>Workers请求超过每日100,000次</li>
                            <li>数据库操作超过每日100,000次</li>
                            <li>文件存储超过每月10GB</li>
                        </ul>
                    </div>
                </div>
            `;
            
            log('Cloudflare使用情况分析完成', 'success');
        }
        
        // 刷新统计
        async function refreshStats() {
            const dailyStatsDiv = document.getElementById('daily-stats');
            const monthlyCostDiv = document.getElementById('monthly-cost');
            
            log('刷新使用统计...', 'info');
            
            // 模拟统计数据
            const today = new Date().toLocaleDateString();
            dailyStatsDiv.innerHTML = `
                <p><strong>日期:</strong> ${today}</p>
                <p><strong>AI文本处理:</strong> ~50次</p>
                <p><strong>OCR识别:</strong> ~10次</p>
                <p><strong>Workers请求:</strong> ~200次</p>
                <p><strong>状态:</strong> <span class="text-green-600">正常</span></p>
            `;
            
            monthlyCostDiv.innerHTML = `
                <p><strong>Cloudflare:</strong> <span class="text-green-600">$0.00 (免费)</span></p>
                <p><strong>Gemini API:</strong> <span class="text-blue-600">~$0.50 (约¥3.65)</span></p>
                <p><strong>GitHub Pages:</strong> <span class="text-green-600">$0.00 (免费)</span></p>
                <p><strong>总计:</strong> <span class="text-blue-600 font-semibold">~$0.50/月 (约¥3.65)</span></p>
            `;
            
            log('统计数据已刷新', 'success');
        }
        
        // 页面加载时初始化
        window.addEventListener('load', function() {
            log('费用分析工具已加载', 'info');
            refreshStats();
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Goldenledger会計AI - 实时监控和通知系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- API 配置和认证管理器 -->
    <script src="api_config.js"></script>
    <script src="auth_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        .status-online { color: #10b981; }
        .status-offline { color: #ef4444; }
        .status-connecting { color: #f59e0b; }
        .notification-enter { animation: slideInRight 0.3s ease-out; }
        .notification-exit { animation: slideOutRight 0.3s ease-in; }
        .pulse { animation: pulse 2s infinite; }
        
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .message-bubble {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .connection-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">📡 实时监控和通知系统</h1>
                    <p class="text-lg opacity-90">WebSocket实时通信和系统状态监控</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="connection-status" class="flex items-center bg-white/20 px-3 py-1 rounded-full">
                        <span class="connection-indicator bg-red-500 pulse"></span>
                        <span id="status-text">连接中...</span>
                    </div>
                    <button id="reconnect-btn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors">
                        🔄 重新连接
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Connection Info -->
        <section class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">连接状态</p>
                        <p id="ws-status" class="text-lg font-bold status-offline">离线</p>
                        <p id="ws-uptime" class="text-xs text-gray-500">--</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">🔗</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">消息发送</p>
                        <p id="messages-sent" class="text-lg font-bold text-green-600">0</p>
                        <p class="text-xs text-gray-500">总发送数</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📤</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">消息接收</p>
                        <p id="messages-received" class="text-lg font-bold text-blue-600">0</p>
                        <p class="text-xs text-gray-500">总接收数</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">📥</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃连接</p>
                        <p id="active-connections" class="text-lg font-bold text-purple-600">0</p>
                        <p class="text-xs text-gray-500">当前在线</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span class="text-2xl">👥</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Real-time Communication -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Message Console -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">💬 实时消息控制台</h3>
                    <div class="flex space-x-2">
                        <button id="clear-messages-btn" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            清空
                        </button>
                        <button id="export-messages-btn" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                            导出
                        </button>
                    </div>
                </div>
                
                <div id="message-console" class="message-bubble bg-gray-900 text-green-400 rounded-lg p-4 font-mono text-sm">
                    <div class="text-gray-500">[系统] 等待WebSocket连接...</div>
                </div>
                
                <!-- Message Input -->
                <div class="mt-4">
                    <div class="flex space-x-2">
                        <input 
                            id="message-input" 
                            type="text" 
                            placeholder="输入测试消息..." 
                            class="flex-1 border rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <button 
                            id="send-message-btn" 
                            class="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600 disabled:opacity-50"
                            disabled
                        >
                            发送
                        </button>
                    </div>
                </div>
            </div>

            <!-- Subscription Management -->
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📋 消息订阅管理</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">系统状态</div>
                            <div class="text-sm text-gray-500">system_status</div>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="subscription-toggle sr-only" data-type="system_status" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">AI处理进度</div>
                            <div class="text-sm text-gray-500">ai_processing</div>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="subscription-toggle sr-only" data-type="ai_processing" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">仕訳记录</div>
                            <div class="text-sm text-gray-500">journal_entry</div>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="subscription-toggle sr-only" data-type="journal_entry" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                            <div class="font-medium">通知消息</div>
                            <div class="text-sm text-gray-500">notification</div>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="subscription-toggle sr-only" data-type="notification" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-t">
                    <button id="update-subscriptions-btn" class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        更新订阅设置
                    </button>
                </div>
            </div>
        </section>

        <!-- Real-time Charts -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">📊 消息流量监控</h3>
                <canvas id="message-flow-chart" width="400" height="300"></canvas>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-sm border">
                <h3 class="text-lg font-semibold mb-4">⚡ 连接状态历史</h3>
                <canvas id="connection-history-chart" width="400" height="300"></canvas>
            </div>
        </section>

        <!-- Notification Area -->
        <div id="notification-area" class="fixed top-4 right-4 z-50 space-y-2">
            <!-- 动态通知将在这里显示 -->
        </div>
    </main>

    <script>
        // 全局变量
        let websocket = null;
        let clientId = 'client_' + Math.random().toString(36).substr(2, 9);
        let messageCount = { sent: 0, received: 0 };
        let connectionStartTime = null;
        let messageFlowChart, connectionHistoryChart;
        let messageFlowData = [];
        let connectionData = [];

        // WebSocket连接管理
        function connectWebSocket() {
            const wsUrl = `ws://localhost:8000/ws/${clientId}`;
            
            try {
                websocket = new WebSocket(wsUrl);
                updateConnectionStatus('connecting', '连接中...');
                
                websocket.onopen = function(event) {
                    connectionStartTime = Date.now();
                    updateConnectionStatus('online', '已连接');
                    addMessage('system', '✅ WebSocket连接成功');
                    
                    // 发送初始订阅
                    updateSubscriptions();
                    
                    // 开始心跳
                    startHeartbeat();
                };
                
                websocket.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                    messageCount.received++;
                    updateMessageStats();
                };
                
                websocket.onclose = function(event) {
                    updateConnectionStatus('offline', '连接断开');
                    addMessage('system', '❌ WebSocket连接断开');
                    
                    // 尝试重连
                    setTimeout(connectWebSocket, 3000);
                };
                
                websocket.onerror = function(error) {
                    addMessage('error', `❌ WebSocket错误: ${error.message || '连接失败'}`);
                };
                
            } catch (error) {
                addMessage('error', `❌ 连接失败: ${error.message}`);
                updateConnectionStatus('offline', '连接失败');
            }
        }

        // 更新连接状态
        function updateConnectionStatus(status, text) {
            const statusElement = document.getElementById('ws-status');
            const statusText = document.getElementById('status-text');
            const indicator = document.querySelector('.connection-indicator');
            
            statusText.textContent = text;
            
            // 移除所有状态类
            statusElement.classList.remove('status-online', 'status-offline', 'status-connecting');
            indicator.classList.remove('bg-green-500', 'bg-red-500', 'bg-yellow-500', 'pulse');
            
            switch(status) {
                case 'online':
                    statusElement.classList.add('status-online');
                    statusElement.textContent = '在线';
                    indicator.classList.add('bg-green-500');
                    document.getElementById('send-message-btn').disabled = false;
                    break;
                case 'offline':
                    statusElement.classList.add('status-offline');
                    statusElement.textContent = '离线';
                    indicator.classList.add('bg-red-500', 'pulse');
                    document.getElementById('send-message-btn').disabled = true;
                    break;
                case 'connecting':
                    statusElement.classList.add('status-connecting');
                    statusElement.textContent = '连接中';
                    indicator.classList.add('bg-yellow-500', 'pulse');
                    document.getElementById('send-message-btn').disabled = true;
                    break;
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            const { type, data, timestamp } = message;
            
            switch(type) {
                case 'system_status':
                    updateSystemStats(data);
                    addMessage('system', `📊 系统状态更新: ${data.active_connections} 个活跃连接`);
                    break;
                    
                case 'ai_processing':
                    addMessage('ai', `🤖 AI处理: ${JSON.stringify(data)}`);
                    showNotification('AI处理更新', 'AI正在处理新的记账请求', 'info');
                    break;
                    
                case 'journal_entry':
                    addMessage('journal', `📋 新仕訳: ${data.description} - ¥${data.amount}`);
                    showNotification('新仕訳记录', `${data.description} - ¥${data.amount}`, 'success');
                    break;
                    
                case 'notification':
                    addMessage('notification', `📢 通知: ${data.message || JSON.stringify(data)}`);
                    if (data.message) {
                        showNotification('系统通知', data.message, 'info');
                    }
                    break;
                    
                case 'heartbeat':
                    // 心跳响应，更新连接时间
                    updateUptime();
                    break;
                    
                case 'error':
                    addMessage('error', `❌ 错误: ${data.error}`);
                    showNotification('系统错误', data.error, 'error');
                    break;
                    
                default:
                    addMessage('unknown', `❓ 未知消息类型: ${type}`);
            }
            
            // 更新图表数据
            updateChartData();
        }

        // 添加消息到控制台
        function addMessage(type, content) {
            const console = document.getElementById('message-console');
            const timestamp = new Date().toLocaleTimeString('ja-JP');
            
            let className = 'text-green-400';
            switch(type) {
                case 'error': className = 'text-red-400'; break;
                case 'system': className = 'text-blue-400'; break;
                case 'ai': className = 'text-purple-400'; break;
                case 'journal': className = 'text-yellow-400'; break;
                case 'notification': className = 'text-cyan-400'; break;
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = className;
            messageDiv.textContent = `[${timestamp}] ${content}`;
            
            console.appendChild(messageDiv);
            console.scrollTop = console.scrollHeight;
            
            // 保持最多100条消息
            while (console.children.length > 100) {
                console.removeChild(console.firstChild);
            }
        }

        // 发送消息
        function sendMessage(messageData) {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify(messageData));
                messageCount.sent++;
                updateMessageStats();
                return true;
            }
            return false;
        }

        // 更新订阅设置
        function updateSubscriptions() {
            const checkedTypes = [];
            document.querySelectorAll('.subscription-toggle:checked').forEach(checkbox => {
                checkedTypes.push(checkbox.getAttribute('data-type'));
            });
            
            const subscribeMessage = {
                type: 'subscribe',
                data: {
                    message_types: checkedTypes
                }
            };
            
            if (sendMessage(subscribeMessage)) {
                addMessage('system', `📋 已订阅 ${checkedTypes.length} 种消息类型`);
            }
        }

        // 更新统计信息
        function updateMessageStats() {
            document.getElementById('messages-sent').textContent = messageCount.sent;
            document.getElementById('messages-received').textContent = messageCount.received;
        }

        function updateSystemStats(data) {
            document.getElementById('active-connections').textContent = data.active_connections || 0;
        }

        function updateUptime() {
            if (connectionStartTime) {
                const uptime = Math.floor((Date.now() - connectionStartTime) / 1000);
                const hours = Math.floor(uptime / 3600);
                const minutes = Math.floor((uptime % 3600) / 60);
                const seconds = uptime % 60;
                
                document.getElementById('ws-uptime').textContent = 
                    `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        // 显示通知
        function showNotification(title, message, type = 'info') {
            const notificationArea = document.getElementById('notification-area');
            
            const notification = document.createElement('div');
            notification.className = `notification-enter max-w-sm bg-white border-l-4 p-4 shadow-lg rounded-lg`;
            
            switch(type) {
                case 'success':
                    notification.classList.add('border-green-500');
                    break;
                case 'error':
                    notification.classList.add('border-red-500');
                    break;
                case 'warning':
                    notification.classList.add('border-yellow-500');
                    break;
                default:
                    notification.classList.add('border-blue-500');
            }
            
            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-800">${title}</h4>
                        <p class="text-sm text-gray-600">${message}</p>
                    </div>
                    <button class="ml-2 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                        ✕
                    </button>
                </div>
            `;
            
            notificationArea.appendChild(notification);
            
            // 自动移除通知
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.classList.add('notification-exit');
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // 心跳机制
        function startHeartbeat() {
            setInterval(() => {
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    sendMessage({
                        type: 'heartbeat',
                        data: { timestamp: Date.now() }
                    });
                }
            }, 30000); // 每30秒发送心跳
        }

        // 初始化图表
        function initCharts() {
            // 消息流量图表
            const flowCtx = document.getElementById('message-flow-chart').getContext('2d');
            messageFlowChart = new Chart(flowCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '发送',
                        data: [],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '接收',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // 连接状态图表
            const connCtx = document.getElementById('connection-history-chart').getContext('2d');
            connectionHistoryChart = new Chart(connCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '连接状态',
                        data: [],
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4,
                        stepped: true
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: { 
                            beginAtZero: true,
                            max: 1,
                            ticks: {
                                callback: function(value) {
                                    return value === 1 ? '在线' : '离线';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新图表数据
        function updateChartData() {
            const now = new Date().toLocaleTimeString();
            
            // 更新消息流量图表
            messageFlowData.push({
                time: now,
                sent: messageCount.sent,
                received: messageCount.received
            });
            
            if (messageFlowData.length > 20) {
                messageFlowData.shift();
            }
            
            messageFlowChart.data.labels = messageFlowData.map(d => d.time);
            messageFlowChart.data.datasets[0].data = messageFlowData.map(d => d.sent);
            messageFlowChart.data.datasets[1].data = messageFlowData.map(d => d.received);
            messageFlowChart.update('none');
            
            // 更新连接状态图表
            const isConnected = websocket && websocket.readyState === WebSocket.OPEN ? 1 : 0;
            connectionData.push({ time: now, status: isConnected });
            
            if (connectionData.length > 20) {
                connectionData.shift();
            }
            
            connectionHistoryChart.data.labels = connectionData.map(d => d.time);
            connectionHistoryChart.data.datasets[0].data = connectionData.map(d => d.status);
            connectionHistoryChart.update('none');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 连接WebSocket
            connectWebSocket();
            
            // 重连按钮
            document.getElementById('reconnect-btn').addEventListener('click', function() {
                if (websocket) {
                    websocket.close();
                }
                connectWebSocket();
            });
            
            // 发送消息按钮
            document.getElementById('send-message-btn').addEventListener('click', function() {
                const input = document.getElementById('message-input');
                const message = input.value.trim();
                
                if (message) {
                    const testMessage = {
                        type: 'test',
                        data: { message: message }
                    };
                    
                    if (sendMessage(testMessage)) {
                        addMessage('user', `📤 发送: ${message}`);
                        input.value = '';
                    }
                }
            });
            
            // 回车发送
            document.getElementById('message-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('send-message-btn').click();
                }
            });
            
            // 更新订阅按钮
            document.getElementById('update-subscriptions-btn').addEventListener('click', updateSubscriptions);
            
            // 清空消息按钮
            document.getElementById('clear-messages-btn').addEventListener('click', function() {
                document.getElementById('message-console').innerHTML = '<div class="text-gray-500">[系统] 消息已清空</div>';
            });
            
            // 导出消息按钮
            document.getElementById('export-messages-btn').addEventListener('click', function() {
                const messages = document.getElementById('message-console').textContent;
                const blob = new Blob([messages], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `websocket-messages-${new Date().toISOString().split('T')[0]}.txt`;
                a.click();
                URL.revokeObjectURL(url);
            });
            
            // 定期更新运行时间
            setInterval(updateUptime, 1000);
            
            // 定期更新图表
            setInterval(updateChartData, 5000);
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付处理中 - GoldenLedger</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="api_config.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00d4aa 0%, #6c5ce7 100%); }
        
        .success-card { 
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #00d4aa;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success-icon {
            animation: scaleIn 0.5s ease-out;
        }
        
        @keyframes scaleIn {
            0% { transform: scale(0); }
            100% { transform: scale(1); }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        .progress-bar {
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Processing Card -->
        <div id="processingCard" class="success-card rounded-2xl p-8 text-center">
            <div class="loading-spinner mx-auto mb-6"></div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4">支付处理中...</h2>
            <p class="text-gray-600 mb-6">正在验证您的支付信息，请稍候</p>
            
            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                <div id="progressBar" class="progress-bar bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full"></div>
            </div>
            
            <div id="statusText" class="text-sm text-gray-500">连接PayPal服务器...</div>
        </div>
        
        <!-- Success Card (Hidden initially) -->
        <div id="successCard" class="success-card rounded-2xl p-8 text-center hidden">
            <div class="success-icon text-6xl mb-6">🎉</div>
            <h2 class="text-3xl font-bold text-gray-800 mb-4">支付成功！</h2>
            <p class="text-gray-600 mb-6">恭喜您成功购买了套餐</p>
            
            <!-- Payment Details -->
            <div id="paymentDetails" class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h4 class="font-semibold text-gray-800 mb-3">支付详情</h4>
                <div id="detailsContent">
                    <!-- Details will be populated by JavaScript -->
                </div>
            </div>
            
            <!-- Account Setup -->
            <div id="accountSetup" class="bg-blue-50 rounded-lg p-4 mb-6">
                <h4 class="font-semibold text-blue-800 mb-2">🔐 账户设置</h4>
                <p class="text-blue-700 text-sm mb-3">我们正在为您创建账户...</p>
                <div class="loading-spinner mx-auto" style="width: 24px; height: 24px; border-width: 2px;"></div>
            </div>
            
            <div id="nextSteps" class="hidden">
                <button id="continueBtn" class="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-green-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105">
                    开始使用 GoldenLedger
                </button>
            </div>
        </div>
        
        <!-- Error Card (Hidden initially) -->
        <div id="errorCard" class="success-card rounded-2xl p-8 text-center hidden">
            <div class="text-6xl mb-6">❌</div>
            <h2 class="text-2xl font-bold text-red-600 mb-4">支付验证失败</h2>
            <p id="errorMessage" class="text-gray-600 mb-6">支付验证过程中出现问题</p>
            <button id="retryBtn" class="bg-red-500 text-white font-semibold py-2 px-6 rounded-lg hover:bg-red-600 transition-colors">
                重试
            </button>
        </div>
    </div>

    <script>
        class PaymentProcessor {
            constructor() {
                this.progressBar = document.getElementById('progressBar');
                this.statusText = document.getElementById('statusText');
                this.processingCard = document.getElementById('processingCard');
                this.successCard = document.getElementById('successCard');
                this.errorCard = document.getElementById('errorCard');
                
                this.init();
            }
            
            async init() {
                // 从URL参数获取支付信息
                const urlParams = new URLSearchParams(window.location.search);
                const orderID = urlParams.get('orderID');
                const planId = urlParams.get('planId');
                
                if (!orderID || !planId) {
                    this.showError('缺少支付参数');
                    return;
                }
                
                await this.processPayment(orderID, planId);
            }
            
            async processPayment(orderID, planId) {
                try {
                    // Step 1: 验证支付
                    this.updateProgress(20, '验证支付信息...');
                    await this.sleep(1000);
                    
                    const paymentResult = await this.verifyPayment(orderID, planId);
                    
                    if (!paymentResult.success) {
                        throw new Error(paymentResult.error || '支付验证失败');
                    }
                    
                    // Step 2: 创建或获取用户账户
                    this.updateProgress(60, '设置用户账户...');
                    await this.sleep(1000);
                    
                    const userResult = await this.setupUserAccount(paymentResult);
                    
                    // Step 3: 完成设置
                    this.updateProgress(100, '完成设置...');
                    await this.sleep(500);
                    
                    // 显示成功页面
                    this.showSuccess(paymentResult, userResult);
                    
                } catch (error) {
                    console.error('Payment processing error:', error);
                    this.showError(error.message);
                }
            }
            
            async verifyPayment(orderID, planId) {
                const response = await fetch(window.GoldenLedgerAPI.url('/api/paypal/verify-payment'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ orderID, planId })
                });
                
                return await response.json();
            }
            
            async setupUserAccount(paymentResult) {
                // 这里实现用户账户设置逻辑
                // 如果用户不存在，自动注册
                // 如果用户存在，自动登录

                const userEmail = paymentResult.payer_info?.email;
                const userName = paymentResult.payer_info?.name || 'PayPal User';

                if (!userEmail) {
                    throw new Error('无法获取用户邮箱信息');
                }

                // 尝试自动注册/登录
                const response = await fetch(window.GoldenLedgerAPI.url('/api/auth/auto-register'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: userEmail,
                        name: userName,
                        source: 'paypal_payment',
                        order_id: paymentResult.order_id
                    })
                });

                const result = await response.json();
                console.log('🔍 自动注册API响应:', result);

                if (result.success) {
                    // 检查是否已经有登录信息
                    const existingToken = localStorage.getItem('goldenledger_session_token');
                    const isNewUser = result.data.user.temp_password;

                    if (!existingToken || isNewUser) {
                        // 新用户或未登录，保存认证信息
                        localStorage.setItem('goldenledger_session_token', result.data.session_token);
                        localStorage.setItem('goldenledger_user', JSON.stringify(result.data.user));
                    }

                    // 保存支付成功信息供dashboard使用
                    localStorage.setItem('paymentSuccess', JSON.stringify({
                        planId: paymentResult.plan_id,
                        orderId: paymentResult.order_id,
                        amount: paymentResult.amount,
                        currency: paymentResult.currency,
                        timestamp: new Date().toISOString(),
                        message: '支付已成功验证，套餐已激活！',
                        payerInfo: paymentResult.payer_info
                    }));

                    // 如果是新用户且有临时密码，保存到localStorage供登录页面使用
                    if (isNewUser) {
                        localStorage.setItem('pendingPaymentAuth', JSON.stringify({
                            payerEmail: userEmail,
                            payerName: userName,
                            tempPassword: result.data.user.temp_password,
                            timestamp: new Date().toISOString(),
                            isNewUser: true
                        }));
                    }
                } else {
                    throw new Error(result.error || '账户设置失败');
                }

                return result;
            }
            
            updateProgress(percentage, status) {
                this.progressBar.style.width = percentage + '%';
                this.statusText.textContent = status;
            }
            
            showSuccess(paymentResult, userResult) {
                this.processingCard.classList.add('hidden');
                this.successCard.classList.remove('hidden');
                this.successCard.classList.add('fade-in');
                
                // 填充支付详情
                const detailsContent = document.getElementById('detailsContent');
                detailsContent.innerHTML = `
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">套餐:</span>
                            <span class="font-medium">${paymentResult.plan_id === 'basic' ? 'Basic Plan' : 'Pro Plan'}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">金额:</span>
                            <span class="font-medium">${paymentResult.amount} ${paymentResult.currency}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">订单ID:</span>
                            <span class="font-medium text-xs">${paymentResult.order_id}</span>
                        </div>
                    </div>
                `;
                
                // 显示账户设置完成
                setTimeout(() => {
                    const accountSetup = document.getElementById('accountSetup');
                    const isNewUser = userResult.data.user.temp_password;

                    if (isNewUser) {
                        // 新用户，显示临时登录信息
                        accountSetup.innerHTML = `
                            <h4 class="font-semibold text-blue-800 mb-3">🔑 账户已创建</h4>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                <p class="text-blue-800 font-medium mb-2">临时登录信息：</p>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between items-center">
                                        <span class="text-blue-600">邮箱：</span>
                                        <span class="font-mono bg-blue-100 px-2 py-1 rounded text-blue-800">${userResult.data.user.email}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-blue-600">临时密码：</span>
                                        <div class="flex items-center space-x-2">
                                            <span id="tempPasswordDisplay" class="font-mono bg-blue-100 px-2 py-1 rounded text-blue-800">${userResult.data.user.temp_password}</span>
                                            <button onclick="copyTempPassword()" class="text-blue-600 hover:text-blue-800 p-1">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-blue-600 text-xs mt-3">💡 请保存此信息，登录后建议修改密码</p>
                            </div>
                        `;
                        accountSetup.className = 'bg-blue-50 rounded-lg p-4 mb-6';
                    } else {
                        // 已有用户
                        accountSetup.innerHTML = `
                            <h4 class="font-semibold text-green-800 mb-2">✅ 账户设置完成</h4>
                            <p class="text-green-700 text-sm">欢迎使用 GoldenLedger！</p>
                        `;
                        accountSetup.className = 'bg-green-50 rounded-lg p-4 mb-6';
                    }

                    // 显示继续按钮
                    document.getElementById('nextSteps').classList.remove('hidden');
                }, 2000);
                
                // 设置继续按钮事件
                document.getElementById('continueBtn').onclick = () => {
                    // 检查是否有临时密码（新用户）
                    const hasTempPassword = userResult.data.user.temp_password;
                    const hasSessionToken = localStorage.getItem('goldenledger_session_token');

                    if (hasTempPassword) {
                        // 新用户，跳转到登录页面
                        window.location.href = '/login_simple.html?payment_success=1&return=%2Fmaster_dashboard.html';
                    } else if (hasSessionToken) {
                        // 已登录用户，直接进入控制台
                        window.location.href = '/master_dashboard.html';
                    } else {
                        // 未登录，跳转到登录页面
                        window.location.href = '/login_simple.html?return=%2Fmaster_dashboard.html';
                    }
                };

                // 复制临时密码功能
                window.copyTempPassword = function() {
                    const tempPassword = document.getElementById('tempPasswordDisplay');
                    if (tempPassword) {
                        navigator.clipboard.writeText(tempPassword.textContent).then(() => {
                            // 显示复制成功提示
                            const button = event.target.closest('button');
                            const originalIcon = button.innerHTML;
                            button.innerHTML = '<i class="fas fa-check text-green-600"></i>';
                            setTimeout(() => {
                                button.innerHTML = originalIcon;
                            }, 2000);
                        }).catch(err => {
                            console.error('复制失败:', err);
                            // 降级方案：选中文本
                            const range = document.createRange();
                            range.selectNode(tempPassword);
                            window.getSelection().removeAllRanges();
                            window.getSelection().addRange(range);
                        });
                    }
                };
            }
            
            showError(message) {
                this.processingCard.classList.add('hidden');
                this.errorCard.classList.remove('hidden');
                document.getElementById('errorMessage').textContent = message;
                
                document.getElementById('retryBtn').onclick = () => {
                    window.location.reload();
                };
            }
            
            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // 页面加载时启动支付处理
        document.addEventListener('DOMContentLoaded', () => {
            new PaymentProcessor();
        });
    </script>
</body>
</html>

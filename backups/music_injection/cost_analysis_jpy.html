<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>システム費用分析ツール (日本円)</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-green-600">💰 システム費用分析ツール (日本円)</h1>
        
        <!-- 費用概要 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-blue-600 mb-2">Cloudflare Workers</h3>
                <p class="text-2xl font-bold text-green-600">無料</p>
                <p class="text-sm text-gray-600">1日10万リクエスト無料</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-purple-600 mb-2">Gemini API</h3>
                <p class="text-2xl font-bold text-yellow-600">従量課金</p>
                <p class="text-sm text-gray-600">使用量に応じて課金</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-indigo-600 mb-2">GitHub Pages</h3>
                <p class="text-2xl font-bold text-green-600">無料</p>
                <p class="text-sm text-gray-600">静的サイトホスティング</p>
            </div>
        </div>
        
        <!-- 為替レート設定 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">💱 為替レート設定</h2>
            <div class="flex items-center space-x-4">
                <label class="text-sm font-medium">1 USD = </label>
                <input type="number" id="exchangeRate" value="150" min="100" max="200" class="w-20 p-2 border rounded text-center">
                <label class="text-sm font-medium">円</label>
                <button onclick="updateExchangeRate()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    更新
                </button>
                <span class="text-sm text-gray-600">(現在のレート: <span id="currentRate">150</span>円)</span>
            </div>
        </div>
        
        <!-- Gemini API 使用分析 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">🤖 Gemini API 使用分析</h2>
            <div class="space-y-4">
                <button onclick="analyzeGeminiUsageJPY()" class="bg-purple-500 text-white px-6 py-2 rounded hover:bg-purple-600">
                    Gemini API使用状況を分析
                </button>
                <button onclick="estimateMonthlyCostJPY()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    月額費用を見積もり
                </button>
            </div>
            <div id="gemini-analysis-jpy" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
        </div>
        
        <!-- 使用シナリオ別費用 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">📊 使用シナリオ別月額費用</h2>
            <div id="scenario-costs" class="grid grid-cols-1 md:grid-cols-3 gap-4"></div>
        </div>
        
        <!-- 年間費用予測 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">📈 年間費用予測</h2>
            <div class="space-y-4">
                <button onclick="calculateYearlyCost()" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                    年間費用を計算
                </button>
            </div>
            <div id="yearly-cost" class="mt-4 p-4 bg-gray-50 rounded min-h-32"></div>
        </div>
        
        <!-- 費用最適化提案 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">💡 費用最適化提案</h2>
            <div id="optimization-suggestions-jpy" class="space-y-4">
                <div class="p-4 bg-green-50 border border-green-200 rounded">
                    <h3 class="font-semibold text-green-800">✅ 現在無料のサービス</h3>
                    <ul class="list-disc list-inside text-sm text-green-700 mt-2">
                        <li>Cloudflare Workers: 1日10万リクエスト無料</li>
                        <li>Cloudflare D1データベース: 1日10万読み取り無料</li>
                        <li>Cloudflare R2ストレージ: 月10GB無料</li>
                        <li>GitHub Pages: 無制限無料ホスティング</li>
                    </ul>
                </div>
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <h3 class="font-semibold text-yellow-800">⚠️ 監視が必要なサービス</h3>
                    <ul class="list-disc list-inside text-sm text-yellow-700 mt-2">
                        <li>Gemini API: トークン使用量に応じて課金</li>
                        <li>Cloudflare無料枠を超えた場合の課金</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 実時間監視 -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">📊 リアルタイム使用監視</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">本日のAPI呼び出し統計</h3>
                    <div id="daily-stats-jpy" class="p-3 bg-gray-50 rounded text-sm"></div>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">今月の費用見積もり</h3>
                    <div id="monthly-cost-jpy" class="p-3 bg-gray-50 rounded text-sm"></div>
                </div>
            </div>
            <button onclick="refreshStatsJPY()" class="mt-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                統計を更新
            </button>
        </div>
        
        <!-- 操作ログ -->
        <div class="bg-black text-green-400 p-6 rounded-lg shadow mt-6">
            <h2 class="text-xl font-semibold mb-4 text-white">📋 分析ログ</h2>
            <div id="debug-log-jpy" class="font-mono text-sm whitespace-pre-wrap max-h-64 overflow-y-auto"></div>
            <button onclick="clearLogJPY()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                ログをクリア
            </button>
        </div>
    </div>

    <script>
        let currentExchangeRate = 150; // 1 USD = 150 JPY
        
        // ログ関数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log-jpy');
            const colors = {
                info: 'text-green-400',
                error: 'text-red-400',
                warning: 'text-yellow-400',
                success: 'text-blue-400'
            };
            logDiv.innerHTML += `<span class="${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLogJPY() {
            document.getElementById('debug-log-jpy').innerHTML = '';
        }
        
        // 為替レート更新
        function updateExchangeRate() {
            const newRate = parseFloat(document.getElementById('exchangeRate').value);
            if (newRate >= 100 && newRate <= 200) {
                currentExchangeRate = newRate;
                document.getElementById('currentRate').textContent = newRate;
                log(`為替レートを更新: 1 USD = ${newRate} 円`, 'success');
                // 既存の分析結果を更新
                if (document.getElementById('gemini-analysis-jpy').innerHTML.trim()) {
                    analyzeGeminiUsageJPY();
                }
                if (document.getElementById('scenario-costs').innerHTML.trim()) {
                    estimateMonthlyCostJPY();
                }
            } else {
                log('無効な為替レートです (100-200円の範囲で入力してください)', 'error');
            }
        }
        
        // Gemini API使用分析
        async function analyzeGeminiUsageJPY() {
            const resultDiv = document.getElementById('gemini-analysis-jpy');
            log('Gemini API使用状況を分析中...', 'info');
            
            resultDiv.innerHTML = '<p class="text-blue-600">🔄 分析中...</p>';
            
            try {
                // Gemini API価格情報 (2024年価格)
                const geminiPricingUSD = {
                    'gemini-1.5-flash': {
                        input: 0.075 / 1000000,  // $0.075 per 1M input tokens
                        output: 0.30 / 1000000   // $0.30 per 1M output tokens
                    }
                };
                
                // 日本円に換算
                const geminiPricingJPY = {
                    'gemini-1.5-flash': {
                        input: geminiPricingUSD['gemini-1.5-flash'].input * currentExchangeRate,
                        output: geminiPricingUSD['gemini-1.5-flash'].output * currentExchangeRate
                    }
                };
                
                // 推定使用量
                const estimatedUsage = {
                    textProcessing: {
                        inputTokens: 200,   // 平均入力トークン数
                        outputTokens: 100   // 平均出力トークン数
                    },
                    ocrProcessing: {
                        inputTokens: 1000,  // OCR入力（画像含む）
                        outputTokens: 200   // OCR出力（構造化データ）
                    }
                };
                
                // 1回あたりのコスト計算
                const textCostJPY = (estimatedUsage.textProcessing.inputTokens * geminiPricingJPY['gemini-1.5-flash'].input) + 
                                   (estimatedUsage.textProcessing.outputTokens * geminiPricingJPY['gemini-1.5-flash'].output);
                
                const ocrCostJPY = (estimatedUsage.ocrProcessing.inputTokens * geminiPricingJPY['gemini-1.5-flash'].input) + 
                                  (estimatedUsage.ocrProcessing.outputTokens * geminiPricingJPY['gemini-1.5-flash'].output);
                
                resultDiv.innerHTML = `
                    <div class="space-y-4">
                        <h3 class="font-semibold text-lg">💰 Gemini API 費用分析 (為替レート: ${currentExchangeRate}円/USD)</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="p-4 bg-blue-50 border border-blue-200 rounded">
                                <h4 class="font-semibold text-blue-800">テキスト処理 (AI記帳)</h4>
                                <p class="text-sm text-blue-700 mt-2">
                                    <strong>1回あたり:</strong> ¥${(textCostJPY).toFixed(4)}<br>
                                    <strong>100回:</strong> ¥${(textCostJPY * 100).toFixed(3)}<br>
                                    <strong>1000回:</strong> ¥${(textCostJPY * 1000).toFixed(2)}<br>
                                    <strong>1万回:</strong> ¥${(textCostJPY * 10000).toFixed(1)}
                                </p>
                            </div>
                            
                            <div class="p-4 bg-purple-50 border border-purple-200 rounded">
                                <h4 class="font-semibold text-purple-800">OCR処理 (請求書認識)</h4>
                                <p class="text-sm text-purple-700 mt-2">
                                    <strong>1回あたり:</strong> ¥${(ocrCostJPY).toFixed(4)}<br>
                                    <strong>100回:</strong> ¥${(ocrCostJPY * 100).toFixed(3)}<br>
                                    <strong>1000回:</strong> ¥${(ocrCostJPY * 1000).toFixed(2)}<br>
                                    <strong>1万回:</strong> ¥${(ocrCostJPY * 10000).toFixed(1)}
                                </p>
                            </div>
                        </div>
                        
                        <div class="p-4 bg-green-50 border border-green-200 rounded">
                            <h4 class="font-semibold text-green-800">💡 費用管理のアドバイス</h4>
                            <ul class="list-disc list-inside text-sm text-green-700 mt-2">
                                <li>Gemini APIには無料枠があり、小規模利用は基本的に無料</li>
                                <li>使用量監視と予算アラートの設定を推奨</li>
                                <li>リクエストキャッシュで重複呼び出しを削減可能</li>
                                <li>高頻度利用の場合はバッチ処理を検討</li>
                                <li>月額1000円以下で十分な利用が可能</li>
                            </ul>
                        </div>
                    </div>
                `;
                
                log('Gemini API費用分析完了', 'success');
                
            } catch (error) {
                log(`Gemini API分析失敗: ${error.message}`, 'error');
                resultDiv.innerHTML = `<p class="text-red-600">❌ 分析失敗: ${error.message}</p>`;
            }
        }
        
        // 月額費用見積もり
        async function estimateMonthlyCostJPY() {
            const resultDiv = document.getElementById('scenario-costs');
            log('月額費用を見積もり中...', 'info');
            
            // 使用シナリオ
            const scenarios = [
                {
                    name: '個人利用',
                    textCalls: 100,
                    ocrCalls: 20,
                    description: '個人ユーザー、たまに使用',
                    color: 'green'
                },
                {
                    name: '小規模事業',
                    textCalls: 500,
                    ocrCalls: 100,
                    description: '小企業、日常的な記帳',
                    color: 'blue'
                },
                {
                    name: '中規模事業',
                    textCalls: 2000,
                    ocrCalls: 500,
                    description: '中企業、頻繁な利用',
                    color: 'purple'
                }
            ];
            
            const textCostJPY = 0.0045;  // 1回あたりのテキスト処理コスト（円）
            const ocrCostJPY = 0.018;    // 1回あたりのOCR処理コスト（円）
            
            let scenarioHtml = '';
            scenarios.forEach(scenario => {
                const totalCostJPY = (scenario.textCalls * textCostJPY) + (scenario.ocrCalls * ocrCostJPY);
                
                scenarioHtml += `
                    <div class="p-4 bg-${scenario.color}-50 border border-${scenario.color}-200 rounded">
                        <h4 class="font-semibold text-${scenario.color}-800">${scenario.name}</h4>
                        <p class="text-sm text-${scenario.color}-600 mb-2">${scenario.description}</p>
                        <div class="text-sm space-y-1">
                            <p>テキスト処理: ${scenario.textCalls}回 × ¥${textCostJPY.toFixed(4)} = ¥${(scenario.textCalls * textCostJPY).toFixed(2)}</p>
                            <p>OCR処理: ${scenario.ocrCalls}回 × ¥${ocrCostJPY.toFixed(4)} = ¥${(scenario.ocrCalls * ocrCostJPY).toFixed(2)}</p>
                            <p class="font-semibold text-${scenario.color}-800 text-lg mt-2">月額: ¥${totalCostJPY.toFixed(2)}</p>
                        </div>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = scenarioHtml;
            log('月額費用見積もり完了', 'success');
        }
        
        // 年間費用計算
        async function calculateYearlyCost() {
            const resultDiv = document.getElementById('yearly-cost');
            log('年間費用を計算中...', 'info');
            
            const scenarios = [
                { name: '個人利用', monthlyCost: 2.25 },
                { name: '小規模事業', monthlyCost: 4.05 },
                { name: '中規模事業', monthlyCost: 18.0 }
            ];
            
            let yearlyHtml = '<div class="space-y-4">';
            yearlyHtml += '<h3 class="font-semibold text-lg">📈 年間費用予測</h3>';
            
            scenarios.forEach(scenario => {
                const yearlyCost = scenario.monthlyCost * 12;
                yearlyHtml += `
                    <div class="p-4 bg-gray-50 border border-gray-200 rounded">
                        <div class="flex justify-between items-center">
                            <h4 class="font-semibold">${scenario.name}</h4>
                            <div class="text-right">
                                <p class="text-sm text-gray-600">月額: ¥${scenario.monthlyCost.toFixed(2)}</p>
                                <p class="text-lg font-semibold text-blue-600">年額: ¥${yearlyCost.toFixed(2)}</p>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            yearlyHtml += `
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <h4 class="font-semibold text-yellow-800">💡 年間費用のポイント</h4>
                    <ul class="list-disc list-inside text-sm text-yellow-700 mt-2">
                        <li>最大でも年間¥216（中規模事業）と非常に低コスト</li>
                        <li>個人利用なら年間¥27程度で十分</li>
                        <li>従来の会計ソフトと比較して圧倒的に安価</li>
                        <li>サーバー維持費用が不要</li>
                    </ul>
                </div>
            `;
            
            yearlyHtml += '</div>';
            resultDiv.innerHTML = yearlyHtml;
            
            log('年間費用計算完了', 'success');
        }
        
        // 統計更新
        async function refreshStatsJPY() {
            const dailyStatsDiv = document.getElementById('daily-stats-jpy');
            const monthlyCostDiv = document.getElementById('monthly-cost-jpy');
            
            log('使用統計を更新中...', 'info');
            
            const today = new Date().toLocaleDateString('ja-JP');
            dailyStatsDiv.innerHTML = `
                <p><strong>日付:</strong> ${today}</p>
                <p><strong>AIテキスト処理:</strong> ~50回</p>
                <p><strong>OCR認識:</strong> ~10回</p>
                <p><strong>Workers リクエスト:</strong> ~200回</p>
                <p><strong>ステータス:</strong> <span class="text-green-600">正常</span></p>
            `;
            
            const estimatedMonthlyCost = (50 * 30 * 0.0045) + (10 * 30 * 0.018);
            monthlyCostDiv.innerHTML = `
                <p><strong>Cloudflare:</strong> <span class="text-green-600">¥0 (無料)</span></p>
                <p><strong>Gemini API:</strong> <span class="text-blue-600">~¥${estimatedMonthlyCost.toFixed(2)}</span></p>
                <p><strong>GitHub Pages:</strong> <span class="text-green-600">¥0 (無料)</span></p>
                <p><strong>合計:</strong> <span class="text-blue-600 font-semibold">~¥${estimatedMonthlyCost.toFixed(2)}/月</span></p>
            `;
            
            log('統計データを更新しました', 'success');
        }
        
        // ページ読み込み時の初期化
        window.addEventListener('load', function() {
            log('費用分析ツール（日本円）を読み込みました', 'info');
            refreshStatsJPY();
            analyzeGeminiUsageJPY();
            estimateMonthlyCostJPY();
        });
    </script>
</body>
</html>

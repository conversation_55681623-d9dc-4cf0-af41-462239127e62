<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支払い完了 - GoldenLedger AI記帳システム</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- PayPal 配置 -->
    <script src="paypal_config.js"></script>
    
    <!-- API 配置 -->
    <script src="api_config.js"></script>
    
    <style>
        * {
            font-family: 'Noto Sans JP', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            min-height: 100vh;
        }

        .tech-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: techPulse 4s ease-in-out infinite alternate;
        }

        @keyframes techPulse {
            0% { opacity: 0.5; }
            100% { opacity: 0.8; }
        }

        .cyber-glass {
            backdrop-filter: blur(20px);
            background: rgba(0, 20, 40, 0.15);
            border: 1px solid rgba(120, 219, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #00d4ff;
            animation: confetti-fall 3s linear infinite;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(120, 219, 255, 0.6);
            border-radius: 50%;
            animation: float 6s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-10vh) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="tech-bg">
    <!-- 浮动粒子效果 -->
    <div class="floating-particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 15%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 25%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 35%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 5%; animation-delay: 0.5s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 1.5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 2.5s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 3.5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 4.5s;"></div>
    </div>

    <!-- 彩带效果 -->
    <div id="confettiContainer"></div>

    <!-- 主要内容 -->
    <div class="min-h-screen flex items-center justify-center px-4 relative z-10">
        <div class="cyber-glass rounded-3xl p-12 max-w-2xl w-full text-center">
            <!-- 成功图标 -->
            <div class="success-animation mb-8">
                <div class="w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check text-4xl text-white"></i>
                </div>
            </div>

            <!-- 成功消息 -->
            <h1 class="text-4xl font-bold text-green-400 mb-4">
                支払い完了！
            </h1>
            
            <p class="text-xl text-cyan-200 mb-8">
                ありがとうございます。サブスクリプションが正常に有効化されました。
            </p>

            <!-- 订阅详情 -->
            <div class="cyber-glass rounded-2xl p-6 mb-8 text-left">
                <h3 class="text-lg font-semibold text-cyan-400 mb-4">
                    <i class="fas fa-receipt mr-2"></i>サブスクリプション詳細
                </h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-cyan-300">プラン:</span>
                        <span class="text-white font-semibold" id="planName">読み込み中...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-cyan-300">料金:</span>
                        <span class="text-white font-semibold" id="planPrice">読み込み中...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-cyan-300">サブスクリプションID:</span>
                        <span class="text-white font-mono text-sm" id="subscriptionId">読み込み中...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-cyan-300">開始日:</span>
                        <span class="text-white" id="startDate">読み込み中...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-cyan-300">次回請求日:</span>
                        <span class="text-white" id="nextBillingDate">読み込み中...</span>
                    </div>
                </div>
            </div>

            <!-- 功能解锁提示 -->
            <div class="cyber-glass rounded-2xl p-6 mb-8 text-left">
                <h3 class="text-lg font-semibold text-cyan-400 mb-4">
                    <i class="fas fa-unlock mr-2"></i>解除された機能
                </h3>
                
                <ul class="space-y-2 text-cyan-200" id="unlockedFeatures">
                    <!-- 功能列表将通过 JavaScript 动态生成 -->
                </ul>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="dashboard.html" class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white py-3 px-8 rounded-xl font-semibold hover:from-cyan-600 hover:to-blue-700 transition-all duration-300">
                    <i class="fas fa-tachometer-alt mr-2"></i>ダッシュボードへ
                </a>
                
                <a href="user_settings.html" class="bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 px-8 rounded-xl font-semibold hover:from-purple-600 hover:to-pink-700 transition-all duration-300">
                    <i class="fas fa-cog mr-2"></i>設定
                </a>
            </div>

            <!-- 支持信息 -->
            <div class="mt-8 pt-6 border-t border-cyan-400/20">
                <p class="text-cyan-300/70 text-sm mb-2">
                    ご質問やサポートが必要な場合は、お気軽にお問い合わせください。
                </p>
                <a href="mailto:<EMAIL>" class="text-cyan-400 hover:text-cyan-300 text-sm transition-colors">
                    <i class="fas fa-envelope mr-1"></i><EMAIL>
                </a>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSuccessPage();
            createConfetti();
        });

        // 初始化成功页面
        async function initSuccessPage() {
            try {
                // 从 URL 参数获取信息
                const urlParams = new URLSearchParams(window.location.search);
                const planId = urlParams.get('plan');
                const subscriptionId = urlParams.get('subscription');

                if (!planId || !subscriptionId) {
                    throw new Error('Missing required parameters');
                }

                // 获取套餐信息
                const plan = window.PayPalConfig.plans[planId];
                if (!plan) {
                    throw new Error('Invalid plan ID');
                }

                // 更新页面信息
                updatePageInfo(plan, subscriptionId);

                // 获取订阅详细信息
                await fetchSubscriptionDetails(subscriptionId);

            } catch (error) {
                console.error('Failed to initialize success page:', error);
                showError('ページの初期化に失敗しました');
            }
        }

        // 更新页面信息
        function updatePageInfo(plan, subscriptionId) {
            document.getElementById('planName').textContent = plan.name;
            document.getElementById('planPrice').textContent = plan.price === 0 ? '無料' : `¥${plan.price.toLocaleString()}/月`;
            document.getElementById('subscriptionId').textContent = subscriptionId;
            document.getElementById('startDate').textContent = new Date().toLocaleDateString('ja-JP');
            
            // 计算下次计费日期（一个月后）
            const nextBilling = new Date();
            nextBilling.setMonth(nextBilling.getMonth() + 1);
            document.getElementById('nextBillingDate').textContent = nextBilling.toLocaleDateString('ja-JP');

            // 显示解锁的功能
            const featuresContainer = document.getElementById('unlockedFeatures');
            featuresContainer.innerHTML = plan.features.map(feature => 
                `<li><i class="fas fa-check text-green-400 mr-2"></i>${feature}</li>`
            ).join('');
        }

        // 获取订阅详细信息
        async function fetchSubscriptionDetails(subscriptionId) {
            try {
                const token = localStorage.getItem('goldenledger_session_token');
                if (!token) return;

                const response = await fetch(window.GoldenLedgerAPI.url('/api/subscription/details'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        subscription_id: subscriptionId
                    })
                });

                const result = await response.json();
                if (result.success && result.subscription) {
                    // 更新实际的订阅信息
                    updateSubscriptionInfo(result.subscription);
                }
            } catch (error) {
                console.error('Failed to fetch subscription details:', error);
            }
        }

        // 更新订阅信息
        function updateSubscriptionInfo(subscription) {
            if (subscription.start_time) {
                document.getElementById('startDate').textContent = 
                    new Date(subscription.start_time).toLocaleDateString('ja-JP');
            }
            
            if (subscription.next_billing_time) {
                document.getElementById('nextBillingDate').textContent = 
                    new Date(subscription.next_billing_time).toLocaleDateString('ja-JP');
            }
        }

        // 创建彩带效果
        function createConfetti() {
            const container = document.getElementById('confettiContainer');
            const colors = ['#00d4ff', '#0099cc', '#00ff88', '#ff6b6b', '#ffd93d'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.animationDelay = Math.random() * 3 + 's';
                    confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    
                    container.appendChild(confetti);
                    
                    // 3秒后移除
                    setTimeout(() => {
                        if (confetti.parentNode) {
                            confetti.parentNode.removeChild(confetti);
                        }
                    }, 5000);
                }, i * 100);
            }
        }

        // 显示错误信息
        function showError(message) {
            alert(message); // 简单的错误显示，可以后续改进
        }
    </script>
</body>
</html>
